//
//  KSPrivateProtectLocationMangaer.h
//  QQKSong
//
//  Created by s<PERSON><PERSON><PERSON><PERSON> on 2021/11/23.
//  Copyright © 2021 Tencent. All rights reserved.
//
/*
 个人隐私保护法已经上线
 1. 甲方【不同意】以【任何】理由【未经报备】【上架】应用商店，倘若检测发现问题会予以【下架】。
 2. 上架应用商店前需提交工信部审核
 3. 地理位置信息属于敏感隐私，K歌没有正当理由需减少调用
 */
#import <Foundation/Foundation.h>
#import <CoreLocation/CoreLocation.h>

typedef void (^PrivateProtectLogBlock)(NSString *message);


typedef NS_ENUM(NSInteger, KSPPLocationRequestType) {
    KSPPLocationRequestType_Default             = 0, //默认请求，仅允许第一次请求，后续同一位置发起的请求都会被block并返回缓存
    KSPPLocationRequestType_WantForceUpdate     = 1, //希望强制请求，根据内部的逻辑判断，尽可能的去发起请求
    KSPPLocationRequestType_NeedForceCache      = 2, //希望强制缓存，希望不要真实的发起请求，直接返回缓存的位置信息，这里是为了避免修改老业务代码，如果新增的业务希望读取缓存，请直接使用[[CLLocationoMangaer alloc] init].location
};

/*
 可以用这个类来通知LocationProtectManager将要请求位置信息的个别位置，但是具体是否放行还是要在KSPrivateProtectLocationManager这个类内部判断
 
 后续可以在这个类里做扩展
 */
@interface KSPPLocationRequestAdditionInfo : NSObject
//是否请求真正的更新,具体是否更新需要在这个类里面判断
@property (nonatomic, assign) KSPPLocationRequestType additionRequestType;
//目前这个请求仅Hippy在用
@property (nonatomic, strong) NSString *hippyProject;

@end

//是否展示权限提醒
@interface KSPPLocationDenyAlertAdditionInfo : NSObject
//是否请求真正的更新,具体是否更新需要在这个类里面判断
@property (nonatomic, assign) BOOL wantforceGrant;

@end

/*
 位置信息保护主类
 
 ⚠️特殊说明：这个类并【不用于】真正的【申请位置信息】，申请位置信息请【照常使用】【CLLocationManager】即可，如果申请前需要对这次请求做一些操作，是要通过这个Manager进行特殊处理
 ⚠️特殊说明：这个类并【不用于】真正的【申请位置信息】，申请位置信息请【照常使用】【CLLocationManager】即可，如果申请前需要对这次请求做一些操作，是要通过这个Manager进行特殊处理
 ⚠️特殊说明：这个类并【不用于】真正的【申请位置信息】，申请位置信息请【照常使用】【CLLocationManager】即可，如果申请前需要对这次请求做一些操作，是要通过这个Manager进行特殊处理
 
 为了防止第三方SDK的调用，所以这里使用了Hook的方法
 */
@interface KSPrivateProtectLocationManager : NSObject

+ (KSPrivateProtectLocationManager *)sharedManager;

- (void)prepareData;

/// 设置wns开关
/// - Parameter enable: YES：异步查询CLAuthorization，NO：调用线程查询
+ (void)setWnsAsyncGetCLAuthorizationDisable:(BOOL)enable;
/// fishhook CNCopyCurrentNetworkInfo
+ (void)privateHoookCNCopyCurrentNetworkInfo;

//是否内部调用
- (void)setInternalBuild:(BOOL)internal;
//日志打印回调
- (void)setLogBlock:(PrivateProtectLogBlock)block;
//delay弹窗需要的时间间隔
- (void)setDenyAlertGap:(NSInteger)denyAlertHour;

- (void)setUseCachedCLAuthorizationStatus:(BOOL)enable;

//需要确保WNS启动后执行这里的方法,下次启动生效
- (void)setWNSSwitch:(BOOL)hook;

//开始启动倒计时，如果不运行这里，会导致位置信息无法获取
- (void)startLaunchCountDown;

//只针对下一次请求地理位置【信息】生效
- (void)additionInfoWhenWillRequestUpdate:(KSPPLocationRequestAdditionInfo *)req;

//只针对下一次请求地理位置【信息】请求生效
- (void)additionInfoWheUserDeny:(KSPPLocationDenyAlertAdditionInfo *)req;

//当前是否有位置权限
- (BOOL)hasLocationRight;

//展示拒绝弹窗
- (void)showDenyAlertView;

//上一次缓存的位置信息，可能会返回nil（没有权限或者本地无缓存的情况）
- (CLLocation *)getLastLocation;

// 是否已持有弹窗对象
- (BOOL)hasDialog;

/// 弹窗对象是否正在展示中
- (BOOL)isDialogShowing;

/// 重置弹窗对象
- (void)resetDialog;
@end
