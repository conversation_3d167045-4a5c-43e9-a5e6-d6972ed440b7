
//
//  KSPrivateProtectLocationMangaer.m
//  QQKSong
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2021/11/23.
//  Copyright © 2021 Tencent. All rights reserved.
//

#import <mach-o/dyld.h>
#import <objc/runtime.h>
#import "KSPrivateProtectLocationManager.h"
#import <CoreLocation/CoreLocation.h>
#import "KSBasicUI.h"
#import "BaseModule.h"
#import "ksppl_fishhook.h"
#import "KSUIImage.h"
#import <WnsSDK/KSLogDef.h>
#import <COMPServiceManager/WnsConfigManagerProtocol.h>

#define KSNotification_ProtectLocation_Dialog_Dismiss          @"KSNotification_ProtectLocation_Dialog_Dismiss"

#define ks_manager_key @"mgr"
#define ks_delegate_key @"dlg"

#define ks_ppl_userdefaultkey_locations             @"ks_ppl_userdefaultkey_locations"
#define ks_ppl_userdefaultkey_locations_lastdate    @"ks_ppl_userdefaultkey_locations_lastdate"
#define ks_ppl_userdefaultkey_deny_lastdate         @"ks_ppl_userdefaultkey_deny_lastdate"
#define ks_ppl_userdefaultkey_enableHook            @"ks_ppl_userdefaultkey_enableHook"

#define ks_ppl_userdefaultkey_asyncGetCLAuthorizationDisable @"ks_ppl_userdefaultkey_asyncGetCLAuthorizationDisable"

#define KSPP_Info(...)    {[[KSPrivateProtectLocationManager sharedManager] printLog:__VA_ARGS__];}
#define KSPLog   KSPP_Info

/*
 个人隐私保护法已经上线
 1. 甲方不同意以各种理由未经报备上架应用商店，倘若检测发现问题会予以下架。
 2. 上架应用商店前需提交工信部审核
 3. 地理位置信息属于敏感隐私，K歌没有正当理由需减少调用
 4. 不能每次启动都调用位置信息
 */

@implementation KSPPLocationRequestAdditionInfo

@end

@implementation KSPPLocationDenyAlertAdditionInfo : NSObject

@end

@interface KSPrivateProtectLocationManager()<CLLocationManagerDelegate>

@property (nonatomic, strong) NSMutableArray *delegateManagers; //保存的生成的CLLocationManager和对应Delegate

@property (nonatomic, strong) NSMutableArray *requestRecords; //保存现在位置信息请求的路径的信息

@property (nonatomic, assign) BOOL useCachedCLAuthorizationStatus;
@property (nonatomic, assign) CLAuthorizationStatus clAuthStatusDefault_1;// 位置授权状态（未查询到时初始-1，业务使用请校验>0）

@property (nonatomic, strong) NSArray *cachedLocations; //保存的更新的位置

@property (nonatomic, assign) NSInteger tryUpdateCount; //仅内部使用，用于标记总共调用了多少次更新

@property (nonatomic, assign) NSInteger realUpdateCount; //仅内部使用，用于标记总共实际向系统发起了多少次请求

@property (nonatomic, assign) BOOL inLaunchProtection; // 是否在启动保护中（有权限的情况下）

@property (nonatomic, assign) BOOL allowFirstRequestInLaunch; // 是否允许启动保护中第一次请求 （有权限的情况下）

@property (nonatomic, strong) KSPPLocationRequestAdditionInfo *reqAdditionInfo; //用于记录最近的特殊外部位置信息请求，需要在读取后清空

@property (nonatomic, strong) KSPPLocationDenyAlertAdditionInfo *denyAdditionInfo; //用于记录最近的特殊外部位置信息请求，需要在读取后清空

@property (nonatomic, strong) NSNumber *locationReqMarkAddress; //当前记录下请求的地址信息

@property (nonatomic, assign) BOOL isStatusChanged; //是否Location权限的状态改变了

@property (nonatomic, assign) BOOL enableHookFounction; //当前启动Hook是否生效

@property (nonatomic, assign) BOOL internalBuild; //是否是内部构建

@property (nonatomic, assign) NSInteger denyAlertGap; //拒绝弹窗间隔时间

@property (nonatomic, copy) PrivateProtectLogBlock logBlock; //外部打印日志

@property (nonatomic, strong) NSMutableArray *tempLogCache;

@property (nonatomic, strong) KSOperationalDialog *dialog;

@end

@implementation KSPrivateProtectLocationManager
+(void)load{
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        //这里Hook CLLocation Manager的几个基础操作，将相关操作转到此类里面，下面这几个方法都会发起位置信息请求
        SEL originalSelector1 = @selector(setDelegate:);
        SEL swizzledSelector1 = @selector(setPrivateDelegate:);

        SEL originalSelector2 = @selector(startUpdatingLocation);
        SEL swizzledSelector2 = @selector(privateStartLocationUpdate);
        
        SEL originalSelector3 = @selector(requestWhenInUseAuthorization);
        SEL swizzledSelector3 = @selector(privateRequestWhenInUseAuthorization);
        
        SEL originalSelector4 = @selector(requestLocation);
        SEL swizzledSelector4 = @selector(privateRequestLocation);
        
        SEL originalSelector5 = @selector(location);
        SEL swizzledSelector5 = @selector(privateLocation);
        [KSPrivateProtectLocationManager originalSelector:originalSelector1 swizzleSelector:swizzledSelector1];
        [KSPrivateProtectLocationManager originalSelector:originalSelector2 swizzleSelector:swizzledSelector2];
        [KSPrivateProtectLocationManager originalSelector:originalSelector3 swizzleSelector:swizzledSelector3];
        [KSPrivateProtectLocationManager originalSelector:originalSelector4 swizzleSelector:swizzledSelector4];
        [KSPrivateProtectLocationManager originalSelector:originalSelector5 swizzleSelector:swizzledSelector5];
    });
    
}

// +load方法中触发XPC导致可能
+ (void)privateHoookCNCopyCurrentNetworkInfo {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        BOOL canHook = [[NSUserDefaults standardUserDefaults] boolForKey:ks_ppl_userdefaultkey_enableHook];
        
        if ([COMP_SERVICE_FOR(WnsConfigManagerProtocol) isPGSH]) {
            canHook = NO;
            KLog(@"isPGSH");
        }
        
        KLog(@"privateHoookCNCopyCurrentNetworkInfo %@", @(canHook));
        if (!canHook) {
            return;
        }
        
        if ([self isValidAsynGetCLAuthorization]) {
            KLog(@"异步查询CLLocation权限");
            [self dispatchGetInGlobalQueue:^NSObject *{
                CLAuthorizationStatus authorizationStatus = [self getLocationAuthorizationStatus];
                if (authorizationStatus == kCLAuthorizationStatusAuthorizedWhenInUse ||
                    authorizationStatus == kCLAuthorizationStatusAuthorizedAlways)
                {
                    return @(YES);
                }
                else
                {
                    return @(NO);
                }
            } callBackInMainQueue:^(NSObject *obj) {
                KLog(@"异步查询CLLocation权限 callback result: %@", obj);
                NSNumber *result = SAFE_CAST(obj, NSNumber);
                // 允许访问地理位置
                if (result != nil && [result boolValue])
                {
                    [self _privateHoookCurrentNetworkInfo];
                }
            }];
            
        } else {
            CLAuthorizationStatus authorizationStatus = [self getLocationAuthorizationStatus];
            if ((authorizationStatus == kCLAuthorizationStatusAuthorizedWhenInUse ||
                 authorizationStatus == kCLAuthorizationStatusAuthorizedAlways))
            {
                [self _privateHoookCurrentNetworkInfo];
            }
        }
    });
}

static BOOL gAsyncGetCLAuthorizationEnable = YES;
+ (void)setWnsAsyncGetCLAuthorizationDisable:(BOOL)disable {
    gAsyncGetCLAuthorizationEnable = !disable;
    
    [[NSUserDefaults standardUserDefaults] setBool:disable forKey:ks_ppl_userdefaultkey_asyncGetCLAuthorizationDisable];
}

+ (BOOL)isValidAsynGetCLAuthorization
{
    BOOL isDisable = [[NSUserDefaults standardUserDefaults] boolForKey:ks_ppl_userdefaultkey_asyncGetCLAuthorizationDisable];
    KLog(@"isValidAsynGetCLAuthorization: %@", @(isDisable));
    
    return !isDisable;
}

+ (void)_privateHoookCurrentNetworkInfo {
    KLog(@"_privateHoookCurrentNetworkInfo");
    //根据Q音同学的反馈，CNCopyCurrentNetworkInfo内部也会去访问位置信息，也需要被hook掉
    struct ksppl_rebinding rebindingArray[] = {
        {"CNCopyCurrentNetworkInfo", (void *)ks_CNCopyCurrentNetworkInfo, (void **)&orig_CNCopyCurrentNetworkInfo},
    };
    ksppl_rebind_symbols(rebindingArray, 1);
    
    KLog(@"_privateHoookCurrentNetworkInfo finish");
}

+ (CLAuthorizationStatus)getLocationAuthorizationStatus
{
    BOOL useCacheCLStatus = [KSPrivateProtectLocationManager sharedManager].useCachedCLAuthorizationStatus;
    if (useCacheCLStatus) {
        CLAuthorizationStatus status = [KSPrivateProtectLocationManager sharedManager].clAuthStatusDefault_1;
        if (status >= 0) {
            KLog(@"CLAuthorizationStatus use cache: %d", status);
            return status;
        }
    }
    
    if (@available(iOS 14, *))
    {
        static CLLocationManager *locationManager = nil;
        static dispatch_once_t onceToken;
        dispatch_once(&onceToken, ^{
            locationManager = [[CLLocationManager alloc] init];
        });
        
        return locationManager.authorizationStatus;
    }
    else
    {
        return [CLLocationManager authorizationStatus];
    }
}

typedef NSObject *(^KSDispatchBlock)(void);
typedef void (^KSDispatchCallbackBlock)(NSObject *obj);

+ (void)dispatchGetInGlobalQueue:(KSDispatchBlock)getObjBlock
             callBackInMainQueue:(KSDispatchCallbackBlock)objBackblock
{
    if (nil != getObjBlock)
    {
        dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
            NSObject *obj = getObjBlock();
            if (nil != objBackblock)
            {
                dispatch_async(dispatch_get_main_queue(), ^{
                        objBackblock(obj);
                });
            }
        });
    }
    else
    {
        ASSERT(0);
    }
}

// CFDictionaryRef CNCopyCurrentNetworkInfo        (CFStringRef interfaceName)
static CFDictionaryRef (*orig_CNCopyCurrentNetworkInfo)(CFStringRef interfaceName);
 
static CFDictionaryRef ks_CNCopyCurrentNetworkInfo(CFStringRef interfaceName) {
    return NULL;
}


+ (KSPrivateProtectLocationManager*)sharedManager{
    
    static KSPrivateProtectLocationManager *sharedManager = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        sharedManager = [[KSPrivateProtectLocationManager alloc] init];
//        [sharedManager prepareData];
    });
    return sharedManager;
}

- (instancetype)init {
    if (self = [super init]) {
        _clAuthStatusDefault_1 = -1;
        _useCachedCLAuthorizationStatus = YES;
    }
    
    return self;
}

//日志打印回调
- (void)setLogBlock:(PrivateProtectLogBlock)block
{
    _logBlock = block;
    
    if (self.tempLogCache.count > 0) {
        for (NSString *str in self.tempLogCache) {
            _logBlock(str);
        }
        self.tempLogCache = nil;
    }
}

- (void)setInternalBuild:(BOOL)internal
{
    _internalBuild = internal;
}
//delay弹窗需要的时间间隔
- (void)setDelayAlertGap:(NSInteger)denyAlertHour
{
    _denyAlertGap = denyAlertHour;
}

//需要确保WNS启动后执行这里的方法,下次启动生效
- (void)setWNSSwitch:(BOOL)hook
{
    [[NSUserDefaults standardUserDefaults] setBool:hook forKey:ks_ppl_userdefaultkey_enableHook];
    [[NSUserDefaults standardUserDefaults] synchronize];
}

- (void)setUseCachedCLAuthorizationStatus:(BOOL)enable {
    _useCachedCLAuthorizationStatus = enable;
}

- (void)prepareData
{
#ifdef DEBUG
    self.internalBuild = YES;
#endif
    self.tempLogCache = [[NSMutableArray alloc] init];
    //读取本地缓存位置信息
    NSData *arrayData = [[NSUserDefaults standardUserDefaults] objectForKey:ks_ppl_userdefaultkey_locations];
    NSDate *lastDate = [[NSUserDefaults standardUserDefaults] objectForKey:ks_ppl_userdefaultkey_locations_lastdate];
    
    //读取下是否允许hook
    if  ([[NSUserDefaults standardUserDefaults] objectForKey:ks_ppl_userdefaultkey_enableHook]) {
        self.enableHookFounction = [[NSUserDefaults standardUserDefaults] boolForKey:ks_ppl_userdefaultkey_enableHook];
    }
    else {
        //从来没打开过的话，为了合规，默认为可以hook
        self.enableHookFounction = YES;
    }
    
    //如果有缓存数据了 且 上次缓存数据的时间是同一天之内
    if (arrayData && lastDate && [KSPrivateProtectLocationManager isSameDay:[NSDate date] date2:lastDate]) {
        //直接使用缓存
        //数据安全侧不允许每次启动的时候都读取安全信息
        self.cachedLocations = [NSKeyedUnarchiver unarchiveObjectWithData:arrayData];
        KSPLog(@"当天缓存位置：%@", self.cachedLocations.lastObject);
    }
    else {
        //如果没有本地数据，或者夸天了，仅允许第一次请求地理位置信息
        self.allowFirstRequestInLaunch = YES;
    }

    self.inLaunchProtection = YES;
    
    if ([self.class isValidAsynGetCLAuthorization]) {
        KS_WEAK_SELF(self);
        [self.class dispatchGetInGlobalQueue:^NSObject *{
            CHECK_SELF_AND_RETURN_WITH(nil);
            
            CLAuthorizationStatus status = [self.class getLocationAuthorizationStatus];
            return @(status);
        } callBackInMainQueue:^(NSObject *obj) {
            CHECK_SELF_AND_RETURN();
            
            NSNumber *statusNum = SAFE_CAST(obj, NSNumber);
            if (statusNum) {
                self.clAuthStatusDefault_1 = (CLAuthorizationStatus)[statusNum integerValue];
            }
        }];
    } else {
        self.clAuthStatusDefault_1 = [self.class getLocationAuthorizationStatus];
    }
}

-(void)startLaunchCountDown
{
    //这里延迟10S后，开放业务层地理信息获取
    KS_WEAK_SELF(self);
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(15 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        CHECK_SELF_AND_RETURN();
        self.inLaunchProtection = NO;
    });
}

#pragma mark - Hook的方法
//❌Hook用，不建议添加此函数的外部或内部调用❌
-(void)privateRequestWhenInUseAuthorization
{
    if (![KSPrivateProtectLocationManager sharedManager].enableHookFounction) {
        [self privateRequestWhenInUseAuthorization];
        return;
    }
    KSPLog(@"请求位置权限");
    
    if ([KSPrivateProtectLocationManager sharedManager].internalBuild) {
        NSString *name = [[NSThread callStackSymbols] safeObjectAtIndex:2];
        KSPLog(@"[仅内部版提示]为保护用户隐私,%@处的请求了位置权限",name);
    }
    
    
    if ([CLLocationManager authorizationStatus] != kCLAuthorizationStatusNotDetermined) {
        if (![[KSPrivateProtectLocationManager sharedManager] hasLocationRight]) {
            [[KSPrivateProtectLocationManager sharedManager] failWithError:[NSError errorWithDomain:@"无权限" code:kCLErrorDenied userInfo:0]];
        }
        return;
    }
    
    if ([KSPrivateProtectLocationManager sharedManager].dialog) {
        return;
    }
    
    KSOperationalDialog *dialog = [[KSOperationalDialog alloc] init];
    [dialog setHeadingTopImage:[KSKUIImage imageNamed:@"location_banner_rect"]];
    [dialog setHeadingCaption:@"定位权限申请"];

    if ([KSPrivateProtectLocationManager sharedManager].internalBuild) {
        if ([[NSThread callStackReturnAddresses] containsObject:[KSPrivateProtectLocationManager sharedManager].locationReqMarkAddress]) {
            NSMutableString *tips = [NSMutableString stringWithFormat:@"【内部】不建议在didChangeAuthorizationStatus里请求权限，会有合规问题"];
            [dialog addContentText:tips];
            //这里合规问题的原因：在用户切换前后态后，当状态变动，可能在非需要位置的页面请求位置权限，这里甲方是不允许的
            //比如 去到过动态页 -> 在点歌台切后台 —> 修改位置权限为询问 -> 用户切前台 -> 点歌台弹出位置权限请求
            KSPLog(@"内部:不建议在didChangeAuthorizationStatus里请求权限，会有合规问题");
        }
    }
    
    NSString *title = @"全民K歌需要获取「位置信息」权限，以保证使用附近的人、地区榜、开播、收货地址等功能";
    
    if([KSPrivateProtectLocationManager sharedManager].isLocationRightForThirdPartyPage) {
        if ([KSPrivateProtectLocationManager sharedManager].internalBuild) {
            KSPLog(@"[仅内部版提示]如果申请位置权限前没有调用useKGReminder，会更改申请文案");
            //By灵犀合规 - 广告落地页会有广告主自行取申请一些权限，但是这个时候显示的主体是全民K歌，这是不允许的
        }
        title = @"「第三方页面」向「全民K歌」申请获取您的「位置信息」权限";
    }

    NSMutableAttributedString *mutableAttriString = [[NSMutableAttributedString alloc] init];
    NSAttributedString *attriString = [[NSAttributedString alloc] initWithString:title attributes:@{NSFontAttributeName:[UIFont systemFontOfSize:14 weight:UIFontWeightMedium], NSForegroundColorAttributeName:[UIColor ks_blackColor]}];
    [mutableAttriString setAttributedString:attriString];
    NSRange range = [title rangeOfString:@"「位置信息」"];
    [mutableAttriString addAttributes:@{NSFontAttributeName:[UIFont systemFontOfSize:14 weight:UIFontWeightMedium], NSForegroundColorAttributeName:[UIColor ks_colorWithRGBString:@"#fe4f4f"]} range:range];
    
   
    [dialog addContentAttributedText:mutableAttriString];
    [dialog addControlButton:@"取消" subtitle:nil buttonStyle:KSBasicDialogButtonStyle_Normal callback:^(KSDialog * _Nonnull dialog) {
        [[NSNotificationCenter defaultCenter] postNotificationName:KSNotification_ProtectLocation_Dialog_Dismiss object:nil];
        
        [dialog dismiss];
    }];
    
    [dialog addControlButton:@"确定" subtitle:nil buttonStyle:KSBasicDialogButtonStyle_Emphasis callback:^(KSDialog * _Nonnull dialog) {
        
        [KSPrivateProtectLocationManager sharedManager].dialog = nil;
        
        [dialog dismiss];
        
        [[NSNotificationCenter defaultCenter] postNotificationName:KSNotification_ProtectLocation_Dialog_Dismiss object:nil userInfo:@{@"isFromConfirm": @"1"}];
        
        [self privateRequestWhenInUseAuthorization];
    }];
    
    [dialog show];
    
    [[NSUserDefaults standardUserDefaults] setObject:[NSDate date] forKey:ks_ppl_userdefaultkey_deny_lastdate];
    [[NSUserDefaults standardUserDefaults] synchronize];
    [KSPrivateProtectLocationManager sharedManager].dialog = dialog;

}

//❌Hook用，不建议添加此函数的外部或内部调用❌
- (void)privateRequestLocation
{
    if (![KSPrivateProtectLocationManager sharedManager].enableHookFounction) {
        [self privateRequestLocation];
        return;
    }

    if ([KSPrivateProtectLocationManager sharedManager].internalBuild) {
        NSString *name = [[NSThread callStackSymbols] safeObjectAtIndex:2];
        KSPLog(@"【仅内部打印】为保护用户隐私,%@处的requestLocation调用已被替换到KSPrivateProtectLocationManager",name);
        [KSPrivateProtectLocationManager sharedManager].tryUpdateCount ++;
    }
    [[KSPrivateProtectLocationManager sharedManager] updateLocations];
}

//❌Hook用，不建议添加此函数的外部或内部调用❌
- (void)setPrivateDelegate:(id<CLLocationManagerDelegate>)delegate
{
    if (![KSPrivateProtectLocationManager sharedManager].enableHookFounction) {
        [self setPrivateDelegate:delegate];
        return;
    }
    
    if ([KSPrivateProtectLocationManager sharedManager].internalBuild) {
        NSString *name = [[NSThread callStackSymbols] safeObjectAtIndex:2];
        KSPLog(@"[仅内部版提示]为保护用户隐私,%@处的CLLocationManagerDelegate调用已被替换到KSPrivateProtectLocationManager",name);
    }
    
#ifdef DEBUG
    [KSToast showToast:@"【开发⚠️】CLLocationManagerDelegate被Hook了，需要了解详情的同学请搜索这段文案"];
    KSPLog(@"【开发提示⚠️】Delegate被hook了，替换了调用，此为合规改动");
    /**
     ⚠️本类仅实现了下面几个方法 以及 相应的转发，如果需要添加新方法，请在本类里面添加，并【更新这里的注释】
     - (void)locationManagerDidChangeAuthorization:(CLLocationManager *)manager
     - (void)locationManager:(CLLocationManager *)manager didChangeAuthorizationStatus:(CLAuthorizationStatus)status
     - (void)locationManager:(CLLocationManager *)manager didUpdateLocations:(NSArray *)locations
     - (void)locationManager:(CLLocationManager *)manager didFailWithError:(NSError *)error
     ⚠️本类仅实现了下面几个方法 以及 相应的转发，如果需要添加新方法，请在本类里面添加，并【更新这里的注释】
     */
    
#endif

    //将delegate和manager对应保护起来，
    KSWeakRef *dlg = [[KSWeakRef alloc] init];
    dlg.ref = delegate;
    
    KSWeakRef *mgr = [[KSWeakRef alloc] init];
    mgr.ref = self;
    [[KSPrivateProtectLocationManager sharedManager].delegateManagers addObject:@{ks_manager_key:mgr, ks_delegate_key:dlg}];
    [self setPrivateDelegate:[KSPrivateProtectLocationManager sharedManager]];
    
}

//❌Hook用，不建议添加此函数的外部或内部调用❌
- (void)privateStartLocationUpdate
{
    if (![KSPrivateProtectLocationManager sharedManager].enableHookFounction) {
        [self privateStartLocationUpdate];
        return;
    }
    
    if (![[KSPrivateProtectLocationManager sharedManager] hasLocationRight]) {
        [[KSPrivateProtectLocationManager sharedManager] failWithError:[NSError errorWithDomain:@"无权限" code:kCLErrorDenied userInfo:0]];
        KSPLog(@"没有权限，先不真的发起请求了");
        return;
    }
    
    if ([KSPrivateProtectLocationManager sharedManager].internalBuild) {
        NSString *name = [[NSThread callStackSymbols] safeObjectAtIndex:2];
        KSPLog(@"【仅内部打印】为保护用户隐私,%@处的startLocationUpdate调用已被替换到KSPrivateProtectLocationManager",name);
        [KSPrivateProtectLocationManager sharedManager].tryUpdateCount ++;
    }
    
    KS_WEAK_SELF(self);
    [[KSPrivateProtectLocationManager sharedManager] checkIfCanUpadteLocation:^(BOOL ret){
        CHECK_SELF_AND_RETURN();
        if (!ret) {
            KSPLog(@"用户决定不获取位置信息");
            return;
        }
        
        if ([[KSPrivateProtectLocationManager sharedManager] canUpdateLocationInfo]) {
            [KSPrivateProtectLocationManager sharedManager].allowFirstRequestInLaunch = NO;
            //这里是CLLocation调用的startLocationUpdate， self是CLLocationManager
            [self privateStartLocationUpdate];
            if ([KSPrivateProtectLocationManager sharedManager].internalBuild) {
                [KSPrivateProtectLocationManager sharedManager].realUpdateCount ++;
                dispatch_async(dispatch_get_main_queue(), ^{
                    [KSToast showToast:[NSString stringWithFormat:@"内部:更新位置信息，总调用:%ld,实际访问:%ld",[KSPrivateProtectLocationManager sharedManager].tryUpdateCount,[KSPrivateProtectLocationManager sharedManager].realUpdateCount]];
                });
            }
            [[NSUserDefaults standardUserDefaults] setObject:[NSDate date] forKey:ks_ppl_userdefaultkey_locations_lastdate];
            [[NSUserDefaults standardUserDefaults] synchronize];
        }
        else {
            if ([KSPrivateProtectLocationManager sharedManager].internalBuild) {
                NSString *name = [[NSThread callStackSymbols] safeObjectAtIndex:2];
                KSPLog(@"【仅内部打印】为保护用户隐私,%@处的startLocationUpdate仅允许调用一次",name);
                dispatch_async(dispatch_get_main_queue(), ^{
                    [KSToast showToast:[NSString stringWithFormat:@"内部:更新位置信息，总调用:%ld,实际访问:%ld",[KSPrivateProtectLocationManager sharedManager].tryUpdateCount,[KSPrivateProtectLocationManager sharedManager].realUpdateCount]];
                });
                
            }
            if ([[KSPrivateProtectLocationManager sharedManager] hasLocationRight]) {
                [[KSPrivateProtectLocationManager sharedManager] updateLocations];
            }
            else
            {
                [[KSPrivateProtectLocationManager sharedManager] failWithError:[NSError errorWithDomain:@"无权限" code:kCLErrorDenied userInfo:0]];
            }
        }
    }];
}

//❌Hook用，不建议添加此函数的外部或内部调用❌
- (CLLocation *)privateLocation
{
    if (![KSPrivateProtectLocationManager sharedManager].enableHookFounction) {
        return [self privateLocation];
    }
    if ([KSPrivateProtectLocationManager sharedManager].internalBuild) {
        NSString *name = [[NSThread callStackSymbols] safeObjectAtIndex:2];
        KSPLog(@"【仅内部打印】为保护用户隐私,%@处的location调用已被替换到KSPrivateProtectLocationManager",name);
        [KSPrivateProtectLocationManager sharedManager].tryUpdateCount ++;
        dispatch_async(dispatch_get_main_queue(), ^{
            [KSToast showToast:[NSString stringWithFormat:@"内部:更新位置信息，总调用:%ld,实际访问:%ld",[KSPrivateProtectLocationManager sharedManager].tryUpdateCount,[KSPrivateProtectLocationManager sharedManager].realUpdateCount]];
        });
    }
    
    if ([KSPrivateProtectLocationManager sharedManager].hasLocationRight) {
        return [KSPrivateProtectLocationManager sharedManager].cachedLocations.lastObject;
    }
    else {
        return nil;
    }
}
#pragma mark - 其他条件判断等
//检查下是否能直接更新位置信息，如果是第三方页面，这里需要弹个框
- (void)checkIfCanUpadteLocation:(void(^)(BOOL ret))block{
    //需要先检查一下请求的类是那个
    if (self.isLocationRightForThirdPartyPage) {
        KSOperationalDialog *dialog = [[KSOperationalDialog alloc] init];
        [dialog setHeadingTopImage:[KSKUIImage imageNamed:@"location_banner_rect"]];
        [dialog setHeadingCaption:@"定位获取申请"];
        self.dialog = dialog;
        NSString *title = @"「第三方页面」申请使用您当前的「位置信息」";
        
        NSMutableAttributedString *mutableAttriString = [[NSMutableAttributedString alloc] init];
        NSAttributedString *attriString = [[NSAttributedString alloc] initWithString:title attributes:@{NSFontAttributeName:[UIFont systemFontOfSize:14 weight:UIFontWeightMedium], NSForegroundColorAttributeName:[UIColor ks_blackColor]}];
        [mutableAttriString setAttributedString:attriString];
        
        NSRange range = [title rangeOfString:@"「位置信息」"];
        [mutableAttriString addAttributes:@{NSFontAttributeName:[UIFont systemFontOfSize:14 weight:UIFontWeightMedium], NSForegroundColorAttributeName:[UIColor ks_colorWithRGBString:@"#fe4f4f"]} range:range];
        
       
        [dialog addContentAttributedText:mutableAttriString];
        [dialog addControlButton:@"取消" subtitle:nil buttonStyle:KSBasicDialogButtonStyle_Normal callback:^(KSDialog * _Nonnull dialog) {
            [dialog dismiss];
            if (block) {
                block(NO);
            }
        }];
        
        [dialog addControlButton:@"确定" subtitle:nil buttonStyle:KSBasicDialogButtonStyle_Emphasis callback:^(KSDialog * _Nonnull dialog) {
            
            self.dialog = nil;
            [dialog dismiss];
            
            if (block) {
                block(YES);
            }
            
        }];
        dispatch_async(dispatch_get_main_queue(), ^{
            [dialog showInNewWindow];
        });
    }
    else {
        if (block) {
            block(YES);
        }
    }
}

//当前页面是否是第三方页面：主要是广告落地页，会被灵犀怼
- (BOOL)isLocationRightForThirdPartyPage
{
    UIViewController *vc = [UIApplication sharedApplication].keyWindow.rootViewController;
    if ([vc isKindOfClass:UINavigationController.class]) {
        UINavigationController *nav = (UINavigationController *)vc;
        id target = [nav presentedViewController] ? : nav.viewControllers.lastObject;
        NSString *className = NSStringFromClass([target class]);
        if ([className hasPrefix:@"TGGDTWebViewController"] ||
            [className hasPrefix:@"TGGDTVideoStoreViewController"] ||
            [className hasPrefix:@"TMEAdWebViewController"] ||
            [className hasPrefix:@"TMEAdNavigationController"] ||
            [className hasPrefix:@"TMEAdBaseViewController"]) {
            //得发现一个加一个
            //暂时没找到更好的办法统一解决，除非项目内的加标记或者统一放行包含TME/KS/Intoo的页面
            //但这样搞白名单容易有误伤，比如：闪屏页面的VC是TGGDTSplashViewController，同时K歌内会请求更新位置信息，和广告无关，但是因为显示的是闪屏界面所以有误判
            //还不如这样发现一个加一个稳妥，毕竟是内部灵犀审核，改起来也快
            return YES;
        }
        else {
            return NO;
        }
    }
    else {
        return NO;
    }
}

//只针对下一次请求地理位置【权限】生效, 仅调用这个函数后，允许申请位置权限
- (void)additionInfoWhenWillRequestUpdate:(KSPPLocationRequestAdditionInfo *)req
{
    self.reqAdditionInfo = req;
}

//只针对下一次请求地理位置信息请求生效
- (void)additionInfoWheUserDeny:(KSPPLocationDenyAlertAdditionInfo *)req
{
    self.denyAdditionInfo = req;
}

- (void)clearAdditionInfo
{
    self.denyAdditionInfo = nil;
    self.reqAdditionInfo = nil;
}

- (BOOL)canUpdateLocationInfo
{
    BOOL ret = NO;
    id key = [[NSThread callStackReturnAddresses] safeObjectAtIndex:2];
    /*
     这里允许真正去请求地理位置信息条件
     */
     /*
     1. 允许启动中第一次地理信息的获取，在已有缓存的情况下，启动流程中不允许任何情况的获取
     */
    if (self.allowFirstRequestInLaunch){
        ret = YES;
    }

    /*
     2 非启动保护，且相应路径没有请求过，可以获取
     */
    if (!self.inLaunchProtection && ![self.requestRecords containsObject:key]) {
        ret = YES;
        
    }

    /*
     3.处理特殊请求 - 额外的参数，是否允许强制开启获取权限
     */
    if (!self.inLaunchProtection && self.reqAdditionInfo) {
        //3.1 检查当前请求希望的情况
        switch(self.reqAdditionInfo.additionRequestType) {
            case KSPPLocationRequestType_WantForceUpdate:
            {
                //3.1.1希望强制请求，根据内部的逻辑判断，尽可能的去发起请求
                ret = YES;
                if (self.internalBuild) {
                    [KSToast showToast:@"内部:【警告】业务尝试强制更新位置信息，请Leader确定这里数安合规没有问题"];
                    KSPLog(@"【仅内部打印】【警告】业务尝试强制更新位置信息，请Leader确定这里数安合规没有问题");
                }
            }
                break;
            case KSPPLocationRequestType_NeedForceCache:
            {
                //3.1.2 希望强制缓存，希望不要真实的发起请求，直接返回缓存的位置信息
                ret = NO;
            }
                break;
            default:
            case KSPPLocationRequestType_Default:
            {
                //3.1.0 默认请求，仅允许第一次请求，后续同一位置发起的请求都会被block并返回缓存, 不更改ret的值
                break;
            }
        }
        
        // 3.2 当前Hippy项目第一次请求位置信息
        if (self.reqAdditionInfo.additionRequestType != KSPPLocationRequestType_NeedForceCache && !IS_EMPTY_STR_BM(self.reqAdditionInfo.hippyProject)) {
            if (![self.requestRecords containsObject:self.reqAdditionInfo.hippyProject]) {
                ret = YES;
                [self.requestRecords addObject:self.reqAdditionInfo.hippyProject];
            }
        }
    }

    /*
     4. 这里如果是回调了权限状态变更，但实际上权限没有变，这个时候就不要再发起真正的请求了，因为可能存在连续请求两次的情况，这是甲方不允许的
     */
    if ([[NSThread callStackReturnAddresses] containsObject:self.locationReqMarkAddress] && !self.isStatusChanged) {
        ret = NO;
        if (self.internalBuild) {
            KSPLog(@"【仅内部打印】startUpdateLocation由locationManagerDidChangeAuthorization发起，并且权限状态没有变化，这里就不真的发起请求了");
        }
    }

    //不论如何，记录一下当前请求地址
    if (![self.requestRecords containsObject:key]) {
        [self.requestRecords addObject:key];
    }

    [self clearAdditionInfo];
    
    return ret;
}

//拒绝弹窗
- (BOOL)canshowDenyAlertView
{
    BOOL ret = NO;
    
    //条件：通过K歌申请位置权限控制在48小时一次，时间间隔控制支持wns下发,单位：小时, 默认24消失
    NSInteger gap = self.denyAlertGap > 0 ? : 24;
    NSDate *lastShowDate = [[NSUserDefaults standardUserDefaults] objectForKey:ks_ppl_userdefaultkey_deny_lastdate];
    
    
    if (!lastShowDate) {
        //1. 如果没有记录过日期，允许展示
        KSPLog(@"无日期,允许");
        ret = YES;
    }
    else if ([[NSDate date] timeIntervalSinceReferenceDate] - [lastShowDate timeIntervalSinceReferenceDate] >= gap * 3600)
    {
        //2. 记录日期与现在相差2天，允许展示
        KSPLog(@"日期超过2天,允许");
        ret = YES;
    }
    else if (self.denyAdditionInfo && self.denyAdditionInfo.wantforceGrant)
    {
        //3. 业务希望强制展示
        KSPLog(@"强制允许");
        if (self.internalBuild) {
            [KSToast showToast:@"内部:【警告】业务尝试强制提醒位置权限，请Leader确定这里数安合规没有问题"];
            KSPLog(@"【仅内部打印】【警告】业务尝试强制更新位置权限，请Leader确定这里数安合规没有问题");
        }
        ret = YES;
    }
    
    [self clearAdditionInfo];
    return ret;
}

- (BOOL)hasLocationRight
{
    CLAuthorizationStatus status = [self.class getLocationAuthorizationStatus];
    if(status != self.clAuthStatusDefault_1) {
        self.clAuthStatusDefault_1 = status;
    }
    
    if ((status != kCLAuthorizationStatusAuthorizedWhenInUse &&
         status != kCLAuthorizationStatusAuthorizedAlways)) {
        KSPLog(@"位置权限状态：status %d", status);
        return NO;
    }
    
    BOOL locationServicesEnabled = [CLLocationManager locationServicesEnabled];
    
    KSPLog(@"位置权限状态：Enable:%d", [CLLocationManager locationServicesEnabled]);
    return locationServicesEnabled;
}

- (CLLocation *)getLastLocation
{
    if ([KSPrivateProtectLocationManager sharedManager].hasLocationRight) {
        return [KSPrivateProtectLocationManager sharedManager].cachedLocations.lastObject;
    }
    else {
        return nil;
    }
}

- (BOOL)hasDialog
{
    return [KSPrivateProtectLocationManager sharedManager].dialog;
}

- (BOOL)isDialogShowing
{
    return [KSPrivateProtectLocationManager sharedManager].dialog.superview;
}

- (void)resetDialog
{
    // privateRequestWhenInUseAuthorization 方法中判断 dialog不为空就不会继续执行后续的弹窗逻辑, 当用户点击取消时, 仅仅将 dialog dismiss, 但不会置空
    // 这里会导致 APP 生命周期内只展示一次弹窗, 后续其他业务也无法再次拉起询问弹窗, 这里无法确认是否是特定需求, 且改动影响面过大
    // 故此处额外新增一个重置 dialog 方法,强行释放 dialog, 保证需要重复拉起弹窗的业务可以正确的执行
    if ([self isDialogShowing]) {
        [self.dialog dismiss];
    }
    self.dialog = nil;
}

#pragma mark - CLLocationManagerDelegate
- (void)locationManagerDidChangeAuthorization:(CLLocationManager *)manager
{
    if (@available(iOS 14.0, *)) {
        self.locationReqMarkAddress = [[NSThread callStackReturnAddresses] safeObjectAtIndex:2];
        
        if (self.clAuthStatusDefault_1 >= 0 &&
            self.clAuthStatusDefault_1 != manager.authorizationStatus) {
            self.isStatusChanged = YES;
        }
        else {
            self.isStatusChanged = NO;
        }
        self.clAuthStatusDefault_1 = manager.authorizationStatus;
        
        KLog(@"locationManagerDidChangeAuthorization: %d", self.clAuthStatusDefault_1);
       
        NSMutableArray *toDel = [[NSMutableArray alloc] init];
        NSArray *tmpArr = [self threadSafeCopy:self.delegateManagers];
        for (NSDictionary *dic in tmpArr) {
            KSWeakRef *dlg = [dic objectForKey:ks_delegate_key];
            KSWeakRef *mgr = [dic objectForKey:ks_manager_key];
            
            if (!dlg.ref || !mgr.ref) {
                [toDel addObject:dic];
                continue;
            }

            
            if (manager == mgr.ref && [dlg.ref respondsToSelector:@selector(locationManagerDidChangeAuthorization:)]) {
                [dlg.ref locationManagerDidChangeAuthorization:manager];
            }
            //兼容老接口
            else if (manager == mgr.ref && [dlg.ref respondsToSelector:@selector(locationManager:didChangeAuthorizationStatus:)]) {
                [dlg.ref locationManager:manager didChangeAuthorizationStatus:self.clAuthStatusDefault_1];
            }
        }
        [self threadSafeDelFrom:self.delegateManagers withDelArray:toDel];
    }
}

- (void)locationManager:(CLLocationManager *)manager didChangeAuthorizationStatus:(CLAuthorizationStatus)status
{
    KLog(@"locationManager:didChangeAuthorizationStatus: %d", status);
    //iOS15废弃了这个接口，但是iOS14或者以下依然会调用这里
    self.locationReqMarkAddress = [[NSThread callStackReturnAddresses] safeObjectAtIndex:2];
    if (self.clAuthStatusDefault_1 >= 0 &&
        self.clAuthStatusDefault_1 != status) {
        self.isStatusChanged = YES;
    }
    else {
        self.isStatusChanged = NO;
    }
    self.clAuthStatusDefault_1 = status;
    
    NSMutableArray *toDel = [[NSMutableArray alloc] init];
    
    NSArray *tmpArr = [self threadSafeCopy:self.delegateManagers];
    for (NSDictionary *dic in tmpArr) {
        
        KSWeakRef *dlg = [dic objectForKey:ks_delegate_key];
        KSWeakRef *mgr = [dic objectForKey:ks_manager_key];
        
        if (!dlg.ref || !mgr.ref) {
            [toDel addObject:dic];
            continue;
        }
        
        if (manager == mgr.ref && [dlg.ref respondsToSelector:@selector(locationManager:didChangeAuthorizationStatus:)]) {
            [dlg.ref locationManager:manager didChangeAuthorizationStatus:self.clAuthStatusDefault_1];
        }
    }
    
    [self threadSafeDelFrom:self.delegateManagers withDelArray:toDel];
}

- (void)locationManager:(CLLocationManager *)manager didUpdateLocations:(NSArray *)locations
{
    self.cachedLocations = locations;

    //本地缓存位置信息
    NSData *arrayData = [NSKeyedArchiver archivedDataWithRootObject:locations];
    [[NSUserDefaults standardUserDefaults] setObject:arrayData forKey:ks_ppl_userdefaultkey_locations];
    [[NSUserDefaults standardUserDefaults] synchronize];
    KSPLog(@"获取了一次位置信息：%@",locations.lastObject);
    
    NSMutableArray *toDel = [[NSMutableArray alloc] init];
    NSArray *tmpArr = [self threadSafeCopy:self.delegateManagers];
    for (NSDictionary *dic in tmpArr) {
        
        KSWeakRef *dlg = [dic objectForKey:ks_delegate_key];
        KSWeakRef *mgr = [dic objectForKey:ks_manager_key];
        
        if (!dlg.ref || !mgr.ref) {
            [toDel addObject:dic];
        }
        dispatch_async(dispatch_get_main_queue(), ^{
            if (manager == mgr.ref && [dlg.ref respondsToSelector:@selector(locationManager:didUpdateLocations:)]) {
                [dlg.ref locationManager:manager didUpdateLocations:locations];
            }
        });
    }
    
    [self threadSafeDelFrom:self.delegateManagers withDelArray:toDel];
}

- (void)locationManager:(CLLocationManager *)manager didFailWithError:(NSError *)error
{
    NSMutableArray *toDel = [[NSMutableArray alloc] init];
    NSArray *tmpArr = [self threadSafeCopy:self.delegateManagers];
    for (NSDictionary *dic in tmpArr) {
        
        KSWeakRef *dlg = [dic objectForKey:ks_delegate_key];
        KSWeakRef *mgr = [dic objectForKey:ks_manager_key];
        
        if (!dlg.ref || !mgr.ref) {
            [toDel addObject:dic];
        }
        
        if (manager == mgr.ref && [dlg.ref respondsToSelector:@selector(locationManager:didFailWithError:)]) {
            [dlg.ref locationManager:manager didFailWithError:error];
        }
    }
    [self threadSafeDelFrom:self.delegateManagers withDelArray:toDel];
}

- (void)showDenyAlertView
{
    if ([self canshowDenyAlertView]) {
        dispatch_async(dispatch_get_main_queue(), ^{
            KSBasicDialog *dialog = [[KSBasicDialog alloc] init];

            [dialog addContentText:@"相关服务需要访问您的位置,请到设置里开启"];
            
            [dialog addControlButton:@"取消" subtitle:nil buttonStyle:KSBasicDialogButtonStyle_Normal callback:^(KSDialog * _Nonnull dialog) {
                [[NSNotificationCenter defaultCenter] postNotificationName:KSNotification_ProtectLocation_Dialog_Dismiss object:nil];
                [dialog dismiss];
            }];
            [dialog addControlButton:@"前往设置" subtitle:nil buttonStyle:KSBasicDialogButtonStyle_Bold callback:^(KSDialog * _Nonnull dialog) {
                NSURL *openSettingsURL = [NSURL URLWithString:UIApplicationOpenSettingsURLString];
                [[UIApplication sharedApplication] openURL:openSettingsURL options:@{} completionHandler:^(BOOL success) {
                    
                }];
                [[NSNotificationCenter defaultCenter] postNotificationName:KSNotification_ProtectLocation_Dialog_Dismiss object:nil];
                [dialog dismiss];
            }];
            
            [dialog setCloseButtonCallback:^(KSDialog * _Nonnull dialog) {
                [[NSNotificationCenter defaultCenter] postNotificationName:KSNotification_ProtectLocation_Dialog_Dismiss object:nil];
                [dialog dismiss];
            }];
            [dialog show];

            [[NSUserDefaults standardUserDefaults] setObject:[NSDate date] forKey:ks_ppl_userdefaultkey_deny_lastdate];
            [[NSUserDefaults standardUserDefaults] synchronize];
        });
    }
}

#pragma mark - 通知本地保存的已注册Delegate
//通知全体
- (void)didChangeAuthorizationStatus
{
    NSMutableArray *toDel = [[NSMutableArray alloc] init];
    NSArray *tmpArr = [self threadSafeCopy:self.delegateManagers];
    for (NSDictionary *dic in tmpArr) {
        
        KSWeakRef *dlg = [dic objectForKey:ks_delegate_key];
        KSWeakRef *mgr = [dic objectForKey:ks_manager_key];
        
        if (!dlg.ref || !mgr.ref) {
            [toDel addObject:dic];
        }
        
        if (mgr.ref && [dlg.ref respondsToSelector:@selector(locationManager:didChangeAuthorizationStatus:)]) {
            [dlg.ref locationManager:mgr.ref didChangeAuthorizationStatus:self.clAuthStatusDefault_1];
        }
    }
    [self threadSafeDelFrom:self.delegateManagers withDelArray:toDel];
}
//通知全体
- (void)updateLocations
{
    NSMutableArray *toDel = [[NSMutableArray alloc] init];
    NSArray *tmpArr = [self threadSafeCopy:self.delegateManagers];
    for (NSDictionary *dic in tmpArr) {
        
        KSWeakRef *dlg = [dic objectForKey:ks_delegate_key];
        KSWeakRef *mgr = [dic objectForKey:ks_manager_key];
        
        if (!dlg.ref || !mgr.ref) {
            [toDel addObject:dic];
        }
        dispatch_async(dispatch_get_main_queue(), ^{
            if (mgr.ref && [dlg.ref respondsToSelector:@selector(locationManager:didUpdateLocations:)]) {
                [dlg.ref locationManager:mgr.ref didUpdateLocations:self.cachedLocations];
            }
        });
    }
    [self threadSafeDelFrom:self.delegateManagers withDelArray:toDel];
}
//通知全体
- (void)failWithError:(NSError *)error
{
    NSMutableArray *toDel = [[NSMutableArray alloc] init];
    NSArray *tmpArr = [self threadSafeCopy:self.delegateManagers];
    for (NSDictionary *dic in tmpArr) {
        
        KSWeakRef *dlg = [dic objectForKey:ks_delegate_key];
        KSWeakRef *mgr = [dic objectForKey:ks_manager_key];
        
        if (!dlg.ref || !mgr.ref) {
            [toDel addObject:dic];
        }
        
        if (mgr.ref && [dlg.ref respondsToSelector:@selector(locationManager:didFailWithError:)]) {
            [dlg.ref locationManager:mgr.ref didFailWithError:error];
        }
    }
    [self threadSafeDelFrom:self.delegateManagers withDelArray:toDel];
}


#pragma mark - swizzle
+ (void)originalSelector:(SEL)originalSelector swizzleSelector:(SEL)swizzledSelector
{
    Method originalMethod = class_getInstanceMethod([CLLocationManager class], originalSelector);
    Method swizzledMethod = class_getInstanceMethod([KSPrivateProtectLocationManager class], swizzledSelector);

    BOOL registerMethod = class_addMethod([CLLocationManager class],
                                          swizzledSelector,
                                          method_getImplementation(swizzledMethod),
                                          method_getTypeEncoding(swizzledMethod));
    if (!registerMethod) {
        return;
    }
    
    // 需要更新swizzledMethod变量,获取当前Car类中xxx_run:的Method指针
    swizzledMethod = class_getInstanceMethod([CLLocationManager class], swizzledSelector);
    if (!swizzledMethod) {
        return;
    }
    
    // 后续流程与之前的一致
    BOOL didAddMethod = class_addMethod([CLLocationManager class],
                                        originalSelector,
                                        method_getImplementation(swizzledMethod),
                                        method_getTypeEncoding(swizzledMethod));
    if (didAddMethod) {
        class_replaceMethod([CLLocationManager class],
                            swizzledSelector,
                            method_getImplementation(originalMethod),
                            method_getTypeEncoding(originalMethod));
    } else {
        method_exchangeImplementations(originalMethod, swizzledMethod);
    }
    
}

#pragma mark - 多线程保护
- (dispatch_queue_t)muteQueue
{
    static dispatch_queue_t changeQueue = nil;
    static dispatch_once_t changeToken;
    dispatch_once(&changeToken, ^{
        changeQueue = dispatch_queue_create("privateProtectQueue", DISPATCH_QUEUE_SERIAL);
    });
    return changeQueue;
}

//确保这里是同步的，单线程运行
- (NSArray *)threadSafeCopy:(NSMutableArray *)array{
    __block NSArray *temp = nil;
    
    dispatch_sync(self.muteQueue, ^{
        temp = [array copy];
    });
    
    return temp;
}

//确保这里是同步的，单线程运行
- (void)threadSafeDelFrom:(NSMutableArray *)mutableArray withDelArray:(NSArray *)arr
{
    dispatch_sync(self.muteQueue, ^{
        [mutableArray removeObjectsInArray:arr];
    });
}


#pragma mark - lazy load
- (NSMutableArray *)delegateManagers
{
    if (!_delegateManagers) {
        _delegateManagers = [[NSMutableArray alloc] init];
    }
    return _delegateManagers;
}

- (NSMutableArray *)requestRecords
{
    if (!_requestRecords) {
        _requestRecords = [[NSMutableArray alloc] init];
    }
    return _requestRecords;
}


#pragma mark - 工具方法
+ (BOOL)isSameDay:(NSDate*)date1 date2:(NSDate*)date2
{
    if (!date1 || !date2)
    {
        return NO;
    }
    NSCalendar* calendar = [NSCalendar currentCalendar];
    
    unsigned unitFlags = NSCalendarUnitYear | NSCalendarUnitMonth |NSCalendarUnitDay;
    NSDateComponents* comp1 = [calendar components:unitFlags fromDate:date1];
    NSDateComponents* comp2 = [calendar components:unitFlags fromDate:date2];
    
    return [comp1 day]   == [comp2 day] &&
    [comp1 month] == [comp2 month] &&
    [comp1 year]  == [comp2 year];
}

-(void)printLog:(NSString *)msg, ...
{
    va_list args;
    va_start(args, msg);
    NSString *msgStr = [[NSString alloc] initWithFormat:msg arguments:args];
    va_end(args);

    msgStr = [NSString stringWithFormat:@"[PrivateProtect]%@",msgStr];
    if (self.logBlock) {
        self.logBlock(msgStr);
    }
    else {
        [self.tempLogCache addObject:msgStr];
    }
}

@end
