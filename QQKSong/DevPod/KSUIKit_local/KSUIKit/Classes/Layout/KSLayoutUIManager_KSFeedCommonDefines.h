//
//  KSLayoutUIManager_KSFeedCommonDefines.h
//  QQKSong
//
//  Created by <PERSON> on 2023/12/3.
//  Copyright © 2023 Tencent. All rights reserved.
//

#ifndef KSLayoutUIManager_KSFeedCommonDefines_h
#define KSLayoutUIManager_KSFeedCommonDefines_h

@class KSLayoutRichText;
@class KSUIItemIconView;
@class KSUIItemButton;
@class KSUIOriginalImageItem;
@class KSVideoDisplayItem;
@class KSLottieAnimationItem;
@class KSMarqueeItem;
@class KSUIItemLocalImage;
@class KSTagViewItem;
@class KSUIItemLabel;
@class KSUIAvatarItem;
@class KSUIItem;
@class KSUIItemLocalSequenceImage;
@class KSUIItemCAView;
@class KSGradientTagItem;
@class KSGradientLayerViewItem;
@class KSBlurViewItem;
@class KSBezierPathViewItem;
@class KSVideoAnimationItem;
@class KSUIItemButtonV2;
@class KSUIImageItem;

typedef void (^KSLayoutVideoViewBlock)(KSVideoDisplayItem* layoutItem);
typedef void (^KSLayoutLottieBlock)(KSLottieAnimationItem* layoutItem);
typedef void (^KSLayoutMarqueBlock)(KSMarqueeItem* layoutItem);
typedef void (^KSLayoutCircularImageBlock)(KSUIImageItem* layoutItem);
typedef void (^KSLayoutOriginImageBlock)(KSUIOriginalImageItem* layoutItem);
typedef void (^KSLayoutLocalImageBlock)(KSUIItemLocalImage* layoutItem);
typedef void (^KSLayoutButtonBlock)(KSUIItemButton* layoutItem);
typedef void (^KSLayoutButtonV2Block)(KSUIItemButtonV2* layoutItem);
typedef void (^KSLayoutUIViewBlock)(KSUIItem* layoutItem);
typedef void (^KSLayoutTextBlock)(KSLayoutRichText* layoutTextItems);
typedef void (^KSLayoutNickIconBlock)(KSUIItemIconView* layoutItem);
typedef void (^KSLayoutLableBlock)(KSUIItemLabel* layoutItem);
typedef void (^KSLayoutAvatarItemBlock)(KSUIAvatarItem* avatarItem);    //头像
typedef void (^KSUpdateUIViewBlock)(UIView* uiviewObj);
typedef void (^KSUpdateButtonBlock)(UIButton* uibuttonObj);
typedef void (^KSUpdateOriginImageBlock)(UIImageView* uiimageviewObj);
typedef void (^KSUpdateKSTagViewBlock)(KSTagViewItem* tagViewObj);    //布局标签
typedef void (^KSUpdateKSGradientTagViewBlock)(KSGradientTagItem* tagViewObj);    //布局标签
typedef void (^KSLayoutSequenceImageBlock)(KSUIItemLocalSequenceImage* layoutItem);
typedef void (^KSLayoutCAViewBlock)(KSUIItemCAView* layoutItem);
typedef void (^KSLayoutGradientLayerViewBlock)(KSGradientLayerViewItem* layoutItem);
typedef void (^KSLayoutBlurViewBlock)(KSBlurViewItem* layoutItem);
typedef void (^KSLayoutBezierPathViewBlock)(KSBezierPathViewItem* layoutItem);
typedef void (^KSLayoutVideoAnimationBlock)(KSVideoAnimationItem* layoutItem);

typedef NS_ENUM(NSUInteger, TimelineFeedCellTag)
{
    TimelineFeedCellAvatar              = 1000,//头像
    TimelineFeedCellAuthIcon            = 1001,//认证icon
    TimelineFeedCellWealthIcon          = 1002,//财富icon
    TimelineFeedCellSongCover           = 1003,//封面
    TimelineFeedCellUgcTypeLable        = 1004,//角标
    TimelineFeedCellChorusButton        = 1005,//合唱btn
    TimelineFeedCellUgcRankImage        = 1006,//sss等级
    TimelineFeedCellUgcMaskButton       = 1007,//蒙版btn
    TimelineFeedCellSendFlowerImage     = 1008,//送花image
    TimelineFeedCellSendFlowerButton    = 1009,//送花btn
    TimelineFeedCellSendGiftButton      = 1010,//送礼btn
    TimelineFeedCellCommentButton       = 1011,//评论btn
    TimelineFeedCellTopSplitLine        = 1012,//顶部分割线
    TimelineFeedCellBottomSplitLine     = 1013,//底部分割线
    TimelineFeedCellBottomSplitBlock    = 1014,//底部色块
    
    TimelineFeedCellCompetionImage          = 1015,//大赛icon
    TimelineFeedCellCompetionDescribeLable  = 1016,//大赛描述
    TimelineFeedCellCompetionJumpBtn        = 1017,//参赛btn
    TimelineFeedCellCompetionDescribe2Lable = 1018,//大赛描述2
    TimelineFeedCellCompetionTopSplitLine   = 1019,//大赛顶部分割线
    TimelineFeedCellCompetionBottomSplitLine    = 1020,//大赛底部分割线
    TimelineFeedCellCompetionBottomSplitBlock   = 1021,//大赛底部色块
    
    TimelineFeedCellLiveShowOnLineBgImage       = 1022,//直播背景图
    TimelineFeedCellLiveShowOnLinePeopleImage   = 1023,//直播在线人数icon
    TimelineFeedCellLiveShowOnLinePeopleLable   = 1024,//直播在线人数
    TimelineFeedCellLiveShowStateImage          = 1025,//直播状态icon
    TimelineFeedCellLiveShowCoverImage          = 1026,//直播封面
    TimelineFeedCellLiveShowStudioImage         = 1027,//直播studio图片
    TimelineFeedCellLiveShowCopyRoomIdBtn       = 1028,//直播房间号复制btn
    TimelineFeedCellLastShowDurationLabel       = 1029,//直播时长
    TimelineFeedCellLastShowDurationImage       = 1030,//直播时长icon
    
    TimelineFeedCellSoloBackgroundCover         = 1031,//专辑背景色
    TimelineFeedCellSoloCover                   = 1032,//专辑封面
    
    TimelineFeedCellShowUploadingLine           = 1033,//上传cell分割线
    TimelineFeedCellShowShareLine               = 1034,//分享cell分割线
    
    TimelineFeedCellGiftListTotoalCountImage    = 1035,
    TimelineFeedCellGiftListHeadIconImage       = 1036,
    TimelineFeedCellGiftListButtomLine          = 1037,
    TimelineFeedCellFlowerListTotoalCountImage  = 1038,
    TimelineFeedCellFlowerListHeadIconImage     = 1039,
    TimelineFeedCellGiftListRankIconImage       = 1040,
    
    TimelineFeedCellFeedSplitLine               = 1045,
    TimelineFeedCellFeedSplitBlock              = 1046,
    
    TimelineFeedCellVipSingerTopLine            = 1047,
    TimelineFeedCellVipSingerBottomLine         = 1048,
    TimelineFeedCellVipSingerSplitBlock         = 1049,
    
    TimelineFeedCellRecSongTopLine              = 1050,
    TimelineFeedCellRecSongBottomLine           = 1051,
    TimelineFeedCellRecSongSplitBlock           = 1052,
    
    TimelineFeedCellUnknowTypeButton            = 1053,//版本升级提示
    
    TimelineFeedCellUgcAudioCustomView          = 1054,//音频自定义播放view
    TimelineFeedCellUgcAudioBackGroundView      = 1055,
    TimelineFeedCellUgcChorusBtnSpliteLine      = 1056, //合唱的分割线
    
    TimelineFeedCellGiftListClickMaskView       =1057, //礼物榜点击mask
    TimelineFeedCellUgcVideoCustomView          =1058, //音频自定义播放view
    TimelineFeedCellUgcCustomView               =1059, //自定义view
    TimelineFeedCellUgcVideoMaskFakeFeedCustomView    =1060, //假feed 视频类mask
    
    TimelineFeedCellContestChampionImage        = 1061,
    TimelineFeedCellVideoListenLable            = 1062,
    TimelineFeedCellVideoSongNameLable          = 1063,
    
    TimelineFeedCellForwardFeedButton           = 1064,//转发按钮
    
    TimelineFeedCellMusicFeedChoursLeftHead     = 1065,//合唱feed左边头像
    TimelineFeedCellMusicFeedChoursRightHead    = 1066,//合唱feed 右边头像
    TimelineFeedCellMusicFeedChoursPlusIcon     = 1067,//合唱feed +号icon
    
    
    TimelineFeedCellRecFriendTopLine            = 1068,//关系链好友推荐分割线
    TimelineFeedCellRecFriendBottomLine         = 1069,
    TimelineFeedCellRecFriendSplitBlock         = 1070,//关系链好友推荐分割背景块
    
    TimelineFeedCellBeatButton                  = 1071,//打擂按钮
    
    TimelineFeedCellForwardIcon                 = 1072,//转发icon
    TimelineFeedCellForwardNickName             = 1073,//转发人昵称
    TimelineFeedCellFeedRankingIcon             = 1074,//feedranking icon
    TimelineFeedCellFeedRankingSplitLine        = 1075,//feedranking 分割线
    TimelineFeedCellEveryDayTaskWeather         = 1076,//日常任务天气
    TimelineFeedCellEveryDayTaskWeatherDeleteBtn             = 1077,//日常任务天气删除按钮
    
    TimelineFeedCellShowMBarDetailButton        = 1077, //友唱尾巴button
    TimelineFeedCellMBarDetailLocationIcon      = 1078, //友唱尾巴定位Icon
    TimelineFeedCellMBarDetailSplitLine         = 1079, //友唱分隔线
    
    TimelineFeedCellCommentListShowAll          = 1088, //好友tab展示所有评论
    
    TimelineFeedCellRemoved                     = 1089, //vip Icon
    TimelineFeedCellCommentListButtomLine       = 1090, //评论外显底部line
    TimelineFeedCellDeleteBtn                   = 1091, //删除按钮
    
    TimelineFeedCellPayIcon                     = 1092, //付费的角标
    TimelineFeedCellContributeBtn               = 1093, //附近-投稿-跳转按钮
    
    TimelineFeedCellKTVMICImage                 = 1094,
    TimelineFeedCellKTVMASK                     = 1095,
    TimelineFeedCellForwardVipIcon              = 1096, //转发的icon
    
    TimelineFeedCellShowTailDetailButton        = 1097, //小尾巴按钮
    TimelineFeedCellTailDetailIcon              = 1098, //小尾巴图标
    TimelineFeedCellTailDetailSplitLine         = 1099, //小尾巴分割线
    
    TimelineFeedCellPayAlbumFeedBuyButton        = 1100,//付费合集购买按钮
    
    TimelineFeedCellLiveShowGuardBgImage        = 1101 ,
    TimelineFeedCellLiveShowGuardHeadImage      = 1102 ,
    
    
    TimelineFeedCellGiftWorksRankImage          = 1103, //好友/关注收礼周榜 背景图
    TimelineFeedCellGiftWorksRankDescLable1     = 1104, //好友/关注收礼周榜 描述1
    TimelineFeedCellGiftWorksRankDescLable2     = 1105, //好友/关注收礼周榜 描述2
    TimelineFeedCellGiftWorksRankBottomSplitBlock   = 1106,//分割背景条
    TimelineFeedCellGiftWorksRankHeadTag1       = 1107, //Top3 head 1
    TimelineFeedCellGiftWorksRankHeadTag2       = 1108, //Top3 head 2
    TimelineFeedCellGiftWorksRankHeadTag3       = 1109, //Top3 head 3
    TimelineFeedCellGiftWorksRankNum1ImgTag      = 1110, //Top3 Rank Number1 image
    TimelineFeedCellGiftWorksRankNum2ImgTag      = 1111, //Top3 Rank Number2 image
    TimelineFeedCellGiftWorksRankNum3ImgTag      = 1112, //Top3 Rank Number3 image
    TimelineFeedCellGiftWorksRankNickLabelTag1       = 1113, //Top3 Nick Label 1
    TimelineFeedCellGiftWorksRankNickLabelTag2       = 1114, //Top3 Nick Label 2
    TimelineFeedCellGiftWorksRankNickLabelTag3       = 1115, //Top3 Nick Label 3
    TimelineFeedCellMikeSongCover               = 1116 ,//排麦同步到Feed中歌曲的封面
    TimelineFeedCellGiftWorksRankDefaultHeadTag1       = 1117, //Top3 DefaultHead 1
    TimelineFeedCellGiftWorksRankDefaultHeadTag2       = 1118, //Top3 DefaultHead 2
    TimelineFeedCellGiftWorksRankDefaultHeadTag3       = 1119, //Top3 DefaultHead 3
    
    TimelineFeedCellPackageImage                       = 1120 , //礼包入口图标
    TimelineFeedCellPackageNumLabel                    = 1121 , //礼包入口图标数量
    TimelineFeedCellAdUserAvatar                       = 1122 , //广告feed用户头像
    TimelineFeedCellAdPromoteBtn                       = 1123 , //广告推广按钮
    TimelineFeedCellAdImageView                        = 1124 , //广告图片
    TimelineFeedCellAdLinkIcon                         = 1125 , //广告链接icon
    TimelineFeedCellAdLinkText                         = 1126 , //广告链接文案
    
    TimelineFeedCellLiveShowGrayBackground             = 1127 , // 灰色背景
    TimelineFeedCellKTVMicUser                         = 1128 , // 连麦用户
    TimelineFeedCellKTVMicConnect                      = 1129 , // 连麦用户
    TimelineFeedCellForwardAvatar                      = 1130 , //头像
    TimelineFeedCellForwardBackground                  = 1131 , //转发背景
    TimelineFeedCellChorusIcon                         = 1132, // 合唱icon
    TimelineFeedCellCompetitionButton                  = 1133, // 参赛btn
    TimelineFeedCellSonglistBackground                  = 1134, // 歌单背景
    TimelineFeedCellNewButtonV5                         = 1135, // 互动btn
    TimelineFeedCellPendantAvatar                       = 1137, ///头像挂件
    TimelineFeedCellSonglistRightIcon                   = 1138,
    TimelineFeedCellPayAlbumBackground                  = 1139,
    TimelineFeedCellKTVRoomTypeCenterIcon               = 1140,//歌房图片中央图片
    TimelineFeedCellLiveShowTypeCenterIcon              = 1141,//直播中央图片
    TimelineFeedCellInviteSingIcon                      = 1142, // 约唱图标
    TimelineFeedCellUgcPhotoListCustomView              = 1143,//相册列表，9宫格
    TimelineFeedCellLivePKTitleIcon                     = 1144, // 左上角 PK 图标
    TimelineFeedCellFristUgcLeftIcon                    = 1144,
    TimelineFeedCellFristUgcRightIcon                   = 1145,
    TimelineFeedCellFristUgcBg                          = 1146,
    
    TimelineFeedCellGiftChorusIcon                      = 1152, //礼物合唱头部icon
    TimelineFeedCellFollowRecConcern                    = 1153,
    TimelineFeedCellFollowRecClose                      = 1154,
    TimelineFeedCellGiftChorusBtnIcon                   = 1155, //礼物合唱按钮icon
    TimelineFeedCellGiftChorusBlock                     = 1156, //礼物合唱背影block
    TimelineFeedCellRelayGameBG                         = 1157, //  抢麦游戏 背景
    TimelineFeedCellRemarkTagIcon                       = 1158, // 点评左上角图标
    TimelineFeedCellRemarkLookerLabel                   = 1159, // 点评围观人数
    TimelineFeedCellRemarkSeparator1                    = 1160, // 点评分割线
    TimelineFeedCellRemarkSeparator2                    = 1161, // 点评分割线
    TimelineFeedCellRemarkSongBackground                = 1162, // 点评歌曲背景
    TimelineFeedCellRemarkCommentUserVip                = 1163, // 点评评论老师 VIP icon
    TimelineFeedCellLiveAgileTitleIcon                  = 1164, // 左上角 PK 图标
    TimelineFeedCellRemarkGrayBackground                = 1165, // 点评灰色背景
    TimelineFeedCellRemarkEarphoneIcon                  = 1166, // 点评 UGC 耳机
    TimelineFeedCellDaojuCompetitionIcon                = 1167,
    TimelineFeedCellStarChorusBandBody                  = 1168, // 明星合唱带子主体
    TimelineFeedCellStarChorusBandHead                  = 1169, // 明星合唱带子头部
    TimelineFeedCellForwardFeedSplitLine                = 1170, // 转发feed的分割线
    
    TimelineFeedCellKTVRoomStateBgImage                 = 1171, // 歌房左上角黑色背景图
    TimelineFeedCellFirstUgcTrailButton                 = 1172, //feed 首次发布小尾巴
    TimelineFeedCellKTVRoomStateHMarqueLable            = 1173, // 歌房feed发布者状态跑马灯
    TimelineFeedCellVipIcon                             = 1174, // 用户信息中vipIcon
    TimelineFeedCellForwardFeedVipICon                  = 1175, // 转发feed中用户信息中vipIcon
    TimelineFeedCellLiveShowDotImage                    = 1176, // 直播状态小红点
    TimelineFeedCellLiveVideoShowView                   = 1177, // 直播avlayer的容器
    TimelineFeedCellKTVRoomTypeLable                    = 1178, // 歌房/直播feed右上角房间类型label
    TimelineFeedCellKTVRoomLottieAnimationView          = 1179, //歌房lottie动图
    
    TimelineFeedCellPersonalRemarkTopCellTodayText                   = 1180, //个人主页教师顶部点评，统一放到这里
    TimelineFeedCellPersonalRemarkTopCellLine                        = 1181,
    TimelineFeedCellPersonalRemarkTopCellLocalImage                  = 1182,
    TimelineFeedCellPersonalRemarkTopCellOneLine                     = 1183,
    TimelineFeedCellPersonalRemarkTopCellButton                      = 1184,
    TimelineFeedCellPersonalRemarkTopCellBackground                  = 1185,
    
    TimelineFeedCellPersonalRemarkHistoryLocalImage                  = 1186,//个人主页教师历史点评
    TimelineFeedCellPersonalRemarkHistoryOneLine                     = 1187,
    TimelineFeedCellPersonalRemarkHistorySongImageUrl                = 1188,
    TimelineFeedCellPersonalRemarkHistoryCellOneLine                 = 1189,
    TimelineFeedCellPersonalRemarkHistoryCellPayLine                 = 1190,
    TimelineFeedCellPersonalRemarkHistoryCellBackground              = 1191,
    TimelineFeedCellPersonalRemarkHistoryAvatourImage                = 1192,
    TimelineFeedCellCompetitionTrailButton                           = 1193,
    TimelineFeedCellSendGiftGuideButton                              = 1194,//送花引导按钮
    TimelineFeedCellAdVideoView                                      = 1195,
    TimelineFeedCellVerticalSplitLine                                = 1196,//互动按钮的分割线
    TimelineFeedCellJoinChorusButton                                 = 1197, // 加入合唱按钮
    TimelineFeedCellJoinGiftChorusButton                             = 1198, // 加入礼物合唱按钮
    TimelineFeedCellMusicMoodNoteIcon                                = 1199, // 音乐心情的音符icon
    TimelineFeedCellCustomAMSClickView                               = 1200, // 透明的自定义 AMS SDK 广告 view
    TimelineFeedCellFeedPlayBar                                      = 1201, // 图文feed的播放bar
    TimelineFeedCellCommentZoneAvatar                                = 1202, // 评论按钮上的头像
    TimelineFeedCellShowCommentShowVCBtn                             = 1203, // 查看全部评论，弹起半屏评论面板
    TimelineFeedCellSonglistBackground2                              = 1204, // 歌单背景2
    TimelineFeedCellAlbomnSpliteline                                 = 1205, // 专辑用的分割线
    
    TimelineFeedCellAudioSongName                                    = 1206, // 音频feed歌名
    TimelineFeedCellAudioSongContainerView                           = 1207, // 音频feed额外描述的容器
    TimelineFeedCellAudioListenIcon                                  = 1208, // 音频收听icon
    TimelineFeedCellDirectSendGiftButton                             = 1209, // 快捷送礼按钮
    TimelineFeedCellAdFeedRightDescButton                            = 1210,// 广告右侧按钮
    TimelineFeedCellAdFeedThumbImageView1                            = 1211,
    TimelineFeedCellAdFeedThumbImageView2                            = 1212,
    TimelineFeedCellAdFeedThumbImageView3                            = 1213,
    TimelineFeedCellPayAlbunTagView                                  = 1214, // 专辑标签
    TimelineFeedCellRecommendTagView                                 = 1215, //推荐标签
    TimelineFeedCellMoreButton                                       = 1216, // 更多按钮
    TimelineFeedCellLikeButton                                       = 1217, // 点赞按钮
    TimelineFeedCellRecommendContainerView                           = 1218, //推荐卡片feed昵称和描述的容器
    TimelineFeedCellGiftRankTextView                                 = 1219, //礼物榜文案
    TimelineFeedCourseFeedPayTagView                                 = 1220, // 课程相关付费标签
    TimelineCourseFeedActionViewTag                                  = 1221, // 课程Feed可点击view
    TimelineFeedCellFollowUserBtn                                    = 1222, //关注按钮

    TimelineFeedCellRelationNameTag                                  = 1223, // 关系链tag
    TimelineFeedCellTopicUGCMaskTag                                  = 1224, // 话题标签（置顶，精选）
    TimelineFeedCellTopicContent                                     = 1225, // 话题#（置顶，精选）
    
    TimelineFeedCellKTVOnLinePeopleMask                              = 1226, // 歌房Feed观看人数背景
    TimelineFeedCellKTVMICMask                                       = 1227, // 歌房Feed连麦人数背景
    TimelineFeedCellKTVStatusSeperateLine                            = 1228, // 歌房Feed状态分割线
    TimelineFeedCellKTVStatusMask                                    = 1229, // 歌房Feed状态背景
    TimelineFeedCellKTVMyMask                                        = 1230, // 歌房Feed动态发布者在歌房的状态背景
    TimelineFeedCellKTVMyAvatar                                      = 1231, // 歌房Feed动态发布者头像
    TimelineFeedCellKTVMyAvatarAnimation                             = 1232, // 歌房Feed涟漪动画
    TimelineFeedCellKTVFeedAnimation                                 = 1233, // 歌房Feed左上角动图
    TimelineFeedCellKTVFeedMagicColorMask                            = 1234, // 歌房Feed封面魔法色蒙层,现在已经去除
    
    TimelineFeedCellLiveShowStatusSeperateLine                       = 1235, // 直播左上角状态分割线
    TimelineFeedCellLiveShowStatusMask                               = 1236, // 直播左上角状态背景
    TimelineFeedCellLiveShowOnLinePeopleMask                         = 1237, // 直播左下角在线人数背景
    TimelineFeedCellLiveShowRectAnimation                            = 1238, // 直播左上角动效
    
    TimelineFeedCellLastReadHintLable                                = 1239, // 以上为新内容
    TimelineFeedCellKTVMyLeftAvatar                                  = 1240, // 歌房Feed动态左部头像
    TimelineFeedCellKTVMyLeftAvatarAnimation                         = 1241, // 歌房Feed左部涟漪动画
    TimelineFeedCellKTVMyRightAvatar                                 = 1242, // 歌房Feed动态右部头像
    TimelineFeedCellKTVMyRightAvatarAnimation                        = 1243, // 歌房Feed右部涟漪动画
    TimelineFeedCellTopicCoverImage                                  = 1244, // 话题feed封面
    TimelineFeedCellRecTopicTitle                                    = 1245, // 精选话题feed title
    TimelineFeedCellRecTopicReason                                   = 1246, // 精选话题feed 理由标签
    
    TimelineFeedCellKtvReplayGameCoverImage                           = 1247, // 歌房feed抢麦封面图
    TimelineFeedCellKtvReplayGameAvatarLeftTop                       = 1248, // 歌房feed抢麦左上用户头像
    TimelineFeedCellKtvReplayGameAvatarLeftMiddle                    = 1249, // 歌房feed抢麦左中用户头像
    TimelineFeedCellKtvReplayGameAvatarLeftBottom                    = 1250, // 歌房feed抢麦左下用户头像
    TimelineFeedCellKtvReplayGameAvatarRightTop                      = 1251, // 歌房feed抢麦右上用户头像
    TimelineFeedCellKtvReplayGameAvatarRightMiddle                   = 1252, // 歌房feed抢麦右中用户头像
    TimelineFeedCellKtvReplayGameAvatarRightBottom                   = 1253, // 歌房feed抢麦右下用户头像
    TimelineFeedCellKtvReplayGameAvatarLeftScore                     = 1254, // 歌房feed抢麦左侧比分
    TimelineFeedCellKtvReplayGameAvatarRightScore                    = 1255, // 歌房feed抢麦右侧比分
    TimelineFeedCellGiftRankImgView                                  = 1256, // 礼物榜图片
    TimelineFeedCellCoverTagView                                     = 1257, // 神仙翻唱标签
    TimelineFeedCellFamilyTagView                                    = 1258, // 家族标签
    TimelineFeedCellOneShotAdView                                    = 1259, // 透明的自定义 OneShot 广告 view
    
    TimelineFeedCellKtvBackgroundMask                                = 1260, // 歌房feed封面蒙层
    TimelineFeedCellNearbyBubbleView                                 = 1261, // 同城feed冒泡View
    TimelineFeedCellKtvRoomLevel                                     = 1262, // 歌房等级
    TimelineFeedCellHcText                                           = 1263, // 与xxx合唱 文案
    
    TimelineFeedCellGiftWorksRankHeadBgTag                           = 1264, //好友榜 Top3 head background
    TimelineFeedCellGiftWorksRankBottomImg1Tag                       = 1265, //好友榜 Top3 bottom image1
    TimelineFeedCellGiftWorksRankBottomImg2Tag                       = 1266, //好友榜 Top3 bottom image2
    TimelineFeedCellGiftWorksRankBottomImg3Tag                       = 1267, //好友榜 Top3 bottom image3
    
    TimelineFeedCellCustomTMEAdClickView                             = 1268, // 透明的自定义 TMEAdSDK 广告 view
    
    TimelineFeedCellPOIIcon                                          = 1269, // poi地址图标
    TimelineFeedCellPOIText                                          = 1270, // poi地址信息
    TimelineFeedCellRecTagIcon                                       = 1272, // 2022新版Feed 歌名下方的推荐理由标签
    TimelineFeedCellListenNumberIcon                                 = 1273, // 2022新版Feed 歌名下方的收听数icon
    TimelineFeedCellLiveShowRightStatusMask                          = 1274, // 2022新版Feed 直播左上角状态右侧背景色
    TimelineFeedCellLiveShowCurMusicImage                            = 1275, // 2022新版Feed 当前正在播放的歌曲
    TimelineFeedCellKTVRightStatusMask                               = 1276, // 2022新版Feed 歌房左上角状态右侧背景色
    TimelineFeedCellKTVSongNameIcon                                  = 1277, // 2022新版Feed 歌房左下角当前歌曲名
    TimelineFeedCellFadeGiftView                                     = 1278, // 2022新版Feed 操作区域送礼假按钮view
    TimelineFeedCellGiftIcon                                         = 1279, // 2022新版Feed 操作区域送礼icon
    TimelineFeedCellGiftRightArrow                                   = 1280, // 2022新版Feed 操作区域送礼右箭头
    TimelineFeedCellGiftAreaActionButton                             = 1281, // 2022新版Feed 操作区域送礼区域点击事件按钮
    TimelineFeedCellLikeCount                                        = 1282, // 2022新版Feed 操作区域点赞数量
    TimelineFeedCellCommentCount                                     = 1283, // 2022新版Feed 操作区域评论数量
    TimelineFeedCellPayAlbumSingCountIcon                            = 1284, // 2022新版Feed 专辑传唱度icon
    TimelineFeedCellAlbumSongCountIcon                               = 1285, // 2022新版Feed 歌单歌曲数icon
    TimelineFeedCellRemovedFeedBackground                            = 1286, // 2022新版Feed 转发已删除feed的背景
    TimelineFeedCellUgcPhotoListCycleScrollView                      = 1287, // 2022新版Feed 音乐心情相册轮播
    TimelineFeedCellMagicColorView                                   = 1288, // 2022新版Feed 魔法色渐变
    TimelineFeedCellLeftTopTagBgView                                 = 1289, // 2022新版Feed 封面左上角标签背景view
    TimelineFeedCellLeftTopTagTextView                               = 1290, // 2022新版Feed 封面左上角标签文字view
    TimelineFeedCellLeftTopTagBlurView                               = 1291, // 2022新版Feed 封面左上角标签背景高斯模糊view
    TimelineFeedCellLeftTopTagBlurView2                              = 1292, // 2022新版Feed 封面左上角标签背景高斯模糊view2
    TimelineFeedCellGiftButtonLineView                               = 1293, // 2022新版Feed 操作区域礼物按钮与异化按钮的分割线
    TimelineFeedCellFucTypeActionButton                              = 1294, // 2022新版Feed 操作区域送礼右侧异化按钮点击事件按钮
    TimelineFeedCellGiftAreaTopListActionButton                      = 1295, // 2022新版Feed 操作区域送礼榜单区域点击事件按钮
    TimelineFeedCellHalfCircle                                       = 1296, // 2022新版Feed 封面右侧小半圆
    TimelineFeedCellHcVipIcon                                        = 1297, // 2022新版Feed 合唱 vip 限免标签
    TimelineFeedCellLiveShowLeftTopTypeIcon                          = 1298, // 2022新版Feed 直播/歌房左上角“直播” “欢聚歌房”文字图片
    TimelineFeedCellTransferBgButton                                 = 1299, // 2022新版Feed 转发icon背后的按钮用于扩大转发热区
    
    TimelineFeedCellTownMemberBack                                   = 1300, // town成员背景
    TimelineFeedCellTownMemberIcon                                   = 1301, // town icon
    TimelineFeedCellTownActionButton                                 = 1281, // town进房点击事件按钮
    
    TimelineFeedCellCameAdvertClickView                               = 1302, // 游戏广告
    TimelineFeedCellAccompRecHeatCardView                             = 1303, // 伴奏详情页推荐插流作品上热门标签
    
    TimelineFeedCellAdBrokenFrameDesLabelTag                          = 1304, // 世界杯破窗广告描述
    TimelineFeedCellAdBrokenFrameSubDesLabelTag                       = 1305, // 世界杯破窗广告副描述
    TimelineFeedCellAdBrokenFrameIPLogo                               = 1306, // 世界杯破窗广告IpLogo

    TimelineFeedCellGiftTextLabel                                     = 1307, // 2022新版Feed 操作区域普通送礼文案label
    TimelineFeedCellGiftFucTextLabel                                  = 1308, // 2022新版Feed 操作区域合唱等文案label

    TimelineFeedCellBirthdaySettingBtnTag                             = 1309, // 生日小助手设置生日按钮
    
    TimelineFeedCellHeatTopRankBgView                                 = 1310, // 2022 年终盛典热度卡巅峰榜小尾巴背景
    TimelineFeedCellHeatTopRankIconImage                              = 1311, // 2022 年终盛典热度卡巅峰榜小尾巴icon
    TimelineFeedCellHeatTopRankTextLabel                              = 1312, // 2022 年终盛典热度卡巅峰榜小尾巴文案标签
    TimelineFeedCellHeatTopRankButton                                 = 1313, // 2022 年终盛典热度卡巅峰榜小尾巴点击按钮
    TimelineFeedCellAuthFestivalIcon                                  = 1314, // 春节套装icon
    TimelineFeedCellInteractGameWatch                                 = 1315, // 互动游戏去围观
    TimelineFeedCellInteractGameJoinGame                              = 1316, // 互动游戏我也要玩
    TimelineFeedCellInteractGamePKAvatarImage                         = 1317, // 互动游戏pk头像
    TimelineFeedCellInteractGameSegmentIcon                           = 1318, // 互动游戏玩家段位
    TimelineFeedCellInteractGameJumpGameBackground                    = 1319, // 互动游戏跳转游戏背景
    TimelineFeedCellInteractGameJumpGameIcon                          = 1320, // 互动游戏跳转游戏icon
    TimelineFeedCellInteractGameJumpGameActionButton                  = 1321, // 互动游戏跳转游戏btn
    TimelineFeedCellInteractGamePKVideoAnimation                      = 1322, // 互动游戏pk动画
    TimelineFeedCellInteractGamePKAddBackgroud                        = 1323, // 互动游戏pk加入背景
    TimelineFeedCellInteractGamePKAddIcon                             = 1324, // 互动游戏pk加入icon
    
    TimelineFeedCellSocialGameAvatarImage                             = 1325, // 异步社交游戏pk加入icon
    TimelineFeedCellSocialGameGetAwardBtn                             = 1326, // 异步社交游戏按钮
    TimelineFeedCellImmersiveMarkImage                                = 1327, // 临境水印图（临境音质/临境音效）
    TimelineFeedCellLiteKTVContainer                                  = 1328, // liteKTV容器Container
    TimelineFeedCellLiteKTVOnlineNumContainer                         = 1329, // liteKTV容器在线人数汇总Container
    TimelineFeedCellLiteKTVGameStatusLabel                            = 1330, // liteKTV容器在线当前游戏状态
    TimelineFeedCellInteractGameContainer                             = 1331, // 互动游戏Container
    TimelineFeedCellKTVRoomTitleLabel                                 = 1332, // KTV roomTitle label
    TimelineFeedCellInteractGameBottomGiftTextLabel                   = 1333, // 互动游戏底部文案label
    TimelineFeedCellIntimicyFriendIcon                                = 1334, // 密友icon（关注页）
    TimelineFeedCellGiftListFadeView                                  = 1335, // 关注feed礼物榜假按钮
    TimelineFeedCellOfficialNotiHeadImage                             = 1336, // 官方消息Head
    TimelineFeedCellCommentVIPIcon                                    = 1337, // 用户VIP icon
    TimelineFeedCellGiftMessageReplyButton                            = 1338, // 礼物消息回复按钮
    TimelineFeedCellMiniHeatCardFadeView                              = 1339, // Mini热度卡
    TimelineFeedCellMiniHeatCardOfficialTag                           = 1340, // 官方tag
    TimelineFeedCellMiniHeatCardHeader                                = 1341, // 用户头像
    TimelineFeedCellFollowFeedAITakePic                               = 1342, // AI拍同款入口
    TimelineFeedCellAiImageGridTag                                    = 1343, // Ai图文
    TimelineFeedCellDoubleGiftBaseGiftTag                             = 1344, // 双送礼按钮中的基础送礼按钮
};

typedef NS_ENUM(NSUInteger, TimelineFeedCellPackageStyle)
{
    TimelineFeedCellPackageStyleSmall  = 1, //小图
    TimelineFeedCellPackageStyleBig  = 2, //大图
};

#define kCHORUSHEADWIDTH  DYNAMIC_VALUE_UNIVERSAL(30, 30, 30, 30, 30*KSPAD_EXTEND) //合唱双方的头像大小

#define kMAXSHOWROWCOUNT   3 //打擂外显最多3个

#define kIMAGELISTSINGLEPICWIDTH   200 //9宫格单个图片的展示大小
#define kIMAGELISTCOUNT            3   //9宫格单行展示的图片个数
#define kIMAGELISTMAXROWCOUNT      3   //9宫格展示的最大行数
#define kIMAGELISTMAXSHOWCOUNT     9   //9宫格展示的最大图片个数

#define kTimeLineUserHeadPendantWidth DYNAMIC_VALUE_UNIVERSAL(kPendantAvatarSize_40_Width, kPendantAvatarSize_40_Width, kPendantAvatarSize_40_Width, kPendantAvatarSize_40_Width, (kPendantAvatarSize_40_Width*KSPAD_EXTEND))

#define kTimeLineUserHeadPendantHeight DYNAMIC_VALUE_UNIVERSAL(kPendantAvatarSize_40_Height, kPendantAvatarSize_40_Height, kPendantAvatarSize_40_Height, kPendantAvatarSize_40_Height, (kPendantAvatarSize_40_Height*KSPAD_EXTEND))

#define kNormalAudioImageWidth DYNAMIC_VALUE_UNIVERSAL(80, 80, 80, 80, (80*KSPAD_EXTEND))

#define kShortAudioBubbleHeight DYNAMIC_VALUE_UNIVERSAL(35, 35, 35, 35, (35*KSPAD_EXTEND))


#define kTimeLineUserHeadWidth DYNAMIC_VALUE_UNIVERSAL(35, 35, 35, 35, (35*KSPAD_EXTEND))

#define kTimeLineGiftItemUserHeadWidth DYNAMIC_VALUE_UNIVERSAL(30, 30, 30, 30, (30*KSPAD_EXTEND))

#define kTimeLineGiftItemUserHeadSmallWidth DYNAMIC_VALUE_UNIVERSAL(25, 25, 25, 25, (25*KSPAD_EXTEND))

#define kTimeLineMikeSongImageWidth DYNAMIC_VALUE_UNIVERSAL(50, 50, 50, 50, (50*KSPAD_EXTEND))

#define kTimeLineMVFeedWidth DYNAMIC_VALUE_UNIVERSAL(180, 180, 180, 180, (180*KSPAD_EXTEND))
#define kTimeLineMVChorusHeight DYNAMIC_VALUE_UNIVERSAL(20, 20, 20, 20, (20*KSPAD_EXTEND))
static const NSInteger kTimeLineLiveShowTitleLimitedCount = 15;


//6.6改版后的 UI参数

#define kFeedStartX 65
#define kFeedRightMargin 20
#define kFeedMaxRightX (SCREEN_WIDTH - kFeedRightMargin)
#define kNickIconViewWidth DYNAMIC_VALUE_ForAllScreen(30)
#define kNickIconViewHeight DYNAMIC_VALUE_ForAllScreen(25)
#define kNormalAudioCoverCornerRadius DYNAMIC_VALUE_ForAllScreen(6)
#define kNormalAudioCoverWidth DYNAMIC_VALUE_ForAllScreen(60)
#define kNormalVideoCoverHeight DYNAMIC_VALUE_ForAllScreen(145)
#define kNormalLiveShowCoverWidth DYNAMIC_VALUE_UNIVERSAL(173, 173, 192, 192, (192*KSPAD_EXTEND))
#define kNormalLiveShowCoverHeight DYNAMIC_VALUE_UNIVERSAL(230, 230, 256, 256, (256*KSPAD_EXTEND))

#define kNormalAlbumImageHeight  DYNAMIC_VALUE_ForAllScreen(90)
#define kNormalAlbumImageHeightV2  DYNAMIC_VALUE_ForAllScreen(70)
#define kNormalAlbumCoverCornerRadius DYNAMIC_VALUE_ForAllScreen(8)


#define kFeedCoverRadiusCorner   4 //圆角半径
#define kFeedCoverRadiusCornerV2   8 //圆角半径
#define kFeedImageRadiusCorner   8 //圆角半径
#define KSBtnS_Height   25   //小按钮的高度
#define KSBtnL_Height   35   //大按钮的高度
#define KSBorderLineWidth   0.5   //描边

#define KSTailBarYFix   (-9)   //duochun要求的视觉还原
#define KSTailBarHeight   (23.5)   //小尾巴高度

#define KSMargin_Dynamic_70   DYNAMIC_VALUE_WithIPAD(70,(70 * KSPAD_EXTEND))

#define kAudioFeedSecondaryTextColor  [UIColor ks_colorWithRGBHex:0xffffff alpha:0.6]

#define  GiftRankHeadIconCount (3)
#define  FLOWERIMAGTAG       1000
#define  kCoverHeight 70.0
#define  kSmallCoverTopMargin   DYNAMIC_VALUE_WithIPAD(10,10)
#define  kSmallCoverTopMarginV2 0.0
#define  kCoverPlayingHeight 120.0
#define  kFeedLyricViewTopPadding 13.0

#define  kButtomBtnXOffset 0
#define  kButtomBtnYOffset 0
#define  kButtomBtnShadowOpacity  0.04
#define  kButtomBtnShadowColor [UIColor ks_colorWithRGBString:@"ffffff"]
#define  kButtomBtnShadowOffset CGSizeMake(0, 0)

#define  kVideoWidthHeightRate 0.75   //竖版视频按照3：4布局

#define kCommonRecScrollViewPageMaxItem 3
#define kCommonScrollableRecCellHeaderHeight 35   //猜你喜欢feed单个cell高度

#define  kFeedPlayBarX KSMargin_Dynamic_70 //playbar frame.x
#define  kFeedPlayBarWidth 48              //没有播放时feedPlaybar除了歌名应该空出的宽度
#define  kFeedPlayBarWidthWhilePlaying 72  //播放时feedPlaybar除了歌名应该空出的宽度

/// 字体
#define kNickColor  UIColor.ks_primaryTextColor
#define kNickFontBold2 _font_big([UIFont ks_fontWithFontType:KSFontType_MiddleBold])
#define kNickFontBold _font_big([UIFont ks_fontWithFontType:KSFontType_LargeBold])

#define kDescTxtFont  _font_big([UIFont ks_fontWithFontType:KSFontType_Large])        //描述用的字体
#define kDescTxtFont2  _font_big([UIFont ks_fontWithFontType:KSFontType_Middle])     //描述用的字体
#define kSongNameFont  _font_big([UIFont ks_fontWithFontType:KSFontType_MiddleBold])      //歌名
#define kListenCoutnFont  _font_big([UIFont ks_fontWithFontType:KSFontType_Small])    //收听数
#define kTimeSTFont  _font_big([UIFont ks_fontWithFontType:KSFontType_Small])         //时间font
#define kFamilyTagFont _font_big([UIFont systemFontOfSize:13])                        //家族标签font

//推荐卡片流用
#define kRecFeedWidth  (SCREEN_WIDTH -2*KSMargin_20)
#define kRecFeedHeight2Width 1.78
#define kTopTabBarHeight 50
#define kTimelineSearchBarHeight 52
#define kBottomTabBarHeight 60
#define kRecFeedTopMargin 20
#define kRecFeedBottomMargin 10
#define kBgImageRadiusCorner 12
#define kPlayBarHieght KSMargin(40)
#define kDescMarqueeViewHieght KSMargin(20.5)

#define kRecFeedBtnMargin (35)                                                          //互动按钮垂直间隔
#define kRecFeedAvatarWidth (40)                                                        //头像宽度
#define kRecFeedAvatarWithBorderWidth (44)                                              //带边框头像宽度
#define kRecFeedBtnWidth  _size_mid(36)                                              //互动按钮宽度
#define kRecFeedRightSpacing  (20 + kRecFeedBtnWidth + 10)
#define kRecFeedAvatarX (SCREEN_WIDTH - KSMargin_15 - kRecFeedBtnWidth - KSMargin_20)  //头像布局X
#define kRecFeedAvatarWidthBorderWidth (2)


#endif


