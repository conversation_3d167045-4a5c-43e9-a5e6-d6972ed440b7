// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_live_chat_bubble_comm.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>
@class proto_live_chat_bubble_comm_ResourceInfo;

@interface proto_live_chat_bubble_comm_UserBubbleInfo : JceObjectV2

@property (nonatomic, retain, JV2_PROP_GS_V2(stResourceInfo,setStResourceInfo:)) proto_live_chat_bubble_comm_ResourceInfo* JV2_PROP_NM(o,0,stResourceInfo);
@property (nonatomic, assign, JV2_PROP_GS_V2(lBeginTs,setLBeginTs:)) TarsInt64 JV2_PROP_NM(o,1,lBeginTs);
@property (nonatomic, assign, JV2_PROP_GS_V2(lEndTs,setLEndTs:)) TarsInt64 JV2_PROP_NM(o,2,lEndTs);
@property (nonatomic, assign, JV2_PROP_GS_V2(uBubbleStaus,setUBubbleStaus:)) TarsUInt32 JV2_PROP_NM(o,3,uBubbleStaus);

@end
