// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_live_chat_bubble_comm.jce   
// **********************************************************************

#import "proto_live_chat_bubble_comm_ResourceInfo.h"

@implementation proto_live_chat_bubble_comm_ResourceInfo

@synthesize JV2_PROP_NM(o,0,uBubbleId);
@synthesize JV2_PROP_NM(o,1,uBizType);
@synthesize JV2_PROP_NM(o,2,strResourceURL);
@synthesize JV2_PROP_NM(o,3,uPriority);

+ (void)initialize
{
    if (self == [proto_live_chat_bubble_comm_ResourceInfo class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strResourceURL) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_live_chat_bubble_comm.ResourceInfo";
}

@end
