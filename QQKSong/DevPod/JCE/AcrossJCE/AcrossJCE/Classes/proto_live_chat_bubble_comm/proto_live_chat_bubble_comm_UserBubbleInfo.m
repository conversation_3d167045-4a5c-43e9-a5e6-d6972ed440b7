// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_live_chat_bubble_comm.jce   
// **********************************************************************

#import "proto_live_chat_bubble_comm_UserBubbleInfo.h"
#import "proto_live_chat_bubble_comm_ResourceInfo.h"

@implementation proto_live_chat_bubble_comm_UserBubbleInfo

@synthesize JV2_PROP_NM(o,0,stResourceInfo);
@synthesize JV2_PROP_NM(o,1,lBeginTs);
@synthesize JV2_PROP_NM(o,2,lEndTs);
@synthesize JV2_PROP_NM(o,3,uBubbleStaus);

+ (void)initialize
{
    if (self == [proto_live_chat_bubble_comm_UserBubbleInfo class]) {
        [proto_live_chat_bubble_comm_ResourceInfo initialize];
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_live_chat_bubble_comm.UserBubbleInfo";
}

@end
