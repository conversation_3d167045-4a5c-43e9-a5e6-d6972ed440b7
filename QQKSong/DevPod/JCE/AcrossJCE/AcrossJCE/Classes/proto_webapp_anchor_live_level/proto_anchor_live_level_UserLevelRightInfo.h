// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_webapp_anchor_live_level.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>

@interface proto_anchor_live_level_UserLevelRightInfo : JceObjectV2

@property (nonatomic, assign, JV2_PROP_GS_V2(lAnchorId,setLAnchorId:)) TarsInt64 JV2_PROP_NM(o,0,lAnchorId);
@property (nonatomic, assign, JV2_PROP_GS_V2(uLevelId,setULevelId:)) TarsUInt32 JV2_PROP_NM(o,1,uLevelId);
@property (nonatomic, retain, JV2_PROP_GS_V2(strAvatarUrl,setStrAvatarUrl:)) NSString* JV2_PROP_NM(o,2,strAvatarUrl);
@property (nonatomic, retain, JV2_PROP_GS_V2(strMedalUrl,setStrMedalUrl:)) NSString* JV2_PROP_NM(o,3,strMedalUrl);

@end
