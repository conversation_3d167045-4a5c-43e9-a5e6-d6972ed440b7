// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_webapp_anchor_live_level.jce   
// **********************************************************************

#import "proto_anchor_live_level_UserLevelRightInfo.h"

@implementation proto_anchor_live_level_UserLevelRightInfo

@synthesize JV2_PROP_NM(o,0,lAnchorId);
@synthesize JV2_PROP_NM(o,1,uLevelId);
@synthesize JV2_PROP_NM(o,2,strAvatarUrl);
@synthesize JV2_PROP_NM(o,3,strMedalUrl);

+ (void)initialize
{
    if (self == [proto_anchor_live_level_UserLevelRightInfo class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strAvatarUrl) = DefaultTarsString;
        JV2_PROP(strMedalUrl) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_anchor_live_level.UserLevelRightInfo";
}

@end
