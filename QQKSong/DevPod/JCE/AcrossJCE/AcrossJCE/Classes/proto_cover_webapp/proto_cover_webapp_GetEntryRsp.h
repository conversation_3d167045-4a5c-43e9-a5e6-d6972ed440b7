// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_cover_webapp.jce   
// **********************************************************************

#import "JceObjectV2.h"

@interface proto_cover_webapp_GetEntryRsp : JceObjectV2

@property (nonatomic, retain, JV2_PROP_GS_V2(strTitle,setStrTitle:)) NSString* JV2_PROP_NM(o,0,strTitle);
@property (nonatomic, retain, JV2_PROP_GS_V2(strTag,setStrTag:)) NSString* JV2_PROP_NM(o,1,strTag);
@property (nonatomic, retain, JV2_PROP_GS_V2(strSubTitle,setStrSubTitle:)) NSString* JV2_PROP_NM(o,2,strSubTitle);
@property (nonatomic, retain, JV2_PROP_GS_V2(strURL,setStrURL:)) NSString* JV2_PROP_NM(o,3,strURL);
@property (nonatomic, retain, JV2_PROP_GS_V2(strBGColor,setStrBGColor:)) NSString* JV2_PROP_NM(o,4,strBGColor);
@property (nonatomic, assign, JV2_PROP_GS_V2(uUserCoverNum,setUUserCoverNum:)) TarsUInt32 JV2_PROP_NM(o,5,uUserCoverNum);
@property (nonatomic, assign, JV2_PROP_GS_V2(uCoverState,setUCoverState:)) TarsUInt32 JV2_PROP_NM(o,6,uCoverState);

@end
