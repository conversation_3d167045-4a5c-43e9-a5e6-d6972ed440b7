// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_cover_webapp.jce   
// **********************************************************************

#import "proto_cover_webapp_DeleteCoverReq.h"

@implementation proto_cover_webapp_DeleteCoverReq

@synthesize JV2_PROP_NM(o,0,strID);

+ (void)initialize
{
    if (self == [proto_cover_webapp_DeleteCoverReq class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strID) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_cover_webapp.DeleteCoverReq";
}

@end
