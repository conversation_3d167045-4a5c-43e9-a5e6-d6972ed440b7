// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_cover_webapp.jce   
// **********************************************************************

#import "proto_cover_webapp_GetUploadParamReq.h"

@implementation proto_cover_webapp_GetUploadParamReq

@synthesize JV2_PROP_NM(o,0,uType);
@synthesize JV2_PROP_NM(o,1,uSubType);
@synthesize JV2_PROP_NM(o,2,uVideoLengthSec);
+ (void)initialize
{
    if (self == [proto_cover_webapp_GetUploadParamReq class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_cover_webapp.GetUploadParamReq";
}

@end
