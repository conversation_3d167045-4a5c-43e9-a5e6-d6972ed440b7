// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_cover.jce   
// **********************************************************************

#import "JceObjectV2.h"

@interface proto_cover_CoverItem : JceObjectV2

@property (nonatomic, retain, JV2_PROP_GS_V2(strID,setStrID:)) NSString* JV2_PROP_NM(o,0,strID);
@property (nonatomic, retain, JV2_PROP_GS_V2(strURL,setStrURL:)) NSString* JV2_PROP_NM(o,1,strURL);
@property (nonatomic, assign, JV2_PROP_GS_V2(lUid,setLUid:)) TarsInt64 JV2_PROP_NM(o,2,lUid);
@property (nonatomic, assign, JV2_PROP_GS_V2(uType,setUType:)) TarsUInt32 JV2_PROP_NM(o,3,uType);
@property (nonatomic, assign, JV2_PROP_GS_V2(uSubType,setUSubType:)) TarsUInt32 JV2_PROP_NM(o,4,uSubType);
@property (nonatomic, assign, JV2_PROP_GS_V2(lUploadTs,setLUploadTs:)) TarsInt64 JV2_PROP_NM(o,5,lUploadTs);
@property (nonatomic, assign, JV2_PROP_GS_V2(lLastModifyTs,setLLastModifyTs:)) TarsInt64 JV2_PROP_NM(o,6,lLastModifyTs);
@property (nonatomic, assign, JV2_PROP_GS_V2(uVerifyStatus,setUVerifyStatus:)) TarsUInt32 JV2_PROP_NM(o,7,uVerifyStatus);
@property (nonatomic, assign, JV2_PROP_GS_V2(uStatus,setUStatus:)) TarsUInt32 JV2_PROP_NM(o,8,uStatus);
@property (nonatomic, assign, JV2_PROP_GS_V2(uCtrLevel,setUCtrLevel:)) TarsUInt32 JV2_PROP_NM(o,9,uCtrLevel);
@property (nonatomic, retain, JV2_PROP_GS_V2(mapExt,setMapExt:)) NSDictionary* JV2_PROP_EX(o,10,mapExt,M09ONSStringONSString);

@end
