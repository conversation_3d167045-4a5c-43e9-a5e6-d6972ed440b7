// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_cover.jce   
// **********************************************************************

#import "proto_cover_CoverItem.h"

@implementation proto_cover_CoverItem

@synthesize JV2_PROP_NM(o,0,strID);
@synthesize JV2_PROP_NM(o,1,strURL);
@synthesize JV2_PROP_NM(o,2,lUid);
@synthesize JV2_PROP_NM(o,3,uType);
@synthesize JV2_PROP_NM(o,4,uSubType);
@synthesize JV2_PROP_NM(o,5,lUploadTs);
@synthesize JV2_PROP_NM(o,6,lLastModifyTs);
@synthesize JV2_PROP_NM(o,7,uVerifyStatus);
@synthesize JV2_PROP_NM(o,8,uStatus);
@synthesize JV2_PROP_NM(o,9,uCtrLevel);
@synthesize JV2_PROP_EX(o,10,mapExt,M09ONSStringONSString);

+ (void)initialize
{
    if (self == [proto_cover_CoverItem class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strID) = DefaultTarsString;
        JV2_PROP(strURL) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_cover.CoverItem";
}

@end
