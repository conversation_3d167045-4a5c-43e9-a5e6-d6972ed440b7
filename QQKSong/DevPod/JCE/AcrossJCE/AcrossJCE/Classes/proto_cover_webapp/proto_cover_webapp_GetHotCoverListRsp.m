// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_cover_webapp.jce   
// **********************************************************************

#import "proto_cover_webapp_GetHotCoverListRsp.h"
#import "proto_cover_CoverItem.h"

@implementation proto_cover_webapp_GetHotCoverListRsp

@synthesize JV2_PROP_NM(o,0,strPassback);
@synthesize JV2_PROP_NM(o,1,iHasMore);
@synthesize JV2_PROP_EX(o,2,vec<PERSON><PERSON><PERSON>ist,VOproto_cover_CoverItem);

+ (void)initialize
{
    if (self == [proto_cover_webapp_GetHotCoverListRsp class]) {
        [proto_cover_CoverItem initialize];
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strPassback) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_cover_webapp.GetHotCoverListRsp";
}

@end
