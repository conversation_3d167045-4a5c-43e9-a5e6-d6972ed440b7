// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_cover_webapp.jce   
// **********************************************************************

#import "JceObjectV2.h"
@class proto_cover_CoverItem;

@interface proto_cover_webapp_GetCoverListRsp : JceObjectV2

@property (nonatomic, retain, JV2_PROP_GS_V2(strPassback,setStrPassback:)) NSString* JV2_PROP_NM(o,0,strPassback);
@property (nonatomic, assign, JV2_PROP_GS_V2(iHasMore,setIHasMore:)) TarsInt32 JV2_PROP_NM(o,1,iHasMore);
@property (nonatomic, retain, JV2_PROP_GS_V2(vecCoverList,setVecCoverList:)) NSArray* JV2_PROP_EX(o,2,vecCoverList,VOproto_cover_CoverItem);

@end
