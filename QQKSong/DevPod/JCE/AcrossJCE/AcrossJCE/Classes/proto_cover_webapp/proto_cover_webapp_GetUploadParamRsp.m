// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_cover_webapp.jce   
// **********************************************************************

#import "proto_cover_webapp_GetUploadParamRsp.h"

@implementation proto_cover_webapp_GetUploadParamRsp

@synthesize JV2_PROP_NM(o,0,vctExtByte);

+ (void)initialize
{
    if (self == [proto_cover_webapp_GetUploadParamRsp class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_cover_webapp.GetUploadParamRsp";
}

@end
