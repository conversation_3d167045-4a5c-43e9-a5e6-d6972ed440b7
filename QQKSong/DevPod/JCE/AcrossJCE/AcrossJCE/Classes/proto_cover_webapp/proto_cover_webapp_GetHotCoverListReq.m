// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_cover_webapp.jce   
// **********************************************************************

#import "proto_cover_webapp_GetHotCoverListReq.h"

@implementation proto_cover_webapp_GetHotCoverListReq

@synthesize JV2_PROP_NM(o,0,strPassback);

+ (void)initialize
{
    if (self == [proto_cover_webapp_GetHotCoverListReq class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strPassback) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_cover_webapp.GetHotCoverListReq";
}

@end
