// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_cover_webapp.jce   
// **********************************************************************

#import "proto_cover_webapp_GetEntryRsp.h"

@implementation proto_cover_webapp_GetEntryRsp

@synthesize JV2_PROP_NM(o,0,strTitle);
@synthesize JV2_PROP_NM(o,1,strTag);
@synthesize JV2_PROP_NM(o,2,strSubTitle);
@synthesize JV2_PROP_NM(o,3,strURL);
@synthesize JV2_PROP_NM(o,4,strBGColor);
@synthesize JV2_PROP_NM(o,5,uUserCoverNum);
@synthesize JV2_PROP_NM(o,6,uCoverState);
+ (void)initialize
{
    if (self == [proto_cover_webapp_GetEntryRsp class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strTitle) = DefaultTarsString;
        JV2_PROP(strTag) = DefaultTarsString;
        JV2_PROP(strSubTitle) = DefaultTarsString;
        JV2_PROP(strURL) = DefaultTarsString;
        JV2_PROP(strBGColor) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_cover_webapp.GetEntryRsp";
}

@end
