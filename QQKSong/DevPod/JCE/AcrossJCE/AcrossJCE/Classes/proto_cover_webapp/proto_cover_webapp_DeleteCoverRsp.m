// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_cover_webapp.jce   
// **********************************************************************

#import "proto_cover_webapp_DeleteCoverRsp.h"

@implementation proto_cover_webapp_DeleteCoverRsp

@synthesize JV2_PROP_NM(o,0,iRet);

+ (void)initialize
{
    if (self == [proto_cover_webapp_DeleteCoverRsp class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_cover_webapp.DeleteCoverRsp";
}

@end
