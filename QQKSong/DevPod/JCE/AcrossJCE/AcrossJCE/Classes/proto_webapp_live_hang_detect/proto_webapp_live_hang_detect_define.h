// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_webapp_live_hang_detect.jce   
// **********************************************************************

#import "JceObjectV2.h"

#define proto_webapp_live_hang_detect_LIVE_HANG_DETECT_WEBAPP_MOD_ID ((TarsUInt64)0)

#define proto_webapp_live_hang_detect_CMD_LIVE_HANG_DETECT_WEBAPP_REPORT_CLICK @"interact.live_hang.detect_webapp.report_click"

#define proto_webapp_live_hang_detect_CMD_LIVE_HANG_DETECT_WEBAPP_REPORT_HANG_TOAST @"interact.live_hang.detect_webapp.report_hang_toast"

