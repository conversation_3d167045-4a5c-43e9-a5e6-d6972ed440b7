// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_webapp_live_hang_detect.jce   
// **********************************************************************

#import "proto_live_hang_ReportClickRsp.h"

@implementation proto_live_hang_ReportClickRsp

@synthesize JV2_PROP_NM(o,0,uReportInterval);

+ (void)initialize
{
    if (self == [proto_live_hang_ReportClickRsp class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_live_hang.ReportClickRsp";
}

@end
