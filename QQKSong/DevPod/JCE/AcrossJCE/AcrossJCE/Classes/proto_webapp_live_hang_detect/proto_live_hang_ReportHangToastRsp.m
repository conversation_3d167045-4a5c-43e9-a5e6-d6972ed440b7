// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_webapp_live_hang_detect.jce   
// **********************************************************************

#import "proto_live_hang_ReportHangToastRsp.h"

@implementation proto_live_hang_ReportHangToastRsp

@synthesize JV2_PROP_NM(o,0,iErrCode);
@synthesize JV2_PROP_NM(o,1,strErrMsg);

+ (void)initialize
{
    if (self == [proto_live_hang_ReportHangToastRsp class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strErrMsg) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_live_hang.ReportHangToastRsp";
}

@end
