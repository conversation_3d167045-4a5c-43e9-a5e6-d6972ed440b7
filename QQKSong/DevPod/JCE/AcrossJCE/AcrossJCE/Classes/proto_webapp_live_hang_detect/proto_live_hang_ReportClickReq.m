// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_webapp_live_hang_detect.jce   
// **********************************************************************

#import "proto_live_hang_ReportClickReq.h"

@implementation proto_live_hang_ReportClickReq

@synthesize JV2_PROP_NM(o,0,uAppPlatId);
@synthesize JV2_PROP_NM(o,1,llAnchorId);
@synthesize JV2_PROP_NM(o,2,strRoomId);
@synthesize JV2_PROP_NM(o,3,strShowId);
@synthesize JV2_PROP_NM(o,4,uClickType);
@synthesize JV2_PROP_EX(o,5,mapExtra,M09ONSStringONSString);

+ (void)initialize
{
    if (self == [proto_live_hang_ReportClickReq class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strRoomId) = DefaultTarsString;
        JV2_PROP(strShowId) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_live_hang.ReportClickReq";
}

@end
