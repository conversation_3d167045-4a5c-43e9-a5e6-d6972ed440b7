// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_webapp_live_hang_detect.jce   
// **********************************************************************

#import "JceObjectV2.h"

@interface proto_live_hang_ReportClickReq : JceObjectV2

@property (nonatomic, assign, JV2_PROP_GS_V2(uAppPlatId,setUAppPlatId:)) TarsUInt32 JV2_PROP_NM(o,0,uAppPlatId);
@property (nonatomic, assign, JV2_PROP_GS_V2(llAnchorId,setLlAnchorId:)) TarsInt64 JV2_PROP_NM(o,1,llAnchorId);
@property (nonatomic, retain, JV2_PROP_GS_V2(strRoomId,setStrRoomId:)) NSString* JV2_PROP_NM(o,2,strRoomId);
@property (nonatomic, retain, JV2_PROP_GS_V2(strShowId,setStrShowId:)) NSString* JV2_PROP_NM(o,3,strShowId);
@property (nonatomic, assign, JV2_PROP_GS_V2(uClickType,setUClickType:)) TarsUInt32 JV2_PROP_NM(o,4,uClickType);
@property (nonatomic, retain, JV2_PROP_GS_V2(mapExtra,setMapExtra:)) NSDictionary* JV2_PROP_EX(o,5,mapExtra,M09ONSStringONSString);

@end
