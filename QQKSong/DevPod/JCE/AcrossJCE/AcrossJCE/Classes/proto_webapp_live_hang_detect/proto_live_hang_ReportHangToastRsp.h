// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_webapp_live_hang_detect.jce   
// **********************************************************************

#import "JceObjectV2.h"

@interface proto_live_hang_ReportHangToastRsp : JceObjectV2

@property (nonatomic, assign, JV2_PROP_GS_V2(iErrCode,setIErrCode:)) TarsInt32 JV2_PROP_NM(o,0,iErrCode);
@property (nonatomic, retain, JV2_PROP_GS_V2(strErrMsg,setStrErrMsg:)) NSString* JV2_PROP_NM(o,1,strErrMsg);

@end
