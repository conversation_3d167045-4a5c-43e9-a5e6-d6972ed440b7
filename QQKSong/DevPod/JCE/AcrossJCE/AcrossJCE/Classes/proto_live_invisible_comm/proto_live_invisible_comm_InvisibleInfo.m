// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_live_invisible_comm.jce   
// **********************************************************************

#import "proto_live_invisible_comm_InvisibleInfo.h"

@implementation proto_live_invisible_comm_InvisibleInfo

@synthesize JV2_PROP_NM(o,0,uIsPrivilege);
@synthesize JV2_PROP_NM(o,1,uStatus);
@synthesize JV2_PROP_NM(o,2,lStartTs);
@synthesize JV2_PROP_NM(o,3,lEndTs);
@synthesize JV2_PROP_NM(o,4,strNickName);
@synthesize JV2_PROP_NM(o,5,strAvatarURL);

+ (void)initialize
{
    if (self == [proto_live_invisible_comm_InvisibleInfo class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strNickName) = DefaultTarsString;
        JV2_PROP(strAvatarURL) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_live_invisible_comm.InvisibleInfo";
}

@end
