// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_live_invisible_comm.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>

@interface proto_live_invisible_comm_InvisibleInfo : JceObjectV2

@property (nonatomic, assign, JV2_PROP_GS_V2(uIsPrivilege,setUIsPrivilege:)) TarsUInt32 JV2_PROP_NM(o,0,uIsPrivilege);
@property (nonatomic, assign, JV2_PROP_GS_V2(uStatus,setUStatus:)) TarsUInt32 JV2_PROP_NM(o,1,uStatus);
@property (nonatomic, assign, JV2_PROP_GS_V2(lStartTs,setLStartTs:)) TarsInt64 JV2_PROP_NM(o,2,lStartTs);
@property (nonatomic, assign, JV2_PROP_GS_V2(lEndTs,setLEndTs:)) TarsInt64 JV2_PROP_NM(o,3,lEndTs);
@property (nonatomic, retain, JV2_PROP_GS_V2(strNickName,setStrNickName:)) NSString* JV2_PROP_NM(o,4,strNickName);
@property (nonatomic, retain, JV2_PROP_GS_V2(strAvatarURL,setStrAvatarURL:)) NSString* JV2_PROP_NM(o,5,strAvatarURL);

@end
