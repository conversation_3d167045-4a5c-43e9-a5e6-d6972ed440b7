// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_across_interactive_msg.jce   
// **********************************************************************

#import "JceObjectV2.h"
#import "proto_across_interactive_msg_MsgParam.h"
#import "proto_across_interactive_msg_Uinfo.h"

@interface proto_across_interactive_msg_AcrossMsg : JceObjectV2

@property (nonatomic, assign, JV2_PROP_GS_V2(iMsgType,setIMsgType:)) TarsUInt32 JV2_PROP_NM(o,0,iMsgType);
@property (nonatomic, retain, JV2_PROP_GS_V2(strRoomID,setStrRoomID:)) NSString* JV2_PROP_NM(o,1,strRoomID);
@property (nonatomic, retain, JV2_PROP_GS_V2(strShowID,setStrShowID:)) NSString* JV2_PROP_NM(o,2,strShowID);
@property (nonatomic, retain, JV2_PROP_GS_V2(stAnchor,setStAnchor:)) proto_across_interactive_msg_Uinfo* JV2_PROP_NM(o,3,stAnchor);
@property (nonatomic, retain, JV2_PROP_GS_V2(stSender,setStSender:)) proto_across_interactive_msg_Uinfo* JV2_PROP_NM(o,4,stSender);
@property (nonatomic, assign, JV2_PROP_GS_V2(uSourcePlatApp,setUSourcePlatApp:)) TarsUInt32 JV2_PROP_NM(o,5,uSourcePlatApp);
@property (nonatomic, retain, JV2_PROP_GS_V2(strSeqID,setStrSeqID:)) NSString* JV2_PROP_NM(o,6,strSeqID);
@property (nonatomic, retain, JV2_PROP_GS_V2(stParam,setStParam:)) proto_across_interactive_msg_MsgParam* JV2_PROP_NM(o,7,stParam);
@property (nonatomic, assign, JV2_PROP_GS_V2(lTs,setLTs:)) TarsInt64 JV2_PROP_NM(o,8,lTs);

@end
