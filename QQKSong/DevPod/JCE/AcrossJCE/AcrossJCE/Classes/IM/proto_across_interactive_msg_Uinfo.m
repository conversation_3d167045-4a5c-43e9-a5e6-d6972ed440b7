// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_across_interactive_msg.jce   
// **********************************************************************

#import "proto_across_interactive_msg_Uinfo.h"

@implementation proto_across_interactive_msg_Uinfo

@synthesize JV2_PROP_NM(o,0,lUID);
@synthesize JV2_PROP_NM(o,1,strNick);
@synthesize JV2_PROP_NM(o,2,strAvatar);

+ (void)initialize
{
    if (self == [proto_across_interactive_msg_Uinfo class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strNick) = DefaultTarsString;
        JV2_PROP(strAvatar) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_across_interactive_msg.Uinfo";
}

@end
