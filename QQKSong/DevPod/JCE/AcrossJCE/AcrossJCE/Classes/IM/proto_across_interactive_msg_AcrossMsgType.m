// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_across_interactive_msg.jce   
// **********************************************************************

#import "JceObjectV2.h"
#import "proto_across_interactive_msg_AcrossMsgType.h"

#if TARSV2_ENUM_ETOS_AND_STOE_SUPPORTED

@implementation proto_across_interactive_msg_AcrossMsgTypeHelper

+ (NSString *)etos:(proto_across_interactive_msg_AcrossMsgType)e
{
    switch(e){
        case proto_across_interactive_msg_AcrossMsgType_ACROSS_MSG_TYPE_NONE: return @"proto_across_interactive_msg_AcrossMsgType_ACROSS_MSG_TYPE_NONE";
        case proto_across_interactive_msg_AcrossMsgType_ACROSS_MSG_TYPE_GIFT: return @"proto_across_interactive_msg_AcrossMsgType_ACROSS_MSG_TYPE_GIFT";
        case proto_across_interactive_msg_AcrossMsgType_ACROSS_MSG_TYPE_TEXT: return @"proto_across_interactive_msg_AcrossMsgType_ACROSS_MSG_TYPE_TEXT";
        case proto_across_interactive_msg_AcrossMsgType_ACROSS_MSG_TYPE_HEARTBEAT: return @"proto_across_interactive_msg_AcrossMsgType_ACROSS_MSG_TYPE_HEARTBEAT";
        case proto_across_interactive_msg_AcrossMsgType_ACROSS_MSG_TYPE_ENTER_ROOM: return @"proto_across_interactive_msg_AcrossMsgType_ACROSS_MSG_TYPE_ENTER_ROOM";
        case proto_across_interactive_msg_AcrossMsgType_ACROSS_MSG_TYPE_LEAVE_ROOM: return @"proto_across_interactive_msg_AcrossMsgType_ACROSS_MSG_TYPE_LEAVE_ROOM";
        default: return @"";
    }
}

+ (proto_across_interactive_msg_AcrossMsgType)stoe:(NSString *)s
{
    if(isTarsEnumStringEqual(s, @"proto_across_interactive_msg_AcrossMsgType_ACROSS_MSG_TYPE_NONE")) return proto_across_interactive_msg_AcrossMsgType_ACROSS_MSG_TYPE_NONE;
    if(isTarsEnumStringEqual(s, @"proto_across_interactive_msg_AcrossMsgType_ACROSS_MSG_TYPE_GIFT")) return proto_across_interactive_msg_AcrossMsgType_ACROSS_MSG_TYPE_GIFT;
    if(isTarsEnumStringEqual(s, @"proto_across_interactive_msg_AcrossMsgType_ACROSS_MSG_TYPE_TEXT")) return proto_across_interactive_msg_AcrossMsgType_ACROSS_MSG_TYPE_TEXT;
    if(isTarsEnumStringEqual(s, @"proto_across_interactive_msg_AcrossMsgType_ACROSS_MSG_TYPE_HEARTBEAT")) return proto_across_interactive_msg_AcrossMsgType_ACROSS_MSG_TYPE_HEARTBEAT;
    if(isTarsEnumStringEqual(s, @"proto_across_interactive_msg_AcrossMsgType_ACROSS_MSG_TYPE_ENTER_ROOM")) return proto_across_interactive_msg_AcrossMsgType_ACROSS_MSG_TYPE_ENTER_ROOM;
    if(isTarsEnumStringEqual(s, @"proto_across_interactive_msg_AcrossMsgType_ACROSS_MSG_TYPE_LEAVE_ROOM")) return proto_across_interactive_msg_AcrossMsgType_ACROSS_MSG_TYPE_LEAVE_ROOM;
    return INT32_MIN;
}

@end

#endif
