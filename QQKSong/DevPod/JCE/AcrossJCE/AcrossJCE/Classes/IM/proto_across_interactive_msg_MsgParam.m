// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_across_interactive_msg.jce   
// **********************************************************************

#import "proto_across_interactive_msg_MsgParam.h"

@implementation proto_across_interactive_msg_MsgParam

@synthesize JV2_PROP_NM(o,0,strText);
@synthesize JV2_PROP_NM(o,1,uActionType);
@synthesize JV2_PROP_NM(o,2,strActionText);
@synthesize JV2_PROP_EX(o,3,mapExt,M09ONSStringONSString);
@synthesize JV2_PROP_NM(o,4,strSourcePlatAppLogo);

+ (void)initialize
{
    if (self == [proto_across_interactive_msg_MsgParam class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strText) = DefaultTarsString;
        JV2_PROP(strActionText) = DefaultTarsString;
        JV2_PROP(strSourcePlatAppLogo) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_across_interactive_msg.MsgParam";
}

@end
