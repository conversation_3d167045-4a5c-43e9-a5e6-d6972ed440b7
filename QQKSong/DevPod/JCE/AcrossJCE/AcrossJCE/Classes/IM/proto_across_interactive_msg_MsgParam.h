// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_across_interactive_msg.jce   
// **********************************************************************

#import "JceObjectV2.h"

@interface proto_across_interactive_msg_MsgParam : JceObjectV2

@property (nonatomic, retain, JV2_PROP_GS_V2(strText,setStrText:)) NSString* JV2_PROP_NM(o,0,strText);
@property (nonatomic, assign, JV2_PROP_GS_V2(uActionType,setUActionType:)) TarsUInt32 JV2_PROP_NM(o,1,uActionType);
@property (nonatomic, retain, JV2_PROP_GS_V2(strActionText,setStrActionText:)) NSString* JV2_PROP_NM(o,2,strActionText);
@property (nonatomic, retain, JV2_PROP_GS_V2(mapExt,setMapExt:)) NSDictionary* JV2_PROP_EX(o,3,mapExt,M09ONSStringONSString);
@property (nonatomic, retain, JV2_PROP_GS_V2(strSourcePlatAppLogo,setStrSourcePlatAppLogo:)) NSString* JV2_PROP_NM(o,4,strSourcePlatAppLogo);

@end
