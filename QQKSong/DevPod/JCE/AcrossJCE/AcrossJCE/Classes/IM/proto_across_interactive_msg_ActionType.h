// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_across_interactive_msg.jce   
// **********************************************************************

#import "JceObjectV2.h"

enum {
    proto_across_interactive_msg_ActionType_ACTION_TYPE_NONE = 0,
    proto_across_interactive_msg_ActionType_ACTION_TYPE_FOLLOW = 1,
    proto_across_interactive_msg_ActionType_ACTION_TYPE_AT = 2,
    proto_across_interactive_msg_ActionType_ACTION_TYPE_SHARE = 3,
    proto_across_interactive_msg_ActionType_ACTION_TYPE_PUBLIC_NOTICE = 4,
    proto_across_interactive_msg_ActionType_ACTION_TYPE_FANBASE_OPEN = 5
};
#define proto_across_interactive_msg_ActionType TarsInt32

#if TARSV2_ENUM_ETOS_AND_STOE_SUPPORTED

@interface proto_across_interactive_msg_ActionTypeHelper: TarsEnumHelper

+ (NSString *)etos:(proto_across_interactive_msg_ActionType)e;
+ (proto_across_interactive_msg_ActionType)stoe:(NSString *)s;

@end

#endif
