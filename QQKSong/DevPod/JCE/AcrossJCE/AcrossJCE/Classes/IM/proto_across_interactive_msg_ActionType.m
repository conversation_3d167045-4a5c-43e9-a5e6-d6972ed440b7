// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_across_interactive_msg.jce   
// **********************************************************************

#import "JceObjectV2.h"
#import "proto_across_interactive_msg_ActionType.h"

#if TARSV2_ENUM_ETOS_AND_STOE_SUPPORTED

@implementation proto_across_interactive_msg_ActionTypeHelper

+ (NSString *)etos:(proto_across_interactive_msg_ActionType)e
{
    switch(e){
        case proto_across_interactive_msg_ActionType_ACTION_TYPE_NONE: return @"proto_across_interactive_msg_ActionType_ACTION_TYPE_NONE";
        case proto_across_interactive_msg_ActionType_ACTION_TYPE_FOLLOW: return @"proto_across_interactive_msg_ActionType_ACTION_TYPE_FOLLOW";
        case proto_across_interactive_msg_ActionType_ACTION_TYPE_AT: return @"proto_across_interactive_msg_ActionType_ACTION_TYPE_AT";
        case proto_across_interactive_msg_ActionType_ACTION_TYPE_SHARE: return @"proto_across_interactive_msg_ActionType_ACTION_TYPE_SHARE";
        case proto_across_interactive_msg_ActionType_ACTION_TYPE_PUBLIC_NOTICE: return @"proto_across_interactive_msg_ActionType_ACTION_TYPE_PUBLIC_NOTICE";
        case proto_across_interactive_msg_ActionType_ACTION_TYPE_FANBASE_OPEN: return @"proto_across_interactive_msg_ActionType_ACTION_TYPE_FANBASE_OPEN";
        default: return @"";
    }
}

+ (proto_across_interactive_msg_ActionType)stoe:(NSString *)s
{
    if(isTarsEnumStringEqual(s, @"proto_across_interactive_msg_ActionType_ACTION_TYPE_NONE")) return proto_across_interactive_msg_ActionType_ACTION_TYPE_NONE;
    if(isTarsEnumStringEqual(s, @"proto_across_interactive_msg_ActionType_ACTION_TYPE_FOLLOW")) return proto_across_interactive_msg_ActionType_ACTION_TYPE_FOLLOW;
    if(isTarsEnumStringEqual(s, @"proto_across_interactive_msg_ActionType_ACTION_TYPE_AT")) return proto_across_interactive_msg_ActionType_ACTION_TYPE_AT;
    if(isTarsEnumStringEqual(s, @"proto_across_interactive_msg_ActionType_ACTION_TYPE_SHARE")) return proto_across_interactive_msg_ActionType_ACTION_TYPE_SHARE;
    if(isTarsEnumStringEqual(s, @"proto_across_interactive_msg_ActionType_ACTION_TYPE_PUBLIC_NOTICE")) return proto_across_interactive_msg_ActionType_ACTION_TYPE_PUBLIC_NOTICE;
    if(isTarsEnumStringEqual(s, @"proto_across_interactive_msg_ActionType_ACTION_TYPE_FANBASE_OPEN")) return proto_across_interactive_msg_ActionType_ACTION_TYPE_FANBASE_OPEN;
    return INT32_MIN;
}

@end

#endif
