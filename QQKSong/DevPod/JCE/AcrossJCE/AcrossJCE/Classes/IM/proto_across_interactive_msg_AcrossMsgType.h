// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_across_interactive_msg.jce   
// **********************************************************************

#import "JceObjectV2.h"

enum {
    proto_across_interactive_msg_AcrossMsgType_ACROSS_MSG_TYPE_NONE = 0,
    proto_across_interactive_msg_AcrossMsgType_ACROSS_MSG_TYPE_GIFT = 1,
    proto_across_interactive_msg_AcrossMsgType_ACROSS_MSG_TYPE_TEXT = 2,
    proto_across_interactive_msg_AcrossMsgType_ACROSS_MSG_TYPE_HEARTBEAT = 3,
    proto_across_interactive_msg_AcrossMsgType_ACROSS_MSG_TYPE_ENTER_ROOM = 4,
    proto_across_interactive_msg_AcrossMsgType_ACROSS_MSG_TYPE_LEAVE_ROOM = 5
};
#define proto_across_interactive_msg_AcrossMsgType TarsInt32

#if TARSV2_ENUM_ETOS_AND_STOE_SUPPORTED

@interface proto_across_interactive_msg_AcrossMsgTypeHelper: TarsEnumHelper

+ (NSString *)etos:(proto_across_interactive_msg_AcrossMsgType)e;
+ (proto_across_interactive_msg_AcrossMsgType)stoe:(NSString *)s;

@end

#endif
