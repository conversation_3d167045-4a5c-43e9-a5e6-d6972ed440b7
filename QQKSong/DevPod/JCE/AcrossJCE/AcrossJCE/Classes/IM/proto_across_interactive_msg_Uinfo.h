// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_across_interactive_msg.jce   
// **********************************************************************

#import "JceObjectV2.h"

@interface proto_across_interactive_msg_Uinfo : JceObjectV2

@property (nonatomic, assign, JV2_PROP_GS_V2(lUID,setLUID:)) TarsInt64 JV2_PROP_NM(o,0,lUID);
@property (nonatomic, retain, JV2_PROP_GS_V2(strNick,setStrNick:)) NSString* JV2_PROP_NM(o,1,strNick);
@property (nonatomic, retain, JV2_PROP_GS_V2(strAvatar,setStrAvatar:)) NSString* JV2_PROP_NM(o,2,strAvatar);

@end
