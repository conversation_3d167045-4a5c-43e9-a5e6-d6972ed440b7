// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_across_interactive_msg.jce   
// **********************************************************************

#import "proto_across_interactive_msg_AcrossMsg.h"

@implementation proto_across_interactive_msg_AcrossMsg

@synthesize JV2_PROP_NM(o,0,iMsgType);
@synthesize JV2_PROP_NM(o,1,strRoomID);
@synthesize JV2_PROP_NM(o,2,strShowID);
@synthesize JV2_PROP_NM(o,3,stAnchor);
@synthesize JV2_PROP_NM(o,4,stSender);
@synthesize JV2_PROP_NM(o,5,uSourcePlatApp);
@synthesize JV2_PROP_NM(o,6,strSeqID);
@synthesize JV2_PROP_NM(o,7,stParam);
@synthesize JV2_PROP_NM(o,8,lTs);

+ (void)initialize
{
    if (self == [proto_across_interactive_msg_AcrossMsg class]) {
        [proto_across_interactive_msg_MsgParam initialize];
        [proto_across_interactive_msg_Uinfo initialize];
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strRoomID) = DefaultTarsString;
        JV2_PROP(strShowID) = DefaultTarsString;
        JV2_PROP(strSeqID) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_across_interactive_msg.AcrossMsg";
}

@end
