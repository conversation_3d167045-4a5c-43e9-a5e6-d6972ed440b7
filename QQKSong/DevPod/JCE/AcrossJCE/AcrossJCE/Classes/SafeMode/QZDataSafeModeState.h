// **********************************************************************
// This file was generated by a TAF parser!
// TAF version 2.1.4.2 by WSRD Tencent.
// Generated from `/usr/local/resin_system.mqq.com/webapps/communication/taf/upload/allenlin/safemode.jce'
// **********************************************************************

#import "JceObjectV2.h"

@interface QZDataSafeModeState : JceObjectV2

@property (nonatomic, assign, JV2_PROP_GS_V2(crashCnt,setCrashCnt:)) JceInt32 JV2_PROP_NM(r,1,crashCnt);
@property (nonatomic, assign, JV2_PROP_GS_V2(loginFailCnt,setLoginFailCnt:)) JceInt32 JV2_PROP_NM(r,2,loginFailCnt);
@property (nonatomic, assign, JV2_PROP_GS_V2(forceSafeMode,setForceSafeMode:)) JceInt32 JV2_PROP_NM(r,3,forceSafeMode);
@property (nonatomic, assign, JV2_PROP_GS_V2(disableActiveFeeds,setDisableActiveFeeds:)) JceInt32 JV2_PROP_NM(o,4,disableActiveFeeds);
@property (nonatomic, assign, JV2_PROP_GS_V2(disablePics,setDisablePics:)) JceInt32 JV2_PROP_NM(o,5,disablePics);
@property (nonatomic, assign, JV2_PROP_GS_V2(disableWatermarkCamera,setDisableWatermarkCamera:)) JceInt32 JV2_PROP_NM(o,6,disableWatermarkCamera);
@property (nonatomic, assign, JV2_PROP_GS_V2(disableMicroVideo,setDisableMicroVideo:)) JceInt32 JV2_PROP_NM(o,7,disableMicroVideo);
@property (nonatomic, assign, JV2_PROP_GS_V2(recoverType,setRecoverType:)) JceInt32 JV2_PROP_NM(o,8,recoverType);
@property (nonatomic, retain, JV2_PROP_GS_V2(recoverItems,setRecoverItems:)) NSArray* JV2_PROP_EX(o,9,recoverItems,VONSNumber);
@property (nonatomic, assign, JV2_PROP_GS_V2(disableLoadingImage,setDisableLoadingImage:)) JceInt32 JV2_PROP_NM(o,10,disableLoadingImage);
@property (nonatomic, assign, JV2_PROP_GS_V2(disableCocos2d,setDisableCocos2d:)) JceInt32 JV2_PROP_NM(o,11,disableCocos2d);
@property (nonatomic, retain, JV2_PROP_GS_V2(filterFeedKey,setFilterFeedKey:)) NSMutableDictionary* JV2_PROP_EX(o,12,filterFeedKey,M09ONSStringVONSString);
@property (nonatomic, assign, JV2_PROP_GS_V2(isInSafeMode,setIsInSafeMode:)) JceInt32 JV2_PROP_NM(r,13,isInSafeMode);
@property (nonatomic, assign, JV2_PROP_GS_V2(exceptionCnt,setExceptionCnt:)) JceInt32 JV2_PROP_NM(r,14,exceptionCnt); // 废弃
@property (nonatomic, assign, JV2_PROP_GS_V2(foregroundExceptionCnt,setForegroundExceptionCnt:)) JceInt32 JV2_PROP_NM(r,15,foregroundExceptionCnt);
@property (nonatomic, assign, JV2_PROP_GS_V2(backgroundExceptionCnt,setBackgroundExceptionCnt:)) JceInt32 JV2_PROP_NM(r,16,backgroundExceptionCnt);

@end
