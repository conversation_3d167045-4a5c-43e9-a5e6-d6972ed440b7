// **********************************************************************
// This file was generated by a TAF parser!
// TAF version 2.1.4.2 by WSRD Tencent.
// Generated from `/usr/local/resin_system.mqq.com/webapps/communication/taf/upload/allenlin/safemode.jce'
// **********************************************************************

#import "QZDataSafeModeCfg.h"

@implementation QZDataSafeModeCfg

@synthesize JV2_PROP_NM(r,1,startDuration);
@synthesize JV2_PROP_NM(r,2,crashMax);
@synthesize JV2_PROP_NM(r,3,loginFailMax);
@synthesize JV2_PROP_NM(r,4,downloadURL);
@synthesize JV2_PROP_EX(r,5,crashClearItems,VONSNumber);
@synthesize JV2_PROP_EX(r,6,loginFailClearItems,VONSNumber);
@synthesize JV2_PROP_EX(r,7,repairItems,VONSNumber);

+ (void)initialize
{
    if (self == [QZDataSafeModeCfg class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(downloadURL) = DefaultJceString;
        JV2_PROP(crashClearItems) = DefaultJceArray;
        JV2_PROP(loginFailClearItems) = DefaultJceArray;
        JV2_PROP(repairItems) = DefaultJceArray;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"QZData.SafeModeCfg";
}

@end
