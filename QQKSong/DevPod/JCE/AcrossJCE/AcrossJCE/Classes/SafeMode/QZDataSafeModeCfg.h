// **********************************************************************
// This file was generated by a TAF parser!
// TAF version 2.1.4.2 by WSRD Tencent.
// Generated from `/usr/local/resin_system.mqq.com/webapps/communication/taf/upload/allenlin/safemode.jce'
// **********************************************************************

#import "JceObjectV2.h"

@interface QZDataSafeModeCfg : JceObjectV2

@property (nonatomic, assign, JV2_PROP_GS_V2(startDuration,setStartDuration:)) JceInt32 JV2_PROP_NM(r,1,startDuration);
@property (nonatomic, assign, JV2_PROP_GS_V2(crashMax,setCrashMax:)) JceInt32 JV2_PROP_NM(r,2,crashMax);
@property (nonatomic, assign, JV2_PROP_GS_V2(loginFailMax,setLoginFailMax:)) JceInt32 JV2_PROP_NM(r,3,loginFailMax);
@property (nonatomic, retain, JV2_PROP_GS_V2(downloadURL,setDownloadURL:)) NSString* JV2_PROP_NM(r,4,downloadURL);
@property (nonatomic, retain, JV2_PROP_GS_V2(crashClearItems,setCrashClearItems:)) NSArray* JV2_PROP_EX(r,5,crashClearItems,VONSNumber);
@property (nonatomic, retain, JV2_PROP_GS_V2(loginFailClearItems,setLoginFailClearItems:)) NSArray* JV2_PROP_EX(r,6,loginFailClearItems,VONSNumber);
@property (nonatomic, retain, JV2_PROP_GS_V2(repairItems,setRepairItems:)) NSArray* JV2_PROP_EX(r,7,repairItems,VONSNumber);

@end
