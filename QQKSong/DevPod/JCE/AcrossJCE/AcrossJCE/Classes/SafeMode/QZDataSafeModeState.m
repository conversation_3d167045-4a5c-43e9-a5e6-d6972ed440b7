// **********************************************************************
// This file was generated by a TAF parser!
// TAF version 2.1.4.2 by WSRD Tencent.
// Generated from `/usr/local/resin_system.mqq.com/webapps/communication/taf/upload/allenlin/safemode.jce'
// **********************************************************************

#import "QZDataSafeModeState.h"

@implementation QZDataSafeModeState

@synthesize JV2_PROP_NM(r,1,crashCnt);
@synthesize JV2_PROP_NM(r,2,loginFailCnt);
@synthesize JV2_PROP_NM(r,3,forceSafeMode);
@synthesize JV2_PROP_NM(o,4,disableActiveFeeds);
@synthesize JV2_PROP_NM(o,5,disablePics);
@synthesize JV2_PROP_NM(o,6,disableWatermarkCamera);
@synthesize JV2_PROP_NM(o,7,disableMicroVideo);
@synthesize JV2_PROP_NM(o,8,recoverType);
@synthesize JV2_PROP_EX(o,9,recoverItems,VONSNumber);
@synthesize JV2_PROP_NM(o,10,disableLoadingImage);
@synthesize JV2_PROP_NM(o,11,disableCocos2d);
@synthesize JV2_PROP_EX(o,12,filterFeedKey,M09ONSStringVONSString);
@synthesize JV2_PROP_NM(r,13,isInSafeMode);
@synthesize JV2_PROP_NM(r,14,exceptionCnt);
@synthesize JV2_PROP_NM(r,15,foregroundExceptionCnt);
@synthesize JV2_PROP_NM(r,16,backgroundExceptionCnt);

+ (void)initialize
{
    if (self == [QZDataSafeModeState class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

+ (NSString*)jceType
{
    return @"QZData.SafeModeState";
}

@end
