// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_live_invisible_webapp.jce   
// **********************************************************************

#import "proto_live_invisible_webapp_SetInvisibleStatusRsp.h"

@implementation proto_live_invisible_webapp_SetInvisibleStatusRsp

@synthesize JV2_PROP_NM(o,0,iRet);
@synthesize JV2_PROP_NM(o,1,strMsg);

+ (void)initialize
{
    if (self == [proto_live_invisible_webapp_SetInvisibleStatusRsp class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strMsg) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_live_invisible_webapp.SetInvisibleStatusRsp";
}

@end
