// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_live_invisible_webapp.jce   
// **********************************************************************

#import "proto_live_invisible_webapp_GetInvisibleInfoRsp.h"

@implementation proto_live_invisible_webapp_GetInvisibleInfoRsp

@synthesize JV2_PROP_EX(o,0,mapInvisibleInfo,M09ONSStringOproto_live_invisible_comm_InvisibleInfo);
@synthesize JV2_PROP_NM(o,1,iRet);
@synthesize JV2_PROP_NM(o,2,strMsg);

+ (void)initialize
{
    if (self == [proto_live_invisible_webapp_GetInvisibleInfoRsp class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strMsg) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_live_invisible_webapp.GetInvisibleInfoRsp";
}

@end
