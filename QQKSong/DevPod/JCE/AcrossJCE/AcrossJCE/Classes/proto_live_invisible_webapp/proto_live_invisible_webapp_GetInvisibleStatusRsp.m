// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_live_invisible_webapp.jce   
// **********************************************************************

#import "proto_live_invisible_webapp_GetInvisibleStatusRsp.h"

@implementation proto_live_invisible_webapp_GetInvisibleStatusRsp

@synthesize JV2_PROP_NM(o,0,iRet);
@synthesize JV2_PROP_NM(o,1,strMsg);
@synthesize JV2_PROP_NM(o,2,uStatus);

+ (void)initialize
{
    if (self == [proto_live_invisible_webapp_GetInvisibleStatusRsp class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strMsg) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_live_invisible_webapp.GetInvisibleStatusRsp";
}

@end
