// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_live_invisible_webapp.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>

@interface proto_live_invisible_webapp_GetInvisibleInfoRsp : JceObjectV2

@property (nonatomic, retain, JV2_PROP_GS_V2(mapInvisibleInfo,setMapInvisibleInfo:)) NSDictionary* JV2_PROP_EX(o,0,mapInvisibleInfo,M09ONSStringOproto_live_invisible_comm_InvisibleInfo);
@property (nonatomic, assign, JV2_PROP_GS_V2(iRet,setIRet:)) TarsInt32 JV2_PROP_NM(o,1,iRet);
@property (nonatomic, retain, JV2_PROP_GS_V2(strMsg,setStrMsg:)) NSString* JV2_PROP_NM(o,2,strMsg);

@end
