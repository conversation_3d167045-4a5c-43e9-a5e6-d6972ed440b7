// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_live_invisible_webapp.jce   
// **********************************************************************

#import "proto_live_invisible_webapp_GetInvisibleStatusReq.h"

@implementation proto_live_invisible_webapp_GetInvisibleStatusReq

@synthesize JV2_PROP_NM(o,0,uAppId);
@synthesize JV2_PROP_NM(o,1,strUid);

+ (void)initialize
{
    if (self == [proto_live_invisible_webapp_GetInvisibleStatusReq class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strUid) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_live_invisible_webapp.GetInvisibleStatusReq";
}

@end
