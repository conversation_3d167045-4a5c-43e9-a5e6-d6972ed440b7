// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_live_invisible_webapp.jce   
// **********************************************************************

#import "proto_live_invisible_webapp_SetInvisibleStatusReq.h"

@implementation proto_live_invisible_webapp_SetInvisibleStatusReq

@synthesize JV2_PROP_NM(o,0,uAppId);
@synthesize JV2_PROP_NM(o,1,strUid);
@synthesize JV2_PROP_NM(o,2,uStatus);

+ (void)initialize
{
    if (self == [proto_live_invisible_webapp_SetInvisibleStatusReq class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strUid) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_live_invisible_webapp.SetInvisibleStatusReq";
}

@end
