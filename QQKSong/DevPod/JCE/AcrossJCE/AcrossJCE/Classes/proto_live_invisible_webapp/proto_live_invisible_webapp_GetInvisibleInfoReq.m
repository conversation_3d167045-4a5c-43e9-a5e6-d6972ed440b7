// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_live_invisible_webapp.jce   
// **********************************************************************

#import "proto_live_invisible_webapp_GetInvisibleInfoReq.h"

@implementation proto_live_invisible_webapp_GetInvisibleInfoReq

@synthesize JV2_PROP_NM(o,0,uAppId);
@synthesize JV2_PROP_EX(o,1,vctUid,VONSString);

+ (void)initialize
{
    if (self == [proto_live_invisible_webapp_GetInvisibleInfoReq class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_live_invisible_webapp.GetInvisibleInfoReq";
}

@end
