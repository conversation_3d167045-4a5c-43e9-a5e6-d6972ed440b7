// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_game_comm.jce   
// **********************************************************************

#import "JceObjectV2.h"
#import "proto_interact_game_comm_RoomStatus.h"

#if TARSV2_ENUM_ETOS_AND_STOE_SUPPORTED

@implementation proto_interact_game_comm_RoomStatusHelper

+ (NSString *)etos:(proto_interact_game_comm_RoomStatus)e
{
    switch(e){
        case proto_interact_game_comm_RoomStatus_RoomNone: return @"proto_interact_game_comm_RoomStatus_RoomNone";
        case proto_interact_game_comm_RoomStatus_RoomPending: return @"proto_interact_game_comm_RoomStatus_RoomPending";
        case proto_interact_game_comm_RoomStatus_RoomPlaying: return @"proto_interact_game_comm_RoomStatus_RoomPlaying";
        default: return @"";
    }
}

+ (proto_interact_game_comm_RoomStatus)stoe:(NSString *)s
{
    if(isTarsEnumStringEqual(s, @"proto_interact_game_comm_RoomStatus_RoomNone")) return proto_interact_game_comm_RoomStatus_RoomNone;
    if(isTarsEnumStringEqual(s, @"proto_interact_game_comm_RoomStatus_RoomPending")) return proto_interact_game_comm_RoomStatus_RoomPending;
    if(isTarsEnumStringEqual(s, @"proto_interact_game_comm_RoomStatus_RoomPlaying")) return proto_interact_game_comm_RoomStatus_RoomPlaying;
    return INT32_MIN;
}

@end

#endif
