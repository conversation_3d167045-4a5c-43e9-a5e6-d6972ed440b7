// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_game_comm.jce   
// **********************************************************************

#import "JceObjectV2.h"

enum {
    proto_interact_game_comm_RoomStatus_RoomNone = 0,
    proto_interact_game_comm_RoomStatus_RoomPending = 1,
    proto_interact_game_comm_RoomStatus_RoomPlaying = 2
};
#define proto_interact_game_comm_RoomStatus TarsInt32

#if TARSV2_ENUM_ETOS_AND_STOE_SUPPORTED

@interface proto_interact_game_comm_RoomStatusHelper: TarsEnumHelper

+ (NSString *)etos:(proto_interact_game_comm_RoomStatus)e;
+ (proto_interact_game_comm_RoomStatus)stoe:(NSString *)s;

@end

#endif
