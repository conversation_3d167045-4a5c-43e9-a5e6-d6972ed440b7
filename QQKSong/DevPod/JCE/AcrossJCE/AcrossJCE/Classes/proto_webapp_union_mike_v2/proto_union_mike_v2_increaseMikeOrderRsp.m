// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_webapp_union_mike_v2.jce   
// **********************************************************************

#import "proto_union_mike_v2_IncreaseMikeOrderRsp.h"

@implementation proto_union_mike_v2_IncreaseMikeOrderRsp

@synthesize JV2_PROP_NM(o,0,iErrCode);
@synthesize JV2_PROP_NM(o,1,strErrMsg);

+ (void)initialize
{
    if (self == [proto_union_mike_v2_IncreaseMikeOrderRsp class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strErrMsg) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_union_mike_v2.IncreaseMikeOrderRsp";
}

@end
