// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_webapp_union_mike_v2.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>

#define proto_webapp_union_mike_v2_UNION_MIKE_WEBAPP_MOD_ID ((TarsUInt64)298005458)

#define proto_webapp_union_mike_v2_CMD_UNION_MIKE_WEBAPP_QUERY_MIKE_USER_LIST @"interact.union_mike.webapp.query_mike_user_list"

#define proto_webapp_union_mike_v2_CMD_UNION_MIKE_WEBAPP_QUERY_PENDING_MIKE @"interact.union_mike.webapp.query_pending_mike"

#define proto_webapp_union_mike_v2_CMD_UNION_MIKE_WEBAPP_QUERY_MIKE_DETAIL @"interact.union_mike.webapp.query_mike_detail"

#define proto_webapp_union_mike_v2_CMD_UNION_MIKE_WEBAPP_CHECK_MIKE @"interact.union_mike.webapp.check_mike"

#define proto_webapp_union_mike_v2_CMD_UNION_MIKE_WEBAPP_MIKE_INVITE @"interact.union_mike.webapp.mike_invite"

#define proto_webapp_union_mike_v2_CMD_UNION_MIKE_WEBAPP_MIKE_ANSWER @"interact.union_mike.webapp.mike_answer"

#define proto_webapp_union_mike_v2_CMD_UNION_MIKE_WEBAPP_MIKE_OVER @"interact.union_mike.webapp.mike_over"

#define proto_webapp_union_mike_v2_CMD_UNION_MIKE_WEBAPP_CONTROL_VOLUME @"interact.union_mike.webapp.control_volume"

#define proto_webapp_union_mike_v2_CMD_UNION_MIKE_WEBAPP_QUERY_JOINABLE_MIKE @"interact.union_mike.webapp.query_joinable_mike"

#define proto_webapp_union_mike_v2_CMD_UNION_MIKE_WEBAPP_QUERY_ONLINE_MIKE_LIST @"interact.union_mike.webapp.query_online_mike_list"

#define proto_webapp_union_mike_v2_CMD_UNION_MIKE_WEBAPP_CONTROL_MIKE_JOIN @"interact.union_mike.webapp.control_mike_join"

#define proto_webapp_union_mike_v2_CMD_UNION_MIKE_WEBAPP_NAME_MIKE_LABEL @"interact.union_mike.webapp.name_mike_label"

#define proto_webapp_union_mike_v2_CMD_UNION_MIKE_WEBAPP_INCREASE_MIKE_ORDER @"interact.union_mike.webapp.increase_mike_order"

#define proto_webapp_union_mike_v2_CMD_UNION_MIKE_WEBAPP_HANDLE_MIKE_PLAY @"interact.union_mike.webapp.handle_mike_play"

#define proto_webapp_union_mike_v2_CMD_UNION_MIKE_WEBAPP_CHECK_STREAM_MIX @"interact.union_mike.webapp.check_stream_mix"

#define proto_webapp_union_mike_v2_CMD_UNION_MIKE_WEBAPP_SEARCH_MIKE_USER @"interact.union_mike.webapp.search_mike_user"

#define proto_webapp_union_mike_v2_CMD_UNION_MIKE_WEBAPP_QUERY_MIKE_PROMOTION @"interact.union_mike.webapp.query_mike_promotion"

#define proto_webapp_union_mike_v2_CMD_UNION_MIKE_WEBAPP_RESERVE_MIKE_PLAY @"interact.union_mike.webapp.reserve_mike_play"

#define proto_webapp_union_mike_v2_CMD_UNION_MIKE_WEBAPP_QUERY_MIKE_SWITCH @"interact.union_mike.webapp.query_mike_switch"

#define proto_webapp_union_mike_v2_CMD_UNION_MIKE_WEBAPP_SET_MIKE_SWITCH @"interact.union_mike.webapp.set_mike_switch"

#define proto_webapp_union_mike_v2_CMD_UNION_MIKE_WEBAPP_CHECK_AUDIENCE_RIGHT @"interact.union_mike.webapp.check_audience_right"

#define proto_webapp_union_mike_v2_StrSeqId @"strSeqId"

#define proto_webapp_union_mike_v2_SINGLE_MIKE_LABEL @"single_mike_label"

#define proto_webapp_union_mike_v2_KG_ROOMMSG_TYPE_UNION_MIKE_V2 ((int)212)

#define proto_webapp_union_mike_v2_WESING_ROOMMSG_TYPE_UNION_MIKE_V2 ((int)148)

#define proto_webapp_union_mike_v2_MUSIC_ROOMMSG_TYPE_UNION_MIKE_V2 ((int)57)

#define proto_webapp_union_mike_v2_SUB_ROOMMSG_UNION_MIKE_V2_MIKE_INVITE ((int)1)

#define proto_webapp_union_mike_v2_SUB_ROOMMSG_UNION_MIKE_V2_MIKE_REJECT ((int)2)

#define proto_webapp_union_mike_v2_SUB_ROOMMSG_UNION_MIKE_V2_BROADCAST ((int)3)

#define proto_webapp_union_mike_v2_SUB_ROOMMSG_UNION_MIKE_V2_ANSWER_TIMEOUT ((int)4)

#define proto_webapp_union_mike_v2_SUB_ROOMMSG_UNION_MIKE_V2_INVITE_CANCEL ((int)5)

#define proto_webapp_union_mike_v2_SUB_ROOMMSG_UNION_MIKE_V2_MIKE_NOTIFY ((int)6)

#define proto_webapp_union_mike_v2_SUB_ROOMMSG_UNION_MIKE_V2_MIKE_OPTION_SWITCH_CHANGE ((int)7)

#define proto_webapp_union_mike_v2_SUB_ROOMMSG_UNION_MIKE_V2_MIKE_COMM_TOAST ((int)8)

#define proto_webapp_union_mike_v2_SUB_ROOMMSG_UNION_MIKE_V2_IGNORE_ALL_INVITE ((int)9)

#define proto_webapp_union_mike_v2_SUB_ROOMMSG_UNION_MIKE_V2_MIKE_DETAIL_UPDATE_NOTIFY ((int)10)

#define proto_webapp_union_mike_v2_SUB_ROOMMSG_UNION_MIKE_V2_HAT_GRABBING ((int)101)

#define proto_webapp_union_mike_v2_SUB_ROOMMSG_UNION_MIKE_V2_HAT_GRABBING_INVITE ((int)102)

#define proto_webapp_union_mike_v2_SUB_ROOMMSG_UNION_MIKE_V2_MULTIPLAYER_LEVEL_RUSH ((int)111) // 多人闯关玩法

#define proto_webapp_union_mike_v2_SUB_ROOMMSG_UNION_MIKE_V2_MULTIPLAYER_LEVEL_RUSH_INVITE ((int)112) // 多人闯关玩法邀请

