// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_live_ai_partner_comm.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>

@interface proto_live_ai_partner_comm_AiPartnerSwitch : JceObjectV2

@property (nonatomic, assign, JV2_PROP_GS_V2(uSwitchType,setUSwitchType:)) TarsUInt32 JV2_PROP_NM(o,0,uSwitchType);
@property (nonatomic, retain, JV2_PROP_GS_V2(strSwitchName,setStrSwitchName:)) NSString* JV2_PROP_NM(o,1,strSwitchName);
@property (nonatomic, assign, JV2_PROP_GS_V2(uStatus,setUStatus:)) TarsUInt32 JV2_PROP_NM(o,2,uStatus);

@end
