// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_live_ai_partner_comm.jce   
// **********************************************************************

#import "proto_live_ai_partner_comm_AiPartnerSwitch.h"

@implementation proto_live_ai_partner_comm_AiPartnerSwitch

@synthesize JV2_PROP_NM(o,0,uSwitchType);
@synthesize JV2_PROP_NM(o,1,strSwitchName);
@synthesize JV2_PROP_NM(o,2,uStatus);

+ (void)initialize
{
    if (self == [proto_live_ai_partner_comm_AiPartnerSwitch class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strSwitchName) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_live_ai_partner_comm.AiPartnerSwitch";
}

@end
