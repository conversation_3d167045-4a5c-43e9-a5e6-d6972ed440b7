// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_ktv_ai_host_webapp.jce   
// **********************************************************************

#import "proto_ktv_ai_host_webapp_AiHostInfo.h"
#import "proto_ktv_ai_host_webapp_AiHostRightInfo.h"
#import "proto_ktv_ai_host_webapp_SegmentInfo.h"

@implementation proto_ktv_ai_host_webapp_AiHostInfo

@synthesize JV2_PROP_NM(o,0,iCharacterId);
@synthesize JV2_PROP_NM(o,1,lUid);
@synthesize JV2_PROP_NM(o,2,lTimestamp);
@synthesize JV2_PROP_NM(o,3,strNick);
@synthesize JV2_PROP_NM(o,4,strAudioUrl);
@synthesize JV2_PROP_EX(o,5,vctCharacterTags,VONSString);
@synthesize JV2_PROP_NM(o,6,strIntroduction);
@synthesize JV2_PROP_EX(o,7,vctSegments,VOproto_ktv_ai_host_webapp_SegmentInfo);
@synthesize JV2_PROP_NM(o,8,stRightInfo);

+ (void)initialize
{
    if (self == [proto_ktv_ai_host_webapp_AiHostInfo class]) {
        [proto_ktv_ai_host_webapp_AiHostRightInfo initialize];
        [proto_ktv_ai_host_webapp_SegmentInfo initialize];
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strNick) = DefaultTarsString;
        JV2_PROP(strAudioUrl) = DefaultTarsString;
        JV2_PROP(strIntroduction) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_ktv_ai_host_webapp.AiHostInfo";
}

@end
