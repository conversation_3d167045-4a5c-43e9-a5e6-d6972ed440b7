// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_ktv_ai_host_webapp.jce   
// **********************************************************************

#import "proto_ktv_ai_host_webapp_GetKtvAiHostListRsp.h"
#import "proto_ktv_ai_host_webapp_AiHostInfo.h"
#import "proto_ktv_ai_host_webapp_KtvAiHostListRightBizInfo.h"

@implementation proto_ktv_ai_host_webapp_GetKtvAiHostListRsp

@synthesize JV2_PROP_NM(o,0,bCanOpenAiHost);
@synthesize JV2_PROP_NM(o,1,bOpenAiHost);
@synthesize JV2_PROP_EX(o,2,vctAiHostInfo,VOproto_ktv_ai_host_webapp_AiHostInfo);
@synthesize JV2_PROP_NM(o,3,stCurAiHost);
@synthesize JV2_PROP_NM(o,4,stRightBizInfo);

+ (void)initialize
{
    if (self == [proto_ktv_ai_host_webapp_GetKtvAiHostListRsp class]) {
        [proto_ktv_ai_host_webapp_AiHostInfo initialize];
        [proto_ktv_ai_host_webapp_KtvAiHostListRightBizInfo initialize];
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(bCanOpenAiHost) = NO;
        JV2_PROP(bOpenAiHost) = NO;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_ktv_ai_host_webapp.GetKtvAiHostListRsp";
}

@end
