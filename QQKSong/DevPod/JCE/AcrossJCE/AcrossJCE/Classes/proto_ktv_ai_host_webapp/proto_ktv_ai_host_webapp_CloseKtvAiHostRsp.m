// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_ktv_ai_host_webapp.jce   
// **********************************************************************

#import "proto_ktv_ai_host_webapp_CloseKtvAiHostRsp.h"

@implementation proto_ktv_ai_host_webapp_CloseKtvAiHostRsp

@synthesize JV2_PROP_NM(o,0,strMsg);

+ (void)initialize
{
    if (self == [proto_ktv_ai_host_webapp_CloseKtvAiHostRsp class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strMsg) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_ktv_ai_host_webapp.CloseKtvAiHostRsp";
}

@end
