// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_ktv_ai_host_webapp.jce   
// **********************************************************************

#import "proto_ktv_ai_host_webapp_SegmentInfo.h"

@implementation proto_ktv_ai_host_webapp_SegmentInfo

@synthesize JV2_PROP_NM(o,0,strTitle);
@synthesize JV2_PROP_NM(o,1,strContent);

+ (void)initialize
{
    if (self == [proto_ktv_ai_host_webapp_SegmentInfo class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strTitle) = DefaultTarsString;
        JV2_PROP(strContent) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_ktv_ai_host_webapp.SegmentInfo";
}

@end
