// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_ktv_ai_host_webapp.jce
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>
#import "proto_ktv_ai_host_webapp_RightType.h"

@interface proto_ktv_ai_host_webapp_KtvAiHostListRightBizInfo : JceObjectV2

@property (nonatomic, assign, JV2_PROP_GS_V2(emCurRightType,setEmCurRightType:)) proto_ktv_ai_host_webapp_RightType JV2_PROP_NM(o,0,emCurRightType);
@property (nonatomic, retain, JV2_PROP_GS_V2(strInviteListIcon,setStrInviteListIcon:)) NSString* JV2_PROP_NM(o,1,strInviteListIcon);
@property (nonatomic, retain, JV2_PROP_GS_V2(strInviteListText,setStrInviteListText:)) NSString* JV2_PROP_NM(o,2,strInviteListText);
@property (nonatomic, retain, JV2_PROP_GS_V2(strInviteListBtnText,setStrInviteListBtnText:)) NSString* JV2_PROP_NM(o,3,strInviteListBtnText);
@property (nonatomic, retain, JV2_PROP_GS_V2(strCandidateListIcon,setStrCandidateListIcon:)) NSString* JV2_PROP_NM(o,4,strCandidateListIcon);
@property (nonatomic, retain, JV2_PROP_GS_V2(strCandidateListText,setStrCandidateListText:)) NSString* JV2_PROP_NM(o,5,strCandidateListText);
@property (nonatomic, retain, JV2_PROP_GS_V2(strCandidateListBtnText,setStrCandidateListBtnText:)) NSString* JV2_PROP_NM(o,6,strCandidateListBtnText);
@property (nonatomic, assign, JV2_PROP_GS_V2(bDisplayRightInfo,setBDisplayRightInfo:)) TarsBool JV2_PROP_NM(o,7,bDisplayRightInfo);

@end
