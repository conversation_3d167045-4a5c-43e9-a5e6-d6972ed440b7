// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_ktv_ai_host_webapp.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>

@interface proto_ktv_ai_host_webapp_UserInfo : JceObjectV2

@property (nonatomic, assign, JV2_PROP_GS_V2(lUid,setLUid:)) TarsInt64 JV2_PROP_NM(o,0,lUid);
@property (nonatomic, assign, JV2_PROP_GS_V2(lTimestamp,setLTimestamp:)) TarsInt64 JV2_PROP_NM(o,1,lTimestamp);
@property (nonatomic, retain, JV2_PROP_GS_V2(strNick,setStrNick:)) NSString* JV2_PROP_NM(o,2,strNick);

@end
