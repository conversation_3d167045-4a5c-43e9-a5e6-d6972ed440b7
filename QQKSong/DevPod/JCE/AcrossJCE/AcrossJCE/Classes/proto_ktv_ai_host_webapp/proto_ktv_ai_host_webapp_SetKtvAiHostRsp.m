// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_ktv_ai_host_webapp.jce   
// **********************************************************************

#import "proto_ktv_ai_host_webapp_SetKtvAiHostRsp.h"

@implementation proto_ktv_ai_host_webapp_SetKtvAiHostRsp

@synthesize JV2_PROP_NM(o,0,strMsg);

+ (void)initialize
{
    if (self == [proto_ktv_ai_host_webapp_SetKtvAiHostRsp class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strMsg) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_ktv_ai_host_webapp.SetKtvAiHostRsp";
}

@end
