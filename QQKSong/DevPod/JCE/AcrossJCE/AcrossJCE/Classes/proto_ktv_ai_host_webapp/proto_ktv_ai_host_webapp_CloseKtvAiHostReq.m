// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_ktv_ai_host_webapp.jce   
// **********************************************************************

#import "proto_ktv_ai_host_webapp_CloseKtvAiHostReq.h"

@implementation proto_ktv_ai_host_webapp_CloseKtvAiHostReq

@synthesize JV2_PROP_NM(o,0,strRoomId);
@synthesize JV2_PROP_NM(o,1,strShowId);

+ (void)initialize
{
    if (self == [proto_ktv_ai_host_webapp_CloseKtvAiHostReq class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strRoomId) = DefaultTarsString;
        JV2_PROP(strShowId) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_ktv_ai_host_webapp.CloseKtvAiHostReq";
}

@end
