// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_ktv_ai_host_webapp.jce   
// **********************************************************************

#import "proto_ktv_ai_host_webapp_UserInfo.h"

@implementation proto_ktv_ai_host_webapp_UserInfo

@synthesize JV2_PROP_NM(o,0,lUid);
@synthesize JV2_PROP_NM(o,1,lTimestamp);
@synthesize JV2_PROP_NM(o,2,strNick);

+ (void)initialize
{
    if (self == [proto_ktv_ai_host_webapp_UserInfo class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strNick) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_ktv_ai_host_webapp.UserInfo";
}

@end
