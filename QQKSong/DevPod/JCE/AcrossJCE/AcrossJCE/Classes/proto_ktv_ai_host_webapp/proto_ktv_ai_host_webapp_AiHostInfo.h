// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_ktv_ai_host_webapp.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>
@class proto_ktv_ai_host_webapp_AiHostRightInfo;
@class proto_ktv_ai_host_webapp_SegmentInfo;

@interface proto_ktv_ai_host_webapp_AiHostInfo : JceObjectV2

@property (nonatomic, assign, JV2_PROP_GS_V2(iCharacterId,setICharacterId:)) TarsInt32 JV2_PROP_NM(o,0,iCharacterId);
@property (nonatomic, assign, JV2_PROP_GS_V2(lUid,setLUid:)) TarsInt64 JV2_PROP_NM(o,1,lUid);
@property (nonatomic, assign, JV2_PROP_GS_V2(lTimestamp,setLTimestamp:)) TarsInt64 JV2_PROP_NM(o,2,lTimestamp);
@property (nonatomic, retain, JV2_PROP_GS_V2(strNick,setStrNick:)) NSString* JV2_PROP_NM(o,3,strNick);
@property (nonatomic, retain, JV2_PROP_GS_V2(strAudioUrl,setStrAudioUrl:)) NSString* JV2_PROP_NM(o,4,strAudioUrl);
@property (nonatomic, retain, JV2_PROP_GS_V2(vctCharacterTags,setVctCharacterTags:)) NSArray* JV2_PROP_EX(o,5,vctCharacterTags,VONSString);
@property (nonatomic, retain, JV2_PROP_GS_V2(strIntroduction,setStrIntroduction:)) NSString* JV2_PROP_NM(o,6,strIntroduction);
@property (nonatomic, retain, JV2_PROP_GS_V2(vctSegments,setVctSegments:)) NSArray* JV2_PROP_EX(o,7,vctSegments,VOproto_ktv_ai_host_webapp_SegmentInfo);
@property (nonatomic, retain, JV2_PROP_GS_V2(stRightInfo,setStRightInfo:)) proto_ktv_ai_host_webapp_AiHostRightInfo* JV2_PROP_NM(o,8,stRightInfo);

@end
