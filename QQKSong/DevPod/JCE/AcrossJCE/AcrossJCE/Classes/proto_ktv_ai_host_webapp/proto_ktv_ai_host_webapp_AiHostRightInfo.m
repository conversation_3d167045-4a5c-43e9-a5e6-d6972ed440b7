// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_ktv_ai_host_webapp.jce   
// **********************************************************************

#import "proto_ktv_ai_host_webapp_AiHostRightInfo.h"

@implementation proto_ktv_ai_host_webapp_AiHostRightInfo

@synthesize JV2_PROP_NM(o,0,emRightType);
@synthesize JV2_PROP_NM(o,1,strRightIcon);

+ (void)initialize
{
    if (self == [proto_ktv_ai_host_webapp_AiHostRightInfo class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strRightIcon) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_ktv_ai_host_webapp.AiHostRightInfo";
}

@end
