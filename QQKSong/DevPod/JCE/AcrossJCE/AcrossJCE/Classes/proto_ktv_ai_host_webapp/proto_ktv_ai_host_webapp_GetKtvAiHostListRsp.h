// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_ktv_ai_host_webapp.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>
@class proto_ktv_ai_host_webapp_AiHostInfo;
@class proto_ktv_ai_host_webapp_KtvAiHostListRightBizInfo;

@interface proto_ktv_ai_host_webapp_GetKtvAiHostListRsp : JceObjectV2

@property (nonatomic, assign, JV2_PROP_GS_V2(bCanOpenAiHost,setBCanOpenAiHost:)) TarsBool JV2_PROP_NM(o,0,bCanOpenAiHost);
@property (nonatomic, assign, JV2_PROP_GS_V2(bOpenAiHost,setBOpenAiHost:)) TarsBool JV2_PROP_NM(o,1,bOpenAiHost);
@property (nonatomic, retain, JV2_PROP_GS_V2(vctAiHostInfo,setVctAiHostInfo:)) NSArray* JV2_PROP_EX(o,2,vctAiHostInfo,VOproto_ktv_ai_host_webapp_AiHostInfo);
@property (nonatomic, retain, JV2_PROP_GS_V2(stCurAiHost,setStCurAiHost:)) proto_ktv_ai_host_webapp_AiHostInfo* JV2_PROP_NM(o,3,stCurAiHost);
@property (nonatomic, retain, JV2_PROP_GS_V2(stRightBizInfo,setStRightBizInfo:)) proto_ktv_ai_host_webapp_KtvAiHostListRightBizInfo* JV2_PROP_NM(o,4,stRightBizInfo);

@end
