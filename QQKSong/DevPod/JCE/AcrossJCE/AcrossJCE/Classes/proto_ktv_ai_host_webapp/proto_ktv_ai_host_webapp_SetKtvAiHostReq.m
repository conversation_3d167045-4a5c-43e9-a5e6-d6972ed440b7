// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_ktv_ai_host_webapp.jce   
// **********************************************************************

#import "proto_ktv_ai_host_webapp_SetKtvAiHostReq.h"

@implementation proto_ktv_ai_host_webapp_SetKtvAiHostReq

@synthesize JV2_PROP_NM(o,0,strRoomId);
@synthesize JV2_PROP_NM(o,1,iCharacterId);

+ (void)initialize
{
    if (self == [proto_ktv_ai_host_webapp_SetKtvAiHostReq class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strRoomId) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_ktv_ai_host_webapp.SetKtvAiHostReq";
}

@end
