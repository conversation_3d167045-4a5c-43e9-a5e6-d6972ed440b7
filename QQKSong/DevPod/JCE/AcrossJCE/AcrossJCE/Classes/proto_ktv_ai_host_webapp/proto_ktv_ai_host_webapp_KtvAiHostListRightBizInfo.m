// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_ktv_ai_host_webapp.jce
// **********************************************************************

#import "proto_ktv_ai_host_webapp_KtvAiHostListRightBizInfo.h"

@implementation proto_ktv_ai_host_webapp_KtvAiHostListRightBizInfo

@synthesize JV2_PROP_NM(o,0,emCurRightType);
@synthesize JV2_PROP_NM(o,1,strInviteListIcon);
@synthesize JV2_PROP_NM(o,2,strInviteListText);
@synthesize JV2_PROP_NM(o,3,strInviteListBtnText);
@synthesize JV2_PROP_NM(o,4,strCandidateListIcon);
@synthesize JV2_PROP_NM(o,5,strCandidateListText);
@synthesize JV2_PROP_NM(o,6,strCandidateListBtnText);
@synthesize JV2_PROP_NM(o,7,bDisplayRightInfo);

+ (void)initialize
{
    if (self == [proto_ktv_ai_host_webapp_KtvAiHostListRightBizInfo class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strInviteListIcon) = DefaultTarsString;
        JV2_PROP(strInviteListText) = DefaultTarsString;
        JV2_PROP(strInviteListBtnText) = DefaultTarsString;
        JV2_PROP(strCandidateListIcon) = DefaultTarsString;
        JV2_PROP(strCandidateListText) = DefaultTarsString;
        JV2_PROP(strCandidateListBtnText) = DefaultTarsString;
        JV2_PROP(bDisplayRightInfo) = NO;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_ktv_ai_host_webapp.KtvAiHostListRightBizInfo";
}

@end
