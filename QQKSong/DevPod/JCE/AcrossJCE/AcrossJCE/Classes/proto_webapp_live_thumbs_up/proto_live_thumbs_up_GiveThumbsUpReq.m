// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_webapp_live_thumbs_up.jce   
// **********************************************************************

#import "proto_live_thumbs_up_GiveThumbsUpReq.h"

@implementation proto_live_thumbs_up_GiveThumbsUpReq

@synthesize JV2_PROP_NM(o,0,uAppId);
@synthesize JV2_PROP_NM(o,1,lAnchorId);
@synthesize JV2_PROP_NM(o,2,strRoomId);
@synthesize JV2_PROP_NM(o,3,strShowId);
@synthesize JV2_PROP_NM(o,4,lUserId);
@synthesize JV2_PROP_NM(o,5,uCnt);

+ (void)initialize
{
    if (self == [proto_live_thumbs_up_GiveThumbsUpReq class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strRoomId) = DefaultTarsString;
        JV2_PROP(strShowId) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_live_thumbs_up.GiveThumbsUpReq";
}

@end
