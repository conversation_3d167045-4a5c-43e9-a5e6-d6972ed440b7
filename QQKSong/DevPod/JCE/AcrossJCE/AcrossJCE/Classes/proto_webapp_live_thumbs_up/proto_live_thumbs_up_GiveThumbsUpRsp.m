// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_webapp_live_thumbs_up.jce   
// **********************************************************************

#import "proto_live_thumbs_up_GiveThumbsUpRsp.h"

@implementation proto_live_thumbs_up_GiveThumbsUpRsp

@synthesize JV2_PROP_NM(o,0,iRet);

+ (void)initialize
{
    if (self == [proto_live_thumbs_up_GiveThumbsUpRsp class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_live_thumbs_up.GiveThumbsUpRsp";
}

@end
