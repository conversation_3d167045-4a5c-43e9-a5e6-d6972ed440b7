// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_webapp_live_thumbs_up.jce   
// **********************************************************************

#import "JceObjectV2.h"

@interface proto_live_thumbs_up_GiveThumbsUpReq : JceObjectV2

@property (nonatomic, assign, JV2_PROP_GS_V2(uAppId,setUAppId:)) TarsUInt32 JV2_PROP_NM(o,0,uAppId);
@property (nonatomic, assign, JV2_PROP_GS_V2(lAnchorId,setLAnchorId:)) TarsInt64 JV2_PROP_NM(o,1,lAnchorId);
@property (nonatomic, retain, JV2_PROP_GS_V2(strRoomId,setStrRoomId:)) NSString* JV2_PROP_NM(o,2,strRoomId);
@property (nonatomic, retain, JV2_PROP_GS_V2(strShowId,setStrShowId:)) NSString* JV2_PROP_NM(o,3,strShowId);
@property (nonatomic, assign, JV2_PROP_GS_V2(lUserId,setLUserId:)) TarsInt64 JV2_PROP_NM(o,4,lUserId);
@property (nonatomic, assign, JV2_PROP_GS_V2(uCnt,setUCnt:)) TarsUInt32 JV2_PROP_NM(o,5,uCnt);

@end
