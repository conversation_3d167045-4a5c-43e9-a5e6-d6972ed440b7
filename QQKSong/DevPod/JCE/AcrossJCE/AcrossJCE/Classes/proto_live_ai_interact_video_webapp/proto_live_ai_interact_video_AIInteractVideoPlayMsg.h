// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_live_ai_interact_video_webapp.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>
@class proto_live_ai_interact_video_AIInteractVideo;

@interface proto_live_ai_interact_video_AIInteractVideoPlayMsg : JceObjectV2

@property (nonatomic, retain, JV2_PROP_GS_V2(stPlay,setStPlay:)) proto_live_ai_interact_video_AIInteractVideo* JV2_PROP_NM(o,0,stPlay);
@property (nonatomic, assign, JV2_PROP_GS_V2(llTriggerTs,setLlTriggerTs:)) TarsInt64 JV2_PROP_NM(o,1,llTriggerTs);
@property (nonatomic, retain, JV2_PROP_GS_V2(strMikeId,setStrMikeId:)) NSString* JV2_PROP_NM(o,2,strMikeId);
@property (nonatomic, retain, JV2_PROP_GS_V2(strSequence,setStrSequence:)) NSString* JV2_PROP_NM(o,3,strSequence);

@end
