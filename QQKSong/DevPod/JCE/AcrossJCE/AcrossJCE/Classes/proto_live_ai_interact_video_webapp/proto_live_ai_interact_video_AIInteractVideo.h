// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_live_ai_interact_video_webapp.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>
#import "proto_live_ai_interact_video_emAIInteractTriggerAction.h"

@interface proto_live_ai_interact_video_AIInteractVideo : JceObjectV2

@property (nonatomic, retain, JV2_PROP_GS_V2(strResourceUrl,setStrResourceUrl:)) NSString* JV2_PROP_NM(o,0,strResourceUrl);
@property (nonatomic, assign, JV2_PROP_GS_V2(uPlayTs,setUPlayTs:)) TarsUInt32 JV2_PROP_NM(o,1,uPlayTs);
@property (nonatomic, retain, JV2_PROP_GS_V2(strVideoId,setStrVideoId:)) NSString* JV2_PROP_NM(o,2,strVideoId);
@property (nonatomic, assign, JV2_PROP_GS_V2(iAction,setIAction:)) proto_live_ai_interact_video_emAIInteractTriggerAction JV2_PROP_NM(o,3,iAction);
@property (nonatomic, retain, JV2_PROP_GS_V2(strTriggerId,setStrTriggerId:)) NSString* JV2_PROP_NM(o,4,strTriggerId);
@property (nonatomic, retain, JV2_PROP_GS_V2(strTriggerAnchorId,setStrTriggerAnchorId:)) NSString* JV2_PROP_NM(o,5,strTriggerAnchorId);

@end
