// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_live_ai_interact_video_webapp.jce   
// **********************************************************************

#import "proto_live_ai_interact_video_AIInteractVideo.h"

@implementation proto_live_ai_interact_video_AIInteractVideo

@synthesize JV2_PROP_NM(o,0,strResourceUrl);
@synthesize JV2_PROP_NM(o,1,uPlayTs);
@synthesize JV2_PROP_NM(o,2,strVideoId);
@synthesize JV2_PROP_NM(o,3,iAction);
@synthesize JV2_PROP_NM(o,4,strTriggerId);
@synthesize JV2_PROP_NM(o,5,strTriggerAnchorId);

+ (void)initialize
{
    if (self == [proto_live_ai_interact_video_AIInteractVideo class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strResourceUrl) = DefaultTarsString;
        JV2_PROP(strVideoId) = DefaultTarsString;
        JV2_PROP(strTriggerId) = DefaultTarsString;
        JV2_PROP(strTriggerAnchorId) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_live_ai_interact_video.AIInteractVideo";
}

@end
