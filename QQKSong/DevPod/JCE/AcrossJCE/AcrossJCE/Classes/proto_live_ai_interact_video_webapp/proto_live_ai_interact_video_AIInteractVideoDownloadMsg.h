// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_live_ai_interact_video_webapp.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>
@class proto_live_ai_interact_video_AIInteractVideo;

@interface proto_live_ai_interact_video_AIInteractVideoDownloadMsg : JceObjectV2

@property (nonatomic, retain, JV2_PROP_GS_V2(vctResources,setVctResources:)) NSArray* JV2_PROP_EX(o,0,vctResources,VOproto_live_ai_interact_video_AIInteractVideo);
@property (nonatomic, retain, JV2_PROP_GS_V2(strDownloadUrl,setStrDownloadUrl:)) NSString* JV2_PROP_NM(o,1,strDownloadUrl);
@property (nonatomic, retain, JV2_PROP_GS_V2(strSequence,setStrSequence:)) NSString* JV2_PROP_NM(o,2,strSequence);
@property (nonatomic, retain, JV2_PROP_GS_V2(strMikeId,setStrMikeId:)) NSString* JV2_PROP_NM(o,3,strMikeId);
@property (nonatomic, assign, JV2_PROP_GS_V2(uDelayTs,setUDelayTs:)) TarsUInt32 JV2_PROP_NM(o,4,uDelayTs);

@end
