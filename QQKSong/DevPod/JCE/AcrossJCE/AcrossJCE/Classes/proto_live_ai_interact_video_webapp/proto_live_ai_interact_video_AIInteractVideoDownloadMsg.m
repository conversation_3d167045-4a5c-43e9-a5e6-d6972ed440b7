// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_live_ai_interact_video_webapp.jce   
// **********************************************************************

#import "proto_live_ai_interact_video_AIInteractVideoDownloadMsg.h"
#import "proto_live_ai_interact_video_AIInteractVideo.h"

@implementation proto_live_ai_interact_video_AIInteractVideoDownloadMsg

@synthesize JV2_PROP_EX(o,0,vctResources,VOproto_live_ai_interact_video_AIInteractVideo);
@synthesize JV2_PROP_NM(o,1,strDownloadUrl);
@synthesize JV2_PROP_NM(o,2,strSequence);
@synthesize JV2_PROP_NM(o,3,strMikeId);
@synthesize JV2_PROP_NM(o,4,uDelayTs);

+ (void)initialize
{
    if (self == [proto_live_ai_interact_video_AIInteractVideoDownloadMsg class]) {
        [proto_live_ai_interact_video_AIInteractVideo initialize];
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strDownloadUrl) = DefaultTarsString;
        JV2_PROP(strSequence) = DefaultTarsString;
        JV2_PROP(strMikeId) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_live_ai_interact_video.AIInteractVideoDownloadMsg";
}

@end
