// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_live_ai_interact_video_webapp.jce   
// **********************************************************************

#import "proto_live_ai_interact_video_AIInteractVideoPlayMsg.h"
#import "proto_live_ai_interact_video_AIInteractVideo.h"

@implementation proto_live_ai_interact_video_AIInteractVideoPlayMsg

@synthesize JV2_PROP_NM(o,0,stPlay);
@synthesize JV2_PROP_NM(o,1,llTriggerTs);
@synthesize JV2_PROP_NM(o,2,strMikeId);
@synthesize JV2_PROP_NM(o,3,strSequence);

+ (void)initialize
{
    if (self == [proto_live_ai_interact_video_AIInteractVideoPlayMsg class]) {
        [proto_live_ai_interact_video_AIInteractVideo initialize];
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strMikeId) = DefaultTarsString;
        JV2_PROP(strSequence) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_live_ai_interact_video.AIInteractVideoPlayMsg";
}

@end
