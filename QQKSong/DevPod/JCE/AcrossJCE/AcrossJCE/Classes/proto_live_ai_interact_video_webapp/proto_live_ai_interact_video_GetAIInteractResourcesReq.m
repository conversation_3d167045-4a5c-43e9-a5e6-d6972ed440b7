// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_live_ai_interact_video_webapp.jce   
// **********************************************************************

#import "proto_live_ai_interact_video_GetAIInteractResourcesReq.h"

@implementation proto_live_ai_interact_video_GetAIInteractResourcesReq

@synthesize JV2_PROP_NM(o,0,uAppId);
@synthesize JV2_PROP_NM(o,1,strAnchorId);
@synthesize JV2_PROP_NM(o,2,strMikeId);

+ (void)initialize
{
    if (self == [proto_live_ai_interact_video_GetAIInteractResourcesReq class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strAnchorId) = DefaultTarsString;
        JV2_PROP(strMikeId) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_live_ai_interact_video.GetAIInteractResourcesReq";
}

@end
