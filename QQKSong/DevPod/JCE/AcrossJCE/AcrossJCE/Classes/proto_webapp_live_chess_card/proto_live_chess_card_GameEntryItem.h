// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_webapp_live_chess_card.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>

@interface proto_live_chess_card_GameEntryItem : JceObjectV2

@property (nonatomic, retain, JV2_PROP_GS_V2(strGameId,setStrGameId:)) NSString* JV2_PROP_NM(o,0,strGameId);
@property (nonatomic, retain, JV2_PROP_GS_V2(strGameName,setStrGameName:)) NSString* JV2_PROP_NM(o,1,strGameName);
@property (nonatomic, retain, JV2_PROP_GS_V2(strGameDesc,setStrGameDesc:)) NSString* JV2_PROP_NM(o,2,strGameDesc);
@property (nonatomic, retain, JV2_PROP_GS_V2(strGameIcon,setStrGameIcon:)) NSString* JV2_PROP_NM(o,3,strGameIcon);
@property (nonatomic, retain, JV2_PROP_GS_V2(strJumpUrl,setStrJumpUrl:)) NSString* JV2_PROP_NM(o,4,strJumpUrl);

@end
