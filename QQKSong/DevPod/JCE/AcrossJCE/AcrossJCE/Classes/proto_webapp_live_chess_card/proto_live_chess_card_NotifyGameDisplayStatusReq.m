// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_webapp_live_chess_card.jce   
// **********************************************************************

#import "proto_live_chess_card_NotifyGameDisplayStatusReq.h"

@implementation proto_live_chess_card_NotifyGameDisplayStatusReq

@synthesize JV2_PROP_NM(o,0,uAppId);
@synthesize JV2_PROP_NM(o,1,lAnchorId);
@synthesize JV2_PROP_NM(o,2,strRoomId);
@synthesize JV2_PROP_NM(o,3,strShowId);
@synthesize JV2_PROP_NM(o,4,iDisplayStatus);
@synthesize JV2_PROP_NM(o,5,strGameId);

+ (void)initialize
{
    if (self == [proto_live_chess_card_NotifyGameDisplayStatusReq class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strRoomId) = DefaultTarsString;
        JV2_PROP(strShowId) = DefaultTarsString;
        JV2_PROP(strGameId) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_live_chess_card.NotifyGameDisplayStatusReq";
}

@end
