// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_webapp_live_chess_card.jce   
// **********************************************************************

#import "proto_live_chess_card_GetGameEntryListReq.h"

@implementation proto_live_chess_card_GetGameEntryListReq

@synthesize JV2_PROP_NM(o,0,uAppId);
@synthesize JV2_PROP_NM(o,1,lAnchorId);
@synthesize JV2_PROP_NM(o,2,strRoomId);
@synthesize JV2_PROP_NM(o,3,strPassback);
@synthesize JV2_PROP_NM(o,4,strQua);
@synthesize JV2_PROP_NM(o,5,strDeviceInfo);

+ (void)initialize
{
    if (self == [proto_live_chess_card_GetGameEntryListReq class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strRoomId) = DefaultTarsString;
        JV2_PROP(strPassback) = DefaultTarsString;
        JV2_PROP(strQua) = DefaultTarsString;
        JV2_PROP(strDeviceInfo) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_live_chess_card.GetGameEntryListReq";
}

@end
