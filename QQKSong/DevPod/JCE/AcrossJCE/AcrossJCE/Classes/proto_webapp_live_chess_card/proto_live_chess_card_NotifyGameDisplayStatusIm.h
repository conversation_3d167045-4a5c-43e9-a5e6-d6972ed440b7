// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_webapp_live_chess_card.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>

@interface proto_live_chess_card_NotifyGameDisplayStatusIm : JceObjectV2

@property (nonatomic, assign, JV2_PROP_GS_V2(iDisplayStatus,setIDisplayStatus:)) TarsInt32 JV2_PROP_NM(o,0,iDisplayStatus);
@property (nonatomic, retain, JV2_PROP_GS_V2(strGameId,setStrGameId:)) NSString* JV2_PROP_NM(o,1,strGameId);

@end
