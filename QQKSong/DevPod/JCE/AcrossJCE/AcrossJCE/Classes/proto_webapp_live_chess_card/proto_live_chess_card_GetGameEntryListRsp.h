// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_webapp_live_chess_card.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>
@class proto_live_chess_card_GameEntryItem;

@interface proto_live_chess_card_GetGameEntryListRsp : JceObjectV2

@property (nonatomic, retain, JV2_PROP_GS_V2(vctGameEntryList,setVctGameEntryList:)) NSArray* JV2_PROP_EX(o,0,vctG<PERSON><PERSON><PERSON>ryList,VOproto_live_chess_card_GameEntryItem);
@property (nonatomic, assign, JV2_PROP_GS_V2(iHasMore,setIHasMore:)) TarsUInt32 JV2_PROP_NM(o,1,iHasMore);
@property (nonatomic, retain, JV2_PROP_GS_V2(strPassback,setStrPassback:)) NSString* JV2_PROP_NM(o,2,strPassback);

@end
