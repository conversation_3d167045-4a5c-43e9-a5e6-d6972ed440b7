// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_webapp_live_chess_card.jce   
// **********************************************************************

#import "proto_live_chess_card_NotifyGameDisplayStatusRsp.h"

@implementation proto_live_chess_card_NotifyGameDisplayStatusRsp

@synthesize JV2_PROP_NM(o,0,iCode);
@synthesize JV2_PROP_NM(o,1,strMsg);

+ (void)initialize
{
    if (self == [proto_live_chess_card_NotifyGameDisplayStatusRsp class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strMsg) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_live_chess_card.NotifyGameDisplayStatusRsp";
}

@end
