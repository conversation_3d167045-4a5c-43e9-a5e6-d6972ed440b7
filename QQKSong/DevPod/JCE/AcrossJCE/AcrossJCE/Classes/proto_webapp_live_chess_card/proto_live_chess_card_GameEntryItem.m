// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_webapp_live_chess_card.jce   
// **********************************************************************

#import "proto_live_chess_card_GameEntryItem.h"

@implementation proto_live_chess_card_GameEntryItem

@synthesize JV2_PROP_NM(o,0,strGameId);
@synthesize JV2_PROP_NM(o,1,strGameName);
@synthesize JV2_PROP_NM(o,2,strGameDesc);
@synthesize JV2_PROP_NM(o,3,strGameIcon);
@synthesize JV2_PROP_NM(o,4,strJumpUrl);

+ (void)initialize
{
    if (self == [proto_live_chess_card_GameEntryItem class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strGameId) = DefaultTarsString;
        JV2_PROP(strGameName) = DefaultTarsString;
        JV2_PROP(strGameDesc) = DefaultTarsString;
        JV2_PROP(strGameIcon) = DefaultTarsString;
        JV2_PROP(strJumpUrl) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_live_chess_card.GameEntryItem";
}

@end
