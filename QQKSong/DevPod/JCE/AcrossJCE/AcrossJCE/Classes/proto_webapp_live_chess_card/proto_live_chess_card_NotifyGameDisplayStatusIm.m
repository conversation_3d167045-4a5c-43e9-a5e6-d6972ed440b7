// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_webapp_live_chess_card.jce   
// **********************************************************************

#import "proto_live_chess_card_NotifyGameDisplayStatusIm.h"

@implementation proto_live_chess_card_NotifyGameDisplayStatusIm

@synthesize JV2_PROP_NM(o,0,iDisplayStatus);
@synthesize JV2_PROP_NM(o,1,strGameId);

+ (void)initialize
{
    if (self == [proto_live_chess_card_NotifyGameDisplayStatusIm class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strGameId) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_live_chess_card.NotifyGameDisplayStatusIm";
}

@end
