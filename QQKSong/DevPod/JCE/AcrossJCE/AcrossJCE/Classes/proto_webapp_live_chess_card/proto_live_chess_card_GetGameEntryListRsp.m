// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_webapp_live_chess_card.jce   
// **********************************************************************

#import "proto_live_chess_card_GetGameEntryListRsp.h"
#import "proto_live_chess_card_GameEntryItem.h"

@implementation proto_live_chess_card_GetGameEntryListRsp

@synthesize JV2_PROP_EX(o,0,vctGameEntryList,VOproto_live_chess_card_GameEntryItem);
@synthesize JV2_PROP_NM(o,1,iHasMore);
@synthesize JV2_PROP_NM(o,2,strPassback);

+ (void)initialize
{
    if (self == [proto_live_chess_card_GetGameEntryListRsp class]) {
        [proto_live_chess_card_GameEntryItem initialize];
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strPassback) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_live_chess_card.GetGameEntryListRsp";
}

@end
