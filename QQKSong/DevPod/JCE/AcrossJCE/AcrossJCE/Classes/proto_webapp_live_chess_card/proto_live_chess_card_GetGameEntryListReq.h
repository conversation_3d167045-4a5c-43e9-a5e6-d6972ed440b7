// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_webapp_live_chess_card.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>

@interface proto_live_chess_card_GetGameEntryListReq : JceObjectV2

@property (nonatomic, assign, JV2_PROP_GS_V2(uAppId,setUAppId:)) TarsUInt32 JV2_PROP_NM(o,0,uAppId);
@property (nonatomic, assign, JV2_PROP_GS_V2(lAnchorId,setLAnchorId:)) TarsInt64 JV2_PROP_NM(o,1,lAnchorId);
@property (nonatomic, retain, JV2_PROP_GS_V2(strRoomId,setStrRoomId:)) NSString* JV2_PROP_NM(o,2,strRoomId);
@property (nonatomic, retain, JV2_PROP_GS_V2(strPassback,setStrPassback:)) NSString* JV2_PROP_NM(o,3,strPassback);
@property (nonatomic, retain, JV2_PROP_GS_V2(strQua,setStrQua:)) NSString* JV2_PROP_NM(o,4,strQua);
@property (nonatomic, retain, JV2_PROP_GS_V2(strDeviceInfo,setStrDeviceInfo:)) NSString* JV2_PROP_NM(o,5,strDeviceInfo);

@end
