// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_webapp_live_chess_card.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>

#define proto_webapp_live_chess_card_LIVE_CHESS_CARD_WEBAPP_MOD_ID ((TarsUInt64)0)

#define proto_webapp_live_chess_card_CMD_LIVE_CHESS_CARD_WEBAPP_GET_GAME_ENTRY_LIST @"interact.live_chess_card.webapp.get_game_entry_list"

#define proto_webapp_live_chess_card_CMD_LIVE_CHESS_CARD_WEBAPP_NOTIFY_GAME_DISPLAY_STATUS @"interact.live_chess_card.webapp.notify_game_display_status"

