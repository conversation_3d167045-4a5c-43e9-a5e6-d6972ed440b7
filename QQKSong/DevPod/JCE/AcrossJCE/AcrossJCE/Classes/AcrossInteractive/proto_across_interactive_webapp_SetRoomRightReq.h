// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_across_interactive_webapp.jce   
// **********************************************************************

#import "JceObjectV2.h"

@interface proto_across_interactive_webapp_SetRoomRightReq : JceObjectV2

@property (nonatomic, assign, JV2_PROP_GS_V2(lAnchorId,setLAnchorId:)) TarsInt64 JV2_PROP_NM(o,0,lAnchorId);
@property (nonatomic, assign, JV2_PROP_GS_V2(lOptUid,setLOptUid:)) TarsInt64 JV2_PROP_NM(o,1,lOptUid);
@property (nonatomic, retain, JV2_PROP_GS_V2(strRoomId,setStrRoomId:)) NSString* JV2_PROP_NM(o,2,strRoomId);
@property (nonatomic, retain, JV2_PROP_GS_V2(strShowId,setStrShowId:)) NSString* JV2_PROP_NM(o,3,strShowId);
@property (nonatomic, assign, JV2_PROP_GS_V2(uOptAppId,setUOptAppId:)) TarsUInt32 JV2_PROP_NM(o,4,uOptAppId);
@property (nonatomic, assign, JV2_PROP_GS_V2(lRightMask,setLRightMask:)) TarsInt64 JV2_PROP_NM(o,5,lRightMask);
@property (nonatomic, assign, JV2_PROP_GS_V2(uOptType,setUOptType:)) TarsUInt32 JV2_PROP_NM(o,6,uOptType);
@property (nonatomic, assign, JV2_PROP_GS_V2(lToUid,setLToUid:)) TarsInt64 JV2_PROP_NM(o,7,lToUid);
@property (nonatomic, assign, JV2_PROP_GS_V2(uToAppId,setUToAppId:)) TarsUInt32 JV2_PROP_NM(o,8,uToAppId);

@end
