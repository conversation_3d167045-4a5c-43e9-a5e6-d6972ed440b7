// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_across_interactive_webapp.jce   
// **********************************************************************

#import "proto_across_interactive_webapp_RoomKickOutRsp.h"

@implementation proto_across_interactive_webapp_RoomKickOutRsp

@synthesize JV2_PROP_NM(o,0,strMsg);

+ (void)initialize
{
    if (self == [proto_across_interactive_webapp_RoomKickOutRsp class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strMsg) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_across_interactive_webapp.RoomKickOutRsp";
}

@end
