// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_across_interactive_comm.jce   
// **********************************************************************

#import "JceObjectV2.h"
#import "proto_across_interactive_comm_AcrossInteractiveOptType.h"

#if TARSV2_ENUM_ETOS_AND_STOE_SUPPORTED

@implementation proto_across_interactive_comm_AcrossInteractiveOptTypeHelper

+ (NSString *)etos:(proto_across_interactive_comm_AcrossInteractiveOptType)e
{
    switch(e){
        case proto_across_interactive_comm_AcrossInteractiveOptType_ENUM_OPT_TYPE_ADD: return @"proto_across_interactive_comm_AcrossInteractiveOptType_ENUM_OPT_TYPE_ADD";
        case proto_across_interactive_comm_AcrossInteractiveOptType_ENUM_OPT_TYPE_SUB: return @"proto_across_interactive_comm_AcrossInteractiveOptType_ENUM_OPT_TYPE_SUB";
        default: return @"";
    }
}

+ (proto_across_interactive_comm_AcrossInteractiveOptType)stoe:(NSString *)s
{
    if(isTarsEnumStringEqual(s, @"proto_across_interactive_comm_AcrossInteractiveOptType_ENUM_OPT_TYPE_ADD")) return proto_across_interactive_comm_AcrossInteractiveOptType_ENUM_OPT_TYPE_ADD;
    if(isTarsEnumStringEqual(s, @"proto_across_interactive_comm_AcrossInteractiveOptType_ENUM_OPT_TYPE_SUB")) return proto_across_interactive_comm_AcrossInteractiveOptType_ENUM_OPT_TYPE_SUB;
    return INT32_MIN;
}

@end

#endif
