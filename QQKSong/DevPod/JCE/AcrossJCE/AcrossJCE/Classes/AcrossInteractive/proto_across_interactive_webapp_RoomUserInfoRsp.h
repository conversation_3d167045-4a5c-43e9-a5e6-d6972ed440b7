// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_across_interactive_webapp.jce   
// **********************************************************************

#import "JceObjectV2.h"
#import "proto_across_interactive_comm_UserInfo.h"

@interface proto_across_interactive_webapp_RoomUserInfoRsp : JceObjectV2

@property (nonatomic, retain, JV2_PROP_GS_V2(stUserInfo,setStUserInfo:)) proto_across_interactive_comm_UserInfo* JV2_PROP_NM(o,0,stUserInfo);

@end
