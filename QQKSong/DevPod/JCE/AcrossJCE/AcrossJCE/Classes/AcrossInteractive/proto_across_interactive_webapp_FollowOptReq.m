// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_across_interactive_webapp.jce   
// **********************************************************************

#import "proto_across_interactive_webapp_FollowOptReq.h"

@implementation proto_across_interactive_webapp_FollowOptReq

@synthesize JV2_PROP_NM(o,0,lOptUid);
@synthesize JV2_PROP_NM(o,1,strRoomId);
@synthesize JV2_PROP_NM(o,2,strShowId);
@synthesize JV2_PROP_NM(o,3,uOptAppId);
@synthesize JV2_PROP_NM(o,4,uOptType);
@synthesize JV2_PROP_NM(o,5,lToUid);
@synthesize JV2_PROP_NM(o,6,uToAppId);

+ (void)initialize
{
    if (self == [proto_across_interactive_webapp_FollowOptReq class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strRoomId) = DefaultTarsString;
        JV2_PROP(strShowId) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_across_interactive_webapp.FollowOptReq";
}

@end
