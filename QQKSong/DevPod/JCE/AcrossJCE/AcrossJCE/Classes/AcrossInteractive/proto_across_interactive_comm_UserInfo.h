// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_across_interactive_comm.jce   
// **********************************************************************

#import "JceObjectV2.h"
#import "proto_across_interactive_comm_UserBadInfo.h"
#import "proto_across_interactive_comm_UserKickOutInfo.h"
#import "proto_across_interactive_comm_UserVipInfo.h"

@interface proto_across_interactive_comm_UserInfo : JceObjectV2

@property (nonatomic, assign, JV2_PROP_GS_V2(lUid,setLUid:)) TarsInt64 JV2_PROP_NM(o,0,lUid);
@property (nonatomic, assign, JV2_PROP_GS_V2(uTimestamp,setUTimestamp:)) TarsUInt32 JV2_PROP_NM(o,1,uTimestamp);
@property (nonatomic, retain, JV2_PROP_GS_V2(strLogo,setStrLogo:)) NSString* JV2_PROP_NM(o,2,strLogo);
@property (nonatomic, retain, JV2_PROP_GS_V2(strNick,setStrNick:)) NSString* JV2_PROP_NM(o,3,strNick);
@property (nonatomic, retain, JV2_PROP_GS_V2(mapAuth,setMapAuth:)) NSDictionary* JV2_PROP_EX(o,4,mapAuth,M09ONSNumberONSString);
@property (nonatomic, assign, JV2_PROP_GS_V2(uAppId,setUAppId:)) TarsUInt32 JV2_PROP_NM(o,5,uAppId);
@property (nonatomic, assign, JV2_PROP_GS_V2(uFollow,setUFollow:)) TarsUInt32 JV2_PROP_NM(o,6,uFollow);
@property (nonatomic, assign, JV2_PROP_GS_V2(lRightMask,setLRightMask:)) TarsInt64 JV2_PROP_NM(o,7,lRightMask);
@property (nonatomic, assign, JV2_PROP_GS_V2(uTreasure,setUTreasure:)) TarsUInt32 JV2_PROP_NM(o,8,uTreasure);
@property (nonatomic, retain, JV2_PROP_GS_V2(strBadInfo,setStrBadInfo:)) proto_across_interactive_comm_UserBadInfo* JV2_PROP_NM(o,9,strBadInfo);
@property (nonatomic, retain, JV2_PROP_GS_V2(stKickOutInfo,setStKickOutInfo:)) proto_across_interactive_comm_UserKickOutInfo* JV2_PROP_NM(o,10,stKickOutInfo);
@property (nonatomic, retain, JV2_PROP_GS_V2(stVipInfo,setStVipInfo:)) proto_across_interactive_comm_UserVipInfo* JV2_PROP_NM(o,11,stVipInfo);
@property (nonatomic, retain, JV2_PROP_GS_V2(strPlatformTips,setStrPlatformTips:)) NSString* JV2_PROP_NM(o,12,strPlatformTips);
@property (nonatomic, retain, JV2_PROP_GS_V2(strEncrytUid,setStrEncrytUid:)) NSString* JV2_PROP_NM(o,13,strEncrytUid);

@end
