// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_across_interactive_webapp.jce   
// **********************************************************************

#import "JceObjectV2.h"

@interface proto_across_interactive_webapp_SetRoomRightRsp : JceObjectV2

@property (nonatomic, assign, JV2_PROP_GS_V2(lRightMask,setLRightMask:)) TarsInt64 JV2_PROP_NM(o,0,lRightMask);
@property (nonatomic, assign, JV2_PROP_GS_V2(iRes,setIRes:)) TarsInt32 JV2_PROP_NM(o,1,iRes);
@property (nonatomic, retain, JV2_PROP_GS_V2(strMsg,setStrMsg:)) NSString* JV2_PROP_NM(o,2,strMsg);

@end
