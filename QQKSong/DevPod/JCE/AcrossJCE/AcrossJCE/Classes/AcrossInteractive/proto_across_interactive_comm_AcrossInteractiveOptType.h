// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_across_interactive_comm.jce   
// **********************************************************************

#import "JceObjectV2.h"

enum {
    proto_across_interactive_comm_AcrossInteractiveOptType_ENUM_OPT_TYPE_ADD = 1,
    proto_across_interactive_comm_AcrossInteractiveOptType_ENUM_OPT_TYPE_SUB = 2
};
#define proto_across_interactive_comm_AcrossInteractiveOptType TarsInt32

#if TARSV2_ENUM_ETOS_AND_STOE_SUPPORTED

@interface proto_across_interactive_comm_AcrossInteractiveOptTypeHelper: TarsEnumHelper

+ (NSString *)etos:(proto_across_interactive_comm_AcrossInteractiveOptType)e;
+ (proto_across_interactive_comm_AcrossInteractiveOptType)stoe:(NSString *)s;

@end

#endif
