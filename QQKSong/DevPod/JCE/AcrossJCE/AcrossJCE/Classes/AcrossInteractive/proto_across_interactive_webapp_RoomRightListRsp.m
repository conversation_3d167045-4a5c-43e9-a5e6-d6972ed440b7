// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_across_interactive_webapp.jce   
// **********************************************************************

#import "proto_across_interactive_webapp_RoomRightListRsp.h"

@implementation proto_across_interactive_webapp_RoomRightListRsp

@synthesize JV2_PROP_EX(o,0,vctList,VOproto_across_interactive_comm_UserInfo);
@synthesize JV2_PROP_NM(o,1,bHasMore);
@synthesize JV2_PROP_NM(o,2,strPassBack);

+ (void)initialize
{
    if (self == [proto_across_interactive_webapp_RoomRightListRsp class]) {
        [proto_across_interactive_comm_UserInfo initialize];
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(bHasMore) = NO;
        JV2_PROP(strPassBack) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_across_interactive_webapp.RoomRightListRsp";
}

@end
