// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_across_interactive_comm.jce   
// **********************************************************************

#import "JceObjectV2.h"
#import "proto_across_interactive_comm_AcrossInteractiveRightMask.h"

#if TARSV2_ENUM_ETOS_AND_STOE_SUPPORTED

@implementation proto_across_interactive_comm_AcrossInteractiveRightMaskHelper

+ (NSString *)etos:(proto_across_interactive_comm_AcrossInteractiveRightMask)e
{
    switch(e){
        case proto_across_interactive_comm_AcrossInteractiveRightMask_ENUM_RIGHT_MASK_BAD: return @"proto_across_interactive_comm_AcrossInteractiveRightMask_ENUM_RIGHT_MASK_BAD";
        case proto_across_interactive_comm_AcrossInteractiveRightMask_ENUM_RIGHT_MASK_ADMIN: return @"proto_across_interactive_comm_AcrossInteractiveRightMask_ENUM_RIGHT_MASK_ADMIN";
        case proto_across_interactive_comm_AcrossInteractiveRightMask_ENUM_RIGHT_MASK_KICKOUT: return @"proto_across_interactive_comm_AcrossInteractiveRightMask_ENUM_RIGHT_MASK_KICKOUT";
        default: return @"";
    }
}

+ (proto_across_interactive_comm_AcrossInteractiveRightMask)stoe:(NSString *)s
{
    if(isTarsEnumStringEqual(s, @"proto_across_interactive_comm_AcrossInteractiveRightMask_ENUM_RIGHT_MASK_BAD")) return proto_across_interactive_comm_AcrossInteractiveRightMask_ENUM_RIGHT_MASK_BAD;
    if(isTarsEnumStringEqual(s, @"proto_across_interactive_comm_AcrossInteractiveRightMask_ENUM_RIGHT_MASK_ADMIN")) return proto_across_interactive_comm_AcrossInteractiveRightMask_ENUM_RIGHT_MASK_ADMIN;
    if(isTarsEnumStringEqual(s, @"proto_across_interactive_comm_AcrossInteractiveRightMask_ENUM_RIGHT_MASK_KICKOUT")) return proto_across_interactive_comm_AcrossInteractiveRightMask_ENUM_RIGHT_MASK_KICKOUT;
    return INT32_MIN;
}

@end

#endif
