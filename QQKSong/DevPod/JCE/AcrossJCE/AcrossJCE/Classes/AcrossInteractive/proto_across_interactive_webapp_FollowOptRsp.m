// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_across_interactive_webapp.jce   
// **********************************************************************

#import "proto_across_interactive_webapp_FollowOptRsp.h"

@implementation proto_across_interactive_webapp_FollowOptRsp

@synthesize JV2_PROP_NM(o,0,iRes);
@synthesize JV2_PROP_NM(o,1,strMsg);

+ (void)initialize
{
    if (self == [proto_across_interactive_webapp_FollowOptRsp class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strMsg) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_across_interactive_webapp.FollowOptRsp";
}

@end
