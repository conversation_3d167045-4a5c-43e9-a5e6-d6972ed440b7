// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_across_interactive_comm.jce   
// **********************************************************************

#import "proto_across_interactive_comm_WebappCommData.h"

@implementation proto_across_interactive_comm_WebappCommData

@synthesize JV2_PROP_NM(o,0,strQua);
@synthesize JV2_PROP_NM(o,1,strDeviceInfo);

+ (void)initialize
{
    if (self == [proto_across_interactive_comm_WebappCommData class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strQua) = DefaultTarsString;
        JV2_PROP(strDeviceInfo) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_across_interactive_comm.WebappCommData";
}

@end
