// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_across_interactive_webapp.jce   
// **********************************************************************

#import "proto_across_interactive_webapp_SetRoomRightReq.h"

@implementation proto_across_interactive_webapp_SetRoomRightReq

@synthesize JV2_PROP_NM(o,0,lAnchorId);
@synthesize JV2_PROP_NM(o,1,lOptUid);
@synthesize JV2_PROP_NM(o,2,strRoomId);
@synthesize JV2_PROP_NM(o,3,strShowId);
@synthesize JV2_PROP_NM(o,4,uOptAppId);
@synthesize JV2_PROP_NM(o,5,lRightMask);
@synthesize JV2_PROP_NM(o,6,uOptType);
@synthesize JV2_PROP_NM(o,7,lToUid);
@synthesize JV2_PROP_NM(o,8,uToAppId);

+ (void)initialize
{
    if (self == [proto_across_interactive_webapp_SetRoomRightReq class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strRoomId) = DefaultTarsString;
        JV2_PROP(strShowId) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_across_interactive_webapp.SetRoomRightReq";
}

@end
