// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_across_interactive_comm.jce   
// **********************************************************************

#import "JceObjectV2.h"

enum {
    proto_across_interactive_comm_AcrossInteractiveRightMask_ENUM_RIGHT_MASK_BAD = 1,
    proto_across_interactive_comm_AcrossInteractiveRightMask_ENUM_RIGHT_MASK_ADMIN = 2,
    proto_across_interactive_comm_AcrossInteractiveRightMask_ENUM_RIGHT_MASK_KICKOUT = 4
};
#define proto_across_interactive_comm_AcrossInteractiveRightMask TarsInt32

#if TARSV2_ENUM_ETOS_AND_STOE_SUPPORTED

@interface proto_across_interactive_comm_AcrossInteractiveRightMaskHelper: TarsEnumHelper

+ (NSString *)etos:(proto_across_interactive_comm_AcrossInteractiveRightMask)e;
+ (proto_across_interactive_comm_AcrossInteractiveRightMask)stoe:(NSString *)s;

@end

#endif
