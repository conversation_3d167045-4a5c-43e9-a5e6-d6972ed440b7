// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_across_interactive_comm.jce   
// **********************************************************************

#import "proto_across_interactive_comm_UserBadInfo.h"

@implementation proto_across_interactive_comm_UserBadInfo

@synthesize JV2_PROP_NM(o,0,lOptUid);
@synthesize JV2_PROP_NM(o,1,uOptTs);
@synthesize JV2_PROP_NM(o,2,strBadDetail);

+ (void)initialize
{
    if (self == [proto_across_interactive_comm_UserBadInfo class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strBadDetail) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_across_interactive_comm.UserBadInfo";
}

@end
