// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_across_interactive_webapp.jce   
// **********************************************************************

#import "JceObjectV2.h"
#import "proto_across_interactive_comm_UserInfo.h"

@interface proto_across_interactive_webapp_RoomRightListRsp : JceObjectV2

@property (nonatomic, retain, JV2_PROP_GS_V2(vctList,setVctList:)) NSArray* JV2_PROP_EX(o,0,vctList,VOproto_across_interactive_comm_UserInfo);
@property (nonatomic, assign, JV2_PROP_GS_V2(bHasMore,setBHasMore:)) TarsBool JV2_PROP_NM(o,1,bHasMore);
@property (nonatomic, retain, JV2_PROP_GS_V2(strPassBack,setStrPassBack:)) NSString* JV2_PROP_NM(o,2,strPassBack);

@end
