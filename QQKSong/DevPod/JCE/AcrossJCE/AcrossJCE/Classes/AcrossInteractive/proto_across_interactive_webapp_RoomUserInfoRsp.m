// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_across_interactive_webapp.jce   
// **********************************************************************

#import "proto_across_interactive_webapp_RoomUserInfoRsp.h"

@implementation proto_across_interactive_webapp_RoomUserInfoRsp

@synthesize JV2_PROP_NM(o,0,stUserInfo);

+ (void)initialize
{
    if (self == [proto_across_interactive_webapp_RoomUserInfoRsp class]) {
        [proto_across_interactive_comm_UserInfo initialize];
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_across_interactive_webapp.RoomUserInfoRsp";
}

@end
