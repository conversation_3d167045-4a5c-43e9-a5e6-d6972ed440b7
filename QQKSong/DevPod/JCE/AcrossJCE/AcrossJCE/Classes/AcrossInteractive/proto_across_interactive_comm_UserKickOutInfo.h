// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_across_interactive_comm.jce   
// **********************************************************************

#import "JceObjectV2.h"

@interface proto_across_interactive_comm_UserKickOutInfo : JceObjectV2

@property (nonatomic, assign, JV2_PROP_GS_V2(lOptUid,setLOptUid:)) TarsInt64 JV2_PROP_NM(o,0,lOptUid);
@property (nonatomic, assign, JV2_PROP_GS_V2(uOptTs,setUOptTs:)) TarsUInt32 JV2_PROP_NM(o,1,uOptTs);
@property (nonatomic, retain, JV2_PROP_GS_V2(strKickOutDetail,setStrKickOutDetail:)) NSString* JV2_PROP_NM(o,2,strKickOutDetail);

@end
