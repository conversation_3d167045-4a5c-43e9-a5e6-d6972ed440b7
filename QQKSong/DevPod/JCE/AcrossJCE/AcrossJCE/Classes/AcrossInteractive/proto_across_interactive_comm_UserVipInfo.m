// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_across_interactive_comm.jce   
// **********************************************************************

#import "proto_across_interactive_comm_UserVipInfo.h"

@implementation proto_across_interactive_comm_UserVipInfo

@synthesize JV2_PROP_NM(o,0,uIsVip);
@synthesize JV2_PROP_NM(o,1,uVipLevel);
@synthesize JV2_PROP_NM(o,2,strVipIcon);
@synthesize JV2_PROP_NM(o,3,strBackColor);

+ (void)initialize
{
    if (self == [proto_across_interactive_comm_UserVipInfo class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strVipIcon) = DefaultTarsString;
        JV2_PROP(strBackColor) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_across_interactive_comm.UserVipInfo";
}

@end
