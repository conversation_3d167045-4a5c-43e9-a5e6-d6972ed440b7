// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_across_interactive_webapp.jce   
// **********************************************************************

#import "proto_across_interactive_webapp_SetRoomRightRsp.h"

@implementation proto_across_interactive_webapp_SetRoomRightRsp

@synthesize JV2_PROP_NM(o,0,lRightMask);
@synthesize JV2_PROP_NM(o,1,iRes);
@synthesize JV2_PROP_NM(o,2,strMsg);

+ (void)initialize
{
    if (self == [proto_across_interactive_webapp_SetRoomRightRsp class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strMsg) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_across_interactive_webapp.SetRoomRightRsp";
}

@end
