// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_across_interactive_comm.jce   
// **********************************************************************

#import "proto_across_interactive_comm_UserInfo.h"

@implementation proto_across_interactive_comm_UserInfo

@synthesize JV2_PROP_NM(o,0,lUid);
@synthesize JV2_PROP_NM(o,1,uTimestamp);
@synthesize JV2_PROP_NM(o,2,strLogo);
@synthesize JV2_PROP_NM(o,3,strNick);
@synthesize JV2_PROP_EX(o,4,mapAuth,M09<PERSON>SNumberONSString);
@synthesize JV2_PROP_NM(o,5,uAppId);
@synthesize JV2_PROP_NM(o,6,uFollow);
@synthesize JV2_PROP_NM(o,7,lRightMask);
@synthesize JV2_PROP_NM(o,8,uTreasure);
@synthesize JV2_PROP_NM(o,9,strBadInfo);
@synthesize JV2_PROP_NM(o,10,stKickOutInfo);
@synthesize JV2_PROP_NM(o,11,stVipInfo);
@synthesize JV2_PROP_NM(o,12,strPlatformTips);
@synthesize JV2_PROP_NM(o,13,strEncrytUid);

+ (void)initialize
{
    if (self == [proto_across_interactive_comm_UserInfo class]) {
        [proto_across_interactive_comm_UserBadInfo initialize];
        [proto_across_interactive_comm_UserKickOutInfo initialize];
        [proto_across_interactive_comm_UserVipInfo initialize];
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strLogo) = DefaultTarsString;
        JV2_PROP(strNick) = DefaultTarsString;
        JV2_PROP(strPlatformTips) = DefaultTarsString;
        JV2_PROP(strEncrytUid) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_across_interactive_comm.UserInfo";
}

@end
