// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_across_interactive_comm.jce   
// **********************************************************************

#import "JceObjectV2.h"

@interface proto_across_interactive_comm_UserVipInfo : JceObjectV2

@property (nonatomic, assign, JV2_PROP_GS_V2(uIsVip,setUIsVip:)) TarsUInt32 JV2_PROP_NM(o,0,uIsVip);
@property (nonatomic, assign, JV2_PROP_GS_V2(uVipLevel,setUVipLevel:)) TarsUInt32 JV2_PROP_NM(o,1,uVipLevel);
@property (nonatomic, retain, JV2_PROP_GS_V2(strVipIcon,setStrVipIcon:)) NSString* JV2_PROP_NM(o,2,strVipIcon);
@property (nonatomic, retain, JV2_PROP_GS_V2(strBackColor,setStrBackColor:)) NSString* JV2_PROP_NM(o,3,strBackColor);

@end
