// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_webapp_live_quick_comment.jce   
// **********************************************************************

#import "JceObjectV2.h"

@interface proto_live_quick_comment_QueryQuickCommentConfReq : JceObjectV2

@property (nonatomic, assign, JV2_PROP_GS_V2(uAppId,setUAppId:)) TarsUInt32 JV2_PROP_NM(o,0,uAppId);
@property (nonatomic, assign, JV2_PROP_GS_V2(lAnchorId,setLAnchorId:)) TarsInt64 JV2_PROP_NM(o,1,lAnchorId);
@property (nonatomic, assign, JV2_PROP_GS_V2(uRoomType,setURoomType:)) TarsUInt32 JV2_PROP_NM(o,2,uRoomType);
@property (nonatomic, assign, JV2_PROP_GS_V2(uRoomMode,setURoomMode:)) TarsUInt32 JV2_PROP_NM(o,3,uRoomMode);
@property (nonatomic, assign, JV2_PROP_GS_V2(lUserId,setLUserId:)) TarsInt64 JV2_PROP_NM(o,4,lUserId);
@property (nonatomic, assign, JV2_PROP_GS_V2(uTriggerType,setUTriggerType:)) TarsUInt32 JV2_PROP_NM(o,5,uTriggerType);
@property (nonatomic, retain, JV2_PROP_GS_V2(strQua,setStrQua:)) NSString* JV2_PROP_NM(o,6,strQua);
@property (nonatomic, retain, JV2_PROP_GS_V2(strDeviceInfo,setStrDeviceInfo:)) NSString* JV2_PROP_NM(o,7,strDeviceInfo);

@end
