// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_webapp_live_quick_comment.jce   
// **********************************************************************

#import "JceObjectV2.h"

@interface proto_live_quick_comment_QueryQuickCommentConfRsp : JceObjectV2

@property (nonatomic, retain, JV2_PROP_GS_V2(vecCommentText,setVecCommentText:)) NSArray* JV2_PROP_EX(o,0,vecCommentText,VONSString);
@property (nonatomic, assign, JV2_PROP_GS_V2(uShouldShow,setUShouldShow:)) TarsUInt32 JV2_PROP_NM(o,1,uShouldShow);
@property (nonatomic, assign, JV2_PROP_GS_V2(uDelaySec,setUDelaySec:)) TarsUInt32 JV2_PROP_NM(o,2,uDelaySec);
@property (nonatomic, assign, JV2_PROP_GS_V2(uDurationSec,setUDurationSec:)) TarsUInt32 JV2_PROP_NM(o,3,uDurationSec);

@end
