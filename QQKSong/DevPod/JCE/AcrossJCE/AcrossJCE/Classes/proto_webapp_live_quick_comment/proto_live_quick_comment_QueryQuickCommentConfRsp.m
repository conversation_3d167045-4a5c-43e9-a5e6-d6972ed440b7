// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_webapp_live_quick_comment.jce   
// **********************************************************************

#import "proto_live_quick_comment_QueryQuickCommentConfRsp.h"

@implementation proto_live_quick_comment_QueryQuickCommentConfRsp

@synthesize JV2_PROP_EX(o,0,vecCommentText,VONSString);
@synthesize JV2_PROP_NM(o,1,uShouldShow);
@synthesize JV2_PROP_NM(o,2,uDelaySec);
@synthesize JV2_PROP_NM(o,3,uDurationSec);

+ (void)initialize
{
    if (self == [proto_live_quick_comment_QueryQuickCommentConfRsp class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_live_quick_comment.QueryQuickCommentConfRsp";
}

@end
