// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_webapp_live_quick_comment.jce   
// **********************************************************************

#import "proto_live_quick_comment_QueryQuickCommentConfReq.h"

@implementation proto_live_quick_comment_QueryQuickCommentConfReq

@synthesize JV2_PROP_NM(o,0,uAppId);
@synthesize JV2_PROP_NM(o,1,lAnchorId);
@synthesize JV2_PROP_NM(o,2,uRoomType);
@synthesize JV2_PROP_NM(o,3,uRoomMode);
@synthesize JV2_PROP_NM(o,4,lUserId);
@synthesize JV2_PROP_NM(o,5,uTriggerType);
@synthesize JV2_PROP_NM(o,6,strQua);
@synthesize JV2_PROP_NM(o,7,strDeviceInfo);

+ (void)initialize
{
    if (self == [proto_live_quick_comment_QueryQuickCommentConfReq class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strQua) = DefaultTarsString;
        JV2_PROP(strDeviceInfo) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_live_quick_comment.QueryQuickCommentConfReq";
}

@end
