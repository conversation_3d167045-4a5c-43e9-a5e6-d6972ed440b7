// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_tme_config_webapp.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>

@interface proto_tme_config_webapp_GetCommConfRsp : JceObjectV2

@property (nonatomic, retain, JV2_PROP_GS_V2(mapConfigs,setMapConfigs:)) NSDictionary* JV2_PROP_EX(o,0,mapConfigs,M09ONSStringOproto_tme_config_base_ConfigList);
@property (nonatomic, retain, JV2_PROP_GS_V2(strPassback,setStrPassback:)) NSString* JV2_PROP_NM(o,1,strPassback);
@property (nonatomic, assign, JV2_PROP_GS_V2(lVersion,setLVersion:)) TarsInt64 JV2_PROP_NM(o,2,lVersion);
@property (nonatomic, assign, JV2_PROP_GS_V2(uInterval,setUInterval:)) TarsUInt32 JV2_PROP_NM(o,3,uInterval);

@end
