// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_tme_config_base.jce   
// **********************************************************************

#import "proto_tme_config_base_ConfigList.h"

@implementation proto_tme_config_base_ConfigList

@synthesize JV2_PROP_EX(o,0,mapConf,M09ONSStringONSString);

+ (void)initialize
{
    if (self == [proto_tme_config_base_ConfigList class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_tme_config_base.ConfigList";
}

@end
