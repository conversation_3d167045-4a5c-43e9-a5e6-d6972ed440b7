// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_tme_config_webapp.jce   
// **********************************************************************

#import "proto_tme_config_webapp_GetCommConfRsp.h"

@implementation proto_tme_config_webapp_GetCommConfRsp

@synthesize JV2_PROP_EX(o,0,mapConfigs,M09ONSStringOproto_tme_config_base_ConfigList);
@synthesize JV2_PROP_NM(o,1,strPassback);
@synthesize JV2_PROP_NM(o,2,lVersion);
@synthesize JV2_PROP_NM(o,3,uInterval);

+ (void)initialize
{
    if (self == [proto_tme_config_webapp_GetCommConfRsp class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strPassback) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_tme_config_webapp.GetCommConfRsp";
}

@end
