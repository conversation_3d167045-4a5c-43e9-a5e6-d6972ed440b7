// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_tme_config_webapp.jce   
// **********************************************************************

#import "proto_tme_config_webapp_GetCommConfReq.h"

@implementation proto_tme_config_webapp_GetCommConfReq

@synthesize JV2_PROP_NM(o,0,uBid);
@synthesize JV2_PROP_EX(o,1,vctProjectName,VONSString);
@synthesize JV2_PROP_NM(o,2,strPassback);
@synthesize JV2_PROP_NM(o,3,lVersion);
@synthesize JV2_PROP_NM(o,4,strQua);
@synthesize JV2_PROP_NM(o,5,strDevice);
@synthesize JV2_PROP_NM(o,6,lUid);

+ (void)initialize
{
    if (self == [proto_tme_config_webapp_GetCommConfReq class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strPassback) = DefaultTarsString;
        JV2_PROP(strQua) = DefaultTarsString;
        JV2_PROP(strDevice) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_tme_config_webapp.GetCommConfReq";
}

@end
