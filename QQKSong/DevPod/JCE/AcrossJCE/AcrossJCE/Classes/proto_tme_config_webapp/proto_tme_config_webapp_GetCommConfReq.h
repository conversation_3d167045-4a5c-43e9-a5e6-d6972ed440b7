// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_tme_config_webapp.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>

@interface proto_tme_config_webapp_GetCommConfReq : JceObjectV2

@property (nonatomic, assign, JV2_PROP_GS_V2(uBid,setUBid:)) TarsUInt32 JV2_PROP_NM(o,0,uBid);
@property (nonatomic, retain, JV2_PROP_GS_V2(vctProjectName,setVctProjectName:)) NSArray* JV2_PROP_EX(o,1,vctProjectName,VONSString);
@property (nonatomic, retain, JV2_PROP_GS_V2(strPassback,setStrPassback:)) NSString* JV2_PROP_NM(o,2,strPassback);
@property (nonatomic, assign, JV2_PROP_GS_V2(lVersion,setLVersion:)) TarsInt64 JV2_PROP_NM(o,3,lVersion);
@property (nonatomic, retain, JV2_PROP_GS_V2(strQua,setStrQua:)) NSString* JV2_PROP_NM(o,4,strQua);
@property (nonatomic, retain, JV2_PROP_GS_V2(strDevice,setStrDevice:)) NSString* JV2_PROP_NM(o,5,strDevice);
@property (nonatomic, assign, JV2_PROP_GS_V2(lUid,setLUid:)) TarsInt64 JV2_PROP_NM(o,6,lUid);

@end
