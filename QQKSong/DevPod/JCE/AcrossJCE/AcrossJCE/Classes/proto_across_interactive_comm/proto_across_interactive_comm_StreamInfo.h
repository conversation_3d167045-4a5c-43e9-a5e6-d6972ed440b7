// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_across_interactive_comm.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>

@interface proto_across_interactive_comm_StreamInfo : JceObjectV2

@property (nonatomic, retain, JV2_PROP_GS_V2(strPlayURL,setStrPlayURL:)) NSString* JV2_PROP_NM(o,0,strPlayURL);
@property (nonatomic, retain, JV2_PROP_GS_V2(strPlayURL540,setStrPlayURL540:)) NSString* JV2_PROP_NM(o,1,strPlayURL540);
@property (nonatomic, retain, JV2_PROP_GS_V2(strPlayURL720,setStrPlayURL720:)) NSString* JV2_PROP_NM(o,2,strPlayURL720);
@property (nonatomic, retain, JV2_PROP_GS_V2(strPlayURL1080,setStrPlayURL1080:)) NSString* JV2_PROP_NM(o,3,strPlayURL1080);
@property (nonatomic, retain, JV2_PROP_GS_V2(strPlayURLHLSM3U8,setStrPlayURLHLSM3U8:)) NSString* JV2_PROP_NM(o,4,strPlayURLHLSM3U8);
@property (nonatomic, retain, JV2_PROP_GS_V2(strPlayURLHLSFLV,setStrPlayURLHLSFLV:)) NSString* JV2_PROP_NM(o,5,strPlayURLHLSFLV);

@end
