// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_across_interactive_comm.jce   
// **********************************************************************

#import "JceObjectV2.h"

enum {
    proto_across_interactive_comm_LiveType_LIVE_TYPE_VIDEO = 0,
    proto_across_interactive_comm_LiveType_LIVE_TYPE_AUDIO = 1
};
#define proto_across_interactive_comm_LiveType TarsInt32

#if TARSV2_ENUM_ETOS_AND_STOE_SUPPORTED

@interface proto_across_interactive_comm_LiveTypeHelper: TarsEnumHelper

+ (NSString *)etos:(proto_across_interactive_comm_LiveType)e;
+ (proto_across_interactive_comm_LiveType)stoe:(NSString *)s;

@end

#endif
