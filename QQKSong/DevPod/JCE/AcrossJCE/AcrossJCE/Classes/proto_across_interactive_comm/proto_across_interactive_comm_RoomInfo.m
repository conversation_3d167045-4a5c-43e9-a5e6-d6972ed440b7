// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_across_interactive_comm.jce   
// **********************************************************************

#import "proto_across_interactive_comm_RoomInfo.h"
#import "proto_across_interactive_comm_StreamInfo.h"

@implementation proto_across_interactive_comm_RoomInfo

@synthesize JV2_PROP_NM(o,0,strRoomId);
@synthesize JV2_PROP_NM(o,1,strShowId);
@synthesize JV2_PROP_NM(o,2,uShowStartTs);
@synthesize JV2_PROP_NM(o,3,uShowEndTs);
@synthesize JV2_PROP_NM(o,4,strRoomTitle);
@synthesize JV2_PROP_NM(o,5,strJumpUrl);
@synthesize JV2_PROP_NM(o,6,stStreamInfo);
@synthesize JV2_PROP_NM(o,7,emLiveType);
@synthesize JV2_PROP_EX(o,8,mapExt,M09ONSStringONSString);
@synthesize JV2_PROP_NM(o,9,strCoverUrl);
@synthesize JV2_PROP_NM(o,10,lRoomType);

+ (void)initialize
{
    if (self == [proto_across_interactive_comm_RoomInfo class]) {
        [proto_across_interactive_comm_StreamInfo initialize];
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strRoomId) = DefaultTarsString;
        JV2_PROP(strShowId) = DefaultTarsString;
        JV2_PROP(strRoomTitle) = DefaultTarsString;
        JV2_PROP(strJumpUrl) = DefaultTarsString;
        JV2_PROP(strCoverUrl) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_across_interactive_comm.RoomInfo";
}

@end
