// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_across_interactive_comm.jce   
// **********************************************************************

#import "JceObjectV2.h"
#import "proto_across_interactive_comm_LiveType.h"

#if TARSV2_ENUM_ETOS_AND_STOE_SUPPORTED

@implementation proto_across_interactive_comm_LiveTypeHelper

+ (NSString *)etos:(proto_across_interactive_comm_LiveType)e
{
    switch(e){
        case proto_across_interactive_comm_LiveType_LIVE_TYPE_VIDEO: return @"proto_across_interactive_comm_LiveType_LIVE_TYPE_VIDEO";
        case proto_across_interactive_comm_LiveType_LIVE_TYPE_AUDIO: return @"proto_across_interactive_comm_LiveType_LIVE_TYPE_AUDIO";
        default: return @"";
    }
}

+ (proto_across_interactive_comm_LiveType)stoe:(NSString *)s
{
    if(isTarsEnumStringEqual(s, @"proto_across_interactive_comm_LiveType_LIVE_TYPE_VIDEO")) return proto_across_interactive_comm_LiveType_LIVE_TYPE_VIDEO;
    if(isTarsEnumStringEqual(s, @"proto_across_interactive_comm_LiveType_LIVE_TYPE_AUDIO")) return proto_across_interactive_comm_LiveType_LIVE_TYPE_AUDIO;
    return INT32_MIN;
}

@end

#endif
