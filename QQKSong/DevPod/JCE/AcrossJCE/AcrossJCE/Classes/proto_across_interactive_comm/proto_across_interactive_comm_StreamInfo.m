// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_across_interactive_comm.jce   
// **********************************************************************

#import "proto_across_interactive_comm_StreamInfo.h"

@implementation proto_across_interactive_comm_StreamInfo

@synthesize JV2_PROP_NM(o,0,strPlayURL);
@synthesize JV2_PROP_NM(o,1,strPlayURL540);
@synthesize JV2_PROP_NM(o,2,strPlayURL720);
@synthesize JV2_PROP_NM(o,3,strPlayURL1080);
@synthesize JV2_PROP_NM(o,4,strPlayURLHLSM3U8);
@synthesize JV2_PROP_NM(o,5,strPlayURLHLSFLV);

+ (void)initialize
{
    if (self == [proto_across_interactive_comm_StreamInfo class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strPlayURL) = DefaultTarsString;
        JV2_PROP(strPlayURL540) = DefaultTarsString;
        JV2_PROP(strPlayURL720) = DefaultTarsString;
        JV2_PROP(strPlayURL1080) = DefaultTarsString;
        JV2_PROP(strPlayURLHLSM3U8) = DefaultTarsString;
        JV2_PROP(strPlayURLHLSFLV) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_across_interactive_comm.StreamInfo";
}

@end
