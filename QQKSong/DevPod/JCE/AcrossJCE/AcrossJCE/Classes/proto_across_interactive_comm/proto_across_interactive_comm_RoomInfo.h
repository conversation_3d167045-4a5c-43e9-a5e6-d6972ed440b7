// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_across_interactive_comm.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>
#import "proto_across_interactive_comm_LiveType.h"
@class proto_across_interactive_comm_StreamInfo;

@interface proto_across_interactive_comm_RoomInfo : JceObjectV2

@property (nonatomic, retain, JV2_PROP_GS_V2(strRoomId,setStrRoomId:)) NSString* JV2_PROP_NM(o,0,strRoomId);
@property (nonatomic, retain, JV2_PROP_GS_V2(strShowId,setStrShowId:)) NSString* JV2_PROP_NM(o,1,strShowId);
@property (nonatomic, assign, JV2_PROP_GS_V2(uShowStartTs,setUShowStartTs:)) TarsUInt32 JV2_PROP_NM(o,2,uShowStartTs);
@property (nonatomic, assign, JV2_PROP_GS_V2(uShowEndTs,setUShowEndTs:)) TarsUInt32 JV2_PROP_NM(o,3,uShowEndTs);
@property (nonatomic, retain, JV2_PROP_GS_V2(strRoomTitle,setStrRoomTitle:)) NSString* JV2_PROP_NM(o,4,strRoomTitle);
@property (nonatomic, retain, JV2_PROP_GS_V2(strJumpUrl,setStrJumpUrl:)) NSString* JV2_PROP_NM(o,5,strJumpUrl);
@property (nonatomic, retain, JV2_PROP_GS_V2(stStreamInfo,setStStreamInfo:)) proto_across_interactive_comm_StreamInfo* JV2_PROP_NM(o,6,stStreamInfo);
@property (nonatomic, assign, JV2_PROP_GS_V2(emLiveType,setEmLiveType:)) proto_across_interactive_comm_LiveType JV2_PROP_NM(o,7,emLiveType);
@property (nonatomic, retain, JV2_PROP_GS_V2(mapExt,setMapExt:)) NSDictionary* JV2_PROP_EX(o,8,mapExt,M09ONSStringONSString);
@property (nonatomic, retain, JV2_PROP_GS_V2(strCoverUrl,setStrCoverUrl:)) NSString* JV2_PROP_NM(o,9,strCoverUrl);
@property (nonatomic, assign, JV2_PROP_GS_V2(lRoomType,setLRoomType:)) TarsInt64 JV2_PROP_NM(o,10,lRoomType);

@end
