// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_live_ai_partner_webapp.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>
@class proto_live_ai_partner_comm_AiPartnerSwitch;

@interface proto_live_ai_partner_webapp_GetAllSwitchRsp : JceObjectV2

@property (nonatomic, assign, JV2_PROP_GS_V2(iRet,setIRet:)) TarsInt32 JV2_PROP_NM(o,0,iRet);
@property (nonatomic, retain, JV2_PROP_GS_V2(strMsg,setStrMsg:)) NSString* JV2_PROP_NM(o,1,strMsg);
@property (nonatomic, retain, JV2_PROP_GS_V2(vctAiPartnerSwitch,setVctAiPartnerSwitch:)) NSArray* JV2_PROP_EX(o,2,vctAiPartnerSwitch,VOproto_live_ai_partner_comm_AiPartnerSwitch);
@property (nonatomic, retain, JV2_PROP_GS_V2(stMainSwitch,setStMainSwitch:)) proto_live_ai_partner_comm_AiPartnerSwitch* JV2_PROP_NM(o,3,stMainSwitch);

@end
