// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_live_ai_partner_webapp.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>

#define proto_live_ai_partner_webapp_INTERACT_LIVE_AI_PARTNER_WEBAPP_MOD_ID ((int)298018972)

#define proto_live_ai_partner_webapp_CMD_INTERACT_LIVE_AI_PARTNER_WEBAPP_GET_ALL_SWITCH @"interact.live_ai_partner.webapp.get_all_switch"

#define proto_live_ai_partner_webapp_CMD_INTERACT_LIVE_AI_PARTNER_WEBAPP_SET_SWITCH_STATUS @"interact.live_ai_partner.webapp.set_switch_status"

#define proto_live_ai_partner_webapp_CMD_INTERACT_LIVE_AI_PARTNER_WEBAPP_GET_ANCHOR_CHARACTER @"interact.live_ai_partner.webapp.get_anchor_character"

#define proto_live_ai_partner_webapp_CMD_INTERACT_LIVE_AI_PARTNER_WEBAPP_SET_ANCHOR_CHARACTER @"interact.live_ai_partner.webapp.set_anchor_character"

#define proto_live_ai_partner_webapp_CMD_INTERACT_LIVE_AI_PARTNER_WEBAPP_GET_USER_PROFILE_PROMPT @"interact.live_ai_partner.webapp.get_user_profile_prompt"

#define proto_live_ai_partner_webapp_CMD_INTERACT_LIVE_AI_PARTNER_WEBAPP_GET_CHAT_HISTORY @"interact.live_ai_partner.webapp.get_chat_history"

