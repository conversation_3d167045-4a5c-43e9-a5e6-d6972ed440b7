// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_live_ai_partner_webapp.jce   
// **********************************************************************

#import "proto_live_ai_partner_webapp_GetAllSwitchRsp.h"
#import "proto_live_ai_partner_comm_AiPartnerSwitch.h"

@implementation proto_live_ai_partner_webapp_GetAllSwitchRsp

@synthesize JV2_PROP_NM(o,0,iRet);
@synthesize JV2_PROP_NM(o,1,strMsg);
@synthesize JV2_PROP_EX(o,2,vctAiPartnerSwitch,VOproto_live_ai_partner_comm_AiPartnerSwitch);
@synthesize JV2_PROP_NM(o,3,stMainSwitch);

+ (void)initialize
{
    if (self == [proto_live_ai_partner_webapp_GetAllSwitchRsp class]) {
        [proto_live_ai_partner_comm_AiPartnerSwitch initialize];
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strMsg) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_live_ai_partner_webapp.GetAllSwitchRsp";
}

@end
