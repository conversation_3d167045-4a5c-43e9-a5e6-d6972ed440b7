// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_live_ai_partner_webapp.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>

@interface proto_live_ai_partner_webapp_GetAllSwitchReq : JceObjectV2

@property (nonatomic, assign, JV2_PROP_GS_V2(uAppId,setUAppId:)) TarsUInt32 JV2_PROP_NM(o,0,uAppId);
@property (nonatomic, retain, JV2_PROP_GS_V2(strUid,setStrUid:)) NSString* JV2_PROP_NM(o,1,strUid);
@property (nonatomic, assign, JV2_PROP_GS_V2(uBizType,setUBizType:)) TarsUInt32 JV2_PROP_NM(o,2,uBizType);

@end
