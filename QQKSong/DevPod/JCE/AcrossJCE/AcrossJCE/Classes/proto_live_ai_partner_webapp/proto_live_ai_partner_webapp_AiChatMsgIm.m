// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_live_ai_partner_webapp.jce   
// **********************************************************************

#import "proto_live_ai_partner_webapp_AiChatMsgIm.h"

@implementation proto_live_ai_partner_webapp_AiChatMsgIm

@synthesize JV2_PROP_NM(o,0,strMsg);

+ (void)initialize
{
    if (self == [proto_live_ai_partner_webapp_AiChatMsgIm class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strMsg) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_live_ai_partner_webapp.AiChatMsgIm";
}

@end
