// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_across_interactive_webapp.jce   
// **********************************************************************

#import "proto_across_interactive_webapp_LiveStatsDataRsp.h"
#import "proto_across_interactive_comm_LiveStatsData.h"

@implementation proto_across_interactive_webapp_LiveStatsDataRsp

@synthesize JV2_PROP_EX(o,0,vctData,VOproto_across_interactive_comm_LiveStatsData);

+ (void)initialize
{
    if (self == [proto_across_interactive_webapp_LiveStatsDataRsp class]) {
        [proto_across_interactive_comm_LiveStatsData initialize];
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_across_interactive_webapp.LiveStatsDataRsp";
}

@end
