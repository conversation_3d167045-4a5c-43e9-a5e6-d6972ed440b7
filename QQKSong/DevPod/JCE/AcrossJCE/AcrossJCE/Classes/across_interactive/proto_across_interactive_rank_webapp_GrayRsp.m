// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_across_interactive_rank_webapp.jce   
// **********************************************************************

#import "proto_across_interactive_rank_webapp_GrayRsp.h"

@implementation proto_across_interactive_rank_webapp_GrayRsp

@synthesize JV2_PROP_NM(o,0,bNew);

+ (void)initialize
{
    if (self == [proto_across_interactive_rank_webapp_GrayRsp class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(bNew) = YES;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_across_interactive_rank_webapp.GrayRsp";
}

@end
