// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_across_interactive_rank_webapp.jce   
// **********************************************************************

#import "proto_across_interactive_rank_webapp_GrayReq.h"

@implementation proto_across_interactive_rank_webapp_GrayReq

@synthesize JV2_PROP_NM(o,0,strEncryptUid);
@synthesize JV2_PROP_NM(o,1,uPlat);
@synthesize JV2_PROP_NM(o,2,strEncryptAnchorUid);
@synthesize JV2_PROP_NM(o,3,strRoomId);
@synthesize JV2_PROP_NM(o,4,strShowId);

+ (void)initialize
{
    if (self == [proto_across_interactive_rank_webapp_GrayReq class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strEncryptUid) = DefaultTarsString;
        JV2_PROP(strEncryptAnchorUid) = DefaultTarsString;
        JV2_PROP(strRoomId) = DefaultTarsString;
        JV2_PROP(strShowId) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_across_interactive_rank_webapp.GrayReq";
}

@end
