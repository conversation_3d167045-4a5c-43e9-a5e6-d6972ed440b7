// **********************************************************************
// This file was generated by a Jce parser!
// Jce version 1.0.0.
// Source file: proto_lucky_star_webapp.jce
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>
#import "JceAcross_proto_interact_lucky_star_comm_TaskShowItem.h"

@interface JceAcross_proto_interact_lucky_star_webapp_GetTaskRsp : JceObjectV2

/** 当前任务 */
@property (nonatomic, strong, JV2_PROP_GS_V2(stTaskShowItem,setStTaskShowItem:)) JceAcross_proto_interact_lucky_star_comm_TaskShowItem* JV2_PROP_NM(o,0,stTaskShowItem) NS_SWIFT_NAME(stTaskShowItem);
@property (nonatomic, assign, JV2_PROP_GS_V2(iIsBasicTask,setIIsBasicTask:)) JceInt32 JV2_PROP_NM(o,1,iIsBasicTask) NS_SWIFT_NAME(iIsBasicTask);
/** 当前关卡，对应x星 */
@property (nonatomic, assign, JV2_PROP_GS_V2(iLevel,setILevel:)) JceInt32 JV2_PROP_NM(o,2,iLevel) NS_SWIFT_NAME(iLevel);
/** 轮询间隔 */
@property (nonatomic, assign, JV2_PROP_GS_V2(iIntervalTs,setIIntervalTs:)) JceInt32 JV2_PROP_NM(o,3,iIntervalTs) NS_SWIFT_NAME(iIntervalTs);
/** 跳转链接 */
@property (nonatomic, strong, JV2_PROP_GS_V2(strJumpUrl,setStrJumpUrl:)) NSString* JV2_PROP_NM(o,4,strJumpUrl) NS_SWIFT_NAME(strJumpUrl);
/** 0: 否 1: 是 */
@property (nonatomic, assign, JV2_PROP_GS_V2(iIsDisplayed,setIIsDisplayed:)) JceInt32 JV2_PROP_NM(o,5,iIsDisplayed) NS_SWIFT_NAME(iIsDisplayed);

@end
