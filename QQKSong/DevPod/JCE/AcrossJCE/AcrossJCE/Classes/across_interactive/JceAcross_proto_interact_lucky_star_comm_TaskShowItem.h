// **********************************************************************
// This file was generated by a Jce parser!
// Jce version 1.0.0.
// Source file: proto_lucky_star_webapp.jce
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>

@interface JceAcross_proto_interact_lucky_star_comm_TaskShowItem : JceObjectV2

@property (nonatomic, strong, JV2_PROP_GS_V2(strTaskName,setStrTaskName:)) NSString* JV2_PROP_NM(o,0,strTaskName) NS_SWIFT_NAME(strTaskName);
@property (nonatomic, assign, JV2_PROP_GS_V2(iTarget,setITarget:)) JceInt32 JV2_PROP_NM(o,1,iTarget) NS_SWIFT_NAME(iTarget);
@property (nonatomic, assign, JV2_PROP_GS_V2(iProcess,setIProcess:)) JceInt32 JV2_PROP_NM(o,2,iProcess) NS_SWIFT_NAME(iProcess);
@property (nonatomic, strong, JV2_PROP_GS_V2(strUnit,setStrUnit:)) NSString* JV2_PROP_NM(o,3,strUnit) NS_SWIFT_NAME(strUnit);

@end
