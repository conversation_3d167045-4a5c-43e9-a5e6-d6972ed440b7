// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_across_interactive_rank_comm.jce   
// **********************************************************************

#import "proto_across_interactive_rank_comm_InteractGiftRankDetail.h"
#import "proto_across_interactive_rank_comm_InteractGiftRankItem.h"

@implementation proto_across_interactive_rank_comm_InteractGiftRankDetail

@synthesize JV2_PROP_EX(o,0,vctGiftRank,VOproto_across_interactive_rank_comm_InteractGiftRankItem);
@synthesize JV2_PROP_NM(o,1,lCurrencyNum);
@synthesize JV2_PROP_NM(o,2,lPropsNum);

+ (void)initialize
{
    if (self == [proto_across_interactive_rank_comm_InteractGiftRankDetail class]) {
        [proto_across_interactive_rank_comm_InteractGiftRankItem initialize];
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_across_interactive_rank_comm.InteractGiftRankDetail";
}

@end
