// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_across_interactive_rank_comm.jce   
// **********************************************************************

#import "proto_across_interactive_rank_comm_InteractGiftRankItem.h"

@implementation proto_across_interactive_rank_comm_InteractGiftRankItem

@synthesize JV2_PROP_NM(o,0,lUid);
@synthesize JV2_PROP_NM(o,1,lCurrencyNum);
@synthesize JV2_PROP_NM(o,2,lPropsNum);
@synthesize JV2_PROP_NM(o,3,uInvisible);
@synthesize JV2_PROP_NM(o,4,strAvatar);
@synthesize JV2_PROP_NM(o,5,uPlat);
@synthesize JV2_PROP_NM(o,6,uRank);

+ (void)initialize
{
    if (self == [proto_across_interactive_rank_comm_InteractGiftRankItem class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strAvatar) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_across_interactive_rank_comm.InteractGiftRankItem";
}

@end
