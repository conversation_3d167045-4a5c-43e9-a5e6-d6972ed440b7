// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_across_interactive_rank_comm.jce   
// **********************************************************************

#import "JceObjectV2.h"
@class proto_across_interactive_rank_comm_InteractGiftRankItem;

@interface proto_across_interactive_rank_comm_InteractGiftRankDetail : JceObjectV2

@property (nonatomic, retain, JV2_PROP_GS_V2(vctGiftRank,setVctGiftRank:)) NSArray* JV2_PROP_EX(o,0,vctGiftRank,VOproto_across_interactive_rank_comm_InteractGiftRankItem);
@property (nonatomic, assign, JV2_PROP_GS_V2(lCurrencyNum,setLCurrencyNum:)) TarsInt64 JV2_PROP_NM(o,1,lCurrencyNum);
@property (nonatomic, assign, JV2_PROP_GS_V2(lPropsNum,setLPropsNum:)) TarsInt64 JV2_PROP_NM(o,2,lPropsNum);

@end
