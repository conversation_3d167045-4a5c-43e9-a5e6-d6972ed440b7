// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_across_interactive_webapp.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>
@class proto_across_interactive_comm_PlatAppInfo;

@interface proto_across_interactive_webapp_DistributeInfoRsp : JceObjectV2

@property (nonatomic, retain, JV2_PROP_GS_V2(vctPlatInfos,setVctPlatInfos:)) NSArray* JV2_PROP_EX(o,0,vctPlatInfos,VOproto_across_interactive_comm_PlatAppInfo);

@end
