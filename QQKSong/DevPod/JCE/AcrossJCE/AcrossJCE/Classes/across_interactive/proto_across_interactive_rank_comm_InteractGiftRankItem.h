// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_across_interactive_rank_comm.jce   
// **********************************************************************

#import "JceObjectV2.h"

@interface proto_across_interactive_rank_comm_InteractGiftRankItem : JceObjectV2

@property (nonatomic, assign, JV2_PROP_GS_V2(lUid,setLUid:)) TarsInt64 JV2_PROP_NM(o,0,lUid);
@property (nonatomic, assign, JV2_PROP_GS_V2(lCurrencyNum,setLCurrencyNum:)) TarsInt64 JV2_PROP_NM(o,1,lCurrencyNum);
@property (nonatomic, assign, JV2_PROP_GS_V2(lPropsNum,setLPropsNum:)) TarsInt64 JV2_PROP_NM(o,2,lPropsNum);
@property (nonatomic, assign, JV2_PROP_GS_V2(uInvisible,setUInvisible:)) TarsUInt32 JV2_PROP_NM(o,3,uInvisible);
@property (nonatomic, retain, JV2_PROP_GS_V2(strAvatar,setStrAvatar:)) NSString* JV2_PROP_NM(o,4,strAvatar);
@property (nonatomic, assign, JV2_PROP_GS_V2(uPlat,setUPlat:)) TarsUInt32 JV2_PROP_NM(o,5,uPlat);
@property (nonatomic, assign, JV2_PROP_GS_V2(uRank,setURank:)) TarsUInt32 JV2_PROP_NM(o,6,uRank);

@end
