// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_across_interactive_webapp.jce   
// **********************************************************************

#import "proto_across_interactive_webapp_LiveStatsDataReq.h"

@implementation proto_across_interactive_webapp_LiveStatsDataReq

@synthesize JV2_PROP_NM(o,0,lAnchorId);
@synthesize JV2_PROP_NM(o,1,uSourcePlatApp);
@synthesize JV2_PROP_NM(o,2,lMask);
@synthesize JV2_PROP_NM(o,3,strShowId);

+ (void)initialize
{
    if (self == [proto_across_interactive_webapp_LiveStatsDataReq class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strShowId) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_across_interactive_webapp.LiveStatsDataReq";
}

@end
