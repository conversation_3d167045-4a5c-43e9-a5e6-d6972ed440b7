// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_across_interactive_webapp.jce   
// **********************************************************************

#import "proto_across_interactive_webapp_DistributeInfoRsp.h"
#import "proto_across_interactive_comm_PlatAppInfo.h"

@implementation proto_across_interactive_webapp_DistributeInfoRsp

@synthesize JV2_PROP_EX(o,0,vctPlatInfos,VOproto_across_interactive_comm_PlatAppInfo);

+ (void)initialize
{
    if (self == [proto_across_interactive_webapp_DistributeInfoRsp class]) {
        [proto_across_interactive_comm_PlatAppInfo initialize];
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_across_interactive_webapp.DistributeInfoRsp";
}

@end
