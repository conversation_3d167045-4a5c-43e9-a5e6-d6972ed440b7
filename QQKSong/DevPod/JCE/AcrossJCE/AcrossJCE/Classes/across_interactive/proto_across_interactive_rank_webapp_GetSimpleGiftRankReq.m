// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_across_interactive_rank_webapp.jce   
// **********************************************************************

#import "proto_across_interactive_rank_webapp_GetSimpleGiftRankReq.h"

@implementation proto_across_interactive_rank_webapp_GetSimpleGiftRankReq

@synthesize JV2_PROP_NM(o,0,strPassBack);
@synthesize JV2_PROP_NM(o,1,strEncryptUid);
@synthesize JV2_PROP_NM(o,2,strEncryptAnchorUid);
@synthesize JV2_PROP_NM(o,3,uPlat);
@synthesize JV2_PROP_NM(o,4,iGiftRankType);
@synthesize JV2_PROP_NM(o,5,uGetNum);
@synthesize JV2_PROP_NM(o,6,strRoomId);
@synthesize JV2_PROP_NM(o,7,strShowId);
@synthesize JV2_PROP_NM(o,8,uIsFilterInvisible);

+ (void)initialize
{
    if (self == [proto_across_interactive_rank_webapp_GetSimpleGiftRankReq class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strPassBack) = DefaultTarsString;
        JV2_PROP(strEncryptUid) = DefaultTarsString;
        JV2_PROP(strEncryptAnchorUid) = DefaultTarsString;
        JV2_PROP(strRoomId) = DefaultTarsString;
        JV2_PROP(strShowId) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_across_interactive_rank_webapp.GetSimpleGiftRankReq";
}

@end
