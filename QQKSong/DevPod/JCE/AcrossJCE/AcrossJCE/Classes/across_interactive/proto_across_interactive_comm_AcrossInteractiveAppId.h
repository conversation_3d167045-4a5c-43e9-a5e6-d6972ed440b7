// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_across_interactive_comm.jce   
// **********************************************************************

#import "JceObjectV2.h"

enum {
    proto_across_interactive_comm_AcrossInteractiveAppId_ENUM_APPID_KG = 1,
    proto_across_interactive_comm_AcrossInteractiveAppId_ENUM_APPID_MUSIC = 2,
    proto_across_interactive_comm_AcrossInteractiveAppId_ENUM_APPID_LAZY = 3,
    proto_across_interactive_comm_AcrossInteractiveAppId_ENUM_APPID_CAR = 4,
    proto_across_interactive_comm_AcrossInteractiveAppId_ENUM_APPID_PUTOO = 5
};
#define proto_across_interactive_comm_AcrossInteractiveAppId TarsInt32

#if TARSV2_ENUM_ETOS_AND_STOE_SUPPORTED

@interface proto_across_interactive_comm_AcrossInteractiveAppIdHelper: TarsEnumHelper

+ (NSString *)etos:(proto_across_interactive_comm_AcrossInteractiveAppId)e;
+ (proto_across_interactive_comm_AcrossInteractiveAppId)stoe:(NSString *)s;

@end

#endif
