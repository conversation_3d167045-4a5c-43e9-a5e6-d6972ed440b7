// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_across_interactive_comm.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>
@class proto_across_interactive_comm_LiveStatsDataConf;

@interface proto_across_interactive_comm_PlatAppInfo : JceObjectV2

@property (nonatomic, assign, JV2_PROP_GS_V2(uPlatAppId,setUPlatAppId:)) TarsUInt32 JV2_PROP_NM(o,0,uPlatAppId);
@property (nonatomic, assign, JV2_PROP_GS_V2(lUid,setLUid:)) TarsInt64 JV2_PROP_NM(o,1,lUid);
@property (nonatomic, retain, JV2_PROP_GS_V2(stDataConf,setStDataConf:)) proto_across_interactive_comm_LiveStatsDataConf* JV2_PROP_NM(o,2,stDataConf);

@end
