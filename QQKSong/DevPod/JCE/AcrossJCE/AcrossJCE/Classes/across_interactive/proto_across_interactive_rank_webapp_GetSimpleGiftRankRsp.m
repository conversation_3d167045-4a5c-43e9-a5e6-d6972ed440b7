// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_across_interactive_rank_webapp.jce   
// **********************************************************************

#import "proto_across_interactive_rank_webapp_GetSimpleGiftRankRsp.h"
#import "proto_across_interactive_rank_comm_InteractGiftRankDetail.h"

@implementation proto_across_interactive_rank_webapp_GetSimpleGiftRankRsp

@synthesize JV2_PROP_NM(o,0,strPassBack);
@synthesize JV2_PROP_NM(o,1,iHasMore);
@synthesize JV2_PROP_NM(o,2,stInteractGiftRankDetail);
@synthesize JV2_PROP_NM(o,3,uInterval);

+ (void)initialize
{
    if (self == [proto_across_interactive_rank_webapp_GetSimpleGiftRankRsp class]) {
        [proto_across_interactive_rank_comm_InteractGiftRankDetail initialize];
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strPassBack) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_across_interactive_rank_webapp.GetSimpleGiftRankRsp";
}

@end
