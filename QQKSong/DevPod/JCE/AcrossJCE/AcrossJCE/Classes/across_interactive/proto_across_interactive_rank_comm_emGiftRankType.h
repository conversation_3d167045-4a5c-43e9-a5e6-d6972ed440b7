// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_across_interactive_rank_comm.jce   
// **********************************************************************

#import "JceObjectV2.h"

enum {
    proto_across_interactive_rank_comm_emGiftRankType_GIFT_RANK_TYPE_DAY = 0,
    proto_across_interactive_rank_comm_emGiftRankType_GIFT_RANK_TYPE_WEEK = 1,
    proto_across_interactive_rank_comm_emGiftRankType_GIFT_RANK_TYPE_MONTH = 2,
    proto_across_interactive_rank_comm_emGiftRankType_GIFT_RANK_TYPE_SHOW = 3
};
#define proto_across_interactive_rank_comm_emGiftRankType TarsInt32

#if TARSV2_ENUM_ETOS_AND_STOE_SUPPORTED

@interface proto_across_interactive_rank_comm_emGiftRankTypeHelper: TarsEnumHelper

+ (NSString *)etos:(proto_across_interactive_rank_comm_emGiftRankType)e;
+ (proto_across_interactive_rank_comm_emGiftRankType)stoe:(NSString *)s;

@end

#endif
