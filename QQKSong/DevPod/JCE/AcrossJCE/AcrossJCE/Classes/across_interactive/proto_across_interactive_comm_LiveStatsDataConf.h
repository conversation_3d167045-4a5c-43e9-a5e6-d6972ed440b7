// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_across_interactive_comm.jce
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>

@interface proto_across_interactive_comm_LiveStatsDataConf : JceObjectV2

@property (nonatomic, assign, JV2_PROP_GS_V2(uPlatAppId,setUPlatAppId:)) TarsUInt32 JV2_PROP_NM(o,0,uPlatAppId);
@property (nonatomic, retain, JV2_PROP_GS_V2(strPlatAppName,setStrPlatAppName:)) NSString* JV2_PROP_NM(o,1,strPlatAppName);
@property (nonatomic, retain, JV2_PROP_GS_V2(strPlatAppNameColor,setStrPlatAppNameColor:)) NSString* JV2_PROP_NM(o,2,strPlatAppNameColor);
@property (nonatomic, retain, JV2_PROP_GS_V2(strBackGroundColor,setStrBackGroundColor:)) NSString* JV2_PROP_NM(o,3,strBackGroundColor);
@property (nonatomic, retain, JV2_PROP_GS_V2(strBackGroundIcon,setStrBackGroundIcon:)) NSString* JV2_PROP_NM(o,4,strBackGroundIcon);
@property (nonatomic, retain, JV2_PROP_GS_V2(strCurrencyName,setStrCurrencyName:)) NSString* JV2_PROP_NM(o,5,strCurrencyName);

@end
