// **********************************************************************
// This file was generated by a Jce parser!
// Jce version 1.0.0.
// Source file: proto_lucky_star_webapp.jce
// **********************************************************************

#import "JceAcross_proto_interact_lucky_star_webapp_GetTaskReq.h"

@implementation JceAcross_proto_interact_lucky_star_webapp_GetTaskReq

@synthesize JV2_PROP_NM(o,0,strEncryptUin);
@synthesize JV2_PROP_NM(o,1,uAppId);

+ (void)initialize
{
    if (self == [JceAcross_proto_interact_lucky_star_webapp_GetTaskReq class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strEncryptUin) = DefaultJceString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_lucky_star_webapp.GetTaskReq";
}

@end
