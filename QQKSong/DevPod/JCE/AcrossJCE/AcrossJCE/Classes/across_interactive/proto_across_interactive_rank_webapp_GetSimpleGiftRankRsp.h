// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_across_interactive_rank_webapp.jce   
// **********************************************************************

#import "JceObjectV2.h"
@class proto_across_interactive_rank_comm_InteractGiftRankDetail;

@interface proto_across_interactive_rank_webapp_GetSimpleGiftRankRsp : JceObjectV2

@property (nonatomic, retain, JV2_PROP_GS_V2(strPassBack,setStrPassBack:)) NSString* JV2_PROP_NM(o,0,strPassBack);
@property (nonatomic, assign, JV2_PROP_GS_V2(iHasMore,setIHasMore:)) TarsInt32 JV2_PROP_NM(o,1,iHasMore);
@property (nonatomic, retain, JV2_PROP_GS_V2(stInteractGiftRankDetail,setStInteractGiftRankDetail:)) proto_across_interactive_rank_comm_InteractGiftRankDetail* JV2_PROP_NM(o,2,stInteractGiftRankDetail);
@property (nonatomic, assign, JV2_PROP_GS_V2(uInterval,setUInterval:)) TarsUInt32 JV2_PROP_NM(o,3,uInterval);

@end
