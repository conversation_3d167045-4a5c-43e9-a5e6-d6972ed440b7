// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_across_interactive_rank_comm.jce   
// **********************************************************************

#import "JceObjectV2.h"
#import "proto_across_interactive_rank_comm_emGiftRankType.h"

#if TARSV2_ENUM_ETOS_AND_STOE_SUPPORTED

@implementation proto_across_interactive_rank_comm_emGiftRankTypeHelper

+ (NSString *)etos:(proto_across_interactive_rank_comm_emGiftRankType)e
{
    switch(e){
        case proto_across_interactive_rank_comm_emGiftRankType_GIFT_RANK_TYPE_DAY: return @"proto_across_interactive_rank_comm_emGiftRankType_GIFT_RANK_TYPE_DAY";
        case proto_across_interactive_rank_comm_emGiftRankType_GIFT_RANK_TYPE_WEEK: return @"proto_across_interactive_rank_comm_emGiftRankType_GIFT_RANK_TYPE_WEEK";
        case proto_across_interactive_rank_comm_emGiftRankType_GIFT_RANK_TYPE_MONTH: return @"proto_across_interactive_rank_comm_emGiftRankType_GIFT_RANK_TYPE_MONTH";
        case proto_across_interactive_rank_comm_emGiftRankType_GIFT_RANK_TYPE_SHOW: return @"proto_across_interactive_rank_comm_emGiftRankType_GIFT_RANK_TYPE_SHOW";
        default: return @"";
    }
}

+ (proto_across_interactive_rank_comm_emGiftRankType)stoe:(NSString *)s
{
    if(isTarsEnumStringEqual(s, @"proto_across_interactive_rank_comm_emGiftRankType_GIFT_RANK_TYPE_DAY")) return proto_across_interactive_rank_comm_emGiftRankType_GIFT_RANK_TYPE_DAY;
    if(isTarsEnumStringEqual(s, @"proto_across_interactive_rank_comm_emGiftRankType_GIFT_RANK_TYPE_WEEK")) return proto_across_interactive_rank_comm_emGiftRankType_GIFT_RANK_TYPE_WEEK;
    if(isTarsEnumStringEqual(s, @"proto_across_interactive_rank_comm_emGiftRankType_GIFT_RANK_TYPE_MONTH")) return proto_across_interactive_rank_comm_emGiftRankType_GIFT_RANK_TYPE_MONTH;
    if(isTarsEnumStringEqual(s, @"proto_across_interactive_rank_comm_emGiftRankType_GIFT_RANK_TYPE_SHOW")) return proto_across_interactive_rank_comm_emGiftRankType_GIFT_RANK_TYPE_SHOW;
    return INT32_MIN;
}

@end

#endif
