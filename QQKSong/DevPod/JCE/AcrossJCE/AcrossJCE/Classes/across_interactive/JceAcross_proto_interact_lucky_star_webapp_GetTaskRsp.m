// **********************************************************************
// This file was generated by a Jce parser!
// Jce version 1.0.0.
// Source file: proto_lucky_star_webapp.jce
// **********************************************************************

#import "JceAcross_proto_interact_lucky_star_webapp_GetTaskRsp.h"

@implementation JceAcross_proto_interact_lucky_star_webapp_GetTaskRsp

@synthesize JV2_PROP_NM(o,0,stTaskShowItem);
@synthesize JV2_PROP_NM(o,1,iIsBasicTask);
@synthesize JV2_PROP_NM(o,2,iLevel);
@synthesize JV2_PROP_NM(o,3,iIntervalTs);
@synthesize JV2_PROP_NM(o,4,strJumpUrl);
@synthesize JV2_PROP_NM(o,5,iIsDisplayed);

+ (void)initialize
{
    if (self == [JceAcross_proto_interact_lucky_star_webapp_GetTaskRsp class]) {
        [JceAcross_proto_interact_lucky_star_comm_TaskShowItem initialize];
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strJumpUrl) = DefaultJceString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_lucky_star_webapp.GetTaskRsp";
}

@end
