// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_across_interactive_comm.jce   
// **********************************************************************

#import "proto_across_interactive_comm_PlatAppInfo.h"
#import "proto_across_interactive_comm_LiveStatsDataConf.h"

@implementation proto_across_interactive_comm_PlatAppInfo

@synthesize JV2_PROP_NM(o,0,uPlatAppId);
@synthesize JV2_PROP_NM(o,1,lUid);
@synthesize JV2_PROP_NM(o,2,stDataConf);

+ (void)initialize
{
    if (self == [proto_across_interactive_comm_PlatAppInfo class]) {
        [proto_across_interactive_comm_LiveStatsDataConf initialize];
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_across_interactive_comm.PlatAppInfo";
}

@end
