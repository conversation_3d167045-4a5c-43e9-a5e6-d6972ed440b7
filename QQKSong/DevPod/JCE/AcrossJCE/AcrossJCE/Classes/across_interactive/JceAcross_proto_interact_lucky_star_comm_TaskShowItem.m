// **********************************************************************
// This file was generated by a Jce parser!
// Jce version 1.0.0.
// Source file: proto_lucky_star_webapp.jce
// **********************************************************************

#import "JceAcross_proto_interact_lucky_star_comm_TaskShowItem.h"

@implementation JceAcross_proto_interact_lucky_star_comm_TaskShowItem

@synthesize JV2_PROP_NM(o,0,strTaskName);
@synthesize JV2_PROP_NM(o,1,iTarget);
@synthesize JV2_PROP_NM(o,2,iProcess);
@synthesize JV2_PROP_NM(o,3,strUnit);

+ (void)initialize
{
    if (self == [JceAcross_proto_interact_lucky_star_comm_TaskShowItem class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strTaskName) = DefaultJceString;
        JV2_PROP(strUnit) = DefaultJceString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_lucky_star_comm.TaskShowItem";
}

@end
