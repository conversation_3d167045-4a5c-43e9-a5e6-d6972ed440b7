// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_across_interactive_comm.jce   
// **********************************************************************

#import "JceObjectV2.h"
#import "proto_across_interactive_comm_AcrossInteractiveAppId.h"

#if TARSV2_ENUM_ETOS_AND_STOE_SUPPORTED

@implementation proto_across_interactive_comm_AcrossInteractiveAppIdHelper

+ (NSString *)etos:(proto_across_interactive_comm_AcrossInteractiveAppId)e
{
    switch(e){
        case proto_across_interactive_comm_AcrossInteractiveAppId_ENUM_APPID_KG: return @"proto_across_interactive_comm_AcrossInteractiveAppId_ENUM_APPID_KG";
        case proto_across_interactive_comm_AcrossInteractiveAppId_ENUM_APPID_MUSIC: return @"proto_across_interactive_comm_AcrossInteractiveAppId_ENUM_APPID_MUSIC";
        case proto_across_interactive_comm_AcrossInteractiveAppId_ENUM_APPID_LAZY: return @"proto_across_interactive_comm_AcrossInteractiveAppId_ENUM_APPID_LAZY";
        case proto_across_interactive_comm_AcrossInteractiveAppId_ENUM_APPID_CAR: return @"proto_across_interactive_comm_AcrossInteractiveAppId_ENUM_APPID_CAR";
        case proto_across_interactive_comm_AcrossInteractiveAppId_ENUM_APPID_PUTOO: return @"proto_across_interactive_comm_AcrossInteractiveAppId_ENUM_APPID_PUTOO";
        default: return @"";
    }
}

+ (proto_across_interactive_comm_AcrossInteractiveAppId)stoe:(NSString *)s
{
    if(isTarsEnumStringEqual(s, @"proto_across_interactive_comm_AcrossInteractiveAppId_ENUM_APPID_KG")) return proto_across_interactive_comm_AcrossInteractiveAppId_ENUM_APPID_KG;
    if(isTarsEnumStringEqual(s, @"proto_across_interactive_comm_AcrossInteractiveAppId_ENUM_APPID_MUSIC")) return proto_across_interactive_comm_AcrossInteractiveAppId_ENUM_APPID_MUSIC;
    if(isTarsEnumStringEqual(s, @"proto_across_interactive_comm_AcrossInteractiveAppId_ENUM_APPID_LAZY")) return proto_across_interactive_comm_AcrossInteractiveAppId_ENUM_APPID_LAZY;
    if(isTarsEnumStringEqual(s, @"proto_across_interactive_comm_AcrossInteractiveAppId_ENUM_APPID_CAR")) return proto_across_interactive_comm_AcrossInteractiveAppId_ENUM_APPID_CAR;
    if(isTarsEnumStringEqual(s, @"proto_across_interactive_comm_AcrossInteractiveAppId_ENUM_APPID_PUTOO")) return proto_across_interactive_comm_AcrossInteractiveAppId_ENUM_APPID_PUTOO;
    return INT32_MIN;
}

@end

#endif
