// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_across_interactive_rank_webapp.jce   
// **********************************************************************

#import "JceObjectV2.h"

@interface proto_across_interactive_rank_webapp_GrayReq : JceObjectV2

@property (nonatomic, retain, JV2_PROP_GS_V2(strEncryptUid,setStrEncryptUid:)) NSString* JV2_PROP_NM(o,0,strEncryptUid);
@property (nonatomic, assign, JV2_PROP_GS_V2(uPlat,setUPlat:)) TarsUInt32 JV2_PROP_NM(o,1,uPlat);
@property (nonatomic, retain, JV2_PROP_GS_V2(strEncryptAnchorUid,setStrEncryptAnchorUid:)) NSString* JV2_PROP_NM(o,2,strEncryptAnchorUid);
@property (nonatomic, retain, JV2_PROP_GS_V2(strRoomId,setStrRoomId:)) NSString* JV2_PROP_NM(o,3,strRoomId);
@property (nonatomic, retain, JV2_PROP_GS_V2(strShowId,setStrShowId:)) NSString* JV2_PROP_NM(o,4,strShowId);

@end
