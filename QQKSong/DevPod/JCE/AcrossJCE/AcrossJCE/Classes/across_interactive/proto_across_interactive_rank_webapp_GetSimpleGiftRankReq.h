// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_across_interactive_rank_webapp.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>

@interface proto_across_interactive_rank_webapp_GetSimpleGiftRankReq : JceObjectV2

@property (nonatomic, retain, JV2_PROP_GS_V2(strPassBack,setStrPassBack:)) NSString* JV2_PROP_NM(o,0,strPassBack);
@property (nonatomic, retain, JV2_PROP_GS_V2(strEncryptUid,setStrEncryptUid:)) NSString* JV2_PROP_NM(o,1,strEncryptUid);
@property (nonatomic, retain, JV2_PROP_GS_V2(strEncryptAnchorUid,setStrEncryptAnchorUid:)) NSString* JV2_PROP_NM(o,2,strEncryptAnchorUid);
@property (nonatomic, assign, JV2_PROP_GS_V2(uPlat,setUPlat:)) TarsUInt32 JV2_PROP_NM(o,3,uPlat);
@property (nonatomic, assign, JV2_PROP_GS_V2(iGiftRankType,setIGiftRankType:)) TarsInt32 JV2_PROP_NM(o,4,iGiftRankType);
@property (nonatomic, assign, JV2_PROP_GS_V2(uGetNum,setUGetNum:)) TarsUInt32 JV2_PROP_NM(o,5,uGetNum);
@property (nonatomic, retain, JV2_PROP_GS_V2(strRoomId,setStrRoomId:)) NSString* JV2_PROP_NM(o,6,strRoomId);
@property (nonatomic, retain, JV2_PROP_GS_V2(strShowId,setStrShowId:)) NSString* JV2_PROP_NM(o,7,strShowId);
@property (nonatomic, assign, JV2_PROP_GS_V2(uIsFilterInvisible,setUIsFilterInvisible:)) TarsUInt32 JV2_PROP_NM(o,8,uIsFilterInvisible);

@end
