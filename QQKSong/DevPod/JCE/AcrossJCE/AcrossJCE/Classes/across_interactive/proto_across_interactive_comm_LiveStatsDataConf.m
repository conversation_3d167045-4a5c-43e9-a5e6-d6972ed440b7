// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_across_interactive_comm.jce
// **********************************************************************

#import "proto_across_interactive_comm_LiveStatsDataConf.h"

@implementation proto_across_interactive_comm_LiveStatsDataConf

@synthesize JV2_PROP_NM(o,0,uPlatAppId);
@synthesize JV2_PROP_NM(o,1,strPlatAppName);
@synthesize JV2_PROP_NM(o,2,strPlatAppNameColor);
@synthesize JV2_PROP_NM(o,3,strBackGroundColor);
@synthesize JV2_PROP_NM(o,4,strBackGroundIcon);
@synthesize JV2_PROP_NM(o,5,strCurrencyName);

+ (void)initialize
{
    if (self == [proto_across_interactive_comm_LiveStatsDataConf class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strPlatAppName) = DefaultTarsString;
        JV2_PROP(strPlatAppNameColor) = DefaultTarsString;
        JV2_PROP(strBackGroundColor) = DefaultTarsString;
        JV2_PROP(strBackGroundIcon) = DefaultTarsString;
        JV2_PROP(strCurrencyName) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_across_interactive_comm.LiveStatsDataConf";
}

@end
