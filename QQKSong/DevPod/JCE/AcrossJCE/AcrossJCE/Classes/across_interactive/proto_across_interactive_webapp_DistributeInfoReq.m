// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_across_interactive_webapp.jce   
// **********************************************************************

#import "proto_across_interactive_webapp_DistributeInfoReq.h"

@implementation proto_across_interactive_webapp_DistributeInfoReq

@synthesize JV2_PROP_NM(o,0,lUid);
@synthesize JV2_PROP_NM(o,1,uSourcePlatApp);

+ (void)initialize
{
    if (self == [proto_across_interactive_webapp_DistributeInfoReq class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_across_interactive_webapp.DistributeInfoReq";
}

@end
