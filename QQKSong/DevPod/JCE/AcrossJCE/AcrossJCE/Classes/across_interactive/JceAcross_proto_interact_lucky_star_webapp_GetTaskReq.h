// **********************************************************************
// This file was generated by a Jce parser!
// Jce version 1.0.0.
// Source file: proto_lucky_star_webapp.jce
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>

/** WEBAPP_CMD_LUCKY_STAR_GET_TASK */
@interface JceAcross_proto_interact_lucky_star_webapp_GetTaskReq : JceObjectV2

/** 主播uin */
@property (nonatomic, strong, JV2_PROP_GS_V2(strEncryptUin,setStrEncryptUin:)) NSString* JV2_PROP_NM(o,0,strEncryptUin) NS_SWIFT_NAME(strEncryptUin);
/** 见 proto_across_interactive_platform_comm.AcrossInteractiveAppId */
@property (nonatomic, assign, JV2_PROP_GS_V2(uAppId,setUAppId:)) JceUInt32 JV2_PROP_NM(o,1,uAppId) NS_SWIFT_NAME(uAppId);

@end
