// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_across_interactive_webapp.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>

@interface proto_across_interactive_webapp_LiveStatsDataReq : JceObjectV2

@property (nonatomic, assign, JV2_PROP_GS_V2(lAnchorId,setLAnchorId:)) TarsInt64 JV2_PROP_NM(o,0,lAnchorId);
@property (nonatomic, assign, JV2_PROP_GS_V2(uSourcePlatApp,setUSourcePlatApp:)) TarsUInt32 JV2_PROP_NM(o,1,uSourcePlatApp);
@property (nonatomic, assign, JV2_PROP_GS_V2(lMask,setLMask:)) TarsInt64 JV2_PROP_NM(o,2,lMask);
@property (nonatomic, retain, JV2_PROP_GS_V2(strShowId,setStrShowId:)) NSString* JV2_PROP_NM(o,3,strShowId);

@end
