// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_webapp_hat_grabbing.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>

@interface proto_mike_hat_grabbing_MikeHatIMMsg : JceObjectV2

@property (nonatomic, retain, JV2_PROP_GS_V2(strMikeId,setStrMikeId:)) NSString* JV2_PROP_NM(o,0,strMikeId);
@property (nonatomic, assign, JV2_PROP_GS_V2(uStatus,setUStatus:)) TarsUInt32 JV2_PROP_NM(o,1,uStatus);
@property (nonatomic, retain, JV2_PROP_GS_V2(mapUserHat,setMapUserHat:)) NSDictionary* JV2_PROP_EX(o,2,mapUserHat,M09ONSStringOproto_mike_hat_grabbing_MikeHatUserItem);
@property (nonatomic, retain, JV2_PROP_GS_V2(strPendantTitle,setStrPendantTitle:)) NSString* JV2_PROP_NM(o,3,strPendantTitle);
@property (nonatomic, retain, JV2_PROP_GS_V2(strPendantImage,setStrPendantImage:)) NSString* JV2_PROP_NM(o,4,strPendantImage);
@property (nonatomic, retain, JV2_PROP_GS_V2(strPendantJumpUrl,setStrPendantJumpUrl:)) NSString* JV2_PROP_NM(o,5,strPendantJumpUrl);
@property (nonatomic, retain, JV2_PROP_GS_V2(strGameExhibitText,setStrGameExhibitText:)) NSString* JV2_PROP_NM(o,6,strGameExhibitText);

@end
