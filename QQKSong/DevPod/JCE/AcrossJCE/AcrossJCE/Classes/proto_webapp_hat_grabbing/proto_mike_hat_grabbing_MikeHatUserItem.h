// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_webapp_hat_grabbing.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>

@interface proto_mike_hat_grabbing_MikeHatUserItem : JceObjectV2

@property (nonatomic, assign, JV2_PROP_GS_V2(llScore,setLlScore:)) TarsInt64 JV2_PROP_NM(o,0,llScore);
@property (nonatomic, retain, JV2_PROP_GS_V2(strUrl,setStrUrl:)) NSString* JV2_PROP_NM(o,1,strUrl);
@property (nonatomic, retain, JV2_PROP_GS_V2(strResourceId,setStrResourceId:)) NSString* JV2_PROP_NM(o,2,strResourceId);
@property (nonatomic, retain, JV2_PROP_GS_V2(strMD5,setStrMD5:)) NSString* JV2_PROP_NM(o,3,strMD5);
@property (nonatomic, retain, JV2_PROP_GS_V2(strHatImage,setStrHatImage:)) NSString* JV2_PROP_NM(o,4,strHatImage);
@property (nonatomic, retain, JV2_PROP_GS_V2(strBackground,setStrBackground:)) NSString* JV2_PROP_NM(o,5,strBackground);

@end
