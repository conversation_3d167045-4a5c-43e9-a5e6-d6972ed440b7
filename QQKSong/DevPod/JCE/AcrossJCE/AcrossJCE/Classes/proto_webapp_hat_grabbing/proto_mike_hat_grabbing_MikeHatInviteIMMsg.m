// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_webapp_hat_grabbing.jce   
// **********************************************************************

#import "proto_mike_hat_grabbing_MikeHatInviteIMMsg.h"
#import "proto_union_mike_v2_MikeUserAccount.h"

@implementation proto_mike_hat_grabbing_MikeHatInviteIMMsg

@synthesize JV2_PROP_NM(o,0,strMikeId);
@synthesize JV2_PROP_NM(o,1,uStatus);
@synthesize JV2_PROP_NM(o,2,stInviter);
@synthesize JV2_PROP_NM(o,3,strNick);
@synthesize JV2_PROP_NM(o,4,strFaceUrl);
@synthesize JV2_PROP_NM(o,5,strInviteMsg);
@synthesize JV2_PROP_NM(o,6,uWaitInterval);

+ (void)initialize
{
    if (self == [proto_mike_hat_grabbing_MikeHatInviteIMMsg class]) {
        [proto_union_mike_v2_MikeUserAccount initialize];
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strMikeId) = DefaultTarsString;
        JV2_PROP(strNick) = DefaultTarsString;
        JV2_PROP(strFaceUrl) = DefaultTarsString;
        JV2_PROP(strInviteMsg) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_mike_hat_grabbing.MikeHatInviteIMMsg";
}

@end
