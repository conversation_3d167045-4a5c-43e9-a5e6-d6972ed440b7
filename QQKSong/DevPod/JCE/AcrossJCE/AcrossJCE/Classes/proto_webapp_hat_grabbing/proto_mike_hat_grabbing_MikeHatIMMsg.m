// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_webapp_hat_grabbing.jce   
// **********************************************************************

#import "proto_mike_hat_grabbing_MikeHatIMMsg.h"

@implementation proto_mike_hat_grabbing_MikeHatIMMsg

@synthesize JV2_PROP_NM(o,0,strMikeId);
@synthesize JV2_PROP_NM(o,1,uStatus);
@synthesize JV2_PROP_EX(o,2,mapUserHat,M09ONSStringOproto_mike_hat_grabbing_MikeHatUserItem);
@synthesize JV2_PROP_NM(o,3,strPendantTitle);
@synthesize JV2_PROP_NM(o,4,strPendantImage);
@synthesize JV2_PROP_NM(o,5,strPendantJumpUrl);
@synthesize JV2_PROP_NM(o,6,strGameExhibitText);

+ (void)initialize
{
    if (self == [proto_mike_hat_grabbing_MikeHatIMMsg class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strMikeId) = DefaultTarsString;
        JV2_PROP(strPendantTitle) = DefaultTarsString;
        JV2_PROP(strPendantImage) = DefaultTarsString;
        JV2_PROP(strPendantJumpUrl) = DefaultTarsString;
        JV2_PROP(strGameExhibitText) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_mike_hat_grabbing.MikeHatIMMsg";
}

@end
