// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_webapp_hat_grabbing.jce   
// **********************************************************************

#import "proto_mike_hat_grabbing_MikeHatUserItem.h"

@implementation proto_mike_hat_grabbing_MikeHatUserItem

@synthesize JV2_PROP_NM(o,0,llScore);
@synthesize JV2_PROP_NM(o,1,strUrl);
@synthesize JV2_PROP_NM(o,2,strResourceId);
@synthesize JV2_PROP_NM(o,3,strMD5);
@synthesize JV2_PROP_NM(o,4,strHatImage);
@synthesize JV2_PROP_NM(o,5,strBackground);

+ (void)initialize
{
    if (self == [proto_mike_hat_grabbing_MikeHatUserItem class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strUrl) = DefaultTarsString;
        JV2_PROP(strResourceId) = DefaultTarsString;
        JV2_PROP(strMD5) = DefaultTarsString;
        JV2_PROP(strHatImage) = DefaultTarsString;
        JV2_PROP(strBackground) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_mike_hat_grabbing.MikeHatUserItem";
}

@end
