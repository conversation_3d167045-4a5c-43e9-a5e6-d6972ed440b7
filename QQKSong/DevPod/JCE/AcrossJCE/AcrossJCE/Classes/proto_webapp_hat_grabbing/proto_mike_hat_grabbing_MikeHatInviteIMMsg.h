// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_webapp_hat_grabbing.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>
@class proto_union_mike_v2_MikeUserAccount;

@interface proto_mike_hat_grabbing_MikeHatInviteIMMsg : JceObjectV2

@property (nonatomic, retain, JV2_PROP_GS_V2(strMikeId,setStrMikeId:)) NSString* JV2_PROP_NM(o,0,strMikeId);
@property (nonatomic, assign, JV2_PROP_GS_V2(uStatus,setUStatus:)) TarsUInt32 JV2_PROP_NM(o,1,uStatus);
@property (nonatomic, retain, JV2_PROP_GS_V2(stInviter,setStInviter:)) proto_union_mike_v2_MikeUserAccount* JV2_PROP_NM(o,2,stInviter);
@property (nonatomic, retain, JV2_PROP_GS_V2(strNick,setStrNick:)) NSString* JV2_PROP_NM(o,3,strNick);
@property (nonatomic, retain, JV2_PROP_GS_V2(strFaceUrl,setStrFaceUrl:)) NSString* JV2_PROP_NM(o,4,strFaceUrl);
@property (nonatomic, retain, JV2_PROP_GS_V2(strInviteMsg,setStrInviteMsg:)) NSString* JV2_PROP_NM(o,5,strInviteMsg);
@property (nonatomic, assign, JV2_PROP_GS_V2(uWaitInterval,setUWaitInterval:)) TarsUInt32 JV2_PROP_NM(o,6,uWaitInterval);

@end
