// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_live_chat_bubble_webapp.jce   
// **********************************************************************

#import "proto_live_chat_bubble_webapp_GetUserBubblesRsp.h"
#import "proto_live_chat_bubble_comm_UserBubbleInfo.h"

@implementation proto_live_chat_bubble_webapp_GetUserBubblesRsp

@synthesize JV2_PROP_EX(o,0,vctBubbleInfo,VOproto_live_chat_bubble_comm_UserBubbleInfo);
@synthesize JV2_PROP_NM(o,1,iRet);
@synthesize JV2_PROP_NM(o,2,strMsg);

+ (void)initialize
{
    if (self == [proto_live_chat_bubble_webapp_GetUserBubblesRsp class]) {
        [proto_live_chat_bubble_comm_UserBubbleInfo initialize];
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strMsg) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_live_chat_bubble_webapp.GetUserBubblesRsp";
}

@end
