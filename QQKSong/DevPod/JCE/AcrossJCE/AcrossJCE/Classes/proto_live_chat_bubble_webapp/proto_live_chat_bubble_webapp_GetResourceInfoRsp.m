// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_live_chat_bubble_webapp.jce   
// **********************************************************************

#import "proto_live_chat_bubble_webapp_GetResourceInfoRsp.h"

@implementation proto_live_chat_bubble_webapp_GetResourceInfoRsp

@synthesize JV2_PROP_EX(o,0,mapResourceInfo,M09ONSNumberOproto_live_chat_bubble_comm_ResourceInfo);
@synthesize JV2_PROP_NM(o,1,iRet);
@synthesize JV2_PROP_NM(o,2,strMsg);

+ (void)initialize
{
    if (self == [proto_live_chat_bubble_webapp_GetResourceInfoRsp class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strMsg) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_live_chat_bubble_webapp.GetResourceInfoRsp";
}

@end
