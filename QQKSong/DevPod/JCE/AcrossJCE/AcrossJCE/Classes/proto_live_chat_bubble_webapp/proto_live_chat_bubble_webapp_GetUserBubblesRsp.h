// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_live_chat_bubble_webapp.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>
@class proto_live_chat_bubble_comm_UserBubbleInfo;

@interface proto_live_chat_bubble_webapp_GetUserBubblesRsp : JceObjectV2

@property (nonatomic, retain, JV2_PROP_GS_V2(vctBubbleInfo,setVctBubbleInfo:)) NSArray* JV2_PROP_EX(o,0,vctBubbleInfo,VOproto_live_chat_bubble_comm_UserBubbleInfo);
@property (nonatomic, assign, JV2_PROP_GS_V2(iRet,setIRet:)) TarsInt32 JV2_PROP_NM(o,1,iRet);
@property (nonatomic, retain, JV2_PROP_GS_V2(strMsg,setStrMsg:)) NSString* JV2_PROP_NM(o,2,strMsg);

@end
