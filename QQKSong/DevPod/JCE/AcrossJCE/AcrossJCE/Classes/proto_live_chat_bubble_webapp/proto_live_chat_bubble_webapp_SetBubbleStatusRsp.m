// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_live_chat_bubble_webapp.jce   
// **********************************************************************

#import "proto_live_chat_bubble_webapp_SetBubbleStatusRsp.h"

@implementation proto_live_chat_bubble_webapp_SetBubbleStatusRsp

@synthesize JV2_PROP_NM(o,0,iRet);
@synthesize JV2_PROP_NM(o,1,strMsg);

+ (void)initialize
{
    if (self == [proto_live_chat_bubble_webapp_SetBubbleStatusRsp class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strMsg) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_live_chat_bubble_webapp.SetBubbleStatusRsp";
}

@end
