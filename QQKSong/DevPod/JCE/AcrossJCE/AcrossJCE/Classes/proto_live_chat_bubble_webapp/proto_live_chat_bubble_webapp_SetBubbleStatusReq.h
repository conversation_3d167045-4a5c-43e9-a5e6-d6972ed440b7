// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_live_chat_bubble_webapp.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>

@interface proto_live_chat_bubble_webapp_SetBubbleStatusReq : JceObjectV2

@property (nonatomic, assign, JV2_PROP_GS_V2(uAppId,setUAppId:)) TarsUInt32 JV2_PROP_NM(o,0,uAppId);
@property (nonatomic, retain, JV2_PROP_GS_V2(strUid,setStrUid:)) NSString* JV2_PROP_NM(o,1,strUid);
@property (nonatomic, assign, JV2_PROP_GS_V2(uBubbleBizType,setUBubbleBizType:)) TarsUInt32 JV2_PROP_NM(o,2,uBubbleBizType);
@property (nonatomic, assign, JV2_PROP_GS_V2(uBubbleStaus,setUBubbleStaus:)) TarsUInt32 JV2_PROP_NM(o,3,uBubbleStaus);

@end
