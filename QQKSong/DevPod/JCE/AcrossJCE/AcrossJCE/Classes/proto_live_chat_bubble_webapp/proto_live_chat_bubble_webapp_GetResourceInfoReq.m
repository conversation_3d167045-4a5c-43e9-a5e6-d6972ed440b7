// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_live_chat_bubble_webapp.jce   
// **********************************************************************

#import "proto_live_chat_bubble_webapp_GetResourceInfoReq.h"

@implementation proto_live_chat_bubble_webapp_GetResourceInfoReq

@synthesize JV2_PROP_NM(o,0,uAppId);
@synthesize JV2_PROP_EX(o,1,vctBubbleIds,VONSNumber);

+ (void)initialize
{
    if (self == [proto_live_chat_bubble_webapp_GetResourceInfoReq class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_live_chat_bubble_webapp.GetResourceInfoReq";
}

@end
