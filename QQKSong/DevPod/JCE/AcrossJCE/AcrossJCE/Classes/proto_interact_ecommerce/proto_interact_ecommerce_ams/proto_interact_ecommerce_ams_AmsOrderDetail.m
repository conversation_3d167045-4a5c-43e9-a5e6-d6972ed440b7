// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_ams.jce   
// **********************************************************************

#import "proto_interact_ecommerce_ams_AmsOrderDetail.h"

@implementation proto_interact_ecommerce_ams_AmsOrderDetail

@synthesize JV2_PROP_NM(o,0,orderNo);
@synthesize JV2_PROP_NM(o,1,source);
@synthesize JV2_PROP_NM(o,2,accountId);
@synthesize JV2_PROP_NM(o,3,traceId);
@synthesize JV2_PROP_NM(o,4,orderPrice);
@synthesize JV2_PROP_NM(o,5,paymentAmount);
@synthesize JV2_PROP_NM(o,6,freight);
@synthesize JV2_PROP_NM(o,7,discount);
@synthesize JV2_PROP_NM(o,8,state);
@synthesize JV2_PROP_NM(o,9,orderCreateTime);
@synthesize JV2_PROP_NM(o,10,orderUpdateTime);
@synthesize JV2_PROP_NM(o,11,orderPayTime);
@synthesize JV2_PROP_NM(o,12,orderFinishTime);
@synthesize JV2_PROP_NM(o,13,orderReturnTime);
@synthesize JV2_PROP_NM(o,14,orderRefundTime);
@synthesize JV2_PROP_NM(o,15,orderSettleTime);
@synthesize JV2_PROP_NM(o,16,productId);
@synthesize JV2_PROP_NM(o,17,productTitle);
@synthesize JV2_PROP_NM(o,18,productPic);
@synthesize JV2_PROP_NM(o,19,productPrice);
@synthesize JV2_PROP_NM(o,20,productNum);
@synthesize JV2_PROP_NM(o,21,commissionRate);
@synthesize JV2_PROP_NM(o,22,commissionAmount);
@synthesize JV2_PROP_NM(o,23,productCategoryId);
@synthesize JV2_PROP_NM(o,24,shopId);
@synthesize JV2_PROP_NM(o,25,shopName);
@synthesize JV2_PROP_NM(o,26,warehouseId);
@synthesize JV2_PROP_NM(o,27,h5Url);
@synthesize JV2_PROP_NM(o,28,miniProgramUrl);
@synthesize JV2_PROP_NM(o,29,miniprogramAppid);
@synthesize JV2_PROP_NM(o,30,miniprogramUsername);
@synthesize JV2_PROP_NM(o,31,tmeBizParam);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_ams_AmsOrderDetail class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(orderNo) = DefaultTarsString;
        JV2_PROP(accountId) = DefaultTarsString;
        JV2_PROP(traceId) = DefaultTarsString;
        JV2_PROP(productId) = DefaultTarsString;
        JV2_PROP(productTitle) = DefaultTarsString;
        JV2_PROP(productPic) = DefaultTarsString;
        JV2_PROP(shopId) = DefaultTarsString;
        JV2_PROP(shopName) = DefaultTarsString;
        JV2_PROP(warehouseId) = DefaultTarsString;
        JV2_PROP(h5Url) = DefaultTarsString;
        JV2_PROP(miniProgramUrl) = DefaultTarsString;
        JV2_PROP(miniprogramAppid) = DefaultTarsString;
        JV2_PROP(miniprogramUsername) = DefaultTarsString;
        JV2_PROP(tmeBizParam) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_ams.AmsOrderDetail";
}

@end
