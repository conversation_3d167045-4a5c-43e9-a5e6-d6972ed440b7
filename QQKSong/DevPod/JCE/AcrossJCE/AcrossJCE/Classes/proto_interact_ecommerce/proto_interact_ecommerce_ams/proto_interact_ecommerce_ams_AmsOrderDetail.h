// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_ams.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>

@interface proto_interact_ecommerce_ams_AmsOrderDetail : JceObjectV2

@property (nonatomic, retain, JV2_PROP_GS_V2(orderNo,setOrderNo:)) NSString* JV2_PROP_NM(o,0,orderNo);
@property (nonatomic, assign, JV2_PROP_GS_V2(source,setSource:)) TarsInt32 JV2_PROP_NM(o,1,source);
@property (nonatomic, retain, JV2_PROP_GS_V2(accountId,setAccountId:)) NSString* JV2_PROP_NM(o,2,accountId);
@property (nonatomic, retain, JV2_PROP_GS_V2(traceId,setTraceId:)) NSString* JV2_PROP_NM(o,3,traceId);
@property (nonatomic, assign, JV2_PROP_GS_V2(orderPrice,setOrderPrice:)) TarsInt64 JV2_PROP_NM(o,4,orderPrice);
@property (nonatomic, assign, JV2_PROP_GS_V2(paymentAmount,setPaymentAmount:)) TarsInt64 JV2_PROP_NM(o,5,paymentAmount);
@property (nonatomic, assign, JV2_PROP_GS_V2(freight,setFreight:)) TarsInt64 JV2_PROP_NM(o,6,freight);
@property (nonatomic, assign, JV2_PROP_GS_V2(discount,setDiscount:)) TarsInt64 JV2_PROP_NM(o,7,discount);
@property (nonatomic, assign, JV2_PROP_GS_V2(state,setState:)) TarsInt32 JV2_PROP_NM(o,8,state);
@property (nonatomic, assign, JV2_PROP_GS_V2(orderCreateTime,setOrderCreateTime:)) TarsInt64 JV2_PROP_NM(o,9,orderCreateTime);
@property (nonatomic, assign, JV2_PROP_GS_V2(orderUpdateTime,setOrderUpdateTime:)) TarsInt64 JV2_PROP_NM(o,10,orderUpdateTime);
@property (nonatomic, assign, JV2_PROP_GS_V2(orderPayTime,setOrderPayTime:)) TarsInt64 JV2_PROP_NM(o,11,orderPayTime);
@property (nonatomic, assign, JV2_PROP_GS_V2(orderFinishTime,setOrderFinishTime:)) TarsInt64 JV2_PROP_NM(o,12,orderFinishTime);
@property (nonatomic, assign, JV2_PROP_GS_V2(orderReturnTime,setOrderReturnTime:)) TarsInt64 JV2_PROP_NM(o,13,orderReturnTime);
@property (nonatomic, assign, JV2_PROP_GS_V2(orderRefundTime,setOrderRefundTime:)) TarsInt64 JV2_PROP_NM(o,14,orderRefundTime);
@property (nonatomic, assign, JV2_PROP_GS_V2(orderSettleTime,setOrderSettleTime:)) TarsInt64 JV2_PROP_NM(o,15,orderSettleTime);
@property (nonatomic, retain, JV2_PROP_GS_V2(productId,setProductId:)) NSString* JV2_PROP_NM(o,16,productId);
@property (nonatomic, retain, JV2_PROP_GS_V2(productTitle,setProductTitle:)) NSString* JV2_PROP_NM(o,17,productTitle);
@property (nonatomic, retain, JV2_PROP_GS_V2(productPic,setProductPic:)) NSString* JV2_PROP_NM(o,18,productPic);
@property (nonatomic, assign, JV2_PROP_GS_V2(productPrice,setProductPrice:)) TarsInt64 JV2_PROP_NM(o,19,productPrice);
@property (nonatomic, assign, JV2_PROP_GS_V2(productNum,setProductNum:)) TarsInt64 JV2_PROP_NM(o,20,productNum);
@property (nonatomic, assign, JV2_PROP_GS_V2(commissionRate,setCommissionRate:)) TarsInt64 JV2_PROP_NM(o,21,commissionRate);
@property (nonatomic, assign, JV2_PROP_GS_V2(commissionAmount,setCommissionAmount:)) TarsInt64 JV2_PROP_NM(o,22,commissionAmount);
@property (nonatomic, assign, JV2_PROP_GS_V2(productCategoryId,setProductCategoryId:)) TarsInt64 JV2_PROP_NM(o,23,productCategoryId);
@property (nonatomic, retain, JV2_PROP_GS_V2(shopId,setShopId:)) NSString* JV2_PROP_NM(o,24,shopId);
@property (nonatomic, retain, JV2_PROP_GS_V2(shopName,setShopName:)) NSString* JV2_PROP_NM(o,25,shopName);
@property (nonatomic, retain, JV2_PROP_GS_V2(warehouseId,setWarehouseId:)) NSString* JV2_PROP_NM(o,26,warehouseId);
@property (nonatomic, retain, JV2_PROP_GS_V2(h5Url,setH5Url:)) NSString* JV2_PROP_NM(o,27,h5Url);
@property (nonatomic, retain, JV2_PROP_GS_V2(miniProgramUrl,setMiniProgramUrl:)) NSString* JV2_PROP_NM(o,28,miniProgramUrl);
@property (nonatomic, retain, JV2_PROP_GS_V2(miniprogramAppid,setMiniprogramAppid:)) NSString* JV2_PROP_NM(o,29,miniprogramAppid);
@property (nonatomic, retain, JV2_PROP_GS_V2(miniprogramUsername,setMiniprogramUsername:)) NSString* JV2_PROP_NM(o,30,miniprogramUsername);
@property (nonatomic, retain, JV2_PROP_GS_V2(tmeBizParam,setTmeBizParam:)) NSString* JV2_PROP_NM(o,31,tmeBizParam);

@end
