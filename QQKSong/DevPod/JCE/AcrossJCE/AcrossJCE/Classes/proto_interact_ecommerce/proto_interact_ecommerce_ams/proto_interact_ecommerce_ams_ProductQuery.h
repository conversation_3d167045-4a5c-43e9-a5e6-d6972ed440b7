// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_ams.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>
@class proto_interact_ecommerce_ams_Range;

@interface proto_interact_ecommerce_ams_ProductQuery : JceObjectV2

@property (nonatomic, retain, JV2_PROP_GS_V2(keyword,setKeyword:)) NSString* JV2_PROP_NM(o,0,keyword);
@property (nonatomic, retain, JV2_PROP_GS_V2(categoryId,setCategoryId:)) NSArray* JV2_PROP_EX(o,1,categoryId,VONSString);
@property (nonatomic, retain, JV2_PROP_GS_V2(source,setSource:)) NSArray* JV2_PROP_EX(o,2,source,VONSNumber);
@property (nonatomic, retain, JV2_PROP_GS_V2(price,setPrice:)) proto_interact_ecommerce_ams_Range* JV2_PROP_NM(o,3,price);
@property (nonatomic, retain, JV2_PROP_GS_V2(commission,setCommission:)) proto_interact_ecommerce_ams_Range* JV2_PROP_NM(o,4,commission);
@property (nonatomic, retain, JV2_PROP_GS_V2(commissionRate,setCommissionRate:)) proto_interact_ecommerce_ams_Range* JV2_PROP_NM(o,5,commissionRate);
@property (nonatomic, assign, JV2_PROP_GS_V2(searchType,setSearchType:)) TarsUInt32 JV2_PROP_NM(o,6,searchType);

@end
