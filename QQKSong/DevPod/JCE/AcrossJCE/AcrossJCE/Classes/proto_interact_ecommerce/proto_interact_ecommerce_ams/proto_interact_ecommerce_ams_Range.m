// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_ams.jce   
// **********************************************************************

#import "proto_interact_ecommerce_ams_Range.h"

@implementation proto_interact_ecommerce_ams_Range

@synthesize JV2_PROP_NM(o,0,min);
@synthesize JV2_PROP_NM(o,1,max);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_ams_Range class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_ams.Range";
}

@end
