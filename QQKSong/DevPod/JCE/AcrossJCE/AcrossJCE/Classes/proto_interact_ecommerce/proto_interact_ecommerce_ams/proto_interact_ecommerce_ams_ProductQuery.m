// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_ams.jce   
// **********************************************************************

#import "proto_interact_ecommerce_ams_ProductQuery.h"
#import "proto_interact_ecommerce_ams_Range.h"

@implementation proto_interact_ecommerce_ams_ProductQuery

@synthesize JV2_PROP_NM(o,0,keyword);
@synthesize JV2_PROP_EX(o,1,categoryId,VONSString);
@synthesize JV2_PROP_EX(o,2,source,VONSNumber);
@synthesize JV2_PROP_NM(o,3,price);
@synthesize JV2_PROP_NM(o,4,commission);
@synthesize JV2_PROP_NM(o,5,commissionRate);
@synthesize JV2_PROP_NM(o,6,searchType);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_ams_ProductQuery class]) {
        [proto_interact_ecommerce_ams_Range initialize];
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(keyword) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_ams.ProductQuery";
}

@end
