// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_product.jce   
// **********************************************************************

#import "proto_interact_ecommerce_product_ProductSortInfo.h"

@implementation proto_interact_ecommerce_product_ProductSortInfo

@synthesize JV2_PROP_NM(o,0,sortId);
@synthesize JV2_PROP_NM(o,1,sortName);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_product_ProductSortInfo class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(sortName) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_product.ProductSortInfo";
}

@end
