// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_product.jce   
// **********************************************************************

#import "proto_interact_ecommerce_product_ProductPlatInfo.h"

@implementation proto_interact_ecommerce_product_ProductPlatInfo

@synthesize JV2_PROP_NM(o,0,platId);
@synthesize JV2_PROP_NM(o,1,platName);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_product_ProductPlatInfo class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(platName) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_product.ProductPlatInfo";
}

@end
