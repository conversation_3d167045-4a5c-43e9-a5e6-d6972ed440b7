// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_product.jce   
// **********************************************************************

#import "proto_interact_ecommerce_product_CategoryInfo.h"

@implementation proto_interact_ecommerce_product_CategoryInfo

@synthesize JV2_PROP_NM(o,0,firstCategoryId);
@synthesize JV2_PROP_NM(o,1,firstCategoryName);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_product_CategoryInfo class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(firstCategoryName) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_product.CategoryInfo";
}

@end
