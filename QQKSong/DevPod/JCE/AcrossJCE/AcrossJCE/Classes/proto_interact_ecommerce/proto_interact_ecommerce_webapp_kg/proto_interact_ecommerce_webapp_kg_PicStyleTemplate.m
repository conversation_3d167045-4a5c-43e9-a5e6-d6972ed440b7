// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_webapp_kg.jce   
// **********************************************************************

#import "proto_interact_ecommerce_webapp_kg_PicStyleTemplate.h"
#import "proto_interact_ecommerce_comm_ProductDetailURL.h"
#import "proto_interact_ecommerce_comm_ProductExtraLable.h"

@implementation proto_interact_ecommerce_webapp_kg_PicStyleTemplate

@synthesize JV2_PROP_NM(o,0,strJumpLink);
@synthesize JV2_PROP_NM(o,1,strTitle);
@synthesize JV2_PROP_NM(o,2,strPic);
@synthesize JV2_PROP_NM(o,3,strContent);
@synthesize JV2_PROP_NM(o,4,stProductDetailURL);
@synthesize JV2_PROP_NM(o,5,source);
@synthesize JV2_PROP_EX(o,6,vctProductExtraLable,VOproto_interact_ecommerce_comm_ProductExtraLable);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_webapp_kg_PicStyleTemplate class]) {
        [proto_interact_ecommerce_comm_ProductDetailURL initialize];
        [proto_interact_ecommerce_comm_ProductExtraLable initialize];
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strJumpLink) = DefaultTarsString;
        JV2_PROP(strTitle) = DefaultTarsString;
        JV2_PROP(strPic) = DefaultTarsString;
        JV2_PROP(strContent) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_webapp_kg.PicStyleTemplate";
}

@end
