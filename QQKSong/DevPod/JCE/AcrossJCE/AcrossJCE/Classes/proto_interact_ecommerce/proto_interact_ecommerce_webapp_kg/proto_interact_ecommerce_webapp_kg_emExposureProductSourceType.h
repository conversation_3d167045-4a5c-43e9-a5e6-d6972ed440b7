// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_webapp_kg.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>

enum {
    proto_interact_ecommerce_webapp_kg_emExposureProductSourceType_EM_EXPOSURE_PRODUCT_SOURCE_MALL_TAB_COMMON = 1,
    proto_interact_ecommerce_webapp_kg_emExposureProductSourceType_EM_EXPOSURE_PRODUCT_SOURCE_MALL_TAB_ANCHOR_REC = 2,
    proto_interact_ecommerce_webapp_kg_emExposureProductSourceType_EM_EXPOSURE_PRODUCT_SOURCE_MALL_TAB_HOT_REC = 3,
    proto_interact_ecommerce_webapp_kg_emExposureProductSourceType_EM_EXPOSURE_PRODUCT_SOURCE_REC_FEED_PROMOTE = 4,
    proto_interact_ecommerce_webapp_kg_emExposureProductSourceType_EM_EXPOSURE_PRODUCT_SOURCE_UGC_PAGE_PROMOTE = 5,
    proto_interact_ecommerce_webapp_kg_emExposureProductSourceType_EM_EXPOSURE_PRODUCT_SOURCE_HOME_PAGE_MASTER = 6,
    proto_interact_ecommerce_webapp_kg_emExposureProductSourceType_EM_EXPOSURE_PRODUCT_SOURCE_HOME_PAGE_GIEST = 7,
    proto_interact_ecommerce_webapp_kg_emExposureProductSourceType_EM_EXPOSURE_PRODUCT_SOURCE_LIVE_ROOM = 8,
    proto_interact_ecommerce_webapp_kg_emExposureProductSourceType_EM_EXPOSURE_PRODUCT_SOURCE_LIVE_ACTIVITY_PAGE = 9,
    proto_interact_ecommerce_webapp_kg_emExposureProductSourceType_EM_EXPOSURE_PRODUCT_SOURCE_UGC_COMPETITION = 10
};
#define proto_interact_ecommerce_webapp_kg_emExposureProductSourceType TarsInt32

