// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_webapp_kg.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>
#import "proto_interact_ecommerce_comm_emEcommerceScene.h"
#import "proto_interact_ecommerce_comm_emProductLabelEntrySource.h"

@interface proto_interact_ecommerce_webapp_kg_GetProductListReq : JceObjectV2

@property (nonatomic, assign, JV2_PROP_GS_V2(lAnchorId,setLAnchorId:)) TarsInt64 JV2_PROP_NM(o,0,lAnchorId);
@property (nonatomic, assign, JV2_PROP_GS_V2(uPageNo,setUPageNo:)) TarsUInt32 JV2_PROP_NM(o,1,uPageNo);
@property (nonatomic, assign, JV2_PROP_GS_V2(uPageSize,setUPageSize:)) TarsUInt32 JV2_PROP_NM(o,2,uPageSize);
@property (nonatomic, assign, JV2_PROP_GS_V2(uReqSource,setUReqSource:)) TarsUInt32 JV2_PROP_NM(o,3,uReqSource);
@property (nonatomic, assign, JV2_PROP_GS_V2(iEcommerceScene,setIEcommerceScene:)) proto_interact_ecommerce_comm_emEcommerceScene JV2_PROP_NM(o,4,iEcommerceScene);
@property (nonatomic, retain, JV2_PROP_GS_V2(strExt,setStrExt:)) NSString* JV2_PROP_NM(o,5,strExt);
@property (nonatomic, assign, JV2_PROP_GS_V2(iLabelEntrySource,setILabelEntrySource:)) proto_interact_ecommerce_comm_emProductLabelEntrySource JV2_PROP_NM(o,6,iLabelEntrySource);

@end
