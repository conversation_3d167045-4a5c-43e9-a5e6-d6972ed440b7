// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_webapp_kg.jce   
// **********************************************************************

#import "proto_interact_ecommerce_webapp_kg_KgSetUgcProductReq.h"

@implementation proto_interact_ecommerce_webapp_kg_KgSetUgcProductReq

@synthesize JV2_PROP_NM(o,0,strUgcId);
@synthesize JV2_PROP_NM(o,1,uAnchorId);
@synthesize JV2_PROP_NM(o,2,strProductId);
@synthesize JV2_PROP_NM(o,3,strTitle);
@synthesize JV2_PROP_NM(o,4,iSource);
@synthesize JV2_PROP_NM(o,6,uReqSource);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_webapp_kg_KgSetUgcProductReq class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strUgcId) = DefaultTarsString;
        JV2_PROP(strProductId) = DefaultTarsString;
        JV2_PROP(strTitle) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_webapp_kg.KgSetUgcProductReq";
}

@end
