// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_webapp_kg.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>
@class proto_interact_ecommerce_comm_ProductDetailURL;
@class proto_interact_ecommerce_comm_ProductExtraLable;

@interface proto_interact_ecommerce_webapp_kg_PicStyleTemplate : JceObjectV2

@property (nonatomic, retain, JV2_PROP_GS_V2(strJumpLink,setStrJumpLink:)) NSString* JV2_PROP_NM(o,0,strJumpLink);
@property (nonatomic, retain, JV2_PROP_GS_V2(strTitle,setStrTitle:)) NSString* JV2_PROP_NM(o,1,strTitle);
@property (nonatomic, retain, JV2_PROP_GS_V2(strPic,setStrPic:)) NSString* JV2_PROP_NM(o,2,strPic);
@property (nonatomic, retain, JV2_PROP_GS_V2(strContent,setStrContent:)) NSString* JV2_PROP_NM(o,3,strContent);
@property (nonatomic, retain, JV2_PROP_GS_V2(stProductDetailURL,setStProductDetailURL:)) proto_interact_ecommerce_comm_ProductDetailURL* JV2_PROP_NM(o,4,stProductDetailURL);
@property (nonatomic, assign, JV2_PROP_GS_V2(source,setSource:)) TarsInt32 JV2_PROP_NM(o,5,source);
@property (nonatomic, retain, JV2_PROP_GS_V2(vctProductExtraLable,setVctProductExtraLable:)) NSArray* JV2_PROP_EX(o,6,vctProductExtraLable,VOproto_interact_ecommerce_comm_ProductExtraLable);

@end
