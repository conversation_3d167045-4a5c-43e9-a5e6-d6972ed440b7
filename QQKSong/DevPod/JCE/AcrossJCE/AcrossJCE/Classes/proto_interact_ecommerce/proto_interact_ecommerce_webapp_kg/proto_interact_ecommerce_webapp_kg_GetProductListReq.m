// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_webapp_kg.jce   
// **********************************************************************

#import "proto_interact_ecommerce_webapp_kg_GetProductListReq.h"

@implementation proto_interact_ecommerce_webapp_kg_GetProductListReq

@synthesize JV2_PROP_NM(o,0,lAnchorId);
@synthesize JV2_PROP_NM(o,1,uPageNo);
@synthesize JV2_PROP_NM(o,2,uPageSize);
@synthesize JV2_PROP_NM(o,3,uReqSource);
@synthesize JV2_PROP_NM(o,4,iEcommerceScene);
@synthesize JV2_PROP_NM(o,5,strExt);
@synthesize JV2_PROP_NM(o,6,iLabelEntrySource);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_webapp_kg_GetProductListReq class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strExt) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_webapp_kg.GetProductListReq";
}

@end
