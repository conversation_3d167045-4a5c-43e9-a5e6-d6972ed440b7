// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_webapp_kg.jce   
// **********************************************************************

#import "proto_interact_ecommerce_webapp_kg_KgGetUgcProductRsp.h"
#import "proto_interact_ecommerce_comm_ProductInfo.h"

@implementation proto_interact_ecommerce_webapp_kg_KgGetUgcProductRsp

@synthesize JV2_PROP_NM(o,0,iHasProduct);
@synthesize JV2_PROP_NM(o,1,iUgcProductStyle);
@synthesize JV2_PROP_NM(o,2,bytes);
@synthesize JV2_PROP_NM(o,3,strProductId);
@synthesize JV2_PROP_NM(o,4,stProductInfo);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_webapp_kg_KgGetUgcProductRsp class]) {
        [proto_interact_ecommerce_comm_ProductInfo initialize];
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strProductId) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_webapp_kg.KgGetUgcProductRsp";
}

@end
