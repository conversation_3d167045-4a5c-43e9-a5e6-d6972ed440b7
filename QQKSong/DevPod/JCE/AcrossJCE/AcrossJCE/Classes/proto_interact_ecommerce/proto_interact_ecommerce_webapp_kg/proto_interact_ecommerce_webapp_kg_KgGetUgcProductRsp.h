// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_webapp_kg.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>
@class proto_interact_ecommerce_comm_ProductInfo;
#import "proto_interact_ecommerce_webapp_kg_emUgcProductStyle.h"

@interface proto_interact_ecommerce_webapp_kg_KgGetUgcProductRsp : JceObjectV2

@property (nonatomic, assign, JV2_PROP_GS_V2(iHasProduct,setIHasProduct:)) TarsInt32 JV2_PROP_NM(o,0,iHasProduct);
@property (nonatomic, assign, JV2_PROP_GS_V2(iUgcProductStyle,setIUgcProductStyle:)) proto_interact_ecommerce_webapp_kg_emUgcProductStyle JV2_PROP_NM(o,1,iUgcProductStyle);
@property (nonatomic, retain, JV2_PROP_GS_V2(bytes,setBytes:)) NSData* JV2_PROP_NM(o,2,bytes);
@property (nonatomic, retain, JV2_PROP_GS_V2(strProductId,setStrProductId:)) NSString* JV2_PROP_NM(o,3,strProductId);
@property (nonatomic, retain, JV2_PROP_GS_V2(stProductInfo,setStProductInfo:)) proto_interact_ecommerce_comm_ProductInfo* JV2_PROP_NM(o,4,stProductInfo);

@end
