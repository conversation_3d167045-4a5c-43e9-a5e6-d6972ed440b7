// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_webapp_kg.jce   
// **********************************************************************

#import "proto_interact_ecommerce_webapp_kg_KgBatchGetUgcProductReq.h"
#import "proto_interact_ecommerce_webapp_kg_KgGetUgcProductReq.h"

@implementation proto_interact_ecommerce_webapp_kg_KgBatchGetUgcProductReq

@synthesize JV2_PROP_EX(o,0,vecReq,VOproto_interact_ecommerce_webapp_kg_KgGetUgcProductReq);
@synthesize JV2_PROP_NM(o,1,uReqSource);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_webapp_kg_KgBatchGetUgcProductReq class]) {
        [proto_interact_ecommerce_webapp_kg_KgGetUgcProductReq initialize];
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_webapp_kg.KgBatchGetUgcProductReq";
}

@end
