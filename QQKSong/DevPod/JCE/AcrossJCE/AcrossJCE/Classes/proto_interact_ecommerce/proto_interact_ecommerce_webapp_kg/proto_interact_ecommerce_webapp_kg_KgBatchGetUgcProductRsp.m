// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_webapp_kg.jce   
// **********************************************************************

#import "proto_interact_ecommerce_webapp_kg_KgBatchGetUgcProductRsp.h"

@implementation proto_interact_ecommerce_webapp_kg_KgBatchGetUgcProductRsp

@synthesize JV2_PROP_EX(o,0,mapRsp,M09ONSStringOproto_interact_ecommerce_webapp_kg_KgGetUgcProductRsp);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_webapp_kg_KgBatchGetUgcProductRsp class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_webapp_kg.KgBatchGetUgcProductRsp";
}

@end
