// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_webapp.jce   
// **********************************************************************

#import "proto_interact_ecommerce_webapp_SetAnchorQualificationRsp.h"

@implementation proto_interact_ecommerce_webapp_SetAnchorQualificationRsp

@synthesize JV2_PROP_NM(o,0,iRet);
@synthesize JV2_PROP_NM(o,1,strMsg);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_webapp_SetAnchorQualificationRsp class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strMsg) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_webapp.SetAnchorQualificationRsp";
}

@end
