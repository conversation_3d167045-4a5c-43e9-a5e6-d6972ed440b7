// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_webapp.jce   
// **********************************************************************

#import "proto_interact_ecommerce_webapp_SetLivingCoverReq.h"

@implementation proto_interact_ecommerce_webapp_SetLivingCoverReq

@synthesize JV2_PROP_NM(o,0,lAnchorId);
@synthesize JV2_PROP_NM(o,1,strAnchorId);
@synthesize JV2_PROP_NM(o,2,uAppId);
@synthesize JV2_PROP_NM(o,3,lProductId);
@synthesize JV2_PROP_NM(o,4,strProductId);
@synthesize JV2_PROP_NM(o,5,isCancelProductCover);
@synthesize JV2_PROP_NM(o,6,source);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_webapp_SetLivingCoverReq class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strAnchorId) = DefaultTarsString;
        JV2_PROP(strProductId) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_webapp.SetLivingCoverReq";
}

@end
