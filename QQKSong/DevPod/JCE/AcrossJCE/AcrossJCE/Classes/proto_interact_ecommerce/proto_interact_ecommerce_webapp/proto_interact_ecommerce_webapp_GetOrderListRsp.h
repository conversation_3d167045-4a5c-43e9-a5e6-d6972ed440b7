// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_webapp.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>
@class proto_interact_ecommerce_comm_OrderInfo;

@interface proto_interact_ecommerce_webapp_GetOrderListRsp : JceObjectV2

@property (nonatomic, retain, JV2_PROP_GS_V2(vctOrderList,setVctOrderList:)) NSArray* JV2_PROP_EX(o,0,vct<PERSON><PERSON><PERSON><PERSON>ist,VOproto_interact_ecommerce_comm_OrderInfo);
@property (nonatomic, assign, JV2_PROP_GS_V2(uHasMore,setUHasMore:)) TarsUInt32 JV2_PROP_NM(o,1,uHasMore);
@property (nonatomic, retain, JV2_PROP_GS_V2(strPassBack,setStrPassBack:)) NSString* JV2_PROP_NM(o,2,strPassBack);
@property (nonatomic, assign, JV2_PROP_GS_V2(uTotalNum,setUTotalNum:)) TarsUInt32 JV2_PROP_NM(o,3,uTotalNum);

@end
