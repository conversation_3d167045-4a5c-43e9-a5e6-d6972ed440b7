// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_webapp.jce   
// **********************************************************************

#import "proto_interact_ecommerce_webapp_GetCustomerHotlineRsp.h"
#import "proto_interact_ecommerce_comm_CustomerHotlineInfo.h"

@implementation proto_interact_ecommerce_webapp_GetCustomerHotlineRsp

@synthesize JV2_PROP_NM(o,0,iRet);
@synthesize JV2_PROP_NM(o,1,strMsg);
@synthesize JV2_PROP_EX(o,2,customerInfo,VOproto_interact_ecommerce_comm_CustomerHotlineInfo);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_webapp_GetCustomerHotlineRsp class]) {
        [proto_interact_ecommerce_comm_CustomerHotlineInfo initialize];
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strMsg) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_webapp.GetCustomerHotlineRsp";
}

@end
