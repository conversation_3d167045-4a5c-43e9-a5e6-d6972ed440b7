// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_webapp.jce   
// **********************************************************************

#import "proto_interact_ecommerce_webapp_GetCustomerHotlineReq.h"

@implementation proto_interact_ecommerce_webapp_GetCustomerHotlineReq

@synthesize JV2_PROP_NM(o,0,uAppId);
@synthesize JV2_PROP_NM(o,1,ID);
@synthesize JV2_PROP_NM(o,2,idType);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_webapp_GetCustomerHotlineReq class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(ID) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_webapp.GetCustomerHotlineReq";
}

@end
