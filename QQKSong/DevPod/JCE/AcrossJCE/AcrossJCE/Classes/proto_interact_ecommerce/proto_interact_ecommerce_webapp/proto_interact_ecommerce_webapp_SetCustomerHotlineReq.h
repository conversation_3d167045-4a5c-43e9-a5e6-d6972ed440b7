// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_webapp.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>
@class proto_interact_ecommerce_comm_CustomerHotlineInfo;

@interface proto_interact_ecommerce_webapp_SetCustomerHotlineReq : JceObjectV2

@property (nonatomic, assign, JV2_PROP_GS_V2(guildId,setGuildId:)) TarsInt64 JV2_PROP_NM(o,0,guildId);
@property (nonatomic, assign, JV2_PROP_GS_V2(uAppId,setUAppId:)) TarsUInt32 JV2_PROP_NM(o,1,uAppId);
@property (nonatomic, assign, JV2_PROP_GS_V2(oprType,setOprType:)) TarsUInt32 JV2_PROP_NM(o,2,oprType);
@property (nonatomic, retain, JV2_PROP_GS_V2(customerInfo,setCustomerInfo:)) proto_interact_ecommerce_comm_CustomerHotlineInfo* JV2_PROP_NM(o,3,customerInfo);

@end
