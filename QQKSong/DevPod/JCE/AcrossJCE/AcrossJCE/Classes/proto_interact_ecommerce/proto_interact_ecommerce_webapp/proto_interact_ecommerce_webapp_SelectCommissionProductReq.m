// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_webapp.jce   
// **********************************************************************

#import "proto_interact_ecommerce_webapp_SelectCommissionProductReq.h"
#import "proto_interact_ecommerce_ams_PdtSelectSort.h"
#import "proto_interact_ecommerce_ams_ProductQuery.h"

@implementation proto_interact_ecommerce_webapp_SelectCommissionProductReq

@synthesize JV2_PROP_NM(o,0,uAppId);
@synthesize JV2_PROP_NM(o,1,productQuery);
@synthesize JV2_PROP_NM(o,2,sort);
@synthesize JV2_PROP_NM(o,3,pageNo);
@synthesize JV2_PROP_NM(o,4,pageSize);
@synthesize JV2_PROP_EX(o,5,links,VONSString);
@synthesize JV2_PROP_EX(o,6,productIDs,VONSString);
@synthesize JV2_PROP_NM(o,7,source);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_webapp_SelectCommissionProductReq class]) {
        [proto_interact_ecommerce_ams_PdtSelectSort initialize];
        [proto_interact_ecommerce_ams_ProductQuery initialize];
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_webapp.SelectCommissionProductReq";
}

@end
