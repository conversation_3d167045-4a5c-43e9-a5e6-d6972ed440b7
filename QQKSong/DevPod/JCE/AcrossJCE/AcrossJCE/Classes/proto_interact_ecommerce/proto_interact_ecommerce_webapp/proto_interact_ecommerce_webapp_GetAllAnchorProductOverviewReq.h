// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_webapp.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>
@class proto_interact_ecommerce_comm_AnchorProductOverviewFilter;

@interface proto_interact_ecommerce_webapp_GetAllAnchorProductOverviewReq : JceObjectV2

@property (nonatomic, assign, JV2_PROP_GS_V2(lGuildId,setLGuildId:)) TarsInt64 JV2_PROP_NM(o,0,lGuildId);
@property (nonatomic, assign, JV2_PROP_GS_V2(uAppId,setUAppId:)) TarsUInt32 JV2_PROP_NM(o,1,uAppId);
@property (nonatomic, assign, JV2_PROP_GS_V2(uPageNo,setUPageNo:)) TarsUInt32 JV2_PROP_NM(o,2,uPageNo);
@property (nonatomic, assign, JV2_PROP_GS_V2(uPageSize,setUPageSize:)) TarsUInt32 JV2_PROP_NM(o,3,uPageSize);
@property (nonatomic, retain, JV2_PROP_GS_V2(filters,setFilters:)) NSArray* JV2_PROP_EX(o,4,filters,VOproto_interact_ecommerce_comm_AnchorProductOverviewFilter);
@property (nonatomic, retain, JV2_PROP_GS_V2(strIdentity,setStrIdentity:)) NSString* JV2_PROP_NM(o,5,strIdentity);

@end
