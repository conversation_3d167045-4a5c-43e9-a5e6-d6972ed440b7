// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_webapp.jce   
// **********************************************************************

#import "proto_interact_ecommerce_webapp_GetOrderListReq.h"

@implementation proto_interact_ecommerce_webapp_GetOrderListReq

@synthesize JV2_PROP_NM(o,0,uAppId);
@synthesize JV2_PROP_NM(o,1,strUid);
@synthesize JV2_PROP_NM(o,2,uOrderStatus);
@synthesize JV2_PROP_NM(o,3,strPassBack);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_webapp_GetOrderListReq class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strUid) = DefaultTarsString;
        JV2_PROP(strPassBack) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_webapp.GetOrderListReq";
}

@end
