// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_webapp.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>
#import "proto_interact_ecommerce_comm_emEcommerceScene.h"
#import "proto_interact_ecommerce_comm_emProductInfoMask.h"
#import "proto_interact_ecommerce_comm_emProductLabelEntrySource.h"

@interface proto_interact_ecommerce_webapp_GetProductListReq : JceObjectV2

@property (nonatomic, assign, JV2_PROP_GS_V2(lAnchorId,setLAnchorId:)) TarsInt64 JV2_PROP_NM(o,0,lAnchorId);
@property (nonatomic, retain, JV2_PROP_GS_V2(strAnchorId,setStrAnchorId:)) NSString* JV2_PROP_NM(o,1,strAnchorId);
@property (nonatomic, assign, JV2_PROP_GS_V2(uAppId,setUAppId:)) TarsUInt32 JV2_PROP_NM(o,2,uAppId);
@property (nonatomic, assign, JV2_PROP_GS_V2(uPageNo,setUPageNo:)) TarsUInt32 JV2_PROP_NM(o,3,uPageNo);
@property (nonatomic, assign, JV2_PROP_GS_V2(uPageSize,setUPageSize:)) TarsUInt32 JV2_PROP_NM(o,4,uPageSize);
@property (nonatomic, retain, JV2_PROP_GS_V2(strEncrytAnchorId,setStrEncrytAnchorId:)) NSString* JV2_PROP_NM(o,5,strEncrytAnchorId);
@property (nonatomic, assign, JV2_PROP_GS_V2(iEcommerceScene,setIEcommerceScene:)) proto_interact_ecommerce_comm_emEcommerceScene JV2_PROP_NM(o,6,iEcommerceScene);
@property (nonatomic, assign, JV2_PROP_GS_V2(bGetAll,setBGetAll:)) TarsBool JV2_PROP_NM(o,7,bGetAll);
@property (nonatomic, assign, JV2_PROP_GS_V2(iProductInfoMask,setIProductInfoMask:)) proto_interact_ecommerce_comm_emProductInfoMask JV2_PROP_NM(o,8,iProductInfoMask);
@property (nonatomic, retain, JV2_PROP_GS_V2(strExt,setStrExt:)) NSString* JV2_PROP_NM(o,9,strExt);
@property (nonatomic, assign, JV2_PROP_GS_V2(iLabelEntrySource,setILabelEntrySource:)) proto_interact_ecommerce_comm_emProductLabelEntrySource JV2_PROP_NM(o,10,iLabelEntrySource);

@end
