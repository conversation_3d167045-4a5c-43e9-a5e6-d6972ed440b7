// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_webapp.jce   
// **********************************************************************

#import "proto_interact_ecommerce_webapp_SetCustomerHotlineReq.h"
#import "proto_interact_ecommerce_comm_CustomerHotlineInfo.h"

@implementation proto_interact_ecommerce_webapp_SetCustomerHotlineReq

@synthesize JV2_PROP_NM(o,0,guildId);
@synthesize JV2_PROP_NM(o,1,uAppId);
@synthesize JV2_PROP_NM(o,2,oprType);
@synthesize JV2_PROP_NM(o,3,customerInfo);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_webapp_SetCustomerHotlineReq class]) {
        [proto_interact_ecommerce_comm_CustomerHotlineInfo initialize];
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_webapp.SetCustomerHotlineReq";
}

@end
