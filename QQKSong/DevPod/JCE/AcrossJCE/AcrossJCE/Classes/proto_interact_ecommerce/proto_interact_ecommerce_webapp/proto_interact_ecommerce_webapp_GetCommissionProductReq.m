// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_webapp.jce   
// **********************************************************************

#import "proto_interact_ecommerce_webapp_GetCommissionProductReq.h"
#import "proto_interact_ecommerce_comm_SeachFilter.h"

@implementation proto_interact_ecommerce_webapp_GetCommissionProductReq

@synthesize JV2_PROP_NM(o,0,uAppId);
@synthesize JV2_PROP_NM(o,1,pageNo);
@synthesize JV2_PROP_NM(o,2,pageSize);
@synthesize JV2_PROP_EX(o,3,filters,VOproto_interact_ecommerce_comm_SeachFilter);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_webapp_GetCommissionProductReq class]) {
        [proto_interact_ecommerce_comm_SeachFilter initialize];
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_webapp.GetCommissionProductReq";
}

@end
