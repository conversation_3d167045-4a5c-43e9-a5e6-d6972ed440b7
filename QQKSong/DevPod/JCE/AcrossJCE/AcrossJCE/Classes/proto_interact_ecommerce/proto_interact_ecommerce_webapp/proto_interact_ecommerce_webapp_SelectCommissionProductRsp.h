// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_webapp.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>
@class proto_interact_ecommerce_comm_ProductInfo;

@interface proto_interact_ecommerce_webapp_SelectCommissionProductRsp : JceObjectV2

@property (nonatomic, retain, JV2_PROP_GS_V2(products,setProducts:)) NSArray* JV2_PROP_EX(o,0,products,VOproto_interact_ecommerce_comm_ProductInfo);
@property (nonatomic, assign, JV2_PROP_GS_V2(totalCount,setTotalCount:)) TarsUInt32 JV2_PROP_NM(o,1,totalCount);
@property (nonatomic, assign, JV2_PROP_GS_V2(iRet,setIRet:)) TarsInt32 JV2_PROP_NM(o,2,iRet);
@property (nonatomic, retain, JV2_PROP_GS_V2(strMsg,setStrMsg:)) NSString* JV2_PROP_NM(o,3,strMsg);

@end
