// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_webapp.jce   
// **********************************************************************

#import "proto_interact_ecommerce_webapp_SetAnchorQualificationReq.h"

@implementation proto_interact_ecommerce_webapp_SetAnchorQualificationReq

@synthesize JV2_PROP_NM(o,0,lGuildId);
@synthesize JV2_PROP_NM(o,1,lAnchorId);
@synthesize JV2_PROP_NM(o,2,strAnchorId);
@synthesize JV2_PROP_NM(o,3,uAppId);
@synthesize JV2_PROP_NM(o,4,strPlatformAct);
@synthesize JV2_PROP_NM(o,5,strIdentity);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_webapp_SetAnchorQualificationReq class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strAnchorId) = DefaultTarsString;
        JV2_PROP(strPlatformAct) = DefaultTarsString;
        JV2_PROP(strIdentity) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_webapp.SetAnchorQualificationReq";
}

@end
