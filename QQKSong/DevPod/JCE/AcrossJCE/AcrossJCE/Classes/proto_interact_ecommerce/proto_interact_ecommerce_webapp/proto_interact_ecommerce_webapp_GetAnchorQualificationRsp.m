// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_webapp.jce   
// **********************************************************************

#import "proto_interact_ecommerce_webapp_GetAnchorQualificationRsp.h"

@implementation proto_interact_ecommerce_webapp_GetAnchorQualificationRsp

@synthesize JV2_PROP_NM(o,0,isSettledIn);
@synthesize JV2_PROP_NM(o,1,iRet);
@synthesize JV2_PROP_NM(o,2,strMsg);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_webapp_GetAnchorQualificationRsp class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strMsg) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_webapp.GetAnchorQualificationRsp";
}

@end
