// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_webapp.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>
@class proto_interact_ecommerce_ams_PdtSelectSort;
@class proto_interact_ecommerce_ams_ProductQuery;

@interface proto_interact_ecommerce_webapp_SelectCommissionProductReq : JceObjectV2

@property (nonatomic, assign, JV2_PROP_GS_V2(uAppId,setUAppId:)) TarsUInt32 JV2_PROP_NM(o,0,uAppId);
@property (nonatomic, retain, JV2_PROP_GS_V2(productQuery,setProductQuery:)) proto_interact_ecommerce_ams_ProductQuery* JV2_PROP_NM(o,1,productQuery);
@property (nonatomic, retain, JV2_PROP_GS_V2(sort,setSort:)) proto_interact_ecommerce_ams_PdtSelectSort* JV2_PROP_NM(o,2,sort);
@property (nonatomic, assign, JV2_PROP_GS_V2(pageNo,setPageNo:)) TarsUInt32 JV2_PROP_NM(o,3,pageNo);
@property (nonatomic, assign, JV2_PROP_GS_V2(pageSize,setPageSize:)) TarsUInt32 JV2_PROP_NM(o,4,pageSize);
@property (nonatomic, retain, JV2_PROP_GS_V2(links,setLinks:)) NSArray* JV2_PROP_EX(o,5,links,VONSString);
@property (nonatomic, retain, JV2_PROP_GS_V2(productIDs,setProductIDs:)) NSArray* JV2_PROP_EX(o,6,productIDs,VONSString);
@property (nonatomic, assign, JV2_PROP_GS_V2(source,setSource:)) TarsInt32 JV2_PROP_NM(o,7,source);

@end
