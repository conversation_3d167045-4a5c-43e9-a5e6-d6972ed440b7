// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_webapp.jce   
// **********************************************************************

#import "proto_interact_ecommerce_webapp_SelectCommissionProductRsp.h"
#import "proto_interact_ecommerce_comm_ProductInfo.h"

@implementation proto_interact_ecommerce_webapp_SelectCommissionProductRsp

@synthesize JV2_PROP_EX(o,0,products,VOproto_interact_ecommerce_comm_ProductInfo);
@synthesize JV2_PROP_NM(o,1,totalCount);
@synthesize JV2_PROP_NM(o,2,iRet);
@synthesize JV2_PROP_NM(o,3,strMsg);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_webapp_SelectCommissionProductRsp class]) {
        [proto_interact_ecommerce_comm_ProductInfo initialize];
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strMsg) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_webapp.SelectCommissionProductRsp";
}

@end
