// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_webapp.jce   
// **********************************************************************

#import "proto_interact_ecommerce_webapp_BatchGetProductInfoReq.h"

@implementation proto_interact_ecommerce_webapp_BatchGetProductInfoReq

@synthesize JV2_PROP_EX(o,0,vctProductId,VONSString);
@synthesize JV2_PROP_NM(o,1,uAppId);
@synthesize JV2_PROP_NM(o,2,source);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_webapp_BatchGetProductInfoReq class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_webapp.BatchGetProductInfoReq";
}

@end
