// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_webapp.jce   
// **********************************************************************

#import "proto_interact_ecommerce_webapp_BatchGetProductInfoRsp.h"

@implementation proto_interact_ecommerce_webapp_BatchGetProductInfoRsp

@synthesize JV2_PROP_EX(o,0,mapProductInfo,M09ONSStringOproto_interact_ecommerce_comm_ProductInfo);
@synthesize JV2_PROP_NM(o,1,iRet);
@synthesize JV2_PROP_NM(o,2,strMsg);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_webapp_BatchGetProductInfoRsp class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strMsg) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_webapp.BatchGetProductInfoRsp";
}

@end
