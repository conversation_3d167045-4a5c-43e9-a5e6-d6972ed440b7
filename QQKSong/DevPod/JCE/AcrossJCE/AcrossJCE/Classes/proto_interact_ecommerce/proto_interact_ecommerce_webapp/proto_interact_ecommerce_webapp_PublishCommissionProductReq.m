// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_webapp.jce   
// **********************************************************************

#import "proto_interact_ecommerce_webapp_PublishCommissionProductReq.h"
#import "proto_interact_ecommerce_comm_ProductInfo.h"

@implementation proto_interact_ecommerce_webapp_PublishCommissionProductReq

@synthesize JV2_PROP_NM(o,0,uAppId);
@synthesize JV2_PROP_NM(o,1,product);
@synthesize JV2_PROP_EX(o,2,anchorIDs,VONSString);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_webapp_PublishCommissionProductReq class]) {
        [proto_interact_ecommerce_comm_ProductInfo initialize];
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_webapp.PublishCommissionProductReq";
}

@end
