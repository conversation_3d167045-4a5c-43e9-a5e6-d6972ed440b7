// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_webapp.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>
#import "proto_interact_ecommerce_webapp_emExpertSignStatus.h"

@interface proto_interact_ecommerce_webapp_GetGuildQualificationRsp : JceObjectV2

@property (nonatomic, assign, JV2_PROP_GS_V2(isSettledIn,setIsSettledIn:)) TarsUInt32 JV2_PROP_NM(o,0,isSettledIn);
@property (nonatomic, assign, JV2_PROP_GS_V2(isCommissionProducts,setIsCommissionProducts:)) TarsUInt32 JV2_PROP_NM(o,1,isCommissionProducts);
@property (nonatomic, assign, JV2_PROP_GS_V2(iRet,setIRet:)) TarsInt32 JV2_PROP_NM(o,2,iRet);
@property (nonatomic, retain, JV2_PROP_GS_V2(strMsg,setStrMsg:)) NSString* JV2_PROP_NM(o,3,strMsg);
@property (nonatomic, retain, JV2_PROP_GS_V2(strJsonAuthMsg,setStrJsonAuthMsg:)) NSString* JV2_PROP_NM(o,4,strJsonAuthMsg);
@property (nonatomic, assign, JV2_PROP_GS_V2(iExpertSignStatus,setIExpertSignStatus:)) proto_interact_ecommerce_webapp_emExpertSignStatus JV2_PROP_NM(o,5,iExpertSignStatus);
@property (nonatomic, assign, JV2_PROP_GS_V2(isOfficeProductTargetingQua,setIsOfficeProductTargetingQua:)) TarsUInt32 JV2_PROP_NM(o,6,isOfficeProductTargetingQua);

@end
