// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_webapp.jce   
// **********************************************************************

#import "proto_interact_ecommerce_webapp_OperateProductReq.h"
#import "proto_interact_ecommerce_comm_ProductInfo.h"

@implementation proto_interact_ecommerce_webapp_OperateProductReq

@synthesize JV2_PROP_NM(o,0,lGuildId);
@synthesize JV2_PROP_NM(o,1,lAnchorId);
@synthesize JV2_PROP_NM(o,2,strAnchorId);
@synthesize JV2_PROP_NM(o,3,uAppId);
@synthesize JV2_PROP_NM(o,4,stProductInfo);
@synthesize JV2_PROP_NM(o,5,uOprType);
@synthesize JV2_PROP_NM(o,6,strIdentity);
@synthesize JV2_PROP_EX(o,7,vctProductInfo,VOproto_interact_ecommerce_comm_ProductInfo);
@synthesize JV2_PROP_NM(o,8,emScene);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_webapp_OperateProductReq class]) {
        [proto_interact_ecommerce_comm_ProductInfo initialize];
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strAnchorId) = DefaultTarsString;
        JV2_PROP(strIdentity) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_webapp.OperateProductReq";
}

@end
