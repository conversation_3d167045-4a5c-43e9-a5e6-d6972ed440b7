// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_webapp.jce   
// **********************************************************************

#import "proto_interact_ecommerce_webapp_GetDataOverviewRsp.h"

@implementation proto_interact_ecommerce_webapp_GetDataOverviewRsp

@synthesize JV2_PROP_NM(o,0,lTotalTransactionAmount);
@synthesize JV2_PROP_NM(o,1,lDayTransactionAmount);
@synthesize JV2_PROP_NM(o,2,lMonthTransactionAmount);
@synthesize JV2_PROP_NM(o,3,lAnchorNum);
@synthesize JV2_PROP_NM(o,4,iRet);
@synthesize JV2_PROP_NM(o,5,strMsg);
@synthesize JV2_PROP_NM(o,6,lUnsettledAmount);
@synthesize JV2_PROP_NM(o,7,lSettlementAmount);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_webapp_GetDataOverviewRsp class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strMsg) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_webapp.GetDataOverviewRsp";
}

@end
