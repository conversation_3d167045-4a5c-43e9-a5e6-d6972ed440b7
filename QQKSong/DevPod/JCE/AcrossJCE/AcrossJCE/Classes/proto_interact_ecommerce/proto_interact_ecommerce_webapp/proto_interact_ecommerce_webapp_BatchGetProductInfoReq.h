// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_webapp.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>

@interface proto_interact_ecommerce_webapp_BatchGetProductInfoReq : JceObjectV2

@property (nonatomic, retain, JV2_PROP_GS_V2(vctProductId,setVctProductId:)) NSArray* JV2_PROP_EX(o,0,vctProductId,VONSString);
@property (nonatomic, assign, JV2_PROP_GS_V2(uAppId,setUAppId:)) TarsUInt32 JV2_PROP_NM(o,1,uAppId);
@property (nonatomic, assign, JV2_PROP_GS_V2(source,setSource:)) TarsInt32 JV2_PROP_NM(o,2,source);

@end
