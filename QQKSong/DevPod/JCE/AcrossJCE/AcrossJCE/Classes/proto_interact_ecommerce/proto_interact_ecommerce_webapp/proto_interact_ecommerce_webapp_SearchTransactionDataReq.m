// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_webapp.jce   
// **********************************************************************

#import "proto_interact_ecommerce_webapp_SearchTransactionDataReq.h"

@implementation proto_interact_ecommerce_webapp_SearchTransactionDataReq

@synthesize JV2_PROP_NM(o,0,lGuildId);
@synthesize JV2_PROP_NM(o,1,uAppId);
@synthesize JV2_PROP_NM(o,2,lStartTimeStamp);
@synthesize JV2_PROP_NM(o,3,lEndTimeStamp);
@synthesize JV2_PROP_NM(o,4,strIdentity);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_webapp_SearchTransactionDataReq class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strIdentity) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_webapp.SearchTransactionDataReq";
}

@end
