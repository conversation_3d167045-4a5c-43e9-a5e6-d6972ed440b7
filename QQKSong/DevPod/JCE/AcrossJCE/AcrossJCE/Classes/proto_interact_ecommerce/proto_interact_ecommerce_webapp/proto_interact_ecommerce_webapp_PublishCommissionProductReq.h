// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_webapp.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>
@class proto_interact_ecommerce_comm_ProductInfo;

@interface proto_interact_ecommerce_webapp_PublishCommissionProductReq : JceObjectV2

@property (nonatomic, assign, JV2_PROP_GS_V2(uAppId,setUAppId:)) TarsUInt32 JV2_PROP_NM(o,0,uAppId);
@property (nonatomic, retain, JV2_PROP_GS_V2(product,setProduct:)) proto_interact_ecommerce_comm_ProductInfo* JV2_PROP_NM(o,1,product);
@property (nonatomic, retain, JV2_PROP_GS_V2(anchorIDs,setAnchorIDs:)) NSArray* JV2_PROP_EX(o,2,anchorIDs,VONSString);

@end
