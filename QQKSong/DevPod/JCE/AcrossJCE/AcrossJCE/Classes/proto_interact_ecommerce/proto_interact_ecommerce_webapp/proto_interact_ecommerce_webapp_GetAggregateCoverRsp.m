// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_webapp.jce   
// **********************************************************************

#import "proto_interact_ecommerce_webapp_GetAggregateCoverRsp.h"
#import "proto_interact_ecommerce_comm_AggregateCoverInfo.h"

@implementation proto_interact_ecommerce_webapp_GetAggregateCoverRsp

@synthesize JV2_PROP_EX(o,0,vctCoverInfo,VOproto_interact_ecommerce_comm_AggregateCoverInfo);
@synthesize JV2_PROP_NM(o,1,isNeedDisplay);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_webapp_GetAggregateCoverRsp class]) {
        [proto_interact_ecommerce_comm_AggregateCoverInfo initialize];
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_webapp.GetAggregateCoverRsp";
}

@end
