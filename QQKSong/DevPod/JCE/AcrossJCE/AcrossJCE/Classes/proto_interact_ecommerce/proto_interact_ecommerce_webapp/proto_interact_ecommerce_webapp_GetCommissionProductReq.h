// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_webapp.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>
@class proto_interact_ecommerce_comm_SeachFilter;

@interface proto_interact_ecommerce_webapp_GetCommissionProductReq : JceObjectV2

@property (nonatomic, assign, JV2_PROP_GS_V2(uAppId,setUAppId:)) TarsUInt32 JV2_PROP_NM(o,0,uAppId);
@property (nonatomic, assign, JV2_PROP_GS_V2(pageNo,setPageNo:)) TarsUInt32 JV2_PROP_NM(o,1,pageNo);
@property (nonatomic, assign, JV2_PROP_GS_V2(pageSize,setPageSize:)) TarsUInt32 JV2_PROP_NM(o,2,pageSize);
@property (nonatomic, retain, JV2_PROP_GS_V2(filters,setFilters:)) NSArray* JV2_PROP_EX(o,3,filters,VOproto_interact_ecommerce_comm_SeachFilter);

@end
