// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_webapp.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>
@class proto_interact_ecommerce_comm_ProductInfo;

@interface proto_interact_ecommerce_webapp_GetLivingCoverRsp : JceObjectV2

@property (nonatomic, retain, JV2_PROP_GS_V2(stProductInfo,setStProductInfo:)) proto_interact_ecommerce_comm_ProductInfo* JV2_PROP_NM(o,0,stProductInfo);
@property (nonatomic, assign, JV2_PROP_GS_V2(isShowProductCover,setIsShowProductCover:)) TarsUInt32 JV2_PROP_NM(o,1,isShowProductCover);

@end
