// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_webapp.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>

@interface proto_interact_ecommerce_webapp_GetDataOverviewRsp : JceObjectV2

@property (nonatomic, assign, JV2_PROP_GS_V2(lTotalTransactionAmount,setLTotalTransactionAmount:)) TarsInt64 JV2_PROP_NM(o,0,lTotalTransactionAmount);
@property (nonatomic, assign, JV2_PROP_GS_V2(lDayTransactionAmount,setLDayTransactionAmount:)) TarsInt64 JV2_PROP_NM(o,1,lDayTransactionAmount);
@property (nonatomic, assign, JV2_PROP_GS_V2(lMonthTransactionAmount,setLMonthTransactionAmount:)) TarsInt64 JV2_PROP_NM(o,2,lMonthTransactionAmount);
@property (nonatomic, assign, JV2_PROP_GS_V2(lAnchorNum,setLAnchorNum:)) TarsInt64 JV2_PROP_NM(o,3,lAnchorNum);
@property (nonatomic, assign, JV2_PROP_GS_V2(iRet,setIRet:)) TarsInt32 JV2_PROP_NM(o,4,iRet);
@property (nonatomic, retain, JV2_PROP_GS_V2(strMsg,setStrMsg:)) NSString* JV2_PROP_NM(o,5,strMsg);
@property (nonatomic, assign, JV2_PROP_GS_V2(lUnsettledAmount,setLUnsettledAmount:)) TarsInt64 JV2_PROP_NM(o,6,lUnsettledAmount);
@property (nonatomic, assign, JV2_PROP_GS_V2(lSettlementAmount,setLSettlementAmount:)) TarsInt64 JV2_PROP_NM(o,7,lSettlementAmount);

@end
