// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_webapp.jce   
// **********************************************************************

#import "proto_interact_ecommerce_webapp_GetOrderListRsp.h"
#import "proto_interact_ecommerce_comm_OrderInfo.h"

@implementation proto_interact_ecommerce_webapp_GetOrderListRsp

@synthesize JV2_PROP_EX(o,0,vctOrderList,VOproto_interact_ecommerce_comm_OrderInfo);
@synthesize JV2_PROP_NM(o,1,uHasMore);
@synthesize JV2_PROP_NM(o,2,strPassBack);
@synthesize JV2_PROP_NM(o,3,uTotalNum);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_webapp_GetOrderListRsp class]) {
        [proto_interact_ecommerce_comm_OrderInfo initialize];
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strPassBack) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_webapp.GetOrderListRsp";
}

@end
