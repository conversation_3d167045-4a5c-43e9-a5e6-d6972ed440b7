// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_webapp.jce   
// **********************************************************************

#import "proto_interact_ecommerce_webapp_GetAllAnchorProductOverviewReq.h"
#import "proto_interact_ecommerce_comm_AnchorProductOverviewFilter.h"

@implementation proto_interact_ecommerce_webapp_GetAllAnchorProductOverviewReq

@synthesize JV2_PROP_NM(o,0,lGuildId);
@synthesize JV2_PROP_NM(o,1,uAppId);
@synthesize JV2_PROP_NM(o,2,uPageNo);
@synthesize JV2_PROP_NM(o,3,uPageSize);
@synthesize JV2_PROP_EX(o,4,filters,VOproto_interact_ecommerce_comm_AnchorProductOverviewFilter);
@synthesize JV2_PROP_NM(o,5,strIdentity);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_webapp_GetAllAnchorProductOverviewReq class]) {
        [proto_interact_ecommerce_comm_AnchorProductOverviewFilter initialize];
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strIdentity) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_webapp.GetAllAnchorProductOverviewReq";
}

@end
