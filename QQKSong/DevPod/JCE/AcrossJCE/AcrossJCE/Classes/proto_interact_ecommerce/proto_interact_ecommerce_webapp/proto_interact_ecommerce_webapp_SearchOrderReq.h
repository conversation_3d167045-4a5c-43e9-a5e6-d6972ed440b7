// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_webapp.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>

@interface proto_interact_ecommerce_webapp_SearchOrderReq : JceObjectV2

@property (nonatomic, assign, JV2_PROP_GS_V2(lGuildId,setLGuildId:)) TarsInt64 JV2_PROP_NM(o,0,lGuildId);
@property (nonatomic, assign, JV2_PROP_GS_V2(lAnchorId,setLAnchorId:)) TarsInt64 JV2_PROP_NM(o,1,lAnchorId);
@property (nonatomic, retain, JV2_PROP_GS_V2(strAnchorId,setStrAnchorId:)) NSString* JV2_PROP_NM(o,2,strAnchorId);
@property (nonatomic, assign, JV2_PROP_GS_V2(lProductId,setLProductId:)) TarsInt64 JV2_PROP_NM(o,3,lProductId);
@property (nonatomic, retain, JV2_PROP_GS_V2(strProductId,setStrProductId:)) NSString* JV2_PROP_NM(o,4,strProductId);
@property (nonatomic, assign, JV2_PROP_GS_V2(uAppId,setUAppId:)) TarsUInt32 JV2_PROP_NM(o,5,uAppId);
@property (nonatomic, assign, JV2_PROP_GS_V2(uOrderStatus,setUOrderStatus:)) TarsUInt32 JV2_PROP_NM(o,6,uOrderStatus);
@property (nonatomic, assign, JV2_PROP_GS_V2(lStartTs,setLStartTs:)) TarsInt64 JV2_PROP_NM(o,7,lStartTs);
@property (nonatomic, assign, JV2_PROP_GS_V2(lEndTs,setLEndTs:)) TarsInt64 JV2_PROP_NM(o,8,lEndTs);
@property (nonatomic, assign, JV2_PROP_GS_V2(uPageNo,setUPageNo:)) TarsUInt32 JV2_PROP_NM(o,9,uPageNo);
@property (nonatomic, assign, JV2_PROP_GS_V2(uPageSize,setUPageSize:)) TarsUInt32 JV2_PROP_NM(o,10,uPageSize);
@property (nonatomic, retain, JV2_PROP_GS_V2(strIdentity,setStrIdentity:)) NSString* JV2_PROP_NM(o,11,strIdentity);

@end
