// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_webapp.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>
@class proto_interact_ecommerce_comm_ProductInfo;
#import "proto_interact_ecommerce_comm_emEcommerceScene.h"

@interface proto_interact_ecommerce_webapp_OperateProductReq : JceObjectV2

@property (nonatomic, assign, JV2_PROP_GS_V2(lGuildId,setLGuildId:)) TarsInt64 JV2_PROP_NM(o,0,lGuildId);
@property (nonatomic, assign, JV2_PROP_GS_V2(lAnchorId,setLAnchorId:)) TarsInt64 JV2_PROP_NM(o,1,lAnchorId);
@property (nonatomic, retain, JV2_PROP_GS_V2(strAnchorId,setStrAnchorId:)) NSString* JV2_PROP_NM(o,2,strAnchorId);
@property (nonatomic, assign, JV2_PROP_GS_V2(uAppId,setUAppId:)) TarsUInt32 JV2_PROP_NM(o,3,uAppId);
@property (nonatomic, retain, JV2_PROP_GS_V2(stProductInfo,setStProductInfo:)) proto_interact_ecommerce_comm_ProductInfo* JV2_PROP_NM(o,4,stProductInfo);
@property (nonatomic, assign, JV2_PROP_GS_V2(uOprType,setUOprType:)) TarsUInt32 JV2_PROP_NM(o,5,uOprType);
@property (nonatomic, retain, JV2_PROP_GS_V2(strIdentity,setStrIdentity:)) NSString* JV2_PROP_NM(o,6,strIdentity);
@property (nonatomic, retain, JV2_PROP_GS_V2(vctProductInfo,setVctProductInfo:)) NSArray* JV2_PROP_EX(o,7,vctProductInfo,VOproto_interact_ecommerce_comm_ProductInfo);
@property (nonatomic, assign, JV2_PROP_GS_V2(emScene,setEmScene:)) proto_interact_ecommerce_comm_emEcommerceScene JV2_PROP_NM(o,8,emScene);

@end
