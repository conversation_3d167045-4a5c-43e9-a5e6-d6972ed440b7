// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_webapp.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>
@class proto_interact_ecommerce_comm_ProductInfo;

@interface proto_interact_ecommerce_webapp_GetProductListRsp : JceObjectV2

@property (nonatomic, retain, JV2_PROP_GS_V2(vctProductInfo,setVctProductInfo:)) NSArray* JV2_PROP_EX(o,0,vctProductInfo,VOproto_interact_ecommerce_comm_ProductInfo);
@property (nonatomic, assign, JV2_PROP_GS_V2(lTotalNum,setLTotalNum:)) TarsInt64 JV2_PROP_NM(o,1,lTotalNum);
@property (nonatomic, assign, JV2_PROP_GS_V2(uHasMore,setUHasMore:)) TarsUInt32 JV2_PROP_NM(o,2,uHasMore);
@property (nonatomic, retain, JV2_PROP_GS_V2(mapShowLabel,setMapShowLabel:)) NSDictionary* JV2_PROP_EX(o,3,mapShowLabel,M09ONSNumberONSNumber);
@property (nonatomic, assign, JV2_PROP_GS_V2(iRet,setIRet:)) TarsInt32 JV2_PROP_NM(o,4,iRet);
@property (nonatomic, retain, JV2_PROP_GS_V2(strMsg,setStrMsg:)) NSString* JV2_PROP_NM(o,5,strMsg);

@end
