// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_webapp.jce   
// **********************************************************************

#import "proto_interact_ecommerce_webapp_GetLivingCoverRsp.h"
#import "proto_interact_ecommerce_comm_ProductInfo.h"

@implementation proto_interact_ecommerce_webapp_GetLivingCoverRsp

@synthesize JV2_PROP_NM(o,0,stProductInfo);
@synthesize JV2_PROP_NM(o,1,isShowProductCover);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_webapp_GetLivingCoverRsp class]) {
        [proto_interact_ecommerce_comm_ProductInfo initialize];
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_webapp.GetLivingCoverRsp";
}

@end
