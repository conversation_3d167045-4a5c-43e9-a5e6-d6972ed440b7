// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_webapp.jce   
// **********************************************************************

#import "proto_interact_ecommerce_webapp_GetProductListReq.h"

@implementation proto_interact_ecommerce_webapp_GetProductListReq

@synthesize JV2_PROP_NM(o,0,lAnchorId);
@synthesize JV2_PROP_NM(o,1,strAnchorId);
@synthesize JV2_PROP_NM(o,2,uAppId);
@synthesize JV2_PROP_NM(o,3,uPageNo);
@synthesize JV2_PROP_NM(o,4,uPageSize);
@synthesize JV2_PROP_NM(o,5,strEncrytAnchorId);
@synthesize JV2_PROP_NM(o,6,iEcommerceScene);
@synthesize JV2_PROP_NM(o,7,bGetAll);
@synthesize JV2_PROP_NM(o,8,iProductInfoMask);
@synthesize JV2_PROP_NM(o,9,strExt);
@synthesize JV2_PROP_NM(o,10,iLabelEntrySource);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_webapp_GetProductListReq class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strAnchorId) = DefaultTarsString;
        JV2_PROP(strEncrytAnchorId) = DefaultTarsString;
        JV2_PROP(bGetAll) = NO;
        JV2_PROP(strExt) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_webapp.GetProductListReq";
}

@end
