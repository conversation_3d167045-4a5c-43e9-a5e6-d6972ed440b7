// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_webapp.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>
@class proto_interact_ecommerce_product_CategoryInfo;
@class proto_interact_ecommerce_product_ProductPlatInfo;
@class proto_interact_ecommerce_product_ProductSortInfo;

@interface proto_interact_ecommerce_webapp_GetProductCategoriesRsp : JceObjectV2

@property (nonatomic, retain, JV2_PROP_GS_V2(platInfos,setPlatInfos:)) NSArray* JV2_PROP_EX(o,0,platInfos,VOproto_interact_ecommerce_product_ProductPlatInfo);
@property (nonatomic, retain, JV2_PROP_GS_V2(categorys,setCategorys:)) NSArray* JV2_PROP_EX(o,1,categorys,VOproto_interact_ecommerce_product_CategoryInfo);
@property (nonatomic, retain, JV2_PROP_GS_V2(sorts,setSorts:)) NSArray* JV2_PROP_EX(o,2,sorts,VOproto_interact_ecommerce_product_ProductSortInfo);
@property (nonatomic, assign, JV2_PROP_GS_V2(iRet,setIRet:)) TarsInt32 JV2_PROP_NM(o,3,iRet);
@property (nonatomic, retain, JV2_PROP_GS_V2(strMsg,setStrMsg:)) NSString* JV2_PROP_NM(o,4,strMsg);

@end
