// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_webapp.jce   
// **********************************************************************

#import "proto_interact_ecommerce_webapp_SearchOrderReq.h"

@implementation proto_interact_ecommerce_webapp_SearchOrderReq

@synthesize JV2_PROP_NM(o,0,lGuildId);
@synthesize JV2_PROP_NM(o,1,lAnchorId);
@synthesize JV2_PROP_NM(o,2,strAnchorId);
@synthesize JV2_PROP_NM(o,3,lProductId);
@synthesize JV2_PROP_NM(o,4,strProductId);
@synthesize JV2_PROP_NM(o,5,uAppId);
@synthesize JV2_PROP_NM(o,6,uOrderStatus);
@synthesize JV2_PROP_NM(o,7,lStartTs);
@synthesize JV2_PROP_NM(o,8,lEndTs);
@synthesize JV2_PROP_NM(o,9,uPageNo);
@synthesize JV2_PROP_NM(o,10,uPageSize);
@synthesize JV2_PROP_NM(o,11,strIdentity);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_webapp_SearchOrderReq class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strAnchorId) = DefaultTarsString;
        JV2_PROP(strProductId) = DefaultTarsString;
        JV2_PROP(strIdentity) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_webapp.SearchOrderReq";
}

@end
