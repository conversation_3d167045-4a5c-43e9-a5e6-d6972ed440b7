// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_webapp.jce   
// **********************************************************************

#import "proto_interact_ecommerce_webapp_GetAnchorQualificationListReq.h"
#import "proto_interact_ecommerce_comm_SeachFilter.h"

@implementation proto_interact_ecommerce_webapp_GetAnchorQualificationListReq

@synthesize JV2_PROP_NM(o,0,lGuildId);
@synthesize JV2_PROP_NM(o,1,uAppId);
@synthesize JV2_PROP_NM(o,2,uPageNo);
@synthesize JV2_PROP_NM(o,3,uPageSize);
@synthesize JV2_PROP_EX(o,4,filters,VOproto_interact_ecommerce_comm_SeachFilter);
@synthesize JV2_PROP_NM(o,5,strIdentity);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_webapp_GetAnchorQualificationListReq class]) {
        [proto_interact_ecommerce_comm_SeachFilter initialize];
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strIdentity) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_webapp.GetAnchorQualificationListReq";
}

@end
