// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_webapp.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>
#import "proto_interact_ecommerce_comm_emEcommerceScene.h"

@interface proto_interact_ecommerce_webapp_GetAllProductListReq : JceObjectV2

@property (nonatomic, assign, JV2_PROP_GS_V2(lGuildId,setLGuildId:)) TarsInt64 JV2_PROP_NM(o,0,lGuildId);
@property (nonatomic, retain, JV2_PROP_GS_V2(strGuildId,setStrGuildId:)) NSString* JV2_PROP_NM(o,1,strGuildId);
@property (nonatomic, assign, JV2_PROP_GS_V2(lAnchorId,setLAnchorId:)) TarsInt64 JV2_PROP_NM(o,2,lAnchorId);
@property (nonatomic, retain, JV2_PROP_GS_V2(strAnchorId,setStrAnchorId:)) NSString* JV2_PROP_NM(o,3,strAnchorId);
@property (nonatomic, assign, JV2_PROP_GS_V2(uAppId,setUAppId:)) TarsUInt32 JV2_PROP_NM(o,4,uAppId);
@property (nonatomic, assign, JV2_PROP_GS_V2(uPageNo,setUPageNo:)) TarsUInt32 JV2_PROP_NM(o,5,uPageNo);
@property (nonatomic, assign, JV2_PROP_GS_V2(uPageSize,setUPageSize:)) TarsUInt32 JV2_PROP_NM(o,6,uPageSize);
@property (nonatomic, retain, JV2_PROP_GS_V2(strIdentity,setStrIdentity:)) NSString* JV2_PROP_NM(o,7,strIdentity);
@property (nonatomic, assign, JV2_PROP_GS_V2(uStatus,setUStatus:)) TarsUInt32 JV2_PROP_NM(o,8,uStatus);
@property (nonatomic, assign, JV2_PROP_GS_V2(iEcommerceScene,setIEcommerceScene:)) proto_interact_ecommerce_comm_emEcommerceScene JV2_PROP_NM(o,9,iEcommerceScene);

@end
