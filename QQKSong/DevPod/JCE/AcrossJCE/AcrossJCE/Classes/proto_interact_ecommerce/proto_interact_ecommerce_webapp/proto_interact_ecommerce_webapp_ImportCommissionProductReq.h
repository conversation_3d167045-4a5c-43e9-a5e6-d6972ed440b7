// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_webapp.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>
@class proto_interact_ecommerce_comm_ProductInfo;

@interface proto_interact_ecommerce_webapp_ImportCommissionProductReq : JceObjectV2

@property (nonatomic, assign, JV2_PROP_GS_V2(uAppId,setUAppId:)) TarsUInt32 JV2_PROP_NM(o,0,uAppId);
@property (nonatomic, retain, JV2_PROP_GS_V2(products,setProducts:)) NSArray* JV2_PROP_EX(o,1,products,VOproto_interact_ecommerce_comm_ProductInfo);
@property (nonatomic, retain, JV2_PROP_GS_V2(operUser,setOperUser:)) NSString* JV2_PROP_NM(o,2,operUser);

@end
