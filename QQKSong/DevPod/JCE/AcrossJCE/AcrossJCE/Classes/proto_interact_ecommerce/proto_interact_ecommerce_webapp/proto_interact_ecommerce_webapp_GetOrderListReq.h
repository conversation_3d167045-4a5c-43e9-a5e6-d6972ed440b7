// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_webapp.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>

@interface proto_interact_ecommerce_webapp_GetOrderListReq : JceObjectV2

@property (nonatomic, assign, JV2_PROP_GS_V2(uAppId,setUAppId:)) TarsUInt32 JV2_PROP_NM(o,0,uAppId);
@property (nonatomic, retain, JV2_PROP_GS_V2(strUid,setStrUid:)) NSString* JV2_PROP_NM(o,1,strUid);
@property (nonatomic, assign, JV2_PROP_GS_V2(uOrderStatus,setUOrderStatus:)) TarsUInt32 JV2_PROP_NM(o,2,uOrderStatus);
@property (nonatomic, retain, JV2_PROP_GS_V2(strPassBack,setStrPassBack:)) NSString* JV2_PROP_NM(o,3,strPassBack);

@end
