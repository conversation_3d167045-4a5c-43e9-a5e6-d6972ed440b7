// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_webapp.jce   
// **********************************************************************

#import "proto_interact_ecommerce_webapp_ExportDataRsp.h"

@implementation proto_interact_ecommerce_webapp_ExportDataRsp

@synthesize JV2_PROP_NM(o,0,iRes);
@synthesize JV2_PROP_NM(o,1,strExportUrl);
@synthesize JV2_PROP_NM(o,2,strExportId);
@synthesize JV2_PROP_NM(o,3,iRet);
@synthesize JV2_PROP_NM(o,4,strMsg);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_webapp_ExportDataRsp class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strExportUrl) = DefaultTarsString;
        JV2_PROP(strExportId) = DefaultTarsString;
        JV2_PROP(strMsg) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_webapp.ExportDataRsp";
}

@end
