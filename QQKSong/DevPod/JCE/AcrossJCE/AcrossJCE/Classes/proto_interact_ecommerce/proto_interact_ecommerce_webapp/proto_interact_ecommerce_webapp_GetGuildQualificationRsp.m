// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_webapp.jce   
// **********************************************************************

#import "proto_interact_ecommerce_webapp_GetGuildQualificationRsp.h"

@implementation proto_interact_ecommerce_webapp_GetGuildQualificationRsp

@synthesize JV2_PROP_NM(o,0,isSettledIn);
@synthesize JV2_PROP_NM(o,1,isCommissionProducts);
@synthesize JV2_PROP_NM(o,2,iRet);
@synthesize JV2_PROP_NM(o,3,strMsg);
@synthesize JV2_PROP_NM(o,4,strJsonAuthMsg);
@synthesize JV2_PROP_NM(o,5,iExpertSignStatus);
@synthesize JV2_PROP_NM(o,6,isOfficeProductTargetingQua);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_webapp_GetGuildQualificationRsp class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strMsg) = DefaultTarsString;
        JV2_PROP(strJsonAuthMsg) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_webapp.GetGuildQualificationRsp";
}

@end
