// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_webapp.jce   
// **********************************************************************

#import "proto_interact_ecommerce_webapp_GetAnchorQualificationListRsp.h"
#import "proto_interact_ecommerce_comm_AnchorInfo.h"

@implementation proto_interact_ecommerce_webapp_GetAnchorQualificationListRsp

@synthesize JV2_PROP_EX(o,0,anchorlist,VOproto_interact_ecommerce_comm_AnchorInfo);
@synthesize JV2_PROP_NM(o,1,lTotalNum);
@synthesize JV2_PROP_NM(o,2,uHasMore);
@synthesize JV2_PROP_NM(o,3,iRet);
@synthesize JV2_PROP_NM(o,4,strMsg);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_webapp_GetAnchorQualificationListRsp class]) {
        [proto_interact_ecommerce_comm_AnchorInfo initialize];
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strMsg) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_webapp.GetAnchorQualificationListRsp";
}

@end
