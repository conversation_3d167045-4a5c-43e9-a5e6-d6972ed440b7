// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_webapp.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>
@class proto_interact_ecommerce_comm_AggregateCoverInfo;

@interface proto_interact_ecommerce_webapp_GetAggregateCoverRsp : JceObjectV2

@property (nonatomic, retain, JV2_PROP_GS_V2(vctCoverInfo,setVctCoverInfo:)) NSArray* JV2_PROP_EX(o,0,vctCoverInfo,VOproto_interact_ecommerce_comm_AggregateCoverInfo);
@property (nonatomic, assign, JV2_PROP_GS_V2(isNeedDisplay,setIsNeedDisplay:)) TarsInt32 JV2_PROP_NM(o,1,isNeedDisplay);

@end
