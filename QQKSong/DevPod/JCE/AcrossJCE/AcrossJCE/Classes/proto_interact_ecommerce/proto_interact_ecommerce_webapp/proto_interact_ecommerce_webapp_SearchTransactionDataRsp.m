// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_webapp.jce   
// **********************************************************************

#import "proto_interact_ecommerce_webapp_SearchTransactionDataRsp.h"
#import "proto_interact_ecommerce_comm_TransactionData.h"

@implementation proto_interact_ecommerce_webapp_SearchTransactionDataRsp

@synthesize JV2_PROP_EX(o,0,vctTransactionData,VOproto_interact_ecommerce_comm_TransactionData);
@synthesize JV2_PROP_NM(o,1,iRet);
@synthesize JV2_PROP_NM(o,2,strMsg);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_webapp_SearchTransactionDataRsp class]) {
        [proto_interact_ecommerce_comm_TransactionData initialize];
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strMsg) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_webapp.SearchTransactionDataRsp";
}

@end
