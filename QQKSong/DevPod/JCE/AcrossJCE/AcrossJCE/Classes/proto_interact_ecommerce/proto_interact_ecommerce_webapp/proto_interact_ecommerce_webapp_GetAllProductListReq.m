// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_webapp.jce   
// **********************************************************************

#import "proto_interact_ecommerce_webapp_GetAllProductListReq.h"

@implementation proto_interact_ecommerce_webapp_GetAllProductListReq

@synthesize JV2_PROP_NM(o,0,lGuildId);
@synthesize JV2_PROP_NM(o,1,strGuildId);
@synthesize JV2_PROP_NM(o,2,lAnchorId);
@synthesize JV2_PROP_NM(o,3,strAnchorId);
@synthesize JV2_PROP_NM(o,4,uAppId);
@synthesize JV2_PROP_NM(o,5,uPageNo);
@synthesize JV2_PROP_NM(o,6,uPageSize);
@synthesize JV2_PROP_NM(o,7,strIdentity);
@synthesize JV2_PROP_NM(o,8,uStatus);
@synthesize JV2_PROP_NM(o,9,iEcommerceScene);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_webapp_GetAllProductListReq class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strGuildId) = DefaultTarsString;
        JV2_PROP(strAnchorId) = DefaultTarsString;
        JV2_PROP(strIdentity) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_webapp.GetAllProductListReq";
}

@end
