// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_webapp.jce   
// **********************************************************************

#import "proto_interact_ecommerce_webapp_GetProductCategoriesRsp.h"
#import "proto_interact_ecommerce_product_CategoryInfo.h"
#import "proto_interact_ecommerce_product_ProductPlatInfo.h"
#import "proto_interact_ecommerce_product_ProductSortInfo.h"

@implementation proto_interact_ecommerce_webapp_GetProductCategoriesRsp

@synthesize JV2_PROP_EX(o,0,platInfos,VOproto_interact_ecommerce_product_ProductPlatInfo);
@synthesize JV2_PROP_EX(o,1,categorys,VOproto_interact_ecommerce_product_CategoryInfo);
@synthesize JV2_PROP_EX(o,2,sorts,VOproto_interact_ecommerce_product_ProductSortInfo);
@synthesize JV2_PROP_NM(o,3,iRet);
@synthesize JV2_PROP_NM(o,4,strMsg);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_webapp_GetProductCategoriesRsp class]) {
        [proto_interact_ecommerce_product_CategoryInfo initialize];
        [proto_interact_ecommerce_product_ProductPlatInfo initialize];
        [proto_interact_ecommerce_product_ProductSortInfo initialize];
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strMsg) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_webapp.GetProductCategoriesRsp";
}

@end
