// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_webapp.jce   
// **********************************************************************

#import "proto_interact_ecommerce_webapp_GetProductInfoReq.h"

@implementation proto_interact_ecommerce_webapp_GetProductInfoReq

@synthesize JV2_PROP_NM(o,0,lProductId);
@synthesize JV2_PROP_NM(o,1,strProductId);
@synthesize JV2_PROP_NM(o,2,uAppId);
@synthesize JV2_PROP_NM(o,3,source);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_webapp_GetProductInfoReq class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strProductId) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_webapp.GetProductInfoReq";
}

@end
