// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_webapp.jce   
// **********************************************************************

#import "proto_interact_ecommerce_webapp_ImportCommissionProductReq.h"
#import "proto_interact_ecommerce_comm_ProductInfo.h"

@implementation proto_interact_ecommerce_webapp_ImportCommissionProductReq

@synthesize JV2_PROP_NM(o,0,uAppId);
@synthesize JV2_PROP_EX(o,1,products,VOproto_interact_ecommerce_comm_ProductInfo);
@synthesize JV2_PROP_NM(o,2,operUser);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_webapp_ImportCommissionProductReq class]) {
        [proto_interact_ecommerce_comm_ProductInfo initialize];
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(operUser) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_webapp.ImportCommissionProductReq";
}

@end
