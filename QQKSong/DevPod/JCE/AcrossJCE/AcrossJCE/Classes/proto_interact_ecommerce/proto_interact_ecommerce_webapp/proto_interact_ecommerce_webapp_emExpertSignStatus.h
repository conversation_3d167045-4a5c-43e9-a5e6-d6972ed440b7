// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_webapp.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>

enum {
    proto_interact_ecommerce_webapp_emExpertSignStatus_EM_SIGN_STATUS_TYPE_NON_EXISTENT = 0,
    proto_interact_ecommerce_webapp_emExpertSignStatus_EM_SIGN_STATUS_TYPE_WAIT_FOR_SIGN = 1,
    proto_interact_ecommerce_webapp_emExpertSignStatus_EM_SIGN_STATUS_TYPE_WAIT_FOR_ADD_ACNHOR = 2,
    proto_interact_ecommerce_webapp_emExpertSignStatus_EM_SIGN_STATUS_TYPE_SIGNING = 3,
    proto_interact_ecommerce_webapp_emExpertSignStatus_EM_SIGN_STATUS_TYPE_SUCC = 4,
    proto_interact_ecommerce_webapp_emExpertSignStatus_EM_SIGN_STATUS_TYPE_FAIL = 5,
    proto_interact_ecommerce_webapp_emExpertSignStatus_EM_SIGN_STATUS_TYPE_DEL = 6
};
#define proto_interact_ecommerce_webapp_emExpertSignStatus TarsInt32

