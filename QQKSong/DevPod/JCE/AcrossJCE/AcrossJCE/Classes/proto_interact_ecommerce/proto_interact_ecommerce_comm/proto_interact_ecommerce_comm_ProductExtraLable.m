// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_comm.jce   
// **********************************************************************

#import "proto_interact_ecommerce_comm_ProductExtraLable.h"

@implementation proto_interact_ecommerce_comm_ProductExtraLable

@synthesize JV2_PROP_NM(o,0,uUniqId);
@synthesize JV2_PROP_NM(o,1,uLableType);
@synthesize JV2_PROP_NM(o,2,strContent1);
@synthesize JV2_PROP_NM(o,3,strBgColor1);
@synthesize JV2_PROP_NM(o,4,strFontColor1);
@synthesize JV2_PROP_NM(o,5,strStrokeColor1);
@synthesize JV2_PROP_NM(o,6,strContent2);
@synthesize JV2_PROP_NM(o,7,strBgColor2);
@synthesize JV2_PROP_NM(o,8,strFontColor2);
@synthesize JV2_PROP_NM(o,9,strStrokeColor2);
@synthesize JV2_PROP_NM(o,10,strCouponBatchId);
@synthesize JV2_PROP_NM(o,11,uLabelSource);
@synthesize JV2_PROP_NM(o,12,strJumpUrl);
@synthesize JV2_PROP_NM(o,13,strFrontIcon);
@synthesize JV2_PROP_NM(o,14,strBackIcon);
@synthesize JV2_PROP_NM(o,15,uRecommendLabelType);
@synthesize JV2_PROP_NM(o,16,uShowPos);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_comm_ProductExtraLable class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strContent1) = DefaultTarsString;
        JV2_PROP(strBgColor1) = DefaultTarsString;
        JV2_PROP(strFontColor1) = DefaultTarsString;
        JV2_PROP(strStrokeColor1) = DefaultTarsString;
        JV2_PROP(strContent2) = DefaultTarsString;
        JV2_PROP(strBgColor2) = DefaultTarsString;
        JV2_PROP(strFontColor2) = DefaultTarsString;
        JV2_PROP(strStrokeColor2) = DefaultTarsString;
        JV2_PROP(strCouponBatchId) = DefaultTarsString;
        JV2_PROP(strJumpUrl) = DefaultTarsString;
        JV2_PROP(strFrontIcon) = DefaultTarsString;
        JV2_PROP(strBackIcon) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_comm.ProductExtraLable";
}

@end
