// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_comm.jce   
// **********************************************************************

#import "proto_interact_ecommerce_comm_AnchorInfo.h"

@implementation proto_interact_ecommerce_comm_AnchorInfo

@synthesize JV2_PROP_NM(o,0,lAnchorId);
@synthesize JV2_PROP_NM(o,1,strAnchorId);
@synthesize JV2_PROP_NM(o,2,strNickName);
@synthesize JV2_PROP_NM(o,3,strPlatformAct);
@synthesize JV2_PROP_EX(o,4,vctIdentity,VONSString);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_comm_AnchorInfo class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strAnchorId) = DefaultTarsString;
        JV2_PROP(strNickName) = DefaultTarsString;
        JV2_PROP(strPlatformAct) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_comm.AnchorInfo";
}

@end
