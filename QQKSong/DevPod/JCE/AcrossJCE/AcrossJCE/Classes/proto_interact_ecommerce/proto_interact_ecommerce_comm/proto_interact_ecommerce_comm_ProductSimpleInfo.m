// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_comm.jce   
// **********************************************************************

#import "proto_interact_ecommerce_comm_ProductSimpleInfo.h"

@implementation proto_interact_ecommerce_comm_ProductSimpleInfo

@synthesize JV2_PROP_NM(o,0,strProductId);
@synthesize JV2_PROP_NM(o,1,source);
@synthesize JV2_PROP_NM(o,2,strProductName);
@synthesize JV2_PROP_NM(o,3,strAnchorId);
@synthesize JV2_PROP_NM(o,4,platId);
@synthesize JV2_PROP_NM(o,5,firstCategoryId);
@synthesize JV2_PROP_NM(o,6,commission);
@synthesize JV2_PROP_NM(o,7,commissionRate);
@synthesize JV2_PROP_NM(o,8,tmeCommission);
@synthesize JV2_PROP_NM(o,9,tmeCommissionRate);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_comm_ProductSimpleInfo class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strProductId) = DefaultTarsString;
        JV2_PROP(strProductName) = DefaultTarsString;
        JV2_PROP(strAnchorId) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_comm.ProductSimpleInfo";
}

@end
