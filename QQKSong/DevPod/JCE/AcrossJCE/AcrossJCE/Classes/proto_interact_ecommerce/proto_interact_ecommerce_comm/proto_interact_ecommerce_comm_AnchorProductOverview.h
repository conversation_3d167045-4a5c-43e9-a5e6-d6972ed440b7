// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_comm.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>

@interface proto_interact_ecommerce_comm_AnchorProductOverview : JceObjectV2

@property (nonatomic, assign, JV2_PROP_GS_V2(lAnchorId,setLAnchorId:)) TarsInt64 JV2_PROP_NM(o,0,lAnchorId);
@property (nonatomic, retain, JV2_PROP_GS_V2(strAnchorId,setStrAnchorId:)) NSString* JV2_PROP_NM(o,1,strAnchorId);
@property (nonatomic, retain, JV2_PROP_GS_V2(strNickName,setStrNickName:)) NSString* JV2_PROP_NM(o,2,strNickName);
@property (nonatomic, assign, JV2_PROP_GS_V2(lAddProductNum,setLAddProductNum:)) TarsInt64 JV2_PROP_NM(o,3,lAddProductNum);
@property (nonatomic, assign, JV2_PROP_GS_V2(lProductOnNum,setLProductOnNum:)) TarsInt64 JV2_PROP_NM(o,4,lProductOnNum);

@end
