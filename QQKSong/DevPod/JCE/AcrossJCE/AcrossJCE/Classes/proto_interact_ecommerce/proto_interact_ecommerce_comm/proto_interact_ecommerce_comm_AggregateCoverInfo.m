// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_comm.jce   
// **********************************************************************

#import "proto_interact_ecommerce_comm_AggregateCoverInfo.h"
#import "proto_interact_ecommerce_comm_AggregateCoverProductInfo.h"

@implementation proto_interact_ecommerce_comm_AggregateCoverInfo

@synthesize JV2_PROP_NM(o,0,strURL);
@synthesize JV2_PROP_NM(o,1,uJumpType);
@synthesize JV2_PROP_NM(o,2,stProductInfo);
@synthesize JV2_PROP_NM(o,3,lAnchorId);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_comm_AggregateCoverInfo class]) {
        [proto_interact_ecommerce_comm_AggregateCoverProductInfo initialize];
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strURL) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_comm.AggregateCoverInfo";
}

@end
