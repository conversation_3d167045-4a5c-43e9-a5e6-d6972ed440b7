// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_comm.jce   
// **********************************************************************

#import "proto_interact_ecommerce_comm_ProductOperations.h"

@implementation proto_interact_ecommerce_comm_ProductOperations

@synthesize JV2_PROP_NM(o,0,uOperationsType);
@synthesize JV2_PROP_NM(o,1,uIsOpen);
@synthesize JV2_PROP_NM(o,2,uStartTs);
@synthesize JV2_PROP_NM(o,3,uEndTs);
@synthesize JV2_PROP_NM(o,4,productId);
@synthesize JV2_PROP_NM(o,5,updateTs);
@synthesize JV2_PROP_NM(o,6,uIsDefault);
@synthesize JV2_PROP_NM(o,7,source);
@synthesize JV2_PROP_NM(o,8,strProductId);
@synthesize JV2_PROP_NM(o,9,lProductSequenceNo);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_comm_ProductOperations class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strProductId) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_comm.ProductOperations";
}

@end
