// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_comm.jce   
// **********************************************************************

#import "proto_interact_ecommerce_comm_DeliveryInfo.h"

@implementation proto_interact_ecommerce_comm_DeliveryInfo

@synthesize JV2_PROP_NM(o,0,strReceiverName);
@synthesize JV2_PROP_NM(o,1,strReceiverPhone);
@synthesize JV2_PROP_NM(o,2,strReceiverAddr);
@synthesize JV2_PROP_NM(o,3,strReceiverProvince);
@synthesize JV2_PROP_NM(o,4,strReceiverCity);
@synthesize JV2_PROP_NM(o,5,strReceiverDistrict);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_comm_DeliveryInfo class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strReceiverName) = DefaultTarsString;
        JV2_PROP(strReceiverPhone) = DefaultTarsString;
        JV2_PROP(strReceiverAddr) = DefaultTarsString;
        JV2_PROP(strReceiverProvince) = DefaultTarsString;
        JV2_PROP(strReceiverCity) = DefaultTarsString;
        JV2_PROP(strReceiverDistrict) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_comm.DeliveryInfo";
}

@end
