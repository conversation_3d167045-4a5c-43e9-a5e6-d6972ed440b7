// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_comm.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>
@class proto_interact_ecommerce_comm_DeliveryInfo;
@class proto_interact_ecommerce_comm_ProductInfo;
@class proto_interact_ecommerce_product_CategoryInfo;
@class proto_interact_ecommerce_product_ProductPlatInfo;
#import "proto_interact_ecommerce_comm_emEcommerceScene.h"

@interface proto_interact_ecommerce_comm_OrderInfo : JceObjectV2

@property (nonatomic, assign, JV2_PROP_GS_V2(lAnchorId,setLAnchorId:)) TarsInt64 JV2_PROP_NM(o,0,lAnchorId);
@property (nonatomic, retain, JV2_PROP_GS_V2(strAnchorId,setStrAnchorId:)) NSString* JV2_PROP_NM(o,1,strAnchorId);
@property (nonatomic, assign, JV2_PROP_GS_V2(lOrderId,setLOrderId:)) TarsInt64 JV2_PROP_NM(o,2,lOrderId);
@property (nonatomic, retain, JV2_PROP_GS_V2(strOrderId,setStrOrderId:)) NSString* JV2_PROP_NM(o,3,strOrderId);
@property (nonatomic, retain, JV2_PROP_GS_V2(stProductInfo,setStProductInfo:)) proto_interact_ecommerce_comm_ProductInfo* JV2_PROP_NM(o,4,stProductInfo);
@property (nonatomic, assign, JV2_PROP_GS_V2(lUserId,setLUserId:)) TarsInt64 JV2_PROP_NM(o,5,lUserId);
@property (nonatomic, retain, JV2_PROP_GS_V2(strNickName,setStrNickName:)) NSString* JV2_PROP_NM(o,6,strNickName);
@property (nonatomic, assign, JV2_PROP_GS_V2(lProductNum,setLProductNum:)) TarsInt64 JV2_PROP_NM(o,7,lProductNum);
@property (nonatomic, assign, JV2_PROP_GS_V2(lOrderTs,setLOrderTs:)) TarsInt64 JV2_PROP_NM(o,8,lOrderTs);
@property (nonatomic, assign, JV2_PROP_GS_V2(uState,setUState:)) TarsUInt32 JV2_PROP_NM(o,9,uState);
@property (nonatomic, assign, JV2_PROP_GS_V2(lAmount,setLAmount:)) TarsInt64 JV2_PROP_NM(o,10,lAmount);
@property (nonatomic, retain, JV2_PROP_GS_V2(strDetail,setStrDetail:)) NSString* JV2_PROP_NM(o,11,strDetail);
@property (nonatomic, assign, JV2_PROP_GS_V2(source,setSource:)) TarsInt32 JV2_PROP_NM(o,12,source);
@property (nonatomic, retain, JV2_PROP_GS_V2(platInfo,setPlatInfo:)) proto_interact_ecommerce_product_ProductPlatInfo* JV2_PROP_NM(o,13,platInfo);
@property (nonatomic, assign, JV2_PROP_GS_V2(commission,setCommission:)) TarsInt64 JV2_PROP_NM(o,14,commission);
@property (nonatomic, assign, JV2_PROP_GS_V2(commissionRate,setCommissionRate:)) TarsInt64 JV2_PROP_NM(o,15,commissionRate);
@property (nonatomic, retain, JV2_PROP_GS_V2(category,setCategory:)) proto_interact_ecommerce_product_CategoryInfo* JV2_PROP_NM(o,16,category);
@property (nonatomic, retain, JV2_PROP_GS_V2(stDeliveryInfo,setStDeliveryInfo:)) proto_interact_ecommerce_comm_DeliveryInfo* JV2_PROP_NM(o,17,stDeliveryInfo);
@property (nonatomic, retain, JV2_PROP_GS_V2(wxMiniprogramAppid,setWxMiniprogramAppid:)) NSString* JV2_PROP_NM(o,18,wxMiniprogramAppid);
@property (nonatomic, retain, JV2_PROP_GS_V2(wxMiniprogramName,setWxMiniprogramName:)) NSString* JV2_PROP_NM(o,19,wxMiniprogramName);
@property (nonatomic, retain, JV2_PROP_GS_V2(H5Url,setH5Url:)) NSString* JV2_PROP_NM(o,20,H5Url);
@property (nonatomic, assign, JV2_PROP_GS_V2(totalAmount,setTotalAmount:)) TarsInt64 JV2_PROP_NM(o,21,totalAmount);
@property (nonatomic, assign, JV2_PROP_GS_V2(lPayTs,setLPayTs:)) TarsInt64 JV2_PROP_NM(o,22,lPayTs);
@property (nonatomic, assign, JV2_PROP_GS_V2(tmeCommission,setTmeCommission:)) TarsInt64 JV2_PROP_NM(o,23,tmeCommission);
@property (nonatomic, assign, JV2_PROP_GS_V2(tmeCommissionRate,setTmeCommissionRate:)) TarsInt64 JV2_PROP_NM(o,24,tmeCommissionRate);
@property (nonatomic, assign, JV2_PROP_GS_V2(originCommission,setOriginCommission:)) TarsInt64 JV2_PROP_NM(o,25,originCommission);
@property (nonatomic, assign, JV2_PROP_GS_V2(originCommissionRate,setOriginCommissionRate:)) TarsInt64 JV2_PROP_NM(o,26,originCommissionRate);
@property (nonatomic, retain, JV2_PROP_GS_V2(strRefundUrl,setStrRefundUrl:)) NSString* JV2_PROP_NM(o,27,strRefundUrl);
@property (nonatomic, assign, JV2_PROP_GS_V2(uAppId,setUAppId:)) TarsUInt32 JV2_PROP_NM(o,28,uAppId);
@property (nonatomic, assign, JV2_PROP_GS_V2(createTime,setCreateTime:)) TarsInt64 JV2_PROP_NM(o,29,createTime);
@property (nonatomic, assign, JV2_PROP_GS_V2(iEcommerceScene,setIEcommerceScene:)) proto_interact_ecommerce_comm_emEcommerceScene JV2_PROP_NM(o,30,iEcommerceScene);
@property (nonatomic, retain, JV2_PROP_GS_V2(strExt,setStrExt:)) NSString* JV2_PROP_NM(o,31,strExt);
@property (nonatomic, assign, JV2_PROP_GS_V2(couponAmount,setCouponAmount:)) TarsInt64 JV2_PROP_NM(o,32,couponAmount);
@property (nonatomic, assign, JV2_PROP_GS_V2(sellerQualification,setSellerQualification:)) TarsInt32 JV2_PROP_NM(o,33,sellerQualification);
@property (nonatomic, assign, JV2_PROP_GS_V2(lGuildId,setLGuildId:)) TarsInt64 JV2_PROP_NM(o,34,lGuildId);

@end
