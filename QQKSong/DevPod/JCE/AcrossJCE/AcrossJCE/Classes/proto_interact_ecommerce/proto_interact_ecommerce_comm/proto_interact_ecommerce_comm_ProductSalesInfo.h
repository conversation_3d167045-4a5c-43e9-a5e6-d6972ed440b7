// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_comm.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>

@interface proto_interact_ecommerce_comm_ProductSalesInfo : JceObjectV2

@property (nonatomic, assign, JV2_PROP_GS_V2(productId,setProductId:)) TarsInt64 JV2_PROP_NM(o,0,productId);
@property (nonatomic, assign, JV2_PROP_GS_V2(num,setNum:)) TarsInt64 JV2_PROP_NM(o,1,num);
@property (nonatomic, retain, JV2_PROP_GS_V2(strProductId,setStrProductId:)) NSString* JV2_PROP_NM(o,2,strProductId);
@property (nonatomic, assign, JV2_PROP_GS_V2(source,setSource:)) TarsInt32 JV2_PROP_NM(o,3,source);
@property (nonatomic, retain, JV2_PROP_GS_V2(strSpuId,setStrSpuId:)) NSString* JV2_PROP_NM(o,4,strSpuId);

@end
