// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_comm.jce   
// **********************************************************************

#import "proto_interact_ecommerce_comm_CustomerHotlineInfo.h"

@implementation proto_interact_ecommerce_comm_CustomerHotlineInfo

@synthesize JV2_PROP_NM(o,0,shopID);
@synthesize JV2_PROP_NM(o,1,QQ);
@synthesize JV2_PROP_NM(o,2,weChat);
@synthesize JV2_PROP_NM(o,3,otherCustomer);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_comm_CustomerHotlineInfo class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(shopID) = DefaultTarsString;
        JV2_PROP(QQ) = DefaultTarsString;
        JV2_PROP(weChat) = DefaultTarsString;
        JV2_PROP(otherCustomer) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_comm.CustomerHotlineInfo";
}

@end
