// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_comm.jce   
// **********************************************************************

#import "proto_interact_ecommerce_comm_AnchorProductOverview.h"

@implementation proto_interact_ecommerce_comm_AnchorProductOverview

@synthesize JV2_PROP_NM(o,0,lAnchorId);
@synthesize JV2_PROP_NM(o,1,strAnchorId);
@synthesize JV2_PROP_NM(o,2,strNickName);
@synthesize JV2_PROP_NM(o,3,lAddProductNum);
@synthesize JV2_PROP_NM(o,4,lProductOnNum);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_comm_AnchorProductOverview class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strAnchorId) = DefaultTarsString;
        JV2_PROP(strNickName) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_comm.AnchorProductOverview";
}

@end
