// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_comm.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>

@interface proto_interact_ecommerce_comm_AggregateCoverProductInfo : JceObjectV2

@property (nonatomic, retain, JV2_PROP_GS_V2(strPic,setStrPic:)) NSString* JV2_PROP_NM(o,0,strPic);
@property (nonatomic, retain, JV2_PROP_GS_V2(strProductName,setStrProductName:)) NSString* JV2_PROP_NM(o,1,strProductName);
@property (nonatomic, assign, JV2_PROP_GS_V2(lCurPrice,setLCurPrice:)) TarsInt64 JV2_PROP_NM(o,2,lCurPrice);
@property (nonatomic, assign, JV2_PROP_GS_V2(lOriPrice,setLOriPrice:)) TarsInt64 JV2_PROP_NM(o,3,lOriPrice);
@property (nonatomic, assign, JV2_PROP_GS_V2(productId,setProductId:)) TarsInt64 JV2_PROP_NM(o,4,productId);
@property (nonatomic, retain, JV2_PROP_GS_V2(strProductId,setStrProductId:)) NSString* JV2_PROP_NM(o,5,strProductId);
@property (nonatomic, assign, JV2_PROP_GS_V2(source,setSource:)) TarsInt32 JV2_PROP_NM(o,6,source);

@end
