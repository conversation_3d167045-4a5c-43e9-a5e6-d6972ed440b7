// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_comm.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>
@class proto_interact_ecommerce_comm_AggregateCoverProductInfo;

@interface proto_interact_ecommerce_comm_AggregateCoverInfo : JceObjectV2

@property (nonatomic, retain, JV2_PROP_GS_V2(strURL,setStrURL:)) NSString* JV2_PROP_NM(o,0,strURL);
@property (nonatomic, assign, JV2_PROP_GS_V2(uJumpType,setUJumpType:)) TarsUInt32 JV2_PROP_NM(o,1,uJumpType);
@property (nonatomic, retain, JV2_PROP_GS_V2(stProductInfo,setStProductInfo:)) proto_interact_ecommerce_comm_AggregateCoverProductInfo* JV2_PROP_NM(o,2,stProductInfo);
@property (nonatomic, assign, JV2_PROP_GS_V2(lAnchorId,setLAnchorId:)) TarsInt64 JV2_PROP_NM(o,3,lAnchorId);

@end
