// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_comm.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>

enum {
    proto_interact_ecommerce_comm_emOrderState_EM_ORDERSTATE_UNSPECIFIED = 0,
    proto_interact_ecommerce_comm_emOrderState_EM_ORDERSTATE_PENDING = 1,
    proto_interact_ecommerce_comm_emOrderState_EM_ORDERSTATE_CANCELED = 2,
    proto_interact_ecommerce_comm_emOrderState_EM_ORDERSTATE_PARTIALPAID = 3,
    proto_interact_ecommerce_comm_emOrderState_EM_ORDERSTATE_PAID = 4,
    proto_interact_ecommerce_comm_emOrderState_EM_ORDERSTATE_PARTIAL_SHIPPED = 5,
    proto_interact_ecommerce_comm_emOrderState_EM_ORDERSTATE_SHIPPED = 6,
    proto_interact_ecommerce_comm_emOrderState_EM_ORDERSTATE_COMPLETED = 7,
    proto_interact_ecommerce_comm_emOrderState_EM_ORDERSTATE_REFUNDED = 8
};
#define proto_interact_ecommerce_comm_emOrderState TarsInt32

