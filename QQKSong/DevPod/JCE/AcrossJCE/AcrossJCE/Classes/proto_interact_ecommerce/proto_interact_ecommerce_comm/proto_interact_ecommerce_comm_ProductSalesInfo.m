// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_comm.jce   
// **********************************************************************

#import "proto_interact_ecommerce_comm_ProductSalesInfo.h"

@implementation proto_interact_ecommerce_comm_ProductSalesInfo

@synthesize JV2_PROP_NM(o,0,productId);
@synthesize JV2_PROP_NM(o,1,num);
@synthesize JV2_PROP_NM(o,2,strProductId);
@synthesize JV2_PROP_NM(o,3,source);
@synthesize JV2_PROP_NM(o,4,strSpuId);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_comm_ProductSalesInfo class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strProductId) = DefaultTarsString;
        JV2_PROP(strSpuId) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_comm.ProductSalesInfo";
}

@end
