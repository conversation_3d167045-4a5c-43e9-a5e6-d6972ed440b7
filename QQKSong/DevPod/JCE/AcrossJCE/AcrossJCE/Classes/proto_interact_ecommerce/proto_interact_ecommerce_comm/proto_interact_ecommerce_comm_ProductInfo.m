// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_comm.jce   
// **********************************************************************

#import "proto_interact_ecommerce_comm_ProductInfo.h"
#import "proto_interact_ecommerce_comm_PriceDisplay.h"
#import "proto_interact_ecommerce_comm_ProductDetailURL.h"
#import "proto_interact_ecommerce_comm_ProductExtraLable.h"
#import "proto_interact_ecommerce_comm_ProductOperations.h"
#import "proto_interact_ecommerce_comm_SKU.h"
#import "proto_interact_ecommerce_product_CategoryInfo.h"
#import "proto_interact_ecommerce_product_ProductPlatInfo.h"

@implementation proto_interact_ecommerce_comm_ProductInfo

@synthesize JV2_PROP_NM(o,0,lProductId);
@synthesize JV2_PROP_NM(o,1,strProductId);
@synthesize JV2_PROP_NM(o,2,strProductName);
@synthesize JV2_PROP_EX(o,3,coverImages,VONSString);
@synthesize JV2_PROP_NM(o,4,strThumbnailImage);
@synthesize JV2_PROP_EX(o,5,skus,VOproto_interact_ecommerce_comm_SKU);
@synthesize JV2_PROP_NM(o,6,minPrice);
@synthesize JV2_PROP_NM(o,7,maxPrice);
@synthesize JV2_PROP_EX(o,8,stOperationsConfig,VOproto_interact_ecommerce_comm_ProductOperations);
@synthesize JV2_PROP_NM(o,9,lProductSequenceNo);
@synthesize JV2_PROP_NM(o,10,sales);
@synthesize JV2_PROP_NM(o,11,description);
@synthesize JV2_PROP_NM(o,12,stock);
@synthesize JV2_PROP_NM(o,13,shopID);
@synthesize JV2_PROP_NM(o,14,detailURL);
@synthesize JV2_PROP_NM(o,15,source);
@synthesize JV2_PROP_NM(o,16,platInfo);
@synthesize JV2_PROP_NM(o,17,category);
@synthesize JV2_PROP_NM(o,18,commission);
@synthesize JV2_PROP_NM(o,19,commissionRate);
@synthesize JV2_PROP_NM(o,20,subtitle);
@synthesize JV2_PROP_NM(o,21,salesTips);
@synthesize JV2_PROP_NM(o,22,status);
@synthesize JV2_PROP_NM(o,23,tmeCommission);
@synthesize JV2_PROP_NM(o,24,tmeCommissionRate);
@synthesize JV2_PROP_NM(o,25,originCommission);
@synthesize JV2_PROP_NM(o,26,originCommissionRate);
@synthesize JV2_PROP_NM(o,27,spuId);
@synthesize JV2_PROP_NM(o,28,stPriceDisplay);
@synthesize JV2_PROP_EX(o,29,vctProductExtraLable,VOproto_interact_ecommerce_comm_ProductExtraLable);
@synthesize JV2_PROP_NM(o,30,lLaunchTimeTs);
@synthesize JV2_PROP_EX(o,31,couponBatchIds,VONSString);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_comm_ProductInfo class]) {
        [proto_interact_ecommerce_comm_PriceDisplay initialize];
        [proto_interact_ecommerce_comm_ProductDetailURL initialize];
        [proto_interact_ecommerce_comm_ProductExtraLable initialize];
        [proto_interact_ecommerce_comm_ProductOperations initialize];
        [proto_interact_ecommerce_comm_SKU initialize];
        [proto_interact_ecommerce_product_CategoryInfo initialize];
                [proto_interact_ecommerce_product_ProductPlatInfo initialize];
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strProductId) = DefaultTarsString;
        JV2_PROP(strProductName) = DefaultTarsString;
        JV2_PROP(strThumbnailImage) = DefaultTarsString;
        JV2_PROP(description) = DefaultTarsString;
        JV2_PROP(shopID) = DefaultTarsString;
        JV2_PROP(subtitle) = DefaultTarsString;
        JV2_PROP(salesTips) = DefaultTarsString;
        JV2_PROP(spuId) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_comm.ProductInfo";
}

@end
