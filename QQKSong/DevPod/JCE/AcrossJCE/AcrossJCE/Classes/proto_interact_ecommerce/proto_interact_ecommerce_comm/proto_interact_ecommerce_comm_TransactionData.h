// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_comm.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>

@interface proto_interact_ecommerce_comm_TransactionData : JceObjectV2

@property (nonatomic, retain, JV2_PROP_GS_V2(strDate,setStrDate:)) NSString* JV2_PROP_NM(o,0,strDate);
@property (nonatomic, assign, JV2_PROP_GS_V2(lTransactionAmount,setLTransactionAmount:)) TarsInt64 JV2_PROP_NM(o,1,lTransactionAmount);
@property (nonatomic, assign, JV2_PROP_GS_V2(lTradingVolume,setLTradingVolume:)) TarsInt64 JV2_PROP_NM(o,2,lTradingVolume);
@property (nonatomic, assign, JV2_PROP_GS_V2(lAnchorNum,setLAnchorNum:)) TarsInt64 JV2_PROP_NM(o,3,lAnchorNum);
@property (nonatomic, assign, JV2_PROP_GS_V2(lUserNum,setLUserNum:)) TarsInt64 JV2_PROP_NM(o,4,lUserNum);
@property (nonatomic, assign, JV2_PROP_GS_V2(lOrderNum,setLOrderNum:)) TarsInt64 JV2_PROP_NM(o,5,lOrderNum);

@end
