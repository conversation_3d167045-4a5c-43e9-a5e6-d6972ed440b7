// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_comm.jce   
// **********************************************************************

#import "proto_interact_ecommerce_comm_AnchorProductOverviewFilter.h"

@implementation proto_interact_ecommerce_comm_AnchorProductOverviewFilter

@synthesize JV2_PROP_NM(o,0,iSearchType);
@synthesize JV2_PROP_NM(o,1,strSearchContent);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_comm_AnchorProductOverviewFilter class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strSearchContent) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_comm.AnchorProductOverviewFilter";
}

@end
