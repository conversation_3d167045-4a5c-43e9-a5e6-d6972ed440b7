// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_comm.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>

@interface proto_interact_ecommerce_comm_PriceDisplay : JceObjectV2

@property (nonatomic, assign, JV2_PROP_GS_V2(lPriceAfterCoupon,setLPriceAfterCoupon:)) TarsInt64 JV2_PROP_NM(o,0,lPriceAfterCoupon);
@property (nonatomic, assign, JV2_PROP_GS_V2(uPricesDisplayType,setUPricesDisplayType:)) TarsUInt32 JV2_PROP_NM(o,1,uPricesDisplayType);
@property (nonatomic, retain, JV2_PROP_GS_V2(strPriceDesription,setStrPriceDesription:)) NSString* JV2_PROP_NM(o,2,strPriceDesription);

@end
