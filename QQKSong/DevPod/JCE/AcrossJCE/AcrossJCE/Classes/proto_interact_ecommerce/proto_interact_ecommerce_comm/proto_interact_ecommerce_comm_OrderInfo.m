// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_comm.jce   
// **********************************************************************

#import "proto_interact_ecommerce_comm_OrderInfo.h"
#import "proto_interact_ecommerce_comm_DeliveryInfo.h"
#import "proto_interact_ecommerce_comm_ProductInfo.h"
#import "proto_interact_ecommerce_product_CategoryInfo.h"
#import "proto_interact_ecommerce_product_ProductPlatInfo.h"

@implementation proto_interact_ecommerce_comm_OrderInfo

@synthesize JV2_PROP_NM(o,0,lAnchorId);
@synthesize JV2_PROP_NM(o,1,strAnchorId);
@synthesize JV2_PROP_NM(o,2,lOrderId);
@synthesize JV2_PROP_NM(o,3,strOrderId);
@synthesize JV2_PROP_NM(o,4,stProductInfo);
@synthesize JV2_PROP_NM(o,5,lUserId);
@synthesize JV2_PROP_NM(o,6,strNickName);
@synthesize JV2_PROP_NM(o,7,lProductNum);
@synthesize JV2_PROP_NM(o,8,lOrderTs);
@synthesize JV2_PROP_NM(o,9,uState);
@synthesize JV2_PROP_NM(o,10,lAmount);
@synthesize JV2_PROP_NM(o,11,strDetail);
@synthesize JV2_PROP_NM(o,12,source);
@synthesize JV2_PROP_NM(o,13,platInfo);
@synthesize JV2_PROP_NM(o,14,commission);
@synthesize JV2_PROP_NM(o,15,commissionRate);
@synthesize JV2_PROP_NM(o,16,category);
@synthesize JV2_PROP_NM(o,17,stDeliveryInfo);
@synthesize JV2_PROP_NM(o,18,wxMiniprogramAppid);
@synthesize JV2_PROP_NM(o,19,wxMiniprogramName);
@synthesize JV2_PROP_NM(o,20,H5Url);
@synthesize JV2_PROP_NM(o,21,totalAmount);
@synthesize JV2_PROP_NM(o,22,lPayTs);
@synthesize JV2_PROP_NM(o,23,tmeCommission);
@synthesize JV2_PROP_NM(o,24,tmeCommissionRate);
@synthesize JV2_PROP_NM(o,25,originCommission);
@synthesize JV2_PROP_NM(o,26,originCommissionRate);
@synthesize JV2_PROP_NM(o,27,strRefundUrl);
@synthesize JV2_PROP_NM(o,28,uAppId);
@synthesize JV2_PROP_NM(o,29,createTime);
@synthesize JV2_PROP_NM(o,30,iEcommerceScene);
@synthesize JV2_PROP_NM(o,31,strExt);
@synthesize JV2_PROP_NM(o,32,couponAmount);
@synthesize JV2_PROP_NM(o,33,sellerQualification);
@synthesize JV2_PROP_NM(o,34,lGuildId);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_comm_OrderInfo class]) {
        [proto_interact_ecommerce_comm_DeliveryInfo initialize];
        [proto_interact_ecommerce_comm_ProductInfo initialize];
        [proto_interact_ecommerce_product_CategoryInfo initialize];
        [proto_interact_ecommerce_product_ProductPlatInfo initialize];
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strAnchorId) = DefaultTarsString;
        JV2_PROP(strOrderId) = DefaultTarsString;
        JV2_PROP(strNickName) = DefaultTarsString;
        JV2_PROP(strDetail) = DefaultTarsString;
        JV2_PROP(wxMiniprogramAppid) = DefaultTarsString;
        JV2_PROP(wxMiniprogramName) = DefaultTarsString;
        JV2_PROP(H5Url) = DefaultTarsString;
        JV2_PROP(strRefundUrl) = DefaultTarsString;
        JV2_PROP(strExt) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_comm.OrderInfo";
}

@end
