// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_comm.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>

@interface proto_interact_ecommerce_comm_ProductDetailURL : JceObjectV2

@property (nonatomic, retain, JV2_PROP_GS_V2(jumpURL,setJumpURL:)) NSString* JV2_PROP_NM(o,0,jumpURL);
@property (nonatomic, retain, JV2_PROP_GS_V2(exposureURL,setExposureURL:)) NSString* JV2_PROP_NM(o,1,exposureURL);
@property (nonatomic, retain, JV2_PROP_GS_V2(clickURL,setClickURL:)) NSString* JV2_PROP_NM(o,2,clickURL);
@property (nonatomic, retain, JV2_PROP_GS_V2(wxMiniprogramAppid,setWxMiniprogramAppid:)) NSString* JV2_PROP_NM(o,3,wxMiniprogramAppid);
@property (nonatomic, retain, JV2_PROP_GS_V2(wxMiniprogramName,setWxMiniprogramName:)) NSString* JV2_PROP_NM(o,4,wxMiniprogramName);
@property (nonatomic, retain, JV2_PROP_GS_V2(H5Url,setH5Url:)) NSString* JV2_PROP_NM(o,5,H5Url);
@property (nonatomic, retain, JV2_PROP_GS_V2(purchasePageURL,setPurchasePageURL:)) NSString* JV2_PROP_NM(o,6,purchasePageURL);

@end
