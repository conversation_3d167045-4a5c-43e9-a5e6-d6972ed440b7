// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_comm.jce   
// **********************************************************************

#import "proto_interact_ecommerce_comm_AccountItem.h"

@implementation proto_interact_ecommerce_comm_AccountItem

@synthesize JV2_PROP_EX(o,0,vctAnchorPool,VONSString);
@synthesize JV2_PROP_NM(o,1,strIdentity);
@synthesize JV2_PROP_NM(o,2,strAccountId);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_comm_AccountItem class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strIdentity) = DefaultTarsString;
        JV2_PROP(strAccountId) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_comm.AccountItem";
}

@end
