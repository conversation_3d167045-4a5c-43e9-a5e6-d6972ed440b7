// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_comm.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>

@interface proto_interact_ecommerce_comm_ProductSimpleInfo : JceObjectV2

@property (nonatomic, retain, JV2_PROP_GS_V2(strProductId,setStrProductId:)) NSString* JV2_PROP_NM(o,0,strProductId);
@property (nonatomic, assign, JV2_PROP_GS_V2(source,setSource:)) TarsInt32 JV2_PROP_NM(o,1,source);
@property (nonatomic, retain, JV2_PROP_GS_V2(strProductName,setStrProductName:)) NSString* JV2_PROP_NM(o,2,strProductName);
@property (nonatomic, retain, JV2_PROP_GS_V2(strAnchorId,setStrAnchorId:)) NSString* JV2_PROP_NM(o,3,strAnchorId);
@property (nonatomic, assign, JV2_PROP_GS_V2(platId,setPlatId:)) TarsUInt32 JV2_PROP_NM(o,4,platId);
@property (nonatomic, assign, JV2_PROP_GS_V2(firstCategoryId,setFirstCategoryId:)) TarsUInt32 JV2_PROP_NM(o,5,firstCategoryId);
@property (nonatomic, assign, JV2_PROP_GS_V2(commission,setCommission:)) TarsInt64 JV2_PROP_NM(o,6,commission);
@property (nonatomic, assign, JV2_PROP_GS_V2(commissionRate,setCommissionRate:)) TarsInt64 JV2_PROP_NM(o,7,commissionRate);
@property (nonatomic, assign, JV2_PROP_GS_V2(tmeCommission,setTmeCommission:)) TarsInt64 JV2_PROP_NM(o,8,tmeCommission);
@property (nonatomic, assign, JV2_PROP_GS_V2(tmeCommissionRate,setTmeCommissionRate:)) TarsInt64 JV2_PROP_NM(o,9,tmeCommissionRate);

@end
