// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_comm.jce   
// **********************************************************************

#import "proto_interact_ecommerce_comm_ProductDetailURL.h"

@implementation proto_interact_ecommerce_comm_ProductDetailURL

@synthesize JV2_PROP_NM(o,0,jumpURL);
@synthesize JV2_PROP_NM(o,1,exposureURL);
@synthesize JV2_PROP_NM(o,2,clickURL);
@synthesize JV2_PROP_NM(o,3,wxMiniprogramAppid);
@synthesize JV2_PROP_NM(o,4,wxMiniprogramName);
@synthesize JV2_PROP_NM(o,5,H5Url);
@synthesize JV2_PROP_NM(o,6,purchasePageURL);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_comm_ProductDetailURL class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(jumpURL) = DefaultTarsString;
        JV2_PROP(exposureURL) = DefaultTarsString;
        JV2_PROP(clickURL) = DefaultTarsString;
        JV2_PROP(wxMiniprogramAppid) = DefaultTarsString;
        JV2_PROP(wxMiniprogramName) = DefaultTarsString;
        JV2_PROP(H5Url) = DefaultTarsString;
        JV2_PROP(purchasePageURL) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_comm.ProductDetailURL";
}

@end
