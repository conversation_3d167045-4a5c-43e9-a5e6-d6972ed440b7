// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_comm.jce   
// **********************************************************************

#import "proto_interact_ecommerce_comm_SKU.h"

@implementation proto_interact_ecommerce_comm_SKU

@synthesize JV2_PROP_NM(o,0,id);
@synthesize JV2_PROP_NM(o,1,productID);
@synthesize JV2_PROP_NM(o,2,price);
@synthesize JV2_PROP_NM(o,3,marketPrice);
@synthesize JV2_PROP_NM(o,4,stock);
@synthesize JV2_PROP_NM(o,5,trackStock);
@synthesize JV2_PROP_NM(o,6,skuID);
@synthesize JV2_PROP_NM(o,7,strProductId);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_comm_SKU class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(skuID) = DefaultTarsString;
        JV2_PROP(strProductId) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_comm.SKU";
}

@end
