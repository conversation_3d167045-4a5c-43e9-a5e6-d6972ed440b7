// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_comm.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>

@interface proto_interact_ecommerce_comm_SKU : JceObjectV2

@property (nonatomic, assign, JV2_PROP_GS_V2(id,setId:)) TarsInt64 JV2_PROP_NM(o,0,id);
@property (nonatomic, assign, JV2_PROP_GS_V2(productID,setProductID:)) TarsInt64 JV2_PROP_NM(o,1,productID);
@property (nonatomic, assign, JV2_PROP_GS_V2(price,setPrice:)) TarsInt64 JV2_PROP_NM(o,2,price);
@property (nonatomic, assign, JV2_PROP_GS_V2(marketPrice,setMarketPrice:)) TarsInt64 JV2_PROP_NM(o,3,marketPrice);
@property (nonatomic, assign, JV2_PROP_GS_V2(stock,setStock:)) TarsInt64 JV2_PROP_NM(o,4,stock);
@property (nonatomic, assign, JV2_PROP_GS_V2(trackStock,setTrackStock:)) TarsUInt32 JV2_PROP_NM(o,5,trackStock);
@property (nonatomic, retain, JV2_PROP_GS_V2(skuID,setSkuID:)) NSString* JV2_PROP_NM(o,6,skuID);
@property (nonatomic, retain, JV2_PROP_GS_V2(strProductId,setStrProductId:)) NSString* JV2_PROP_NM(o,7,strProductId);

@end
