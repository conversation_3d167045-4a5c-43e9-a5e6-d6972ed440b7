// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_comm.jce   
// **********************************************************************

#import "proto_interact_ecommerce_comm_PriceDisplay.h"

@implementation proto_interact_ecommerce_comm_PriceDisplay

@synthesize JV2_PROP_NM(o,0,lPriceAfterCoupon);
@synthesize JV2_PROP_NM(o,1,uPricesDisplayType);
@synthesize JV2_PROP_NM(o,2,strPriceDesription);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_comm_PriceDisplay class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strPriceDesription) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_comm.PriceDisplay";
}

@end
