// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_comm.jce   
// **********************************************************************

#import "proto_interact_ecommerce_comm_AggregateCoverProductInfo.h"

@implementation proto_interact_ecommerce_comm_AggregateCoverProductInfo

@synthesize JV2_PROP_NM(o,0,strPic);
@synthesize JV2_PROP_NM(o,1,strProductName);
@synthesize JV2_PROP_NM(o,2,lCurPrice);
@synthesize JV2_PROP_NM(o,3,lOriPrice);
@synthesize JV2_PROP_NM(o,4,productId);
@synthesize JV2_PROP_NM(o,5,strProductId);
@synthesize JV2_PROP_NM(o,6,source);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_comm_AggregateCoverProductInfo class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strPic) = DefaultTarsString;
        JV2_PROP(strProductName) = DefaultTarsString;
        JV2_PROP(strProductId) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_comm.AggregateCoverProductInfo";
}

@end
