// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_comm.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>
@class proto_interact_ecommerce_product_CategoryInfo;
@class proto_interact_ecommerce_product_ProductPlatInfo;
@class proto_interact_ecommerce_comm_PriceDisplay;
@class proto_interact_ecommerce_comm_ProductDetailURL;
@class proto_interact_ecommerce_comm_ProductExtraLable;
@class proto_interact_ecommerce_comm_ProductOperations;
@class proto_interact_ecommerce_comm_SKU;

@interface proto_interact_ecommerce_comm_ProductInfo : JceObjectV2

@property (nonatomic, assign, JV2_PROP_GS_V2(lProductId,setLProductId:)) TarsInt64 JV2_PROP_NM(o,0,lProductId);
@property (nonatomic, retain, JV2_PROP_GS_V2(strProductId,setStrProductId:)) NSString* JV2_PROP_NM(o,1,strProductId);
@property (nonatomic, retain, JV2_PROP_GS_V2(strProductName,setStrProductName:)) NSString* JV2_PROP_NM(o,2,strProductName);
@property (nonatomic, retain, JV2_PROP_GS_V2(coverImages,setCoverImages:)) NSArray* JV2_PROP_EX(o,3,coverImages,VONSString);
@property (nonatomic, retain, JV2_PROP_GS_V2(strThumbnailImage,setStrThumbnailImage:)) NSString* JV2_PROP_NM(o,4,strThumbnailImage);
@property (nonatomic, retain, JV2_PROP_GS_V2(skus,setSkus:)) NSArray* JV2_PROP_EX(o,5,skus,VOproto_interact_ecommerce_comm_SKU);
@property (nonatomic, assign, JV2_PROP_GS_V2(minPrice,setMinPrice:)) TarsInt64 JV2_PROP_NM(o,6,minPrice);
@property (nonatomic, assign, JV2_PROP_GS_V2(maxPrice,setMaxPrice:)) TarsInt64 JV2_PROP_NM(o,7,maxPrice);
@property (nonatomic, retain, JV2_PROP_GS_V2(stOperationsConfig,setStOperationsConfig:)) NSArray* JV2_PROP_EX(o,8,stOperationsConfig,VOproto_interact_ecommerce_comm_ProductOperations);
@property (nonatomic, assign, JV2_PROP_GS_V2(lProductSequenceNo,setLProductSequenceNo:)) TarsInt64 JV2_PROP_NM(o,9,lProductSequenceNo);
@property (nonatomic, assign, JV2_PROP_GS_V2(sales,setSales:)) TarsInt64 JV2_PROP_NM(o,10,sales);
@property (nonatomic, retain, JV2_PROP_GS_V2(description,setDescription:)) NSString* JV2_PROP_NM(o,11,description);
@property (nonatomic, assign, JV2_PROP_GS_V2(stock,setStock:)) TarsInt64 JV2_PROP_NM(o,12,stock);
@property (nonatomic, retain, JV2_PROP_GS_V2(shopID,setShopID:)) NSString* JV2_PROP_NM(o,13,shopID);
@property (nonatomic, retain, JV2_PROP_GS_V2(detailURL,setDetailURL:)) proto_interact_ecommerce_comm_ProductDetailURL* JV2_PROP_NM(o,14,detailURL);
@property (nonatomic, assign, JV2_PROP_GS_V2(source,setSource:)) TarsInt32 JV2_PROP_NM(o,15,source);
@property (nonatomic, retain, JV2_PROP_GS_V2(platInfo,setPlatInfo:)) proto_interact_ecommerce_product_ProductPlatInfo* JV2_PROP_NM(o,16,platInfo);
@property (nonatomic, retain, JV2_PROP_GS_V2(category,setCategory:)) proto_interact_ecommerce_product_CategoryInfo* JV2_PROP_NM(o,17,category);
@property (nonatomic, assign, JV2_PROP_GS_V2(commission,setCommission:)) TarsInt64 JV2_PROP_NM(o,18,commission);
@property (nonatomic, assign, JV2_PROP_GS_V2(commissionRate,setCommissionRate:)) TarsInt64 JV2_PROP_NM(o,19,commissionRate);
@property (nonatomic, retain, JV2_PROP_GS_V2(subtitle,setSubtitle:)) NSString* JV2_PROP_NM(o,20,subtitle);
@property (nonatomic, retain, JV2_PROP_GS_V2(salesTips,setSalesTips:)) NSString* JV2_PROP_NM(o,21,salesTips);
@property (nonatomic, assign, JV2_PROP_GS_V2(status,setStatus:)) TarsUInt32 JV2_PROP_NM(o,22,status);
@property (nonatomic, assign, JV2_PROP_GS_V2(tmeCommission,setTmeCommission:)) TarsInt64 JV2_PROP_NM(o,23,tmeCommission);
@property (nonatomic, assign, JV2_PROP_GS_V2(tmeCommissionRate,setTmeCommissionRate:)) TarsInt64 JV2_PROP_NM(o,24,tmeCommissionRate);
@property (nonatomic, assign, JV2_PROP_GS_V2(originCommission,setOriginCommission:)) TarsInt64 JV2_PROP_NM(o,25,originCommission);
@property (nonatomic, assign, JV2_PROP_GS_V2(originCommissionRate,setOriginCommissionRate:)) TarsInt64 JV2_PROP_NM(o,26,originCommissionRate);
@property (nonatomic, retain, JV2_PROP_GS_V2(spuId,setSpuId:)) NSString* JV2_PROP_NM(o,27,spuId);
@property (nonatomic, retain, JV2_PROP_GS_V2(stPriceDisplay,setStPriceDisplay:)) proto_interact_ecommerce_comm_PriceDisplay* JV2_PROP_NM(o,28,stPriceDisplay);
@property (nonatomic, retain, JV2_PROP_GS_V2(vctProductExtraLable,setVctProductExtraLable:)) NSArray* JV2_PROP_EX(o,29,vctProductExtraLable,VOproto_interact_ecommerce_comm_ProductExtraLable);
@property (nonatomic, assign, JV2_PROP_GS_V2(lLaunchTimeTs,setLLaunchTimeTs:)) TarsInt64 JV2_PROP_NM(o,30,lLaunchTimeTs);
@property (nonatomic, retain, JV2_PROP_GS_V2(couponBatchIds,setCouponBatchIds:)) NSArray* JV2_PROP_EX(o,31,couponBatchIds,VONSString);

@end
