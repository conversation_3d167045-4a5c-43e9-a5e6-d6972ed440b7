// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_comm.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>
#import "proto_interact_ecommerce_comm_emProductLabelShowPos.h"

@interface proto_interact_ecommerce_comm_ProductExtraLable : JceObjectV2

@property (nonatomic, assign, JV2_PROP_GS_V2(uUniqId,setUUniqId:)) TarsUInt32 JV2_PROP_NM(o,0,uUniqId);
@property (nonatomic, assign, JV2_PROP_GS_V2(uLableType,setULableType:)) TarsUInt32 JV2_PROP_NM(o,1,uLableType);
@property (nonatomic, retain, JV2_PROP_GS_V2(strContent1,setStrContent1:)) NSString* JV2_PROP_NM(o,2,strContent1);
@property (nonatomic, retain, JV2_PROP_GS_V2(strBgColor1,setStrBgColor1:)) NSString* JV2_PROP_NM(o,3,strBgColor1);
@property (nonatomic, retain, JV2_PROP_GS_V2(strFontColor1,setStrFontColor1:)) NSString* JV2_PROP_NM(o,4,strFontColor1);
@property (nonatomic, retain, JV2_PROP_GS_V2(strStrokeColor1,setStrStrokeColor1:)) NSString* JV2_PROP_NM(o,5,strStrokeColor1);
@property (nonatomic, retain, JV2_PROP_GS_V2(strContent2,setStrContent2:)) NSString* JV2_PROP_NM(o,6,strContent2);
@property (nonatomic, retain, JV2_PROP_GS_V2(strBgColor2,setStrBgColor2:)) NSString* JV2_PROP_NM(o,7,strBgColor2);
@property (nonatomic, retain, JV2_PROP_GS_V2(strFontColor2,setStrFontColor2:)) NSString* JV2_PROP_NM(o,8,strFontColor2);
@property (nonatomic, retain, JV2_PROP_GS_V2(strStrokeColor2,setStrStrokeColor2:)) NSString* JV2_PROP_NM(o,9,strStrokeColor2);
@property (nonatomic, retain, JV2_PROP_GS_V2(strCouponBatchId,setStrCouponBatchId:)) NSString* JV2_PROP_NM(o,10,strCouponBatchId);
@property (nonatomic, assign, JV2_PROP_GS_V2(uLabelSource,setULabelSource:)) TarsUInt32 JV2_PROP_NM(o,11,uLabelSource);
@property (nonatomic, retain, JV2_PROP_GS_V2(strJumpUrl,setStrJumpUrl:)) NSString* JV2_PROP_NM(o,12,strJumpUrl);
@property (nonatomic, retain, JV2_PROP_GS_V2(strFrontIcon,setStrFrontIcon:)) NSString* JV2_PROP_NM(o,13,strFrontIcon);
@property (nonatomic, retain, JV2_PROP_GS_V2(strBackIcon,setStrBackIcon:)) NSString* JV2_PROP_NM(o,14,strBackIcon);
@property (nonatomic, assign, JV2_PROP_GS_V2(uRecommendLabelType,setURecommendLabelType:)) TarsUInt32 JV2_PROP_NM(o,15,uRecommendLabelType);
@property (nonatomic, assign, JV2_PROP_GS_V2(uShowPos,setUShowPos:)) proto_interact_ecommerce_comm_emProductLabelShowPos JV2_PROP_NM(o,16,uShowPos);

@end
