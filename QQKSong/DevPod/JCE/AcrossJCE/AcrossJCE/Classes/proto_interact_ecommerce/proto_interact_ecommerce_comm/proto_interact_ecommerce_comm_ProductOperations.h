// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_comm.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>

@interface proto_interact_ecommerce_comm_ProductOperations : JceObjectV2

@property (nonatomic, assign, JV2_PROP_GS_V2(uOperationsType,setUOperationsType:)) TarsUInt32 JV2_PROP_NM(o,0,uOperationsType);
@property (nonatomic, assign, JV2_PROP_GS_V2(uIsOpen,setUIsOpen:)) TarsUInt32 JV2_PROP_NM(o,1,uIsOpen);
@property (nonatomic, assign, JV2_PROP_GS_V2(uStartTs,setUStartTs:)) TarsUInt32 JV2_PROP_NM(o,2,uStartTs);
@property (nonatomic, assign, JV2_PROP_GS_V2(uEndTs,setUEndTs:)) TarsUInt32 JV2_PROP_NM(o,3,uEndTs);
@property (nonatomic, assign, JV2_PROP_GS_V2(productId,setProductId:)) TarsInt64 JV2_PROP_NM(o,4,productId);
@property (nonatomic, assign, JV2_PROP_GS_V2(updateTs,setUpdateTs:)) TarsInt64 JV2_PROP_NM(o,5,updateTs);
@property (nonatomic, assign, JV2_PROP_GS_V2(uIsDefault,setUIsDefault:)) TarsUInt32 JV2_PROP_NM(o,6,uIsDefault);
@property (nonatomic, assign, JV2_PROP_GS_V2(source,setSource:)) TarsInt32 JV2_PROP_NM(o,7,source);
@property (nonatomic, retain, JV2_PROP_GS_V2(strProductId,setStrProductId:)) NSString* JV2_PROP_NM(o,8,strProductId);
@property (nonatomic, assign, JV2_PROP_GS_V2(lProductSequenceNo,setLProductSequenceNo:)) TarsInt64 JV2_PROP_NM(o,9,lProductSequenceNo);

@end
