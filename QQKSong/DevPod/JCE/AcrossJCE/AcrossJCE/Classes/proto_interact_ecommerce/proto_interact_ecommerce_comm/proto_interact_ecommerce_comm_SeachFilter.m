// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_comm.jce   
// **********************************************************************

#import "proto_interact_ecommerce_comm_SeachFilter.h"

@implementation proto_interact_ecommerce_comm_SeachFilter

@synthesize JV2_PROP_NM(o,0,iSearchType);
@synthesize JV2_PROP_NM(o,1,strContent);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_comm_SeachFilter class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strContent) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_comm.SeachFilter";
}

@end
