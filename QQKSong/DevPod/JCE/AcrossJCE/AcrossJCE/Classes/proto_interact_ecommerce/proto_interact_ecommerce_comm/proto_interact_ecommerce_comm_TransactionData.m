// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_comm.jce   
// **********************************************************************

#import "proto_interact_ecommerce_comm_TransactionData.h"

@implementation proto_interact_ecommerce_comm_TransactionData

@synthesize JV2_PROP_NM(o,0,strDate);
@synthesize JV2_PROP_NM(o,1,lTransactionAmount);
@synthesize JV2_PROP_NM(o,2,lTradingVolume);
@synthesize JV2_PROP_NM(o,3,lAnchor<PERSON>um);
@synthesize JV2_PROP_NM(o,4,lUserNum);
@synthesize JV2_PROP_NM(o,5,lOrderNum);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_comm_TransactionData class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strDate) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_comm.TransactionData";
}

@end
