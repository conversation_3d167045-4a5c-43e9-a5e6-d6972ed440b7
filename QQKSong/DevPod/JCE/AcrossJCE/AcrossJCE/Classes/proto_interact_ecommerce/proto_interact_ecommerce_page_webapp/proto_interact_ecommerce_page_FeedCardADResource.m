// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_page_webapp.jce   
// **********************************************************************

#import "proto_interact_ecommerce_page_FeedCardADResource.h"
#import "proto_interact_ecommerce_page_FeedCardADResourceItem.h"

@implementation proto_interact_ecommerce_page_FeedCardADResource

@synthesize JV2_PROP_EX(o,0,vctADResourceItem,VOproto_interact_ecommerce_page_FeedCardADResourceItem);
@synthesize JV2_PROP_NM(o,1,iConfId);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_page_FeedCardADResource class]) {
        [proto_interact_ecommerce_page_FeedCardADResourceItem initialize];
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_page.FeedCardADResource";
}

@end
