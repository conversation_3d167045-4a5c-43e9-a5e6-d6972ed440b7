// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_page_webapp.jce   
// **********************************************************************

#import "proto_interact_ecommerce_page_GetMallTabRsp.h"
#import "proto_interact_ecommerce_page_FeedCardItem.h"
#import "proto_interact_ecommerce_page_QuickBanner.h"
#import "proto_interact_ecommerce_page_RecModule.h"
#import "proto_interact_ecommerce_page_TopBanner.h"

@implementation proto_interact_ecommerce_page_GetMallTabRsp

@synthesize JV2_PROP_NM(o,0,stTopBanner);
@synthesize JV2_PROP_NM(o,1,stQuickBanner);
@synthesize JV2_PROP_NM(o,2,stRecModule);
@synthesize JV2_PROP_EX(o,3,vctFeedCardItem,VOproto_interact_ecommerce_page_FeedCardItem);
@synthesize JV2_PROP_NM(o,4,strPassBack);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_page_GetMallTabRsp class]) {
        [proto_interact_ecommerce_page_FeedCardItem initialize];
        [proto_interact_ecommerce_page_QuickBanner initialize];
        [proto_interact_ecommerce_page_RecModule initialize];
        [proto_interact_ecommerce_page_TopBanner initialize];
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strPassBack) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_page.GetMallTabRsp";
}

@end
