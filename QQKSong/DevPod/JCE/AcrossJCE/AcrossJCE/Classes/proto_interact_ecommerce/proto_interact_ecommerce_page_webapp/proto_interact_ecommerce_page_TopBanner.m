// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_page_webapp.jce   
// **********************************************************************

#import "proto_interact_ecommerce_page_TopBanner.h"
#import "proto_interact_ecommerce_page_TopBannerLiveHotSale.h"
#import "proto_interact_ecommerce_page_TopBannerPicture.h"

@implementation proto_interact_ecommerce_page_TopBanner

@synthesize JV2_PROP_NM(o,0,emBannerType);
@synthesize JV2_PROP_NM(o,1,isDisplay);
@synthesize JV2_PROP_NM(o,2,stTopBannerPicture);
@synthesize JV2_PROP_NM(o,3,stTopBannerLiveHotSale);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_page_TopBanner class]) {
        [proto_interact_ecommerce_page_TopBannerLiveHotSale initialize];
        [proto_interact_ecommerce_page_TopBannerPicture initialize];
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(isDisplay) = NO;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_page.TopBanner";
}

@end
