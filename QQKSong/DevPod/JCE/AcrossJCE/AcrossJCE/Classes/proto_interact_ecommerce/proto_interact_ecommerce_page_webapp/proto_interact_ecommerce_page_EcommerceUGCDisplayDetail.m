// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_page_webapp.jce   
// **********************************************************************

#import "proto_interact_ecommerce_page_EcommerceUGCDisplayDetail.h"

@implementation proto_interact_ecommerce_page_EcommerceUGCDisplayDetail

@synthesize JV2_PROP_NM(o,0,strUGCId);
@synthesize JV2_PROP_NM(o,1,strCover);
@synthesize JV2_PROP_NM(o,2,strPlayUrl);
@synthesize JV2_PROP_NM(o,3,strJumpUrl);
@synthesize JV2_PROP_NM(o,4,strDesc);
@synthesize JV2_PROP_NM(o,5,strFaceUrl);
@synthesize JV2_PROP_NM(o,6,strNick);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_page_EcommerceUGCDisplayDetail class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strUGCId) = DefaultTarsString;
        JV2_PROP(strCover) = DefaultTarsString;
        JV2_PROP(strPlayUrl) = DefaultTarsString;
        JV2_PROP(strJumpUrl) = DefaultTarsString;
        JV2_PROP(strDesc) = DefaultTarsString;
        JV2_PROP(strFaceUrl) = DefaultTarsString;
        JV2_PROP(strNick) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_page.EcommerceUGCDisplayDetail";
}

@end
