// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_page_webapp.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>
@class proto_interact_ecommerce_page_ThemeConfigBackground;
@class proto_interact_ecommerce_page_ThemeConfigTab;

@interface proto_interact_ecommerce_page_ThemeConfigItem : JceObjectV2

@property (nonatomic, assign, JV2_PROP_GS_V2(uConfId,setUConfId:)) TarsUInt32 JV2_PROP_NM(o,0,uConfId);
@property (nonatomic, retain, JV2_PROP_GS_V2(strThemeName,setStrThemeName:)) NSString* JV2_PROP_NM(o,1,strThemeName);
@property (nonatomic, retain, JV2_PROP_GS_V2(stBackgroundConfig,setStBackgroundConfig:)) proto_interact_ecommerce_page_ThemeConfigBackground* JV2_PROP_NM(o,2,stBackgroundConfig);
@property (nonatomic, retain, JV2_PROP_GS_V2(stTabConfig,setStTabConfig:)) proto_interact_ecommerce_page_ThemeConfigTab* JV2_PROP_NM(o,3,stTabConfig);
@property (nonatomic, assign, JV2_PROP_GS_V2(startTimestamp,setStartTimestamp:)) TarsUInt32 JV2_PROP_NM(o,4,startTimestamp);
@property (nonatomic, assign, JV2_PROP_GS_V2(endTimestamp,setEndTimestamp:)) TarsUInt32 JV2_PROP_NM(o,5,endTimestamp);

@end
