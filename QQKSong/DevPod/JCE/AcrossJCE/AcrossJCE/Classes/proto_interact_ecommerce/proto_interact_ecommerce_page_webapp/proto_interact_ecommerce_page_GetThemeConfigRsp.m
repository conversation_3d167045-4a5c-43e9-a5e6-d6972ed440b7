// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_page_webapp.jce   
// **********************************************************************

#import "proto_interact_ecommerce_page_GetThemeConfigRsp.h"
#import "proto_interact_ecommerce_page_ThemeConfigItem.h"

@implementation proto_interact_ecommerce_page_GetThemeConfigRsp

@synthesize JV2_PROP_EX(o,0,vctThemeConfig,VOproto_interact_ecommerce_page_ThemeConfigItem);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_page_GetThemeConfigRsp class]) {
        [proto_interact_ecommerce_page_ThemeConfigItem initialize];
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_page.GetThemeConfigRsp";
}

@end
