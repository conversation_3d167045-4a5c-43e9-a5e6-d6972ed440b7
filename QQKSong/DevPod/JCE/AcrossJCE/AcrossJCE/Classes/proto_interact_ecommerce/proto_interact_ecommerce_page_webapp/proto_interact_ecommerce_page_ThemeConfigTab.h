// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_page_webapp.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>
#import "proto_interact_ecommerce_page_emThemeConfigTabType.h"

@interface proto_interact_ecommerce_page_ThemeConfigTab : JceObjectV2

@property (nonatomic, assign, JV2_PROP_GS_V2(emTabType,setEmTabType:)) proto_interact_ecommerce_page_emThemeConfigTabType JV2_PROP_NM(o,0,emTabType);
@property (nonatomic, retain, JV2_PROP_GS_V2(strTabName,setStrTabName:)) NSString* JV2_PROP_NM(o,1,strTabName);
@property (nonatomic, retain, JV2_PROP_GS_V2(strPicUrl,setStrPicUrl:)) NSString* JV2_PROP_NM(o,2,strPicUrl);

@end
