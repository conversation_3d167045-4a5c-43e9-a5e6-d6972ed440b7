// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_page_webapp.jce   
// **********************************************************************

#import "proto_interact_ecommerce_page_TopBannerLiveHotSale.h"
#import "proto_interact_ecommerce_page_TopBannerLiveHotSaleItem.h"

@implementation proto_interact_ecommerce_page_TopBannerLiveHotSale

@synthesize JV2_PROP_EX(o,0,vctItem,VOproto_interact_ecommerce_page_TopBannerLiveHotSaleItem);
@synthesize JV2_PROP_NM(o,1,strTitle);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_page_TopBannerLiveHotSale class]) {
        [proto_interact_ecommerce_page_TopBannerLiveHotSaleItem initialize];
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strTitle) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_page.TopBannerLiveHotSale";
}

@end
