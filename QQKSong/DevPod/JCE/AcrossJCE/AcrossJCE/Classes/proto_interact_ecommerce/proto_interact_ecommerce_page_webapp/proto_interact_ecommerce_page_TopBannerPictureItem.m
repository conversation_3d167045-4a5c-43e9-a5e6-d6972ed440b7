// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_page_webapp.jce   
// **********************************************************************

#import "proto_interact_ecommerce_page_TopBannerPictureItem.h"

@implementation proto_interact_ecommerce_page_TopBannerPictureItem

@synthesize JV2_PROP_NM(o,0,strPicUrl);
@synthesize JV2_PROP_NM(o,1,strJumpLink);
@synthesize JV2_PROP_NM(o,2,iConfId);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_page_TopBannerPictureItem class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strPicUrl) = DefaultTarsString;
        JV2_PROP(strJumpLink) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_page.TopBannerPictureItem";
}

@end
