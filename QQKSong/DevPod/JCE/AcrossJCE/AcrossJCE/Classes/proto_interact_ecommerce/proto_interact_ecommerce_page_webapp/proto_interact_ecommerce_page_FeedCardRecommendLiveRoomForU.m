// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_page_webapp.jce   
// **********************************************************************

#import "proto_interact_ecommerce_page_FeedCardRecommendLiveRoomForU.h"
#import "proto_interact_ecommerce_page_FeedCardRecommendLiveRoomForUItem.h"

@implementation proto_interact_ecommerce_page_FeedCardRecommendLiveRoomForU

@synthesize JV2_PROP_EX(o,0,vctRecommendLiveRoomForU,VOproto_interact_ecommerce_page_FeedCardRecommendLiveRoomForUItem);
@synthesize JV2_PROP_NM(o,1,strTopPicUrl);
@synthesize JV2_PROP_NM(o,2,strBackgroundPicUrl);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_page_FeedCardRecommendLiveRoomForU class]) {
        [proto_interact_ecommerce_page_FeedCardRecommendLiveRoomForUItem initialize];
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strTopPicUrl) = DefaultTarsString;
        JV2_PROP(strBackgroundPicUrl) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_page.FeedCardRecommendLiveRoomForU";
}

@end
