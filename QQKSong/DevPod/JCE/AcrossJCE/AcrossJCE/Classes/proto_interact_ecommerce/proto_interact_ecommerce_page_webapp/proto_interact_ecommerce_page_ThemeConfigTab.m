// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_page_webapp.jce   
// **********************************************************************

#import "proto_interact_ecommerce_page_ThemeConfigTab.h"

@implementation proto_interact_ecommerce_page_ThemeConfigTab

@synthesize JV2_PROP_NM(o,0,emTabType);
@synthesize JV2_PROP_NM(o,1,strTabName);
@synthesize JV2_PROP_NM(o,2,strPicUrl);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_page_ThemeConfigTab class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strTabName) = DefaultTarsString;
        JV2_PROP(strPicUrl) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_page.ThemeConfigTab";
}

@end
