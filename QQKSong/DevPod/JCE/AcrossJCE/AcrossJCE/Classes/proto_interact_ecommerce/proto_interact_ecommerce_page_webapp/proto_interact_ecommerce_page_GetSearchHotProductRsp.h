// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_page_webapp.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>
@class proto_interact_ecommerce_comm_ProductInfo;

@interface proto_interact_ecommerce_page_GetSearchHotProductRsp : JceObjectV2

@property (nonatomic, retain, JV2_PROP_GS_V2(vctProductInfo,setVctProductInfo:)) NSArray* JV2_PROP_EX(o,0,vctProductInfo,VOproto_interact_ecommerce_comm_ProductInfo);
@property (nonatomic, retain, JV2_PROP_GS_V2(strPassBack,setStrPassBack:)) NSString* JV2_PROP_NM(o,1,strPassBack);
@property (nonatomic, retain, JV2_PROP_GS_V2(vctSearchAndFound,setVctSearchAndFound:)) NSArray* JV2_PROP_EX(o,2,vctSearchAndFound,VONSString);

@end
