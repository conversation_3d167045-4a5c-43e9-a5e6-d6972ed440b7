// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_page_webapp.jce   
// **********************************************************************

#import "proto_interact_ecommerce_page_ThemeConfigItem.h"
#import "proto_interact_ecommerce_page_ThemeConfigBackground.h"
#import "proto_interact_ecommerce_page_ThemeConfigTab.h"

@implementation proto_interact_ecommerce_page_ThemeConfigItem

@synthesize JV2_PROP_NM(o,0,uConfId);
@synthesize JV2_PROP_NM(o,1,strThemeName);
@synthesize JV2_PROP_NM(o,2,stBackgroundConfig);
@synthesize JV2_PROP_NM(o,3,stTabConfig);
@synthesize JV2_PROP_NM(o,4,startTimestamp);
@synthesize JV2_PROP_NM(o,5,endTimestamp);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_page_ThemeConfigItem class]) {
        [proto_interact_ecommerce_page_ThemeConfigBackground initialize];
        [proto_interact_ecommerce_page_ThemeConfigTab initialize];
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strThemeName) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_page.ThemeConfigItem";
}

@end
