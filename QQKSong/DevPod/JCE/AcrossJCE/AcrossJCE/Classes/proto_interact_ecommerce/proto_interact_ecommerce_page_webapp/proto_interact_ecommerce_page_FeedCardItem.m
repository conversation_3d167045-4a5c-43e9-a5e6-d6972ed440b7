// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_page_webapp.jce   
// **********************************************************************

#import "proto_interact_ecommerce_page_FeedCardItem.h"

@implementation proto_interact_ecommerce_page_FeedCardItem

@synthesize JV2_PROP_NM(o,0,emCardType);
@synthesize JV2_PROP_NM(o,1,strJsonData);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_page_FeedCardItem class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strJsonData) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_page.FeedCardItem";
}

@end
