// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_page_webapp.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>

@interface proto_interact_ecommerce_page_EcommerceUGCDisplayDetail : JceObjectV2

@property (nonatomic, retain, JV2_PROP_GS_V2(strUGCId,setStrUGCId:)) NSString* JV2_PROP_NM(o,0,strUGCId);
@property (nonatomic, retain, JV2_PROP_GS_V2(strCover,setStrCover:)) NSString* JV2_PROP_NM(o,1,strCover);
@property (nonatomic, retain, JV2_PROP_GS_V2(strPlayUrl,setStrPlayUrl:)) NSString* JV2_PROP_NM(o,2,strPlayUrl);
@property (nonatomic, retain, JV2_PROP_GS_V2(strJumpUrl,setStrJumpUrl:)) NSString* JV2_PROP_NM(o,3,strJumpUrl);
@property (nonatomic, retain, JV2_PROP_GS_V2(strDesc,setStrDesc:)) NSString* JV2_PROP_NM(o,4,strDesc);
@property (nonatomic, retain, JV2_PROP_GS_V2(strFaceUrl,setStrFaceUrl:)) NSString* JV2_PROP_NM(o,5,strFaceUrl);
@property (nonatomic, retain, JV2_PROP_GS_V2(strNick,setStrNick:)) NSString* JV2_PROP_NM(o,6,strNick);

@end
