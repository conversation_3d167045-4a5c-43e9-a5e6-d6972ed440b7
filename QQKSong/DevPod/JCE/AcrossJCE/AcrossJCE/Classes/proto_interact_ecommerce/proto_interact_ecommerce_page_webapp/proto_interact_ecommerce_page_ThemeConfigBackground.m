// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_page_webapp.jce   
// **********************************************************************

#import "proto_interact_ecommerce_page_ThemeConfigBackground.h"

@implementation proto_interact_ecommerce_page_ThemeConfigBackground

@synthesize JV2_PROP_NM(o,0,emBackgroundType);
@synthesize JV2_PROP_NM(o,1,strBackgroundPicUrl);
@synthesize JV2_PROP_NM(o,2,strBackgroundColor);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_page_ThemeConfigBackground class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strBackgroundPicUrl) = DefaultTarsString;
        JV2_PROP(strBackgroundColor) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_page.ThemeConfigBackground";
}

@end
