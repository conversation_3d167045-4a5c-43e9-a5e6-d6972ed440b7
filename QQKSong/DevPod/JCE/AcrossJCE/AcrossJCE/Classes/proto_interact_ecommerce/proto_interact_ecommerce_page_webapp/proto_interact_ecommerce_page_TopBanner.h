// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_page_webapp.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>
@class proto_interact_ecommerce_page_TopBannerLiveHotSale;
@class proto_interact_ecommerce_page_TopBannerPicture;
#import "proto_interact_ecommerce_page_emTopBannerType.h"

@interface proto_interact_ecommerce_page_TopBanner : JceObjectV2

@property (nonatomic, assign, JV2_PROP_GS_V2(emBannerType,setEmBannerType:)) proto_interact_ecommerce_page_emTopBannerType JV2_PROP_NM(o,0,emBannerType);
@property (nonatomic, assign, JV2_PROP_GS_V2(isDisplay,setIsDisplay:)) TarsBool JV2_PROP_NM(o,1,isDisplay);
@property (nonatomic, retain, JV2_PROP_GS_V2(stTopBannerPicture,setStTopBannerPicture:)) proto_interact_ecommerce_page_TopBannerPicture* JV2_PROP_NM(o,2,stTopBannerPicture);
@property (nonatomic, retain, JV2_PROP_GS_V2(stTopBannerLiveHotSale,setStTopBannerLiveHotSale:)) proto_interact_ecommerce_page_TopBannerLiveHotSale* JV2_PROP_NM(o,3,stTopBannerLiveHotSale);

@end
