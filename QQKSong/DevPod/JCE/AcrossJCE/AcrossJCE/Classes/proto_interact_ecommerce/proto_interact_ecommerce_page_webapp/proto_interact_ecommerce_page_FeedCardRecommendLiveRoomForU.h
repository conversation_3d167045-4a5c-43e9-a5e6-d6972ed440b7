// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_page_webapp.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>
@class proto_interact_ecommerce_page_FeedCardRecommendLiveRoomForUItem;

@interface proto_interact_ecommerce_page_FeedCardRecommendLiveRoomForU : JceObjectV2

@property (nonatomic, retain, JV2_PROP_GS_V2(vctRecommendLiveRoomForU,setVctRecommendLiveRoomForU:)) NSArray* JV2_PROP_EX(o,0,vctRecommendLiveRoomForU,VOproto_interact_ecommerce_page_FeedCardRecommendLiveRoomForUItem);
@property (nonatomic, retain, JV2_PROP_GS_V2(strTopPicUrl,setStrTopPicUrl:)) NSString* JV2_PROP_NM(o,1,strTopPicUrl);
@property (nonatomic, retain, JV2_PROP_GS_V2(strBackgroundPicUrl,setStrBackgroundPicUrl:)) NSString* JV2_PROP_NM(o,2,strBackgroundPicUrl);

@end
