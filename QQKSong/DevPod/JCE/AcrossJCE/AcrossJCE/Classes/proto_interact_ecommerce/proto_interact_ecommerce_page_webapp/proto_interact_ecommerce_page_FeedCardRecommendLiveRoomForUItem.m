// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_page_webapp.jce   
// **********************************************************************

#import "proto_interact_ecommerce_page_FeedCardRecommendLiveRoomForUItem.h"
#import "proto_across_interactive_comm_RoomInfo.h"
#import "proto_across_interactive_comm_UserInfo.h"

@implementation proto_interact_ecommerce_page_FeedCardRecommendLiveRoomForUItem

@synthesize JV2_PROP_NM(o,0,stBasicRoomInfo);
@synthesize JV2_PROP_NM(o,1,stBasicUserInfo);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_page_FeedCardRecommendLiveRoomForUItem class]) {
        [proto_across_interactive_comm_RoomInfo initialize];
        [proto_across_interactive_comm_UserInfo initialize];
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_page.FeedCardRecommendLiveRoomForUItem";
}

@end
