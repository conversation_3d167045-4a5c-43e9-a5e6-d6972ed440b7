// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_page_webapp.jce   
// **********************************************************************

#import "proto_interact_ecommerce_page_GetSearchHotProductRsp.h"
#import "proto_interact_ecommerce_comm_ProductInfo.h"

@implementation proto_interact_ecommerce_page_GetSearchHotProductRsp

@synthesize JV2_PROP_EX(o,0,vctProductInfo,VOproto_interact_ecommerce_comm_ProductInfo);
@synthesize JV2_PROP_NM(o,1,strPassBack);
@synthesize JV2_PROP_EX(o,2,vctSearchAndFound,VONSString);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_page_GetSearchHotProductRsp class]) {
        [proto_interact_ecommerce_comm_ProductInfo initialize];
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strPassBack) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_page.GetSearchHotProductRsp";
}

@end
