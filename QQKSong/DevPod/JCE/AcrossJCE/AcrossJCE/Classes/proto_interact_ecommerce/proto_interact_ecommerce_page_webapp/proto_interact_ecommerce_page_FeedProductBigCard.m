// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_page_webapp.jce   
// **********************************************************************

#import "proto_interact_ecommerce_page_FeedProductBigCard.h"
#import "proto_interact_ecommerce_comm_ProductInfo.h"
#import "proto_interact_ecommerce_page_PurchaseBarrageMsg.h"

@implementation proto_interact_ecommerce_page_FeedProductBigCard

@synthesize JV2_PROP_NM(o,0,strTitleName);
@synthesize JV2_PROP_NM(o,1,strTitlePicURL);
@synthesize JV2_PROP_NM(o,2,strBgdPicURL);
@synthesize JV2_PROP_NM(o,3,strBgdAnimationURL);
@synthesize JV2_PROP_NM(o,4,strBgdMusicURL);
@synthesize JV2_PROP_EX(o,5,vctProducts,VOproto_interact_ecommerce_comm_ProductInfo);
@synthesize JV2_PROP_NM(o,6,uProductLayout);
@synthesize JV2_PROP_NM(o,7,strCardJumpURL);
@synthesize JV2_PROP_NM(o,8,lEffectStartTs);
@synthesize JV2_PROP_NM(o,9,lEffectEndTs);
@synthesize JV2_PROP_NM(o,10,strConfID);
@synthesize JV2_PROP_EX(o,11,vctBarrageMsg,VOproto_interact_ecommerce_page_PurchaseBarrageMsg);
@synthesize JV2_PROP_NM(o,12,strCardJumpText);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_page_FeedProductBigCard class]) {
        [proto_interact_ecommerce_comm_ProductInfo initialize];
        [proto_interact_ecommerce_page_PurchaseBarrageMsg initialize];
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strTitleName) = DefaultTarsString;
        JV2_PROP(strTitlePicURL) = DefaultTarsString;
        JV2_PROP(strBgdPicURL) = DefaultTarsString;
        JV2_PROP(strBgdAnimationURL) = DefaultTarsString;
        JV2_PROP(strBgdMusicURL) = DefaultTarsString;
        JV2_PROP(strCardJumpURL) = DefaultTarsString;
        JV2_PROP(strConfID) = DefaultTarsString;
        JV2_PROP(strCardJumpText) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_page.FeedProductBigCard";
}

@end
