// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_page_webapp.jce   
// **********************************************************************

#import "proto_interact_ecommerce_page_RecModule.h"
#import "proto_interact_ecommerce_page_RecModuleItem.h"

@implementation proto_interact_ecommerce_page_RecModule

@synthesize JV2_PROP_NM(o,0,isDisplay);
@synthesize JV2_PROP_EX(o,1,vctRecModuleItem,VOproto_interact_ecommerce_page_RecModuleItem);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_page_RecModule class]) {
        [proto_interact_ecommerce_page_RecModuleItem initialize];
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(isDisplay) = NO;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_page.RecModule";
}

@end
