// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_page_webapp.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>
@class proto_interact_ecommerce_page_FeedCardItem;
@class proto_interact_ecommerce_page_QuickBanner;
@class proto_interact_ecommerce_page_RecModule;
@class proto_interact_ecommerce_page_TopBanner;

@interface proto_interact_ecommerce_page_GetMallTabRsp : JceObjectV2

@property (nonatomic, retain, JV2_PROP_GS_V2(stTopBanner,setStTopBanner:)) proto_interact_ecommerce_page_TopBanner* JV2_PROP_NM(o,0,stTopBanner);
@property (nonatomic, retain, JV2_PROP_GS_V2(stQuickBanner,setStQuickBanner:)) proto_interact_ecommerce_page_QuickBanner* JV2_PROP_NM(o,1,stQuickBanner);
@property (nonatomic, retain, JV2_PROP_GS_V2(stRecModule,setStRecModule:)) proto_interact_ecommerce_page_RecModule* JV2_PROP_NM(o,2,stRecModule);
@property (nonatomic, retain, JV2_PROP_GS_V2(vctFeedCardItem,setVctFeedCardItem:)) NSArray* JV2_PROP_EX(o,3,vctFeedCardItem,VOproto_interact_ecommerce_page_FeedCardItem);
@property (nonatomic, retain, JV2_PROP_GS_V2(strPassBack,setStrPassBack:)) NSString* JV2_PROP_NM(o,4,strPassBack);

@end
