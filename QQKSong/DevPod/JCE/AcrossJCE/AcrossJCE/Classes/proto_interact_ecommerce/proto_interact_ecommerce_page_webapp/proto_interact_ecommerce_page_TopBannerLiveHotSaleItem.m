// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_page_webapp.jce   
// **********************************************************************

#import "proto_interact_ecommerce_page_TopBannerLiveHotSaleItem.h"
#import "proto_across_interactive_comm_RoomInfo.h"
#import "proto_interact_ecommerce_comm_ProductInfo.h"

@implementation proto_interact_ecommerce_page_TopBannerLiveHotSaleItem

@synthesize JV2_PROP_NM(o,0,stBasicRoomInfo);
@synthesize JV2_PROP_NM(o,1,stProductInfo);
@synthesize JV2_PROP_NM(o,2,strJumpLink);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_page_TopBannerLiveHotSaleItem class]) {
        [proto_across_interactive_comm_RoomInfo initialize];
        [proto_interact_ecommerce_comm_ProductInfo initialize];
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strJumpLink) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_page.TopBannerLiveHotSaleItem";
}

@end
