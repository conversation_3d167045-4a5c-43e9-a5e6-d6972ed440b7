// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_page_webapp.jce   
// **********************************************************************

#import "proto_interact_ecommerce_page_QuickBanner.h"
#import "proto_interact_ecommerce_page_QuickBannerItem.h"

@implementation proto_interact_ecommerce_page_QuickBanner

@synthesize JV2_PROP_NM(o,0,isDisplay);
@synthesize JV2_PROP_EX(o,1,vctQuickBannerItem,VOproto_interact_ecommerce_page_QuickBannerItem);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_page_QuickBanner class]) {
        [proto_interact_ecommerce_page_QuickBannerItem initialize];
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(isDisplay) = NO;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_page.QuickBanner";
}

@end
