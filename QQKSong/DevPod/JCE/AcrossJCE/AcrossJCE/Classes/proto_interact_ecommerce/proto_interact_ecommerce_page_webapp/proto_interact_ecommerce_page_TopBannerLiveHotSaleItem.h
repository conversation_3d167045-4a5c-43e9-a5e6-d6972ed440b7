// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_page_webapp.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>
@class proto_across_interactive_comm_RoomInfo;
@class proto_interact_ecommerce_comm_ProductInfo;

@interface proto_interact_ecommerce_page_TopBannerLiveHotSaleItem : JceObjectV2

@property (nonatomic, retain, JV2_PROP_GS_V2(stBasicRoomInfo,setStBasicRoomInfo:)) proto_across_interactive_comm_RoomInfo* JV2_PROP_NM(o,0,stBasicRoomInfo);
@property (nonatomic, retain, JV2_PROP_GS_V2(stProductInfo,setStProductInfo:)) proto_interact_ecommerce_comm_ProductInfo* JV2_PROP_NM(o,1,stProductInfo);
@property (nonatomic, retain, JV2_PROP_GS_V2(strJumpLink,setStrJumpLink:)) NSString* JV2_PROP_NM(o,2,strJumpLink);

@end
