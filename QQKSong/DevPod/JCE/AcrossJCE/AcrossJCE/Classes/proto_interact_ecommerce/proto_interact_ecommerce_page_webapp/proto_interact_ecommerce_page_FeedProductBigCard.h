// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_page_webapp.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>
@class proto_interact_ecommerce_comm_ProductInfo;
@class proto_interact_ecommerce_page_PurchaseBarrageMsg;

@interface proto_interact_ecommerce_page_FeedProductBigCard : JceObjectV2

@property (nonatomic, retain, JV2_PROP_GS_V2(strTitleName,setStrTitleName:)) NSString* JV2_PROP_NM(o,0,strTitleName);
@property (nonatomic, retain, JV2_PROP_GS_V2(strTitlePicURL,setStrTitlePicURL:)) NSString* JV2_PROP_NM(o,1,strTitlePicURL);
@property (nonatomic, retain, JV2_PROP_GS_V2(strBgdPicURL,setStrBgdPicURL:)) NSString* JV2_PROP_NM(o,2,strBgdPicURL);
@property (nonatomic, retain, JV2_PROP_GS_V2(strBgdAnimationURL,setStrBgdAnimationURL:)) NSString* JV2_PROP_NM(o,3,strBgdAnimationURL);
@property (nonatomic, retain, JV2_PROP_GS_V2(strBgdMusicURL,setStrBgdMusicURL:)) NSString* JV2_PROP_NM(o,4,strBgdMusicURL);
@property (nonatomic, retain, JV2_PROP_GS_V2(vctProducts,setVctProducts:)) NSArray* JV2_PROP_EX(o,5,vctProducts,VOproto_interact_ecommerce_comm_ProductInfo);
@property (nonatomic, assign, JV2_PROP_GS_V2(uProductLayout,setUProductLayout:)) TarsUInt32 JV2_PROP_NM(o,6,uProductLayout);
@property (nonatomic, retain, JV2_PROP_GS_V2(strCardJumpURL,setStrCardJumpURL:)) NSString* JV2_PROP_NM(o,7,strCardJumpURL);
@property (nonatomic, assign, JV2_PROP_GS_V2(lEffectStartTs,setLEffectStartTs:)) TarsInt64 JV2_PROP_NM(o,8,lEffectStartTs);
@property (nonatomic, assign, JV2_PROP_GS_V2(lEffectEndTs,setLEffectEndTs:)) TarsInt64 JV2_PROP_NM(o,9,lEffectEndTs);
@property (nonatomic, retain, JV2_PROP_GS_V2(strConfID,setStrConfID:)) NSString* JV2_PROP_NM(o,10,strConfID);
@property (nonatomic, retain, JV2_PROP_GS_V2(vctBarrageMsg,setVctBarrageMsg:)) NSArray* JV2_PROP_EX(o,11,vctBarrageMsg,VOproto_interact_ecommerce_page_PurchaseBarrageMsg);
@property (nonatomic, retain, JV2_PROP_GS_V2(strCardJumpText,setStrCardJumpText:)) NSString* JV2_PROP_NM(o,12,strCardJumpText);

@end
