// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_interact_ecommerce_page_webapp.jce   
// **********************************************************************

#import "proto_interact_ecommerce_page_GetSearchHotProductReq.h"

@implementation proto_interact_ecommerce_page_GetSearchHotProductReq

@synthesize JV2_PROP_NM(o,0,uAppId);
@synthesize JV2_PROP_NM(o,1,strPassBack);
@synthesize JV2_PROP_NM(o,2,strQua);

+ (void)initialize
{
    if (self == [proto_interact_ecommerce_page_GetSearchHotProductReq class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strPassBack) = DefaultTarsString;
        JV2_PROP(strQua) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_interact_ecommerce_page.GetSearchHotProductReq";
}

@end
