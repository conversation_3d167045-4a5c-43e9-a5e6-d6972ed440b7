// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_live_rtc_dispatch_webapp.jce   
// **********************************************************************

#import "proto_live_rtc_dispatch_ReportSdkFailRsp.h"

@implementation proto_live_rtc_dispatch_ReportSdkFailRsp

@synthesize JV2_PROP_NM(o,0,iDstSdk);

+ (void)initialize
{
    if (self == [proto_live_rtc_dispatch_ReportSdkFailRsp class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_live_rtc_dispatch.ReportSdkFailRsp";
}

@end
