// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_live_rtc_dispatch_webapp.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>
#import "proto_live_rtc_common_emLiveRtcType.h"
#import "proto_live_rtc_common_emLiveScene.h"

@interface proto_live_rtc_dispatch_ReportSdkFailReq : JceObjectV2

@property (nonatomic, assign, JV2_PROP_GS_V2(uAppId,setUAppId:)) TarsUInt32 JV2_PROP_NM(o,0,uAppId);
@property (nonatomic, retain, JV2_PROP_GS_V2(strAnchorId,setStrAnchorId:)) NSString* JV2_PROP_NM(o,1,strAnchorId);
@property (nonatomic, retain, JV2_PROP_GS_V2(strRoomId,setStrRoomId:)) NSString* JV2_PROP_NM(o,2,strRoomId);
@property (nonatomic, retain, JV2_PROP_GS_V2(strShowId,setStrShowId:)) NSString* JV2_PROP_NM(o,3,strShowId);
@property (nonatomic, assign, JV2_PROP_GS_V2(iLiveScene,setILiveScene:)) proto_live_rtc_common_emLiveScene JV2_PROP_NM(o,4,iLiveScene);
@property (nonatomic, assign, JV2_PROP_GS_V2(iFailSdk,setIFailSdk:)) proto_live_rtc_common_emLiveRtcType JV2_PROP_NM(o,5,iFailSdk);
@property (nonatomic, assign, JV2_PROP_GS_V2(iOriSdk,setIOriSdk:)) proto_live_rtc_common_emLiveRtcType JV2_PROP_NM(o,6,iOriSdk);
@property (nonatomic, retain, JV2_PROP_GS_V2(strFailReason,setStrFailReason:)) NSString* JV2_PROP_NM(o,7,strFailReason);
@property (nonatomic, retain, JV2_PROP_GS_V2(strDeviceInfo,setStrDeviceInfo:)) NSString* JV2_PROP_NM(o,8,strDeviceInfo);
@property (nonatomic, retain, JV2_PROP_GS_V2(strQua,setStrQua:)) NSString* JV2_PROP_NM(o,9,strQua);

@end
