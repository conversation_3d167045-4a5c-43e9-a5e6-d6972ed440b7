// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_live_rtc_dispatch_webapp.jce   
// **********************************************************************

#import "proto_live_rtc_dispatch_ReportSdkFailReq.h"

@implementation proto_live_rtc_dispatch_ReportSdkFailReq

@synthesize JV2_PROP_NM(o,0,uAppId);
@synthesize JV2_PROP_NM(o,1,strAnchorId);
@synthesize JV2_PROP_NM(o,2,strRoomId);
@synthesize JV2_PROP_NM(o,3,strShowId);
@synthesize JV2_PROP_NM(o,4,iLiveScene);
@synthesize JV2_PROP_NM(o,5,iFailSdk);
@synthesize JV2_PROP_NM(o,6,iOriSdk);
@synthesize JV2_PROP_NM(o,7,strFailReason);
@synthesize JV2_PROP_NM(o,8,strDeviceInfo);
@synthesize JV2_PROP_NM(o,9,strQua);

+ (void)initialize
{
    if (self == [proto_live_rtc_dispatch_ReportSdkFailReq class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strAnchorId) = DefaultTarsString;
        JV2_PROP(strRoomId) = DefaultTarsString;
        JV2_PROP(strShowId) = DefaultTarsString;
        JV2_PROP(strFailReason) = DefaultTarsString;
        JV2_PROP(strDeviceInfo) = DefaultTarsString;
        JV2_PROP(strQua) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_live_rtc_dispatch.ReportSdkFailReq";
}

@end
