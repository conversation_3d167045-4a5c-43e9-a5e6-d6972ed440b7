// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_webapp_multiplayer_level_rush.jce   
// **********************************************************************

#import "proto_mike_multiplayer_level_rush_MultiplayerLevelRushRoundItem.h"

@implementation proto_mike_multiplayer_level_rush_MultiplayerLevelRushRoundItem

@synthesize JV2_PROP_NM(o,0,uLevelId);
@synthesize JV2_PROP_NM(o,1,llBeginTs);
@synthesize JV2_PROP_NM(o,2,llEndTs);
@synthesize JV2_PROP_NM(o,3,llSprintTs);

+ (void)initialize
{
    if (self == [proto_mike_multiplayer_level_rush_MultiplayerLevelRushRoundItem class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_mike_multiplayer_level_rush.MultiplayerLevelRushRoundItem";
}

@end
