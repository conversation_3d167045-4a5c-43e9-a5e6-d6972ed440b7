// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_webapp_multiplayer_level_rush.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>
@class proto_mike_multiplayer_level_rush_GameStartInitConfig;
@class proto_union_mike_v2_MikeUserAccount;

@interface proto_mike_multiplayer_level_rush_MikeMultiplayerLevelRushInviteIMMsg : JceObjectV2

@property (nonatomic, retain, JV2_PROP_GS_V2(strMikeId,setStrMikeId:)) NSString* JV2_PROP_NM(o,0,strMikeId);
@property (nonatomic, retain, JV2_PROP_GS_V2(strGameId,setStrGameId:)) NSString* JV2_PROP_NM(o,1,strGameId);
@property (nonatomic, assign, JV2_PROP_GS_V2(uStatus,setUStatus:)) TarsUInt32 JV2_PROP_NM(o,2,uStatus);
@property (nonatomic, retain, JV2_PROP_GS_V2(stInviter,setStInviter:)) proto_union_mike_v2_MikeUserAccount* JV2_PROP_NM(o,3,stInviter);
@property (nonatomic, retain, JV2_PROP_GS_V2(stGameStartInitConfig,setStGameStartInitConfig:)) proto_mike_multiplayer_level_rush_GameStartInitConfig* JV2_PROP_NM(o,4,stGameStartInitConfig);

@end
