// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_webapp_multiplayer_level_rush.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>

@interface proto_mike_multiplayer_level_rush_MultiplayerLevelRushRoundItem : JceObjectV2

@property (nonatomic, assign, JV2_PROP_GS_V2(uLevelId,setULevelId:)) TarsUInt32 JV2_PROP_NM(o,0,uLevelId);
@property (nonatomic, assign, JV2_PROP_GS_V2(llBeginTs,setLlBeginTs:)) TarsInt64 JV2_PROP_NM(o,1,llBeginTs);
@property (nonatomic, assign, JV2_PROP_GS_V2(llEndTs,setLlEndTs:)) TarsInt64 JV2_PROP_NM(o,2,llEndTs);
@property (nonatomic, assign, JV2_PROP_GS_V2(llSprintTs,setLlSprintTs:)) TarsInt64 JV2_PROP_NM(o,3,llSprintTs);

@end
