// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_webapp_multiplayer_level_rush.jce   
// **********************************************************************

#import "proto_mike_multiplayer_level_rush_MikeMultiplayerLevelRushUserItem.h"
#import "proto_union_mike_v2_MikeUserAccount.h"

@implementation proto_mike_multiplayer_level_rush_MikeMultiplayerLevelRushUserItem

@synthesize JV2_PROP_NM(o,0,stUser);
@synthesize JV2_PROP_NM(o,1,strNick);
@synthesize JV2_PROP_NM(o,2,strFaceUrl);
@synthesize JV2_PROP_NM(o,3,uScore);
@synthesize JV2_PROP_NM(o,4,uStatus);
@synthesize JV2_PROP_NM(o,5,llJoinTs);
@synthesize JV2_PROP_NM(o,6,llQuitTs);
@synthesize JV2_PROP_NM(o,7,strRtcUserId);

+ (void)initialize
{
    if (self == [proto_mike_multiplayer_level_rush_MikeMultiplayerLevelRushUserItem class]) {
        [proto_union_mike_v2_MikeUserAccount initialize];
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strNick) = DefaultTarsString;
        JV2_PROP(strFaceUrl) = DefaultTarsString;
        JV2_PROP(strRtcUserId) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_mike_multiplayer_level_rush.MikeMultiplayerLevelRushUserItem";
}

@end
