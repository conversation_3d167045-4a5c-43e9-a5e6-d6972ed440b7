// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_webapp_multiplayer_level_rush.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>
@class proto_mike_multiplayer_level_rush_GameStartInitConfig;
@class proto_mike_multiplayer_level_rush_MultiplayerLevelRushRoundItem;

@interface proto_mike_multiplayer_level_rush_MikeMultiplayerLevelRushIMMsg : JceObjectV2

@property (nonatomic, retain, JV2_PROP_GS_V2(strMikeId,setStrMikeId:)) NSString* JV2_PROP_NM(o,0,strMikeId);
@property (nonatomic, retain, JV2_PROP_GS_V2(strGameId,setStrGameId:)) NSString* JV2_PROP_NM(o,1,strGameId);
@property (nonatomic, assign, JV2_PROP_GS_V2(uStatus,setUStatus:)) TarsUInt32 JV2_PROP_NM(o,2,uStatus);
@property (nonatomic, assign, JV2_PROP_GS_V2(uScore,setUScore:)) TarsUInt32 JV2_PROP_NM(o,3,uScore);
@property (nonatomic, assign, JV2_PROP_GS_V2(uLevelId,setULevelId:)) TarsUInt32 JV2_PROP_NM(o,4,uLevelId);
@property (nonatomic, retain, JV2_PROP_GS_V2(stCurRoundItem,setStCurRoundItem:)) proto_mike_multiplayer_level_rush_MultiplayerLevelRushRoundItem* JV2_PROP_NM(o,5,stCurRoundItem);
@property (nonatomic, assign, JV2_PROP_GS_V2(llCurTs,setLlCurTs:)) TarsInt64 JV2_PROP_NM(o,6,llCurTs);
@property (nonatomic, assign, JV2_PROP_GS_V2(llInviteTs,setLlInviteTs:)) TarsInt64 JV2_PROP_NM(o,7,llInviteTs);
@property (nonatomic, assign, JV2_PROP_GS_V2(llStartTs,setLlStartTs:)) TarsInt64 JV2_PROP_NM(o,8,llStartTs);
@property (nonatomic, assign, JV2_PROP_GS_V2(llEndTs,setLlEndTs:)) TarsInt64 JV2_PROP_NM(o,9,llEndTs);
@property (nonatomic, retain, JV2_PROP_GS_V2(mapUserItem,setMapUserItem:)) NSDictionary* JV2_PROP_EX(o,10,mapUserItem,M09ONSStringOproto_mike_multiplayer_level_rush_MikeMultiplayerLevelRushUserItem);
@property (nonatomic, retain, JV2_PROP_GS_V2(stGameStartInitConfig,setStGameStartInitConfig:)) proto_mike_multiplayer_level_rush_GameStartInitConfig* JV2_PROP_NM(o,11,stGameStartInitConfig);
@property (nonatomic, retain, JV2_PROP_GS_V2(mapExtra,setMapExtra:)) NSDictionary* JV2_PROP_EX(o,12,mapExtra,M09ONSStringONSString);

@end
