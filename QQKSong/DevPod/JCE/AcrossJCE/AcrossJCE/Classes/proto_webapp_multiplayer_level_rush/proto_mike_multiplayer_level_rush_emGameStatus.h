// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_webapp_multiplayer_level_rush.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>

enum {
    proto_mike_multiplayer_level_rush_emGameStatus_EM_GAME_STATUS_NONE = 0,
    proto_mike_multiplayer_level_rush_emGameStatus_EM_GAME_STATUS_INVITE = 1,
    proto_mike_multiplayer_level_rush_emGameStatus_EM_GAME_STATUS_GOING = 2,
    proto_mike_multiplayer_level_rush_emGameStatus_EM_GAME_STATUS_SETTLE = 3,
    proto_mike_multiplayer_level_rush_emGameStatus_EM_GAME_STATUS_DISPLAY = 4,
    proto_mike_multiplayer_level_rush_emGameStatus_EM_GAME_STATUS_OVER = 5
};
#define proto_mike_multiplayer_level_rush_emGameStatus TarsInt32

