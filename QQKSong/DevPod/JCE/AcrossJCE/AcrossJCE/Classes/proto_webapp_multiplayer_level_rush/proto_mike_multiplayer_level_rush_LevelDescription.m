// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_webapp_multiplayer_level_rush.jce   
// **********************************************************************

#import "proto_mike_multiplayer_level_rush_LevelDescription.h"

@implementation proto_mike_multiplayer_level_rush_LevelDescription

@synthesize JV2_PROP_NM(o,0,uTargetScore);
@synthesize JV2_PROP_NM(o,1,uDuration);
@synthesize JV2_PROP_NM(o,2,llSprintTs);
@synthesize JV2_PROP_NM(o,3,uLevelId);

+ (void)initialize
{
    if (self == [proto_mike_multiplayer_level_rush_LevelDescription class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_mike_multiplayer_level_rush.LevelDescription";
}

@end
