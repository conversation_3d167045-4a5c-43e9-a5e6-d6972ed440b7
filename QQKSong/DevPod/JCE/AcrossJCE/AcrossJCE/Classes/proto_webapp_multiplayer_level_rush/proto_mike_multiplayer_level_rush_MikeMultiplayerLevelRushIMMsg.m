// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_webapp_multiplayer_level_rush.jce   
// **********************************************************************

#import "proto_mike_multiplayer_level_rush_MikeMultiplayerLevelRushIMMsg.h"
#import "proto_mike_multiplayer_level_rush_GameStartInitConfig.h"
#import "proto_mike_multiplayer_level_rush_MultiplayerLevelRushRoundItem.h"

@implementation proto_mike_multiplayer_level_rush_MikeMultiplayerLevelRushIMMsg

@synthesize JV2_PROP_NM(o,0,strMikeId);
@synthesize JV2_PROP_NM(o,1,strGameId);
@synthesize JV2_PROP_NM(o,2,uStatus);
@synthesize JV2_PROP_NM(o,3,uScore);
@synthesize JV2_PROP_NM(o,4,uLevelId);
@synthesize JV2_PROP_NM(o,5,stCurRoundItem);
@synthesize JV2_PROP_NM(o,6,llCurTs);
@synthesize JV2_PROP_NM(o,7,llInviteTs);
@synthesize JV2_PROP_NM(o,8,llStartTs);
@synthesize JV2_PROP_NM(o,9,llEndTs);
@synthesize JV2_PROP_EX(o,10,mapUserItem,M09ONSStringOproto_mike_multiplayer_level_rush_MikeMultiplayerLevelRushUserItem);
@synthesize JV2_PROP_NM(o,11,stGameStartInitConfig);
@synthesize JV2_PROP_EX(o,12,mapExtra,M09ONSStringONSString);

+ (void)initialize
{
    if (self == [proto_mike_multiplayer_level_rush_MikeMultiplayerLevelRushIMMsg class]) {
        [proto_mike_multiplayer_level_rush_GameStartInitConfig initialize];
        [proto_mike_multiplayer_level_rush_MultiplayerLevelRushRoundItem initialize];
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strMikeId) = DefaultTarsString;
        JV2_PROP(strGameId) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_mike_multiplayer_level_rush.MikeMultiplayerLevelRushIMMsg";
}

@end
