// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_webapp_multiplayer_level_rush.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>

@interface proto_mike_multiplayer_level_rush_LevelDescription : JceObjectV2

@property (nonatomic, assign, JV2_PROP_GS_V2(uTargetScore,setUTargetScore:)) TarsUInt32 JV2_PROP_NM(o,0,uTargetScore);
@property (nonatomic, assign, JV2_PROP_GS_V2(uDuration,setUDuration:)) TarsUInt32 JV2_PROP_NM(o,1,uDuration);
@property (nonatomic, assign, JV2_PROP_GS_V2(llSprintTs,setLlSprintTs:)) TarsUInt32 JV2_PROP_NM(o,2,llSprintTs);
@property (nonatomic, assign, JV2_PROP_GS_V2(uLevelId,setULevelId:)) TarsUInt32 JV2_PROP_NM(o,3,uLevelId);

@end
