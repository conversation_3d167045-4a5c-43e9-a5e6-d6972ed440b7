// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_webapp_multiplayer_level_rush.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>
@class proto_union_mike_v2_MikeUserAccount;

@interface proto_mike_multiplayer_level_rush_MikeMultiplayerLevelRushUserItem : JceObjectV2

@property (nonatomic, retain, JV2_PROP_GS_V2(stUser,setStUser:)) proto_union_mike_v2_MikeUserAccount* JV2_PROP_NM(o,0,stUser);
@property (nonatomic, retain, JV2_PROP_GS_V2(strNick,setStrNick:)) NSString* JV2_PROP_NM(o,1,strNick);
@property (nonatomic, retain, JV2_PROP_GS_V2(strFaceUrl,setStrFaceUrl:)) NSString* JV2_PROP_NM(o,2,strFaceUrl);
@property (nonatomic, assign, JV2_PROP_GS_V2(uScore,setUScore:)) TarsUInt32 JV2_PROP_NM(o,3,uScore);
@property (nonatomic, assign, JV2_PROP_GS_V2(uStatus,setUStatus:)) TarsUInt32 JV2_PROP_NM(o,4,uStatus);
@property (nonatomic, assign, JV2_PROP_GS_V2(llJoinTs,setLlJoinTs:)) TarsInt64 JV2_PROP_NM(o,5,llJoinTs);
@property (nonatomic, assign, JV2_PROP_GS_V2(llQuitTs,setLlQuitTs:)) TarsInt64 JV2_PROP_NM(o,6,llQuitTs);
@property (nonatomic, retain, JV2_PROP_GS_V2(strRtcUserId,setStrRtcUserId:)) NSString* JV2_PROP_NM(o,7,strRtcUserId);

@end
