// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_webapp_multiplayer_level_rush.jce   
// **********************************************************************

#import "proto_mike_multiplayer_level_rush_MikeMultiplayerLevelRushInviteIMMsg.h"
#import "proto_mike_multiplayer_level_rush_GameStartInitConfig.h"
#import "proto_union_mike_v2_MikeUserAccount.h"

@implementation proto_mike_multiplayer_level_rush_MikeMultiplayerLevelRushInviteIMMsg

@synthesize JV2_PROP_NM(o,0,strMikeId);
@synthesize JV2_PROP_NM(o,1,strGameId);
@synthesize JV2_PROP_NM(o,2,uStatus);
@synthesize JV2_PROP_NM(o,3,stInviter);
@synthesize JV2_PROP_NM(o,4,stGameStartInitConfig);

+ (void)initialize
{
    if (self == [proto_mike_multiplayer_level_rush_MikeMultiplayerLevelRushInviteIMMsg class]) {
        [proto_mike_multiplayer_level_rush_GameStartInitConfig initialize];
        [proto_union_mike_v2_MikeUserAccount initialize];
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strMikeId) = DefaultTarsString;
        JV2_PROP(strGameId) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_mike_multiplayer_level_rush.MikeMultiplayerLevelRushInviteIMMsg";
}

@end
