// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_webapp_multiplayer_level_rush.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>

enum {
    proto_mike_multiplayer_level_rush_emUserGameStatus_EM_USER_GAME_STATUS_NO_ANSWER = 0,
    proto_mike_multiplayer_level_rush_emUserGameStatus_EM_USER_GAME_STATUS_ACCEPT = 1,
    proto_mike_multiplayer_level_rush_emUserGameStatus_EM_USER_GAME_STATUS_REJECT = 2,
    proto_mike_multiplayer_level_rush_emUserGameStatus_EM_USER_GAME_STATUS_GOING = 3,
    proto_mike_multiplayer_level_rush_emUserGameStatus_EM_USER_GAME_STATUS_STOP = 4
};
#define proto_mike_multiplayer_level_rush_emUserGameStatus TarsInt32

