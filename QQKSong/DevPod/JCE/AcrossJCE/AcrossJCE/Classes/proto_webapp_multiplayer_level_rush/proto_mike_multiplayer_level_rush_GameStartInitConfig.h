// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_webapp_multiplayer_level_rush.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>
@class proto_mike_multiplayer_level_rush_LevelDescription;

@interface proto_mike_multiplayer_level_rush_GameStartInitConfig : JceObjectV2

@property (nonatomic, assign, JV2_PROP_GS_V2(uParticipantsNums,setUParticipantsNums:)) TarsUInt32 JV2_PROP_NM(o,0,uParticipantsNums);
@property (nonatomic, assign, JV2_PROP_GS_V2(uTotalTargetScore,setUTotalTargetScore:)) TarsUInt32 JV2_PROP_NM(o,1,uTotalTargetScore);
@property (nonatomic, assign, JV2_PROP_GS_V2(uTotalDuration,setUTotalDuration:)) TarsUInt32 JV2_PROP_NM(o,2,uTotalDuration);
@property (nonatomic, retain, JV2_PROP_GS_V2(vctLevelDescription,setVctLevelDescription:)) NSArray* JV2_PROP_EX(o,3,vctLevelDescription,VOproto_mike_multiplayer_level_rush_LevelDescription);

@end
