// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_webapp_multiplayer_level_rush.jce   
// **********************************************************************

#import "proto_mike_multiplayer_level_rush_GameStartInitConfig.h"
#import "proto_mike_multiplayer_level_rush_LevelDescription.h"

@implementation proto_mike_multiplayer_level_rush_GameStartInitConfig

@synthesize JV2_PROP_NM(o,0,uParticipantsNums);
@synthesize JV2_PROP_NM(o,1,uTotalTargetScore);
@synthesize JV2_PROP_NM(o,2,uTotalDuration);
@synthesize JV2_PROP_EX(o,3,vctLevelDescription,VOproto_mike_multiplayer_level_rush_LevelDescription);

+ (void)initialize
{
    if (self == [proto_mike_multiplayer_level_rush_GameStartInitConfig class]) {
        [proto_mike_multiplayer_level_rush_LevelDescription initialize];
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_mike_multiplayer_level_rush.GameStartInitConfig";
}

@end
