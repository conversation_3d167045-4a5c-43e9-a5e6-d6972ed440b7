// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_live_rtc_common.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>
#import "proto_live_rtc_common_emAccountType.h"

@interface proto_live_rtc_common_LiveRtcUser : JceObjectV2

@property (nonatomic, assign, JV2_PROP_GS_V2(iRtcRoomIdType,setIRtcRoomIdType:)) proto_live_rtc_common_emAccountType JV2_PROP_NM(o,0,iRtcRoomIdType);
@property (nonatomic, retain, JV2_PROP_GS_V2(strRtcRoomId,setStrRtcRoomId:)) NSString* JV2_PROP_NM(o,1,strRtcRoomId);
@property (nonatomic, retain, JV2_PROP_GS_V2(strRtcUserId,setStrRtcUserId:)) NSString* JV2_PROP_NM(o,2,strRtcUserId);
@property (nonatomic, retain, JV2_PROP_GS_V2(strStreamId,setStrStreamId:)) NSString* JV2_PROP_NM(o,3,strStreamId);

@end
