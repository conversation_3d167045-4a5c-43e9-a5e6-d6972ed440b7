// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_live_rtc_common.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>

@interface proto_live_rtc_common_LiveRtcStreamTask : JceObjectV2

@property (nonatomic, retain, JV2_PROP_GS_V2(strTaskId,setStrTaskId:)) NSString* JV2_PROP_NM(o,0,strTaskId);
@property (nonatomic, retain, JV2_PROP_GS_V2(strToken,setStrToken:)) NSString* JV2_PROP_NM(o,1,strToken);
@property (nonatomic, retain, JV2_PROP_GS_V2(strInstanceId,setStrInstanceId:)) NSString* JV2_PROP_NM(o,2,strInstanceId);

@end
