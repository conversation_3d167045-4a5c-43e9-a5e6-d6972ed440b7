// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_live_rtc_common.jce   
// **********************************************************************

#import "proto_live_rtc_common_LiveRtcUser.h"

@implementation proto_live_rtc_common_LiveRtcUser

@synthesize JV2_PROP_NM(o,0,iRtcRoomIdType);
@synthesize JV2_PROP_NM(o,1,strRtcRoomId);
@synthesize JV2_PROP_NM(o,2,strRtcUserId);
@synthesize JV2_PROP_NM(o,3,strStreamId);

+ (void)initialize
{
    if (self == [proto_live_rtc_common_LiveRtcUser class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strRtcRoomId) = DefaultTarsString;
        JV2_PROP(strRtcUserId) = DefaultTarsString;
        JV2_PROP(strStreamId) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_live_rtc_common.LiveRtcUser";
}

@end
