// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_live_rtc_common.jce   
// **********************************************************************

#import "proto_live_rtc_common_LiveRtcStreamTask.h"

@implementation proto_live_rtc_common_LiveRtcStreamTask

@synthesize JV2_PROP_NM(o,0,strTaskId);
@synthesize JV2_PROP_NM(o,1,strToken);
@synthesize JV2_PROP_NM(o,2,strInstanceId);

+ (void)initialize
{
    if (self == [proto_live_rtc_common_LiveRtcStreamTask class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strTaskId) = DefaultTarsString;
        JV2_PROP(strToken) = DefaultTarsString;
        JV2_PROP(strInstanceId) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_live_rtc_common.LiveRtcStreamTask";
}

@end
