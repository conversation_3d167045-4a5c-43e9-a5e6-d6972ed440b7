// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_live_rtc_common.jce   
// **********************************************************************

#import "proto_live_rtc_common_LiveRtcClient.h"

@implementation proto_live_rtc_common_LiveRtcClient

@synthesize JV2_PROP_NM(o,0,iRtcType);
@synthesize JV2_PROP_NM(o,1,strRtcAppId);
@synthesize JV2_PROP_NM(o,2,strRtcSecretKey);
@synthesize JV2_PROP_NM(o,3,strRtcClientId);
@synthesize JV2_PROP_NM(o,4,strRtcClientKey);

+ (void)initialize
{
    if (self == [proto_live_rtc_common_LiveRtcClient class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strRtcAppId) = DefaultTarsString;
        JV2_PROP(strRtcSecretKey) = DefaultTarsString;
        JV2_PROP(strRtcClientId) = DefaultTarsString;
        JV2_PROP(strRtcClientKey) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_live_rtc_common.LiveRtcClient";
}

@end
