// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_live_rtc_common.jce   
// **********************************************************************

#import <CocoaJCE/JceObjectV2.h>
#import "proto_live_rtc_common_emLiveRtcType.h"

@interface proto_live_rtc_common_LiveRtcClient : JceObjectV2

@property (nonatomic, assign, JV2_PROP_GS_V2(iRtcType,setIRtcType:)) proto_live_rtc_common_emLiveRtcType JV2_PROP_NM(o,0,iRtcType);
@property (nonatomic, retain, JV2_PROP_GS_V2(strRtcAppId,setStrRtcAppId:)) NSString* JV2_PROP_NM(o,1,strRtcAppId);
@property (nonatomic, retain, JV2_PROP_GS_V2(strRtcSecretKey,setStrRtcSecretKey:)) NSString* JV2_PROP_NM(o,2,strRtcSecretKey);
@property (nonatomic, retain, JV2_PROP_GS_V2(strRtcClientId,setStrRtcClientId:)) NSString* JV2_PROP_NM(o,3,strRtcClientId);
@property (nonatomic, retain, JV2_PROP_GS_V2(strRtcClientKey,setStrRtcClientKey:)) NSString* JV2_PROP_NM(o,4,strRtcClientKey);

@end
