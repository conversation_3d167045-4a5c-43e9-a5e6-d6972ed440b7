// **********************************************************************
// This file was generated by a TARS parser!
// TARS version 1.0.0.   proto_live_rtc_common.jce   
// **********************************************************************

#import "proto_live_rtc_common_ErrMsg.h"

@implementation proto_live_rtc_common_ErrMsg

@synthesize JV2_PROP_NM(o,0,iRet);
@synthesize JV2_PROP_NM(o,1,strErrMsg);

+ (void)initialize
{
    if (self == [proto_live_rtc_common_ErrMsg class]) {
        [super initialize];
    }
}

- (id)init
{
    if (self = [super init]) {
        JV2_PROP(strErrMsg) = DefaultTarsString;
    }
    return self;
}

+ (NSString*)jceType
{
    return @"proto_live_rtc_common.ErrMsg";
}

@end
