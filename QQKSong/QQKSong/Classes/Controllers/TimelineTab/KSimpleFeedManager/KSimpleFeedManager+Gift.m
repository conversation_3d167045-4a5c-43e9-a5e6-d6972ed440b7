//
//  KSimpleFeedManager+Gift.m
//  QQKSong
//
//  Created by she<PERSON><PERSON><PERSON><PERSON> on 2020/1/4.
//  Copyright © 2020 Tencent. All rights reserved.
//

#import "KSimpleFeedManager+Gift.h"
 
#import "KSTraceReportManager+BuyKB.h"
#import "KSTraceReportModel_V2.h"
#import "KSimpleFeed.h"
#import "KSUserProfileVC_V2.h"
#import "KSNavigationManager.h"
#import "KSLayoutableFeedBaseCell+ContinueClickFlower.h"
#import "KSimpleFeedManager+Data.h"
#import "JceIapGift_BonusConsumeUgc.h"
#import "KSGiftBridgeHelper.h"
#import "KSGiftAnimationHelper.h"
#import "KSNewSinglesDetailGiftAlertView.h"
#import "KSimpleFeedManager+CellFactory.h"
#import "JceKG_UserInfo.h"
#import "KSFeedReportDelegate.h"
#import "KSLiveShowRoom+Common.h"
#import "KSIapGift.h"
#import "KSIapGiftManager.h"
#import "KSKTVRoomInfo.h"
#import "KSLiveShowRoom.h"
#import "KSLoginManager.h"
#import "KSMultiGiftBridgeManager.h"
#import "KSRoomGiftInfo.h"
#import "KSUserInfo.h"
#import "KSong+Common.h"
#import "KSGiftBridge+RapidClick_V2.h"
#import "KSABTestManager.h"
#import "JceTimeline_enum_filter_mask.h"
#import "JceTimeline_cell_extrainfo.h"
#import "KSAvatarDownloadManager.h"
#import "KSPropsManager.h"
#import "JceKG_RoomLotteryGift.h"
#import "proto_room_lottery_RoomLotteryGift.h"
#import "JceIapGift_emGiftRightUpperType.h"
#import "JceIapGift_PlaceOrderReq.h"
#import "JceIapGift_GiftUgcReq.h"
#import "JceIapGift_GiftUgcRsp.h"
#import "JceIapGift_RES_CODE.h"
#import "proto_svr_heat_card_EM_HEATCARD_PAY_TYPE.h"
#import "proto_comm_heat_card_HeatCardGiftExtInfo.h"
#import "JceIapGift_emGiftType.h"
#import "proto_new_gift_Gift.h"
#import "JceIapGift_Gift.h"
#import "KSPayAlertView.h"
#import "KSPayPolicyManager.h"
#import "KSPayAlertViewModel.h"
#import "KSStarDiamondManager.h"
#import "proto_quick_gift_webapp_QUICK_SCENE.h"
#import "proto_quick_gift_webapp_QUICK_CONSUME.h"
#import "proto_quick_gift_webapp_QuickSendInfo.h"
#import "proto_quick_gift_webapp_QueryQuickSendInfoWebRsp.h"
#import "KSTimelineRootVC.h"
#import "KSUserProfileFeedAdapter.h"
#import "proto_daily_settle_emConsumeScene.h"
#import "proto_feed_webapp_UGCLightupItem.h"
#import "KSUGCDetailGiftBridge.h"
#import "KSDynamicResItem.h"
#import "KSBurstLightupInfoModel.h"
#import "proto_feed_webapp_GetDataAfterExposureReq.h"
#import "proto_feed_webapp_GetDataAfterExposureRsp.h"
#import "KSProtocolCommands.h"
#import "proto_quick_gift_webapp_QueryQuickSendInfoWebReq.h"
#import "proto_feed_webapp_ENUM_DATA_TYPE_AFTER_EXP.h"
#import "KSIapGiftManager.h"
#import "proto_feed_webapp_ENUM_ONE_CLICK_PAY_TYPE.h"
#import "KSUGCDetailGiftBridge.h"
#import "KSIapGiftManager.h"
#import "JceIapGift_DoGiftExchangeReq.h"
#import "KSTimelineBaseVC+Statistic.h"
#import "KSSendGiftContainerView+AsyncScene.h"
#import "KSGiftManager.h"
#import "KSPageableList.h"
#import "proto_new_gift_Gift.h"
#import "KSAdFreeGiftScene.h"

#import "KSTimelineRootVCTabIndexManager.h"
#import "KSPayManager.h"
#import "KSPayConstants.h"
#define  GiftRankHeadIconCount (3)

@implementation KSimpleFeedManager (Gift)

// 展示送礼物浮层
- (void)showGiftContainerViewWidthTimelineSimpleFeed:(KSimpleFeed *)timelineSimpleFeed
{
    [KSLuckyBoxManager sharedManager].delegate = self;
    self.sendGiftContainerView = [[KSSendGiftContainerView alloc] initWithStyle:KSSendGiftContainerViewStyleWhite];
    if ([timelineSimpleFeed isFollowTabFeed]) {
        self.sendGiftContainerView.reportPosID = NSIToString(BuyKBPid_Feeds_Tab_Follow_SendGift);
    } else if ([timelineSimpleFeed isRecCardFeed]) {
        self.sendGiftContainerView.reportPosID = NSIToString(BuyKBPid_Feeds_Tab_RecFeed_Send_Present);
    }
    
    KSTimelineRootVC *timelineRootVC = SAFE_CAST(self.feedManagerDelegate, [KSTimelineRootVC class]);
    if (timelineRootVC && (timelineRootVC.centerSegControl.selectIndex == KSTimelineTab_Rec ||
        timelineRootVC.centerSegControl.selectIndex == KSTimelineTab_Follow)) {
        self.sendGiftContainerView.enableVIPDirectPurchase = YES;
    }
    
    KSUserProfileFeedAdapter *profileFeedAdapter = SAFE_CAST(self.feedManagerDelegate, [KSUserProfileFeedAdapter class]);
    if (profileFeedAdapter) {
        self.sendGiftContainerView.enableVIPDirectPurchase = YES;
    }
    
    // 是否来自同城
    BOOL isFromNearBy = timelineSimpleFeed.isNearbyFeed;
    
    MusicInteractScene_EnumInteractScene eScene = MusicInteractScene_EnumInteractScene_emSceneDefault;
    if (isFromNearBy) // 来自同城
    {
       eScene = MusicInteractScene_EnumInteractScene_emSceneFeedIntraCity;
    }
    self.sendGiftContainerView.fromScene = eScene;
    
    if (timelineSimpleFeed.liveShow) {
        KSLiveShowRoom *roomInfo = [[KSLiveShowRoom alloc] init];
        roomInfo.strRoomId = timelineSimpleFeed.liveShow.strLiveRoomId;
        roomInfo.strShowId = timelineSimpleFeed.liveShow.strShowId;
        roomInfo.stAnchorInfo = [[JceKG_UserInfo alloc] init];
        roomInfo.stAnchorInfo.jce_uid = timelineSimpleFeed.liveShow.anchorUid;
        roomInfo.stAnchorInfo.jce_nick = timelineSimpleFeed.simpleUser.userinfo.nickName;
        roomInfo.liveshowRoomType = timelineSimpleFeed.liveShow.roomType;
        
        self.sendGiftContainerView.giftMasks = @[[NSString stringWithFormat:@"%d", JceIapGift_emGetGiftType_GET_GIFT_UGC_VIP]];
        self.sendGiftContainerView.exclusiveGiftMasks = JceIapGift_emGetGiftType_GET_GIFT_ROOM_EXCLUSIVE_TABLE;
        self.sendGiftContainerView.luckGiftMasks = JceIapGift_emGetGiftType_GET_GIFT_ROOM_LUCKY_TABLE;
        self.sendGiftContainerView.newGameMask = JceIapGift_emGetGiftType_GET_GIFT_LIVE_NEW_PLAY_TABLE;
        self.sendGiftContainerView.statFrom = KSStatFromLiveShow;
        self.sendGiftContainerView.liveShowRoomInfo = [roomInfo toGiftPanelLiveShowRoom];
    } else if ([timelineSimpleFeed isKtvFeed]) {
        {
            KSKTVRoomInfo *roomInfo = [[KSKTVRoomInfo alloc] init];
            roomInfo.roomID = [timelineSimpleFeed getKtvRoomId];
            roomInfo.showID = [timelineSimpleFeed getKtvShowId];
            roomInfo.ktvRoomType = [timelineSimpleFeed getKtvRoomType];
            roomInfo.anchorInfo = [[KSUserInfo alloc] init];
            roomInfo.anchorInfo.userId = [timelineSimpleFeed getKtvAnchorId];
            
            self.sendGiftContainerView.statFrom = KSStatFromKTV;
            self.sendGiftContainerView.ktvRoomInfo = [roomInfo toGiftPanelKtvRoomInfo];
        }
        KSRoomGiftInfo *roomGiftInfo = [[KSRoomGiftInfo alloc] init];
        roomGiftInfo.effectedUserInfo = [[KSUserInfo alloc] init];
        roomGiftInfo.effectedUserInfo.userId = [timelineSimpleFeed getKtvAnchorId];
        roomGiftInfo.effectedUserInfo.nickName = timelineSimpleFeed.simpleUser.userinfo.nickName;
        
        self.sendGiftContainerView.giftMasks = @[[NSString stringWithFormat:@"%d", JceIapGift_emGetGiftType_GET_GIFT_KTV]];
        self.sendGiftContainerView.exclusiveGiftMasks = JceIapGift_emGetGiftType_GET_GIFT_KTV_ROOM_EXCLUSIVE_TABLE;
        self.sendGiftContainerView.newGameMask = JceIapGift_emGetGiftType_GET_GIFT_KTV_NEW_PLAY_TABLE;
        self.sendGiftContainerView.luckGiftMasks = JceIapGift_emGetGiftType_GET_GIFT_KTV_ROOM_LUCKY_TABLE;
        self.sendGiftContainerView.roomGiftInfo = roomGiftInfo;
    } else {
        self.sendGiftContainerView.giftMasks = @[[NSString stringWithFormat:@"%d", JceIapGift_emGetGiftType_GET_GIFT_ASYNC]];
        self.sendGiftContainerView.exclusiveGiftMasks = JceIapGift_emGetGiftType_GET_GIFT_ASYNC_EXCLUSIVE_TABLE;
        self.sendGiftContainerView.luckGiftMasks = JceIapGift_emGetGiftType_GET_GIFT_ASYNC_LUCKY_TABLE;
        if (timelineSimpleFeed.soloAlbumInfo) {
            self.sendGiftContainerView.statFrom = JceIapGift_CONSUME_LOCATION_E_FEED_ALBUM_CONSUME;
        } else if([self.currentVC isKindOfClass:KSUserProfileVC_V2.class]){
            //区分一下个人主页的feed
           self.sendGiftContainerView.statFrom = JceIapGift_CONSUME_LOCATION_E_PROFILE_FEED_CONSUME;
        } else {
            self.sendGiftContainerView.statFrom = JceIapGift_CONSUME_LOCATION_E_FEED_CONSUME;
        }
        self.sendGiftContainerView.webQuickSence = proto_quick_gift_webapp_QUICK_SCENE_EN_ASYNC_UGC;
        [self.sendGiftContainerView setTimelineSimpleFeed:[timelineSimpleFeed toGiftSimpleFeed]];
    }
    if (self.isShowHeatBtnGiftContainer) {
        // 定位到热度卡
        NSString *heatCardBuyUrl = [timelineSimpleFeed.cellExtraInfo.jce_strHeatCardBuyUrl URLDecodedString];
        NSString *giftId = [KSComHelper getValueForKey:@"uGiftId" inSchemeUrl:heatCardBuyUrl];
        self.sendGiftContainerView.defaultSelectTabIndex = KSSendGiftContainerDefaultSelectTabTypeGift;
        self.sendGiftContainerView.defaultSelectGiftId = [giftId integerValue];
    }
    self.sendGiftContainerView.hideUserRankInfo = YES;
    self.sendGiftContainerView.delegate = self;
    self.sendGiftContainerView.dataSource = self;
    UIView *rootView = [self getViewContainer];
    [self.sendGiftContainerView setupSubviews];
    [self.sendGiftContainerView showInView:rootView];
    [self.sendGiftContainerView loadData];
    if (proto_quick_gift_webapp_QUICK_SCENE_EN_ASYNC_UGC == self.sendGiftContainerView.webQuickSence ) {
        [self.sendGiftContainerView asyncScene_configAGRetainPopHandler];//异步配置策略弹窗
    }
}

- (void)shortSendGiftDirectelyTimelineSimpleFeed:(KSimpleFeed *)timelineSimpleFeed
{
    if (timelineSimpleFeed.liveShow || [timelineSimpleFeed isKtvFeed]) {
        //直接赠送礼物
        KSUidType uid = 0;
        KSStatFrom statFrom = 0;
        if (timelineSimpleFeed.liveShow) {
            uid = timelineSimpleFeed.liveShow.anchorUid;
            statFrom = KSStatFromLiveShow;
        }
        else if ([timelineSimpleFeed isKtvFeed]) {
            uid = [timelineSimpleFeed getKtvAnchorId];
            if ([timelineSimpleFeed isFriendKTVFeed]) {
                statFrom = KSStatFromSocialRoom;
            }
            else {
                statFrom = KSStatFromKTV;
            }
        }
        [KSGiftBridgeHelper checkAnonymousStatusForUid:uid complection:^{
            KSIapGift *gift = [KSGiftBridgeHelper createHeartGift];
            gift.statFrom = statFrom;
            [KSGiftBridgeHelper fillSponsorUserInfoForGift:gift];
            gift.roomGiftInfo.effectedUserInfo = [KSUserInfo new];
            gift.roomGiftInfo.effectedUserInfo.userId = uid;
            
            KSBaseSendGiftInfo *info = [KSBaseSendGiftInfo createWithGift:gift giftCount:1 sendStyle:KSGiftBridgeSendStyleDirectly receiverUid:uid statFrom:statFrom];
            info.shouldIgnoreKBBanlance = NO; // 送礼前忽略余额检查
            UIView *rootView = [self getViewContainer];
            [self.multiGiftBridgeManager sendGiftWithInfo:info atView:rootView detail:timelineSimpleFeed block:nil];
        }];
    } else if ([self.rewardSendGiftVC checkIfShouldShowRewardGiftAlert]) {
        // TODO: 奖励金弹窗待确认 checkIfShouldShowRewardGiftAlert 逻辑有bug
        self.hasRewardsView = NO;
        showReportSceneType scenType = 0;
        if ([self.feedReportDelegate respondsToSelector:@selector(getRewardSendGiftSource)]) {
            scenType = [self.feedReportDelegate getRewardSendGiftSource];
        }
        JceIapGift_BonusConsumeUgc *consumeUgc = [[JceIapGift_BonusConsumeUgc alloc] init];
        consumeUgc.jce_strUgcId = [self getFeedUgcId:timelineSimpleFeed];
        self.rewardSendGiftVC.QUICK_SCENE  = proto_quick_gift_webapp_QUICK_SCENE_EN_ASYNC_UGC;
        [self.rewardSendGiftVC showRewardsAlertViewWithShowType:RewardsDirectSendGiftType
                                                  sendGiftUfrom:JceIapGift_CONSUME_LOCATION_E_FEED_CONSUME
                                                    consumeType:JceIapGift_emBonusConsumeType_BONUS_CONSUME_GIFTUGC
                                                      sceneType:scenType
                                                   bonusConsume:consumeUgc
                                                          toUid:timelineSimpleFeed.simpleUser.userinfo.userId
                                                     simpleFeed:nil];
        KS_WEAK_SELF(self);
        [self.rewardSendGiftVC queryDefaultRewards:^(BOOL isDefault) {
            CHECK_SELF_AND_RETURN();
            if (isDefault) {
                [self.rewardSendGiftVC rewardGiftBtnDidClick:nil];
            } else {
                [KSendRewardGiftAlertViewController showRewardsAlertView:self.rewardSendGiftVC];
                self.hasRewardsView = YES;
            }
        }];
    } else {
        //直接赠送礼物
        KS_WEAK_SELF(self);
        [KSGiftBridgeHelper checkAnonymousStatusForUid:timelineSimpleFeed.simpleUser.userinfo.userId complection:^{
            CHECK_SELF_AND_RETURN();
            KSStatFrom statFrom = KSStatFromKUserProfileVCFeed;
            KSBaseSendGiftInfo *info = [KSBaseSendGiftInfo createWithGift:self.heartGift giftCount:1 sendStyle:KSGiftBridgeSendStyleDirectly receiverUid:0 statFrom:statFrom];
            info.shouldIgnoreKBBanlance = NO; // 送礼前忽略余额检查
            UIView *rootView = [self getViewContainer];
            [self.multiGiftBridgeManager sendGiftWithInfo:info atView:rootView detail:timelineSimpleFeed block:nil];
        }];
    }
}

// 直接送出小额热度卡
- (void)directSendRecMinorHeatCard:(KSimpleFeed *)simpleFeed
{
    KSIapGift *iapGift = [[KSIapGift alloc] initWithProtoGift:simpleFeed.giftGuide.kbGift];
    [self performBaseSendGiftInfoWith:iapGift simpleFeed:simpleFeed];
}

- (void)showRechargeAlert:(NSInteger)neeKBCount toUid:(NSInteger)toUid
 {
     KSPayAlertViewModel *model = [[KSPayAlertViewModel alloc] init];
     model.payCountType = KSPayCountTypeAbsenceKB;
     model.isLandSpace = NO;
     model.kbCount = [KSStarDiamondManager defaultManager].starDiamondNum;
     model.toUid = toUid;
     model.needKBCount = neeKBCount;
     [[KSPayPolicyManager sharedInstance] showPayAlertViewWithModel:model];
 }

# pragma mark 大卡片礼物异化

- (void)onRecBottomSendGiftBtnClick:(KSimpleFeed *)simpleFeed
{
    self.curSimpleFeed.feedSendRewardFrom = KSFeedSendRewardFrom_RecStyleBtnClick;
    
    KSimpleFeedGiftGuide *giftGuide = simpleFeed.giftGuide;
    if (giftGuide.backpackGift) {
        // 背包礼物
        KSIapGift *iapGift = [[KSIapGift alloc] initWithProtoLotteryGift:giftGuide.backpackGift];
        [self performBaseSendGiftInfoWith:iapGift simpleFeed:simpleFeed];
    } else if (giftGuide.kbGift) {
        // KB礼物
        KSIapGift *iapGift = [[KSIapGift alloc] initWithProtoGift:giftGuide.kbGift];
        [self performBaseSendGiftInfoWith:iapGift simpleFeed:simpleFeed];
    } else if (giftGuide.bonusGift) {
        // 奖励金
        proto_new_gift_Gift *bonusGift = simpleFeed.giftGuide.bonusGift;
        showReportSceneType scenType = 0;
        if ([self.feedReportDelegate respondsToSelector:@selector(getRewardSendGiftSource)]) {
            scenType = [self.feedReportDelegate getRewardSendGiftSource];
        }
        JceIapGift_BonusConsumeUgc *consumeUgc = [[JceIapGift_BonusConsumeUgc alloc] init];
        consumeUgc.jce_strUgcId = [self getFeedUgcId:simpleFeed];
        self.rewardSendGiftVC.QUICK_SCENE  = proto_quick_gift_webapp_QUICK_SCENE_EN_ASYNC_UGC;
        [self.rewardSendGiftVC showRewardsAlertViewWithShowType:RewardsDirectSendGiftType
                                                  sendGiftUfrom:JceIapGift_CONSUME_LOCATION_E_FEED_CONSUME
                                                    consumeType:JceIapGift_emBonusConsumeType_BONUS_CONSUME_GIFTUGC
                                                      sceneType:scenType
                                                   bonusConsume:consumeUgc
                                                          toUid:simpleFeed.simpleUser.userinfo.userId
                                                     simpleFeed:nil];
        self.rewardSendGiftVC.configGift = nil;
        if (bonusGift) { 
            self.rewardSendGiftVC.configGift = bonusGift;
            [self.rewardSendGiftVC updateGiftID:bonusGift.jce_uGiftId logoStr:bonusGift.jce_strLogo kbPrice:bonusGift.jce_uPrice bonusPrice:bonusGift.jce_uBonusPrice];
        }
        
        if (!IS_EMPTY_STR_BM(simpleFeed.giftGuide.buttoneStrategyStr)) {
            // ⚠️一定要刷新弹窗文案，要不会展示K币不足，这里不会再由客户端判断余额了
            [self.rewardSendGiftVC updateGiftDescText:simpleFeed.giftGuide.buttoneStrategyStr];
        }
        
        KS_WEAK_SELF(self);
        [self.rewardSendGiftVC queryDefaultRewards:^(BOOL isDefault) {
            CHECK_SELF_AND_RETURN();
            if (isDefault) {
                [self.rewardSendGiftVC rewardGiftBtnDidClick:nil];
            } else {
                [KSendRewardGiftAlertViewController showRewardsAlertView:self.rewardSendGiftVC];
            }
        }];
    }
}

- (void)performBaseSendGiftInfoWith:(KSIapGift *)iapGift simpleFeed:(KSimpleFeed *)simpleFeed
{
    KSBaseSendGiftInfo *info = [[KSBaseSendGiftInfo alloc] initWithGift:iapGift
                                                              giftCount:1
                                                              sendStyle:KSGiftBridgeSendStyleDirectly
                                                            receiverUid:0
                                                               statFrom:KSStatFromDircetPayPersonalConsume];
    info.reportPosid = BuyKBPid_Profile_Header_Gift_Quick_Send;
    [KSGiftBridgeHelper fillSponsorUserInfoForGift:iapGift];
    UIView *rootView = [self getViewContainer];
    [self.multiGiftBridgeManager sendGiftWithInfo:info atView:rootView detail:simpleFeed block:nil];
}

// 大卡片底部快捷送礼引导
- (void)getRecBottomSendGiftViewModel:(void(^)(KSimpleFeedGiftGuide *model))completeBlock
                       isGiftBtnStyle:(BOOL)isGiftBtnStyle
                           simpleFeed:(KSimpleFeed *)simpleFeed
                          triggerType:(NSNumber *)triggerType
{
    // mapExt 作品ugcid 直播间roomid  歌房 roomid和showid
    NSDictionary *mapExt;
    if (!IS_EMPTY_STR_BM(simpleFeed.simpleFeedCommon.strFeedId)) {
        mapExt = @{@"ROOMID": @"",
                   @"SHOWID": @"",
                   @"UGCID": simpleFeed.simpleFeedCommon.strFeedId};
    }
    
    // triggerType:触发原因 1=点赞 2=播放 3=评论
    NSInteger scence = proto_quick_gift_webapp_QUICK_SCENE_EN_ASYNC_CARD_STRIPES;
    switch (triggerType.integerValue) {
        case 1:
        {
            scence = proto_quick_gift_webapp_QUICK_SCENE_EN_ASYNC_CARD_LIKES;
        }
            break;
        case 3:
        {
            scence = proto_quick_gift_webapp_QUICK_SCENE_EN_ASYNC_CARD_COMMENT;
        }
            break;
            
        case 2:
        default:
        {
            scence = proto_quick_gift_webapp_QUICK_SCENE_EN_ASYNC_CARD_STRIPES;
        }
            break;
    }
    
    KS_WEAK_SELF(self);
    [[KSIapGiftManager defaultManager] queryCommonQuickGiftInfoWithScence:scence
                                                                  recvUid:simpleFeed.simpleUser.userinfo.userId
                                                                   mapExt:mapExt
                                                          completionBlock:^(proto_quick_gift_webapp_QueryQuickSendInfoWebRsp *quickSendInfoRsp, proto_room_lottery_RoomLotteryGift *backpackGift, proto_new_gift_Gift *kbGift, proto_new_gift_Gift *bonusGift, NSUInteger uKbBalance, NSUInteger uBonuseBalace, NSInteger reportType) {
        CHECK_SELF_AND_RETURN()
        
        proto_new_gift_Gift *gift = quickSendInfoRsp.jce_stInfo.jce_stGift;
        
        // 组装礼物信息
        KSimpleFeedGiftGuide *model = [[KSimpleFeedGiftGuide alloc] init];
        model.giftDesc = quickSendInfoRsp.jce_strRecDesc;
        model.logoStr = gift.jce_strLogo;
        model.titleStr = gift.jce_strGiftName;
        model.price = gift.jce_uPrice;
        model.subTitleStr = quickSendInfoRsp.jce_stInfo.jce_strButtonCopy; // 已废弃按钮文案
        model.buttoneStrategyStr = quickSendInfoRsp.jce_stInfo.jce_strButtonStrategyCopy; // 按钮文案
        model.badgeStr = quickSendInfoRsp.jce_stInfo.jce_strGiftCopy; // 角标文案
        
        model.uBonuseBalace = uBonuseBalace;
        model.uKbBalance = uKbBalance;
        
        model.backpackGift = backpackGift;
        model.kbGift = kbGift;
        model.bonusGift = bonusGift;
        model.reportType = reportType;
        
        if (completeBlock) {
            completeBlock(model);
        }
    }];
}

// Feed送礼异化为快捷送礼按钮展示信息
- (void)getFeedGiftInfoWithFeed:(KSimpleFeed *)simpleFeed completionBlk:(void(^)(BOOL success))completionBlk {
    NSDictionary *mapExt;
    if (!IS_EMPTY_STR_BM(simpleFeed.simpleFeedCommon.strFeedId)) {
        mapExt = @{@"ROOMID": @"",
                   @"SHOWID": @"",
                   @"UGCID": simpleFeed.simpleFeedCommon.strFeedId};
    } else {
        return;
    }
    proto_quick_gift_webapp_QueryQuickSendInfoWebReq *giftReq = [proto_quick_gift_webapp_QueryQuickSendInfoWebReq new];
    giftReq.jce_uHostUid = (TarsUInt32)[KSLoginManager sharedInstance].curUserInfo.userId;
    giftReq.jce_uScene = proto_quick_gift_webapp_QUICK_SCENE_EN_ASYNC_UGC_DETAIL_FEED;
    giftReq.jce_uRecvUid = (TarsUInt32)simpleFeed.simpleUser.userinfo.userId;
    giftReq.jce_mapExt = mapExt;
    
    proto_feed_webapp_GetDataAfterExposureReq *req = proto_feed_webapp_GetDataAfterExposureReq.new;
    req.jce_uid = (TarsUInt32)[KSLoginManager sharedInstance].curUserInfo.userId;
    req.jce_ugcid = simpleFeed.simpleFeedCommon.strFeedId;
    req.jce_stQueryQuickSendInfoWebReq = giftReq;

    KSProtocolBase *protocol = [[KSProtocolBase alloc] init];
    [protocol startWorkWithCommand:KGCMD_GET_DATA_AFTER_EXPOSURE
                         reqObject:req
                          rspClass:@"proto_feed_webapp_GetDataAfterExposureRsp"
                          rspBlock:^(WnsProtocolBase *workProtocolObj, NSInteger workResult) {
        if (PROTOCOL_SUCCESS(workResult)) {
            proto_feed_webapp_GetDataAfterExposureRsp *btnInfo = SAFE_CAST(workProtocolObj.rspObject, proto_feed_webapp_GetDataAfterExposureRsp);
            if (btnInfo.jce_unDataType <= 0) {
                KErrLog(@"关注Feed 送礼按钮异化信息请求失败  res: %zd", workResult);
                !completionBlk ?: completionBlk(NO);
                return;
            }
            simpleFeed.followGiftBtnInfo = btnInfo;
            // 快捷送礼组装数据
            if (btnInfo.jce_unDataType == proto_feed_webapp_ENUM_DATA_TYPE_AFTER_EXP_EM_DATA_TYPE_AFTER_EXP_QUICK_GIFT) {
                proto_new_gift_Gift *gift = btnInfo.jce_stQueryQuickSendInfoWebRsp.jce_stInfo.jce_stGift;
                
                // 组装礼物信息
                KSimpleFeedGiftGuide *model = [[KSimpleFeedGiftGuide alloc] init];
                model.logoStr = gift.jce_strLogo;
                model.titleStr = gift.jce_strGiftName;
                model.price = gift.jce_uPrice;
                model.buttoneStrategyStr = btnInfo.jce_stQueryQuickSendInfoWebRsp.jce_stInfo.jce_strButtonStrategyCopy; // 按钮文案
                model.badgeStr = btnInfo.jce_stQueryQuickSendInfoWebRsp.jce_stInfo.jce_strGiftCopy; // 角标文案
                
                model.uBonuseBalace = btnInfo.jce_stQueryQuickSendInfoWebRsp.jce_uBonuseBalace;
                model.uKbBalance = btnInfo.jce_stQueryQuickSendInfoWebRsp.jce_uKbBalance;
                
                model.reportType = btnInfo.jce_stQueryQuickSendInfoWebRsp.jce_uReportType;
                model.uIsUnderline = btnInfo.jce_stQueryQuickSendInfoWebRsp.jce_stInfo.jce_uIsUnderline;

                [KSIapGiftManager buildUpGiftWithQuickSendInfo:btnInfo.jce_stQueryQuickSendInfoWebRsp.jce_stInfo giftBlock:^(proto_room_lottery_RoomLotteryGift *backpackGift, proto_new_gift_Gift *kbGift, proto_new_gift_Gift *bonusGift) {
                    model.backpackGift = backpackGift;
                    model.kbGift = kbGift;
                    model.bonusGift = bonusGift;
                }];
                
                simpleFeed.giftGuide = model;
            }
            !completionBlk ?: completionBlk(YES);
        } else {
            !completionBlk ?: completionBlk(NO);
            KLog(@"关注Feed送礼按钮异化请求失败 res: %zd", workResult);
        }
    }];
}

// 看广告免费送礼按钮展示信息
- (void)getFeedFreeADGiftInfoWithScene:(KSAdFreeGiftSceneType)type
                         completionBlk:(void(^)(proto_feed_webapp_GetDataAfterExposureRsp *rsp, KSimpleFeedGiftGuide *gift))completionBlk {
    // 拉取feed列表的时候请求
    proto_quick_gift_webapp_QueryQuickSendInfoWebReq *giftReq = [proto_quick_gift_webapp_QueryQuickSendInfoWebReq new];
    giftReq.jce_uHostUid = (TarsUInt32)[KSLoginManager sharedInstance].curUserInfo.userId;
    giftReq.jce_uScene = proto_quick_gift_webapp_QUICK_SCENE_EN_ASYNC_UGC_DETAIL_FEED;
    giftReq.jce_uRecvUid = (TarsUInt32)[KSLoginManager sharedInstance].curUserInfo.userId;
    
    proto_feed_webapp_GetDataAfterExposureReq *req = proto_feed_webapp_GetDataAfterExposureReq.new;
    req.jce_uid = (TarsUInt32)[KSLoginManager sharedInstance].curUserInfo.userId;
    req.jce_stQueryQuickSendInfoWebReq = giftReq;

    KSProtocolBase *protocol = [[KSProtocolBase alloc] init];
    [protocol startWorkWithCommand:KGCMD_GET_DATA_AFTER_EXPOSURE
                         reqObject:req
                          rspClass:@"proto_feed_webapp_GetDataAfterExposureRsp"
                          rspBlock:^(WnsProtocolBase *workProtocolObj, NSInteger workResult) {
        if (PROTOCOL_SUCCESS(workResult)) {
            proto_feed_webapp_GetDataAfterExposureRsp *btnInfo = SAFE_CAST(workProtocolObj.rspObject, proto_feed_webapp_GetDataAfterExposureRsp);
            if (btnInfo.jce_unDataType <= 0) {
                KErrLog(@"关注 Feed 广告送礼按钮异化信息请求失败  res: %zd", workResult);
                !completionBlk ?: completionBlk(nil, nil);
                return;
            }
            // 快捷送礼组装数据
            KSimpleFeedGiftGuide *model = [[KSimpleFeedGiftGuide alloc] init];
            if (btnInfo.jce_unDataType == proto_feed_webapp_ENUM_DATA_TYPE_AFTER_EXP_EM_DATA_TYPE_AFTER_EXP_QUICK_GIFT) {
                proto_new_gift_Gift *gift;
                
                TarsUInt32 isAdvertGift = btnInfo.jce_stQueryQuickSendInfoWebRsp.jce_stInfo.jce_uIsAdvertGift;
                KSAdFreeGiftScene *followScene = [KSAdFreeGiftScene sceneWithType:type];
                if (isAdvertGift == 1 && followScene.scenceShow) {
                    model.isFreeAd = YES;
                    // 广告免费送外显礼物
                    gift = btnInfo.jce_stQueryQuickSendInfoWebRsp.jce_stInfo.jce_stAdvertGift;
                } else {
                    // 快捷送礼礼物
                    gift = btnInfo.jce_stQueryQuickSendInfoWebRsp.jce_stInfo.jce_stGift;
                }
                
                if (!gift) {
                    !completionBlk ?: completionBlk(nil, nil);
                }
                
                // 组装礼物信息
                model.logoStr = gift.jce_strLogo;
                model.titleStr = gift.jce_strGiftName;
                model.price = gift.jce_uPrice;
                model.buttoneStrategyStr = btnInfo.jce_stQueryQuickSendInfoWebRsp.jce_stInfo.jce_strButtonStrategyCopy; // 按钮文案
                model.badgeStr = btnInfo.jce_stQueryQuickSendInfoWebRsp.jce_stInfo.jce_strGiftCopy; // 角标文案
                
                model.uBonuseBalace = btnInfo.jce_stQueryQuickSendInfoWebRsp.jce_uBonuseBalace;
                model.uKbBalance = btnInfo.jce_stQueryQuickSendInfoWebRsp.jce_uKbBalance;
                
                model.reportType = btnInfo.jce_stQueryQuickSendInfoWebRsp.jce_uReportType;
                model.uIsUnderline = btnInfo.jce_stQueryQuickSendInfoWebRsp.jce_stInfo.jce_uIsUnderline;
                
                KLog(@"关注Feed 送礼按钮异化 IsAdvertGift: %u", isAdvertGift);
                
                [KSIapGiftManager buildUpGiftWithQuickSendInfo:btnInfo.jce_stQueryQuickSendInfoWebRsp.jce_stInfo giftBlock:^(proto_room_lottery_RoomLotteryGift *backpackGift, proto_new_gift_Gift *kbGift, proto_new_gift_Gift *bonusGift) {
                    model.backpackGift = backpackGift;
                    model.kbGift = kbGift;
                    model.bonusGift = bonusGift;
                }];
            }
            !completionBlk ?: completionBlk(btnInfo, model);
        } else {
            !completionBlk ?: completionBlk(nil, nil);
            KLog(@"关注Feed送礼按钮异化请求失败 res: %zd", workResult);
        }
    }];
}

- (void)getRecBottomMinorHeatCardGiftInfoWithUgcId:(NSString *)ugcId
                                     completeBlock:(void(^)(KSimpleFeedGiftGuide *model))completeBlock {
    [[KSIapGiftManager defaultManager] queryRecGiftRankMinorHeatCardInfo:ugcId
                                                         completionBlock:^(proto_new_gift_Gift *kbGift, proto_comm_heat_card_HeatCardGiftExtInfo *giftExtInfo) {
        
        KSimpleFeedGiftGuide *model = [[KSimpleFeedGiftGuide alloc] init];
        if (kbGift) {
            model.botttomSubTitle = giftExtInfo.jce_iPayType == proto_svr_heat_card_EM_HEATCARD_PAY_TYPE_Em_HeatCard_PayType_KB ? @"一键加热" : @"背包加热";
            // ⚠️ giftExtInfo中的strGiftCustomName 只用在右侧异化 小额热度卡礼物名称；底部异化浮层用proto_new_gift_Gift中jce_strGiftName
            model.titleStr = kbGift.jce_strGiftName;
            model.logoStr = kbGift.jce_strLogo;
            model.price = kbGift.jce_uPrice;
            model.subTitleStr = [kbGift.jce_strRightUpperTag safeObjectForKey:@(JceIapGift_emGiftRightUpperType_GIFT_RIGH_UPPER_TYPE_OTHER)]; // 异化礼物右上角tag or 底部浮层曝光数
            model.isMinorHeatCard = YES;
            model.minorHeatCardId = kbGift.jce_uGiftId;
            model.kbGift = kbGift;
            model.giftExtInfo = giftExtInfo;
        }
        if (completeBlock) {
            completeBlock(model);
        }
    }];
}

// 礼物榜异化点击送礼
- (void)showSendGiftAlertViewWithSimpleFeed:(KSimpleFeed *)timelineSimpleFeed
{
    KS_WEAK_SELF(self);
    [[KSIapGiftManager defaultManager] queryGiftRankConfigInfoWithCompletionBlock:^(proto_room_lottery_RoomLotteryGift *backpackGift, proto_new_gift_Gift *kbGift, proto_new_gift_Gift *bonusGift, proto_quick_gift_webapp_QueryQuickGiftWebRsp *rsp) {
        CHECK_SELF_AND_RETURN();
        
        BOOL isCurrentUser = timelineSimpleFeed.simpleUser.userinfo.userId == [KSLoginManager sharedInstance].curUserInfo.userId;
        if (backpackGift && !isCurrentUser) {
            KS_WEAK_SELF(self);
            [self.rewardSendGiftVC queryPackageKBGiftInfoWithActivityId:NSUToString(timelineSimpleFeed.songinfo.activityId) complete:^{
                CHECK_SELF_AND_RETURN();
                
                KSNewSinglesDetailGiftAlertView *alertView = [[KSNewSinglesDetailGiftAlertView alloc] initWithFrame:CGRectMake((SCREEN_WIDTH - 315) / 2, (SCREEN_HEIGHT - 227.5) / 2 - 30, 315, 227.5)];  
                [alertView setSendBlock:^{
                    CHECK_SELF_AND_RETURN();
                    //直接赠送礼物
                    KS_WEAK_SELF(self);
                    [KSGiftBridgeHelper checkAnonymousStatusForUid:timelineSimpleFeed.simpleUser.userinfo.userId complection:^{
                        CHECK_SELF_AND_RETURN();
                        KSStatFrom statFrom = KSStatFromKUserProfileVCFeed;
                        KSIapGift *gift = [[KSIapGift alloc] initWithProtoLotteryGift:backpackGift];
                        [KSGiftBridgeHelper fillSponsorUserInfoForGift:gift];
                        KSBaseSendGiftInfo *info = [KSBaseSendGiftInfo createWithGift:gift giftCount:1 sendStyle:KSGiftBridgeSendStyleDirectly receiverUid:0 statFrom:statFrom];
                        info.shouldIgnoreKBBanlance = NO; // 送礼前忽略余额检查
                        UIView *rootView = [self getViewContainer];
                        [self.multiGiftBridgeManager sendGiftWithInfo:info atView:rootView detail:timelineSimpleFeed block:nil];
                    }];
                    [[KSTraceReportManager sharedManager] buyKBReportClickOrExpose:YES extraBlock:^(TraceBuyKBReportCommon *reportCommon) {
                        reportCommon.posid = BuyKBPid_TimelineDetail_GiftRank_BackpackGift_Sofa_Alert;
                        reportCommon.commonInt1 = backpackGift.jce_uGiftId;
                        reportCommon.toUid = timelineSimpleFeed.simpleUser.userinfo.userId;
                        reportCommon.ugcId = [self getFeedUgcId:timelineSimpleFeed];
                    }];
                }];
                [alertView updateUIWithLogoStr:backpackGift.jce_strGiftLogo price:backpackGift.jce_uGiftPrice];
                [alertView updateSubtitleText:@"直接送出该背包礼物上榜"];
                UIView *vcView = [[KSNavigationManager sharedManager] getTopViewController].view;
                [alertView showInView:vcView maskViewDismiss:YES];
            }];
            [[KSTraceReportManager sharedManager] buyKBReportClickOrExpose:NO extraBlock:^(TraceBuyKBReportCommon *reportCommon) {
                reportCommon.posid = BuyKBPid_TimelineDetail_GiftRank_BackpackGift_Sofa_Alert;
                reportCommon.commonInt1 = backpackGift.jce_uGiftId;
                reportCommon.toUid = timelineSimpleFeed.simpleUser.userinfo.userId;
                reportCommon.ugcId = [self getFeedUgcId:timelineSimpleFeed];
            }];
            return;
        }
        // 奖励金与背包弹窗
        if ([self.rewardSendGiftVC checkIfShouldShowRewardGiftAlert]) {
            // TODO: 奖励金弹窗待确认 checkIfShouldShowRewardGiftAlert 逻辑有bug
            self.hasRewardsView = NO;
            showReportSceneType scenType = 0;
            if ([self.feedReportDelegate respondsToSelector:@selector(getRewardSendGiftSource)]) {
                scenType = [self.feedReportDelegate getRewardSendGiftSource];
            }
            JceIapGift_BonusConsumeUgc *consumeUgc = [[JceIapGift_BonusConsumeUgc alloc] init];
            consumeUgc.jce_strUgcId = [self getFeedUgcId:timelineSimpleFeed];
            
            // 获取背包礼物信息
            KS_WEAK_SELF(self);
            [self.rewardSendGiftVC queryPackageKBGiftInfoWithActivityId:NSUToString(timelineSimpleFeed.songinfo.activityId) complete:^{
                CHECK_SELF_AND_RETURN();
                self.rewardSendGiftVC.QUICK_SCENE  = proto_quick_gift_webapp_QUICK_SCENE_EN_ASYNC_UGC;
                if (self.rewardSendGiftVC.hasKBGiftInPackage) {
                    [self.rewardSendGiftVC showRewardsAlertViewWithShowType:RewardsAndPackageType
                                                              sendGiftUfrom:JceIapGift_CONSUME_LOCATION_E_FEED_CONSUME
                                                                consumeType:JceIapGift_emBonusConsumeType_BONUS_CONSUME_GIFTUGC
                                                                  sceneType:scenType
                                                               bonusConsume:consumeUgc
                                                                      toUid:timelineSimpleFeed.simpleUser.userinfo.userId
                                                                 simpleFeed:timelineSimpleFeed];
                } else {
                    [self.rewardSendGiftVC showRewardsAlertViewWithShowType:RewardsDirectSendGiftType
                                                              sendGiftUfrom:JceIapGift_CONSUME_LOCATION_E_FEED_CONSUME
                                                                consumeType:JceIapGift_emBonusConsumeType_BONUS_CONSUME_GIFTUGC
                                                                  sceneType:scenType
                                                               bonusConsume:consumeUgc
                                                                      toUid:timelineSimpleFeed.simpleUser.userinfo.userId
                                                                 simpleFeed:timelineSimpleFeed];
                    
                }
                self.rewardSendGiftVC.configGift = nil;
                if (bonusGift) {
                    self.rewardSendGiftVC.configGift = bonusGift;
                    [self.rewardSendGiftVC updateGiftID:bonusGift.jce_uGiftId logoStr:bonusGift.jce_strLogo kbPrice:bonusGift.jce_uPrice bonusPrice:bonusGift.jce_uBonusPrice];
                }
                
                KS_WEAK_SELF(self);
                [self.rewardSendGiftVC queryDefaultRewards:^(BOOL isDefault) {
                    CHECK_SELF_AND_RETURN();
                    if (isDefault) {
                        [self.rewardSendGiftVC rewardGiftBtnDidClick:nil];
                    } else {
                        [KSendRewardGiftAlertViewController showRewardsAlertView:self.rewardSendGiftVC];
                        self.hasRewardsView = YES;
                    }
                }];
            }];
        } else {
            KSNewSinglesDetailGiftAlertView *alertView = [[KSNewSinglesDetailGiftAlertView alloc] initWithFrame:CGRectMake((SCREEN_WIDTH - 315) / 2, (SCREEN_HEIGHT - 227.5) / 2 - 30, 315, 227.5)];
            KS_WEAK_SELF(self);
            [alertView setSendBlock:^{
                CHECK_SELF_AND_RETURN();
                //直接赠送礼物
                KS_WEAK_SELF(self);
                [KSGiftBridgeHelper checkAnonymousStatusForUid:timelineSimpleFeed.simpleUser.userinfo.userId complection:^{
                    CHECK_SELF_AND_RETURN();
                    KSStatFrom statFrom = KSStatFromKUserProfileVCFeed;
                    KSIapGift *iapGift = self.heartGift;
                    if (kbGift) {
                        JceIapGift_Gift *jceGift = [[JceIapGift_Gift alloc] init];
                        jceGift.jce_uGiftId = kbGift.jce_uGiftId;
                        jceGift.jce_uPrice = kbGift.jce_uPrice;
                        jceGift.jce_strLogo = kbGift.jce_strLogo;
                        jceGift.jce_mapLogo = kbGift.jce_mapLogo;
                        jceGift.jce_uFlashType = kbGift.jce_uFlashType;
                        jceGift.jce_strGiftName = kbGift.jce_strGiftName;
                        jceGift.jce_iComboFlag = 0;
                        iapGift = [[KSIapGift alloc] initWithJceGift:jceGift];
                        [KSGiftBridgeHelper fillSponsorUserInfoForGift:iapGift]; // 填充送礼人信息
                    }
                    KSBaseSendGiftInfo *info = [KSBaseSendGiftInfo createWithGift:iapGift giftCount:1 sendStyle:KSGiftBridgeSendStyleDirectly receiverUid:0 statFrom:statFrom];
                    info.shouldIgnoreKBBanlance = NO; // 送礼前忽略余额检查
                    UIView *rootView = [self getViewContainer];
                    [self.multiGiftBridgeManager sendGiftWithInfo:info atView:rootView detail:timelineSimpleFeed block:nil];
                }];
            }];
            if (kbGift) {
                [alertView updateUIWithLogoStr:kbGift.jce_strLogo price:kbGift.jce_uPrice];
            }
            UIView *vcView = [[KSNavigationManager sharedManager] getTopViewController].view;
            [alertView showInView:vcView maskViewDismiss:YES];
        }
    }];
    // 点击上报
    [self traceKBReportDissimilateGiftRankExposeWithSimpleFeed:timelineSimpleFeed isClickOrExpose:YES];
}

// COPY from KSUGCDetailGiftBridge
- (NSString *)getFeedUgcId:(KSimpleFeed *)simpleFeed{
    if (simpleFeed.soloAlbumInfo) {
        return simpleFeed.soloAlbumInfo.strAlbumId;
    } else if (simpleFeed.payAlbumInfo) {
        return simpleFeed.payAlbumInfo.strAlbumId;
    } else if (simpleFeed.isMusicMoodFeed) {
        return simpleFeed.simpleFeedCommon.strFeedId;
    } else {
        return simpleFeed.simpleFeedCommon.strFeedId;
    }
}

- (void)sendFlowerGiftDirectelyTimelineSimpleFeed:(KSimpleFeed *)timelineSimpleFeed
{
    if (!self.sendFlowerDirectlyContainerView)
    {
        self.sendFlowerDirectlyContainerView = [[KSSendGiftContainerView alloc] initWithStyle:KSSendGiftContainerViewStyleWhite];
        self.sendFlowerDirectlyContainerView.delegate = self;
        self.sendFlowerDirectlyContainerView.isSendFlowerDirectley = YES;
        
    }
    
    // 是否来自同城
    BOOL isFromNearBy = timelineSimpleFeed.isNearbyFeed;

    MusicInteractScene_EnumInteractScene eScene = MusicInteractScene_EnumInteractScene_emSceneDefault;
    
    if (isFromNearBy) // 来自同城
    {
       eScene = MusicInteractScene_EnumInteractScene_emSceneFeedIntraCity;
    }
    self.sendFlowerDirectlyContainerView.fromScene = eScene;
    

    if (timelineSimpleFeed.soloAlbumInfo)
    {
        self.sendFlowerDirectlyContainerView.statFrom = JceIapGift_CONSUME_LOCATION_E_FEED_ALBUM_CONSUME;
    }
    else
    {
        self.sendFlowerDirectlyContainerView.statFrom = JceIapGift_CONSUME_LOCATION_E_FEED_CONSUME;
    }
    self.sendGiftContainerView.webQuickSence = proto_quick_gift_webapp_QUICK_SCENE_EN_ASYNC_UGC;
    [self.sendFlowerDirectlyContainerView setTimelineSimpleFeed:[timelineSimpleFeed toGiftSimpleFeed]];
    [self.sendFlowerDirectlyContainerView setupSubviews];
   
    [self.sendFlowerDirectlyContainerView sendFlowerGiftDirectOneTimes];
    
}

//更新K币总数和鲜花总数
- (void)updateGiftStarCountAndFlowerCount:(KSimpleFeedGiftRankItem*)giftRankItem sentGiftCount:(NSInteger )count type:(KSIapGiftType)giftType
{
    if (giftType == KSIapGiftTypeProps)
    {
        //更新鲜花数
        giftRankItem.propsNum +=  count;
    }
    else if (giftType == KSIapGiftTypeFlower)
    {
        //更新鲜花数
        giftRankItem.flowerNum +=  count;
    }
    else
    {
        //其他礼物默认更新K币数
        giftRankItem.giftStarNum += count;
    }
}

// 送礼和送花成功后拉取礼物榜刷新，历史假feed形式无法保证本地礼物榜排序逻辑能够对齐后台顺序
- (void)updateFakeFeedUIWithSimpleFeed:(KSimpleFeed *)simpleFeed sentGiftCount:(NSInteger)count type:(KSIapGiftType)giftType isAnonymous:(BOOL)isAnonymous {
    NSString *ugcid = simpleFeed.simpleFeedCommon.strFeedId;
    if (IS_EMPTY_STR_BM(ugcid)) {
        KERROR(@"feed 送礼成功后feedid异常");
        return;
    }
    
    KS_WEAK_SELF(self);
    KSPageableList *pageList = [KSPageableList new];
    [[KSGiftManager sharedManager] getGiftsRankList:ugcid pageList:pageList giftRankRefer:JceRank_GIFT_RANK_REFER_REFER_UGC_GIFT_RANK completeBlock:^(KSPageableList *pageableList, id param, NSError *error) {
        if (!error) {
            //刷新布局
            RUN_ON_UI_THREAD_ASYNC(^{
                CHECK_SELF_AND_RETURN()
                simpleFeed.layoutInfo = nil;
                // 更新礼物榜排序
                simpleFeed.giftRankInfo.topRankArray = pageableList.datalist;
                // 更新礼物数量
                NSUInteger uStarNum = [(NSNumber*)[pageableList.additionalListData objectForKey:GIFTRANKSTAR_KEY] unsignedIntValue];
                NSUInteger uflowerNum = [(NSNumber*)[pageableList.additionalListData objectForKey:GIFTRANKFLOWER_KEY] unsignedIntValue];
                NSUInteger uPropNum = [(NSNumber*)[pageableList.additionalListData objectForKey:GIFTRANKPROP_KEY] unsignedIntValue];
                NSUInteger uDiamondNum = [(NSNumber*)[pageableList.additionalListData objectForKey:GIFTRANKDIAMOND_KEY] unsignedIntValue];
                
                simpleFeed.giftRankInfo.giftWealth = uStarNum;
                simpleFeed.recivedFlowerInfo.number = uflowerNum;
                simpleFeed.recivedFlowerInfo.uPropsNum = uPropNum;
                simpleFeed.recivedFlowerInfo.diamondNum = uDiamondNum;
                
                [self reloadTable];
            });
        } else {
            KERROR(@"[Feed] refresh giftRank err:%@", error);
        }
    }];
}

- (NSInteger)checkSendGiftHistory:(NSArray *)giftRankArray
{
    NSInteger myGiftItemIndex = -1;
    if (!giftRankArray || giftRankArray.count==0)
    {
        return myGiftItemIndex;
    }
    
    //当前用户是否有送花记录
    for (int i = 0 ; i<giftRankArray.count; i++)
    {
        KSimpleFeedGiftRankItem *giftItem = [KSComHelper getObjectInArray:giftRankArray byIndex:i ofClassType:[KSimpleFeedGiftRankItem class] defaultValue:nil];
        if(giftItem && (giftItem.userInfo.userId == [KSLoginManager sharedInstance].curUserInfo.userId || giftItem.userInfo.realUserId == [KSLoginManager sharedInstance].curUserInfo.userId) && (giftItem.flowerNum > 0 ||giftItem.giftStarNum > 0 || giftItem.propsNum > 0))
        {
            myGiftItemIndex = i;
            return myGiftItemIndex;
        }
    }
    return myGiftItemIndex;
}

- (void)didTapFollowFeedMultiGiftWithGift:(KSFollowFeedMultiSendGiftInfo *)gift selectedWork:(NSArray<KSFollowFeedMultiSendGiftWorkItem *> *)selectedWork {
    [selectedWork enumerateObjectsUsingBlock:^(KSFollowFeedMultiSendGiftWorkItem * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        // 防止连续送礼导致送礼失败 送礼间隔0、0.5、1s
        KS_WEAK_SELF(self);
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * idx * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            CHECK_SELF_AND_RETURN();
            [self sendFollowFeedMultiGiftWithGift:gift work:obj];
        });
    }];
}

- (void)sendFollowFeedMultiGiftWithGift:(KSFollowFeedMultiSendGiftInfo *)gift work:(KSFollowFeedMultiSendGiftWorkItem *)work {
    //直接走作品送礼
    KSStatFrom statFrom = JceIapGift_CONSUME_LOCATION_E_FEED_CONSUME;
    KSIapGift *iapGift = KSIapGift.new;
    iapGift.giftId = gift.uGiftId;
    iapGift.price = gift.uWorthKb;
    iapGift.strLogo = gift.strLogo;
    iapGift.giftName = gift.strGiftName;
    iapGift.bonusPrice = gift.uBonusPrice;
    
    KSBaseSendGiftInfo *info = [KSBaseSendGiftInfo createWithGift:iapGift giftCount:1 sendStyle:KSGiftBridgeSendStyleDirectly receiverUid:work.uid statFrom:statFrom];
    [KSGiftBridgeHelper fillSponsorUserInfoForGift:iapGift];
    KSUserInfo *userInfo = [[KSUserInfo alloc] init];
    userInfo.userId = work.uid;
    userInfo.nickName = work.strNickName;
    [KSGiftBridgeHelper fillEffectedUserInfo:userInfo forGift:iapGift];
    UIViewController *topVC = [[KSNavigationManager sharedManager] getTopViewController];
    KSimpleFeed *feed = [KSimpleFeed.alloc initWithFollowMultiSendItem:work];
    self.giftBridge.ksimpleFeedDetail = [feed toGiftSimpleFeed];
    
    if (gift.unPayType == proto_feed_webapp_ENUM_ONE_CLICK_PAY_TYPE_EM_ONE_CLICK_PAY_BONUS) {
        KS_WEAK_SELF(self);
        [[KSIapGiftManager defaultManager] rewardsDoGiftExchange:iapGift count:1 reqBlock:^(JceIapGift_DoGiftExchangeReq *req) {
            req.jce_iIsDirectSend = 1;
            req.jce_scene = proto_daily_settle_emConsumeScene_QuickGift;
            req.jce_subScene = proto_quick_gift_webapp_QUICK_SCENE_EN_ASYNC_UGC;
        } completeBlock:^(id ksObject, NSDictionary *customInfo, NSError *error) {
            CHECK_SELF_AND_RETURN();
            if (error) { return; }
            iapGift.isPrizeGift = YES;
            info.ignoreGiftCountCheckBeforeSend = YES;
            [self.giftBridge sendGiftWithInfo:info atView:topVC.view];
        }];
    } else {
        [self.giftBridge sendGiftWithInfo:info atView:topVC.view];
    }
    // 营收上报
    if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(onFollowFeedMultiGiftKbClickWithGift:work:)]) {
        [self.feedReportDelegate onFollowFeedMultiGiftKbClickWithGift:gift work:work];
    }
}

// 多人送礼点击上报
- (void)reportFollowFeedMultiGiftClickWithGift:(KSFollowFeedMultiSendGiftInfo *)gift work:(NSArray<KSFollowFeedMultiSendGiftWorkItem *> *)selectedWork {
    if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(onFollowFeedMultiGiftClickWithGift:work:)]) {
        [self.feedReportDelegate onFollowFeedMultiGiftClickWithGift:gift work:selectedWork];
    }
}

#pragma mark -- KSSendGiftContainerViewDataSource
- (BOOL)IsSendGiftContainerViewOpenFromFeed
{
    return YES;
}

#pragma mark -- KSGiftBridgeProtocol
- (void)ksGiftBridgeDidSendFlowerSuccess:(KSGiftBridge *)giftBridge
{
    [KSAlertManager tempAlert:@"赠送鲜花成功"];
}
#pragma mark -- 送礼物浮层 delegate
// 送礼物成功
- (void)sendGiftBaseView:(UIView *)sendGiftBaseView sendGiftSuccessWithGift:(KSIapGift *)gift count:(NSInteger)count {
    /// 用下面的方法
}

/* 通知来源 KS_IAP_GIFT_SEND_GIFT_SUCC_NOTIFICATION  */
- (void)updateGiftRankInfoWhenSuccess:(NSNotification *)notification {
    NSDictionary *infoDic = (NSDictionary*)notification.userInfo;
    NSString *ugcID = [infoDic objectForKey:@"ugcId"];
    KSIapGift *iapGift = [infoDic objectForKey:@"iapGift"];
    NSInteger count = [[infoDic objectForKey:@"count"] integerValue];
    BOOL isAnonymous = [[infoDic objectForKey:@"isAnonymous"] boolValue];
    KINFO(@"[feed] 收到送礼成功通知，count：%ld", (long)count);
    
    if (self.currentVC != [[KSNavigationManager sharedManager] getTopViewController]) {
        return;
    }
    
    KSimpleFeed *sendGiftSimpleFeed = nil;
    if (iapGift.type != KSIapGiftTypeCommon && iapGift.type != KSIapGiftTypeSound && iapGift.type!= KSIapGiftTypeFlower && iapGift.type!= KSIapGiftTypeVip && iapGift.type!= KSIapGiftTypePackage && iapGift.type != KSIapGiftTypeProps && iapGift.type != KSIapGiftTypePrize && iapGift.type != KSIapGiftTypePicture)
    {
        //这里加过滤的原因不清楚，最早是star写的，沿用这个。以后新加一种新的礼物类型这里都需要加上
        return;
    }
    
    //找到赠送礼物对应的Feed
    BOOL isFound = NO;
    if ([notification.name isEqualToString:KS_IAP_GIFT_SEND_GIFT_SUCC_LIVE_SHOW_NOTIFICATION]) {
        if (self.sendGiftSimpleFeed.liveShow) {
            if ([SAFE_STR_BM(infoDic[@"roomId"]) isEqualToString:self.sendGiftSimpleFeed.liveShow.strLiveRoomId]) {
                sendGiftSimpleFeed = self.sendGiftSimpleFeed;
                isFound = YES;
            }
        }
    } else if ([notification.name isEqualToString:KS_IAP_GIFT_SEND_GIFT_SUCC_KTV_NOTIFICATION]) {
        if ([self.sendGiftSimpleFeed isKtvFeed]) {
            if ([SAFE_STR_BM(infoDic[@"roomId"]) isEqualToString:[self.sendGiftSimpleFeed getKtvRoomId]]) {
                sendGiftSimpleFeed = self.sendGiftSimpleFeed;
                isFound = YES;
            }
        }
    } else {
        if (![ugcID isEqualToString:self.sendGiftSimpleFeed.simpleFeedCommon.strFeedId]) {
            NSMutableArray* feedsList = [self delegateFeedListSource];
            for (NSObject *feedObj in feedsList) {
                KSimpleFeed* simpleFeed = [KSimpleFeed safeCastFromObj:feedObj];
                if ([simpleFeed.simpleFeedCommon.strFeedId isEqualToString:ugcID]) {
                    sendGiftSimpleFeed = simpleFeed;
                    isFound = YES;
                    break;
                }
            }
        } else {
            sendGiftSimpleFeed = self.sendGiftSimpleFeed;
            isFound = YES;
        }
    }
    
    if (isFound) {
        NSInteger totalCount = count; //鲜花的price ==0
        if (iapGift.price >= 1)
        {
            totalCount = iapGift.price * count;
        }
        
        //simpleFeed构造小额(标准)热度卡锚点信息
        if (iapGift.flashType == JceIapGift_emGiftType_GIFT_TYPE_SMALL_HEATCARD_GIFT || iapGift.flashType == JceIapGift_emGiftType_GIFT_TYPE_HEAT_CARD_GIFT)
        {
            if (!sendGiftSimpleFeed.giftGuide)
            {
                sendGiftSimpleFeed.giftGuide = [[KSimpleFeedGiftGuide alloc] init];
            }
            proto_new_gift_Gift *kbGift = [[proto_new_gift_Gift alloc] init];
            kbGift.jce_uFlashType = (TarsUInt32)iapGift.flashType;
            sendGiftSimpleFeed.giftGuide.kbGift = kbGift;
        }
        
        [self updateFakeFeedUIWithSimpleFeed:sendGiftSimpleFeed sentGiftCount:totalCount type:iapGift.type isAnonymous:isAnonymous];
        
        [self.sendGiftContainerView asyncScene_starSceneWith:infoDic];
    }
}

- (void)updateGiftRankInfo:(KSIapGift*)gift count:(NSInteger)count
{
    NSInteger starDiamond = gift.price * count;
    
    self.sendGiftSimpleFeed.giftRankInfo.giftWealth =  self.sendGiftSimpleFeed.giftRankInfo.giftWealth + starDiamond;
    
    //更新或增加自己的Item
    KSimpleFeedGiftRankItem *myGiftRankItem;
    NSMutableArray *topRankArray = [[NSMutableArray alloc] init];
    for (KSimpleFeedGiftRankItem *giftRankItem in self.sendGiftSimpleFeed.giftRankInfo.topRankArray) {
        if (giftRankItem.userInfo.userId == [KSLoginManager sharedInstance].curUserInfo.userId) {
            giftRankItem.giftStarNum = giftRankItem.giftStarNum + starDiamond;
            myGiftRankItem = giftRankItem;
        }
        else {
            [topRankArray addObject:giftRankItem];
        }
    }
    
    if (!myGiftRankItem) {
        myGiftRankItem = [[KSimpleFeedGiftRankItem alloc] init];
        KSUserInfo *userInfo = [[KSUserInfo alloc] init];
        userInfo.userId = [KSLoginManager sharedInstance].curUserInfo.userId;
        userInfo.avatarTimestamp = [KSLoginManager sharedInstance].curUserInfo.avatarTimestamp;
        userInfo.nickName = [KSLoginManager sharedInstance].curUserInfo.nickName;
        myGiftRankItem.giftStarNum = starDiamond;
        myGiftRankItem.userInfo = userInfo;
    }
    
    [topRankArray addObject:myGiftRankItem];
    
    //重新排序下
    self.sendGiftSimpleFeed.giftRankInfo.topRankArray = [topRankArray sortedArrayUsingComparator:^NSComparisonResult(id   obj1, id obj2) {
        KSimpleFeedGiftRankItem *rankItem1 = (KSimpleFeedGiftRankItem*)obj1;
        KSimpleFeedGiftRankItem *rankItem2 = (KSimpleFeedGiftRankItem*)obj2;
        
        if (rankItem1.giftStarNum > rankItem2.giftStarNum) {
            return NSOrderedAscending;
        }
        else if (rankItem1.giftStarNum < rankItem2.giftStarNum) {
            return NSOrderedDescending;
        }
        else{
            return NSOrderedSame;
        }
    }];
    
    self.sendGiftSimpleFeed.layoutInfo = nil;
    [self reloadTable];
}

//包月vip消息通知
- (void)vipStatusChanged:(NSNotification*)noti
{
    KSPayManagerBuyVipResult result =((NSNumber*)[noti.userInfo objectForKey:@"result"]).integerValue;
    if (result == KSPayManagerBuyVipResult_Success)
    {
        KLog(@"[包月充值: 动态页面收到包月购买成功通知，刷新全局vip信息]");
        //[[KSLoginManager sharedInstance] refreshLoginUserVipInfo:0];
    }
}

#pragma mark cell可见的时候出现送花曝光倒计时
- (void)beginFlowerExposeTimerWhileAppear
{
    if (self.strShowHippyUrl.length > 0) {
        /// 有半屏hippy，不用异化
        return;
    }
    
    for (KSLayoutableFeedBaseCell *cell in self.visibleCellList) {
        if ([cell isKindOfClass:KSLayoutableFeedBaseCell.class]) {
            [cell beginFeedExposeCountDownTimer];
        }
    }
}

#pragma mark cell不可见的时候停止送花曝光倒计时
- (void)stopFlowerExposeTimerWhileDisappear
{
    for (KSLayoutableFeedBaseCell *cell in self.visibleCellList) {
        if ([cell isKindOfClass:KSLayoutableFeedBaseCell.class]) {
            [cell stopFeedExposeCountDownTimer:@"Disappear"];
        }
    }
}

#pragma mark - KSendRewardGiftAlertViewControllerDelegate

- (void)buyKBWriteReport:(TraceBuyKBReportCommon *)reportCommon
{
    reportCommon.ugcId = self.curSimpleFeed.songinfo.strHalfUgcId;
    reportCommon.songMid = self.curSimpleFeed.songinfo.songMid;
    reportCommon.ugcmask = self.curSimpleFeed.songinfo.ugcMask;
    reportCommon.ugcmaskExt = self.curSimpleFeed.songinfo.ugcMaskExt;
    if (self.hasRewardsView) {
        reportCommon.commonInt11 = 1;
    } else {
        reportCommon.commonInt11 = 2;
    }
    self.hasRewardsView = NO;
}

- (void)showGiftContainerWithPackageGift:(KSendRewardGiftAlertViewController *)con
{
    if (con.simpleFeed && [con.simpleFeed isKindOfClass:KSimpleFeed.class]) {
        [self showGiftContainerViewWidthTimelineSimpleFeed:(KSimpleFeed *)con.simpleFeed];
        [self.sendGiftContainerView moveToPackageTab];
    }
}

#pragma mark - KSLuckyBoxManagerDelegate
- (void)ksLuckyBoxManager:(KSLuckyBoxManager *)luckyBoxManager playAnimationWithGift:(KSIapGift *)gift
{
    // 不管抽到什么礼物都播动画
    [[KSIapGiftManager defaultManager] showGiftAnimationToMasterQueue:gift count:luckyBoxManager.giftCount inView:luckyBoxManager.giftBridge.rapidClickAnimationView delegate:luckyBoxManager.giftBridge];
    //返奖开盒+返奖礼物动画
    [luckyBoxManager playRebateRewardIfExistWithGiftManager:[KSIapGiftManager defaultManager]];
    // 播放动画后立即清理掉
    [[KSLuckyBoxManager sharedManager] clear];
}

#pragma mark - KSSendGiftContainerViewDelegate
- (void)didSendGiftBaseViewClosed:(KSSendGiftBaseView *)sendGiftBaseView {
    if (self.feedManagerDelegate && [self.feedManagerDelegate respondsToSelector:@selector(giftBaseViewDidClosed)]) {
        [self.feedManagerDelegate giftBaseViewDidClosed];
    }
}

// 送礼动画执行完毕
#pragma mark - KSGiftAnimationActionDelegate
- (void)KSGiftAnimationAction:(KSGiftAnimationAction *)giftAnimationAction animationDidFinish:(BOOL)flag
{
    [self.sendGiftContainerView asyncScene_alertIfNeed];
}


#pragma mark - 爆灯送礼
- (void)sendBurstGift:(KSBurstLightupItem *)gift
           simpleFeed:(KSimpleFeed *)simpleFeed
         sendComplete:(void(^)(BOOL sendSuccess))sendComplete
          payComplete:(void(^)(BOOL paySuccess))payComplete {
    
    if (!gift || !gift.uGiftID) {
        KLog(@"爆灯礼物为空");
        return;
    }
    
    KSIapGift *iapGift = [KSIapGift.alloc initWithBurstGift:gift];
    
    [self sendGift:iapGift simpleFeed:simpleFeed sendComplete:sendComplete payComplete:payComplete];
}

/// 爆灯结束后刷新
- (void)updateDataAfterBurstWithDic:(NSDictionary *)infoDic {
    KSIapGift *iapGift = SAFE_CAST([infoDic safeObjectForKey:@"iapGift"], KSIapGift);
    NSInteger count = [infoDic integerValueForKey:@"count"];
    BOOL isAnonymous = [infoDic boolValueForKey:@"isAnonymous"];
    KSimpleFeed *sendGiftSimpleFeed = SAFE_CAST([infoDic safeObjectForKey:@"simpleFeed"], KSimpleFeed);
    
    if (self.currentVC != [[KSNavigationManager sharedManager] getTopViewController]) {
        return;
    }
    
    if (iapGift.type != KSIapGiftTypeCommon && iapGift.type != KSIapGiftTypeSound && iapGift.type!= KSIapGiftTypeFlower && iapGift.type!= KSIapGiftTypeVip && iapGift.type!= KSIapGiftTypePackage && iapGift.type != KSIapGiftTypeProps && iapGift.type != KSIapGiftTypePrize && iapGift.type != KSIapGiftTypePicture) {
        //这里加过滤的原因不清楚，最早是star写的，沿用这个。以后新加一种新的礼物类型这里都需要加上
        return;
    }
    NSInteger totalCount = count;
    if (iapGift.price >= 1) {
        totalCount = iapGift.price * count;
    }
    
    [self updateFakeFeedUIWithSimpleFeed:sendGiftSimpleFeed sentGiftCount:totalCount type:iapGift.type isAnonymous:isAnonymous];
}

#pragma mark - 通用送礼
- (void)sendGift:(KSIapGift *)gift
      simpleFeed:(KSimpleFeed *)simpleFeed
    sendComplete:(void(^)(BOOL sendSuccess))sendComplete
     payComplete:(void(^)(BOOL paySuccess))payComplete {
    
    if (!gift || !gift.giftId) {
        KLog(@"礼物为空");
        return;
    }
    
    KSUserInfo *effectedUserInfo = [[KSUserInfo alloc] init];
    KSUserInfo *sponsorUserInfo = [[KSUserInfo alloc] init];
    
    sponsorUserInfo.userId = KSLoginManager.sharedInstance.curUserInfo.userId;
    sponsorUserInfo.nickName = KSLoginManager.sharedInstance.curUserInfo.nickName;
    effectedUserInfo.userId = simpleFeed.simpleUser.userinfo.userId;
    effectedUserInfo.nickName = simpleFeed.simpleUser.userinfo.nickName;
    
    KSRoomGiftInfo *roomInfo = KSRoomGiftInfo.new;
    roomInfo.sponsorUserInfo = sponsorUserInfo;
    roomInfo.effectedUserInfo = effectedUserInfo;
    gift.roomGiftInfo = roomInfo;
    
    KSBaseSendGiftInfo *sendGiftInfo = [[KSBaseSendGiftInfo alloc] initWithGift:gift
                                                                      giftCount:1
                                                                      sendStyle:KSGiftBridgeSendStyleDirectly
                                                                    receiverUid:simpleFeed.simpleUser.userinfo.userId
                                                                       statFrom:KSStatFromTimeLine];
    sendGiftInfo.forbidNotification = YES;
    sendGiftInfo.requestCompletedBlock = ^(id  _Nullable ksObject, NSDictionary * _Nullable customInfo, NSError * _Nullable error) {
        if (!error) {
            !sendComplete ?: sendComplete(YES);
        } else {
            !sendComplete ?: sendComplete(NO);
            if (error.code == kGiftBridgeErrorCode_BALANCE_NOT_ENOUGH) {
                !payComplete ?: payComplete(NO);
            }
            KLog(@"送爆灯礼物失败 error: %@", error.description);
        }
    };
    sendGiftInfo.payFinishBlock = ^(KSPayAlertViewCallbackModel *model) {
        !payComplete ?: payComplete(YES);
    };
    
    sendGiftInfo.roomGiftInfo = gift.roomGiftInfo;
    self.giftBridge.ksimpleFeedDetail = [simpleFeed toGiftSimpleFeed];
    
    [KSGiftBridgeHelper fillSponsorUserInfoForGift:gift];
    
    UIViewController *topVC = [[KSNavigationManager sharedManager] getTopViewController];
    [self.giftBridge sendGiftWithInfo:sendGiftInfo atView:topVC.view];
}

@end
