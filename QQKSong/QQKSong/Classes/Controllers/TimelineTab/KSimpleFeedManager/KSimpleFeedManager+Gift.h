//
//  KSimpleFeedManager+Gift.h
//  QQKSong
//
//  Created by she<PERSON><PERSON><PERSON><PERSON> on 2020/1/4.
//  Copyright © 2020 Tencent. All rights reserved.
//

#import "KSimpleFeedManager.h"
#import "KSSendGiftContainerView.h"
#import "KSendRewardGiftAlertViewController.h"
#import "KSLuckyBoxManager.h"
#import "KSimpleFeed.h"
#import "KSGiftBridgeProtocol.h"
#import "KSAdFreeGiftScene.h"

@class KSimpleFeed;
@class proto_feed_webapp_UGCLightupItem;
@class KSBurstLightupItem;

@interface KSimpleFeedManager (Gift)<KSSendGiftContainerViewDelegate, KSSendGiftContainerViewDataSource, KSendRewardGiftAlertViewControllerDelegate, KSLuckyBoxManagerDelegate,KSGiftBridgeProtocol>

// 展示送礼物浮层
- (void)showGiftContainerViewWidthTimelineSimpleFeed:(KSimpleFeed *)timelineSimpleFeed;

// 7.0 feed快捷送礼
- (void)shortSendGiftDirectelyTimelineSimpleFeed:(KSimpleFeed *)timelineSimpleFeed;

- (void)sendFlowerGiftDirectelyTimelineSimpleFeed:(KSimpleFeed *)timelineSimpleFeed;

// 礼物榜异化点击送礼
- (void)showSendGiftAlertViewWithSimpleFeed:(KSimpleFeed *)timelineSimpleFeed;

//更新礼物榜
- (void)updateGiftRankInfoWhenSuccess:(NSNotification *)notification;

//包月vip消息通知
- (void)vipStatusChanged:(NSNotification*)noti;


#pragma mark cell可见的时候出现送花曝光倒计时
- (void)beginFlowerExposeTimerWhileAppear;

#pragma mark cell可见的时候停止送花曝光倒计时
- (void)stopFlowerExposeTimerWhileDisappear;

#pragma mark 大卡片礼物异化
- (void)getRecBottomSendGiftViewModel:(void(^)(KSimpleFeedGiftGuide *model))completeBlock
                       isGiftBtnStyle:(BOOL)isGiftBtnStyle
                           simpleFeed:(KSimpleFeed *)simpleFeed
                          triggerType:(NSNumber *)triggerType;

- (void)onRecBottomSendGiftBtnClick:(KSimpleFeed *)simpleFeed;

// 大卡片-小额热度卡透出点击直接送出
- (void)directSendRecMinorHeatCard:(KSimpleFeed *)simpleFeed;

// 大卡片小额热度卡透出礼物信息
- (void)getRecBottomMinorHeatCardGiftInfoWithUgcId:(NSString *)ugcId
                                     completeBlock:(void(^)(KSimpleFeedGiftGuide *model))completeBlock;

#pragma mark Feed送礼异化

// Feed送礼异化按钮展示信息
- (void)getFeedGiftInfoWithFeed:(KSimpleFeed *)simpleFeed completionBlk:(void(^)(BOOL success))completionBlk;

// Feed 看广告免费送礼物
- (void)getFeedFreeADGiftInfoWithScene:(KSAdFreeGiftSceneType)type
                         completionBlk:(void(^)(proto_feed_webapp_GetDataAfterExposureRsp *rsp, KSimpleFeedGiftGuide *gift))completionBlk;

// 关注Feed多人送礼
- (void)didTapFollowFeedMultiGiftWithGift:(KSFollowFeedMultiSendGiftInfo *)gift selectedWork:(NSArray<KSFollowFeedMultiSendGiftWorkItem *> *)selectedWork;
- (void)sendFollowFeedMultiGiftWithGift:(KSFollowFeedMultiSendGiftInfo *)gift work:(KSFollowFeedMultiSendGiftWorkItem *)work;
- (void)reportFollowFeedMultiGiftClickWithGift:(KSFollowFeedMultiSendGiftInfo *)gift work:(NSArray<KSFollowFeedMultiSendGiftWorkItem *> *)selectedWork;

/// 爆灯送礼
/// - Parameters:
///   - gift: 礼物
///   - sendComplete: 送礼状态回调
///   - payComplete: 支付状态回调
///   - simpleFeed: feed数据
- (void)sendBurstGift:(KSBurstLightupItem *)gift
           simpleFeed:(KSimpleFeed *)simpleFeed
         sendComplete:(void(^)(BOOL sendSuccess))sendComplete
          payComplete:(void(^)(BOOL paySuccess))payComplete;


/// 给作品送礼
/// - Parameters:
///   - gift: 礼物
///   - sendComplete: 送礼状态回调
///   - payComplete: 支付状态回调
///   - simpleFeed: feed数据
- (void)sendGift:(KSIapGift *)gift
      simpleFeed:(KSimpleFeed *)simpleFeed
    sendComplete:(void(^)(BOOL sendSuccess))sendComplete
     payComplete:(void(^)(BOOL paySuccess))payComplete;

/// 爆灯结束后刷新
/// - Parameter infoDic: 礼物、feed信息
- (void)updateDataAfterBurstWithDic:(NSDictionary *)infoDic;

@end


