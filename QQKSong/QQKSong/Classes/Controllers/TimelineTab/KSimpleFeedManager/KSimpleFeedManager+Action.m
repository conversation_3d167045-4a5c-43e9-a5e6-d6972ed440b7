//
//  KSimpleFeedManager+Action.m
//  QQKSong
//
//  Created by she<PERSON>t<PERSON><PERSON> on 2020/1/8.
//  Copyright © 2020 Tencent. All rights reserved.
//

#import "KSimpleFeedManager+Action.h"
#import "KSAutoTraceReportManager.h"
#import "JceTimeline_enum_advertType.h"
#import "JceTimeline_enum_rec_item_type.h"
#import "KSimpleFeedManager+Advertisement.h"
#import "KSimpleFeedManager+Comment.h"
#import "KSimpleFeedManager+KSong.h"
#import "KSimpleFeedManager+Gift.h"
#import "KSimpleFeedManager+Share.h"
#import "KSimpleFeedManager+Follow.h"
#import "KSLiveShowRoom+Common.h"
#import "KSimpleFeedManager+Play.h"
#import "KSGlobalPlayItem.h"
#import "KSRelationUserInfo.h"
#import "KSInviteToSingManager.h"
#import "KSUgcPlayManager.h"
#import "KSLiveShowConstants.h"
#import "KSKTVRoomManager.h"
#import "KSNavigationManager.h"
#import "KSABTestManager.h"
#import "KSIntooSchemeHelper.h"
#import "KSimpleFeedManager+PayAlbum.h"
#import "JceTimeline_cell_ugc_dianping.h"
#import "KSimpleFeedManager+Data.h"
#import "KSNavReportModel.h"
#import "KSNewSinglesDetailVC+Action.h"
#import "KSimpleFeedManager+Recommand.h"
#import "KSimpleFeedManager+Delete.h"
#import "KSUgcStarChorusManager.h"
#import "KSTraceReprotHelper_V2+Feed.h"
#import "KSTraceReportModel_V2+ProductType.h"
#import "UIAlertView+Block.h"
#import "KSLayoutableTimelineFeedCellV2+TraceReport.h"
#import "KSLayoutableTimelineFeedCellV2+LayoutV3.h"
#import "KSLiveShowMonitorManager.h"
#import "KSimpleFeedManager+Like.h"
#import "KSTeachSingCourseManager.h"
#import "JceKG_RoomH265TransInfo.h"
#import "JceTimeline_cell_hc.h"
#import "JceTimeline_cell_ktv.h"
#import "JceTimeline_cell_ktv_mike.h"
#import "JceTimeline_cell_live_h265.h"
#import "JceTimeline_enum_relayFeedType.h"
#import "JceTimeline_ktv_lable_item.h"
#import "JceTimeline_s_user.h"
#import "KSCommonModel.h"
#import "KSDrawItemAction.h"
#import "KSHcExtraInfo.h"
#import "KSJceModelBridge.h"
#import "KSKTVRoomInfo.h"
#import "KSLiveShowRoom.h"
#import "KSLoginManager.h"
#import "KSMailSessionDetailViewController.h"
#import "KSModelForRecording.h"
#import "KSPathReportManager.h"
#import "KSRecordStrategyReport.h"
#import "KSRecordingData.h"
#import "KSRecordingStrategies.h"
#import "KSTimelineForward.h"
#import "KSTimelineManager.h"
#import "KSTraceReportHelper.h"
#import "KSTraceReportManager+BuyKB.h"
#import "KSUserInfo+Common.h"
#import "KSimpleManagerDelegate.h"
#import "KSong+Common.h"
#import "KSNetStatusManager.h"
#import "KSCellUser.h"
#import "JceTimeline_s_topic.h"
#import "KSTopicSummaryInfo.h"
#import "KSTraceReportModel_V2.h"
#import "KSLayoutableTimelineFeedCellV2.h"
#import "WnsConfigManager+KSBusiness.h"
#import "KSFilmingModel.h"
#import "JceTimeline_Detail_UgcTopic.h"
#import "KSFilmingViewController.h"
#import "proto_feed_webapp_IgnoreItem.h"
#import "KSLayoutableTimelineFeedCellV2+RecFeed.h"
#import "KSRecFeedBottomGuideFrequencyControlMgr.h"
#import "KSimpleFeedManager+Reprot.h"
#import "KSLocationFeedListViewController.h"
#import "KSTmpAudioPlayManger.h"
#import "KSSongExtraInfo.h"
#import "KSAdFreeGiftManager.h"
#import "KSRootTabBarController.h"
#import "KSTimelineRootVC.h"
#import "KSPersonalGiftVC.h"
#import "proto_social_ktv_room_emSocialKtvCreateEntrance.h"
#import "KSUgcStarChorusManager.h"
#import "KSUserInfo+Follow.h"
#import "KSUserVip.h"
#import "KSUserInfo+Auth.h"
#import "KSVipManager+VipIcon.h"
#import "proto_ktv_recfeed_webapp_KtvItem.h"
#import "KSNSURLUtils.h"
#import "JceTimeline_enum_filter_mask.h"
#import "JceTimeline_cell_extrainfo.h"
#import "KSStudySingManager.h"
#import "KSStudySingReport.h"
#import "proto_feed_webapp_cell_tme_town_feed.h"
#import "rcqmkg_SingCardShow.h"
#import "proto_feed_webapp_cell_ktv_sing_card.h"
#import "proto_feed_webapp_cell_accompany_live_entry.h"
#import "proto_feed_webapp_cell_heat_card.h"
#import "proto_feed_webapp_AsyncWorkRecInfo.h"
#import "KSong+Common.h"
#import "KSUGCDetailGiftBridge.h"
#import "KSBaseSendGiftInfo.h"
#import "KSGiftBridgeHelper.h"
#import "JceIapGift_BonusConsumeUgc.h"
#import "KSTraceReportEnumV2.h"
#import "proto_ktv_recfeed_webapp_RecReason.h"
#import "proto_feed_webapp_ktv_game_status.h"
#import "proto_feed_webapp_game_status_comm.h"
#import "proto_feed_webapp_cell_game.h"
#import "proto_feed_webapp_CellBottomBarLabel.h"
#import "KSLiveShowModuleService.h"
#import "JceKG_ScoreInfoItem.h"
#import "proto_ktv_status_UserStatusDetail.h"
#import "KSLiveShowModeTransferContext.h"
#import "KSLiveShowViewController.h"

#import "KSUserAuthBadgeReporter+Common.h"
#import "KSLiveShowModuleService.h"
#import "JceKG_ScoreInfoItem.h"
#import "KSPayManager.h"
#import "KSTraceReportManager+KGCommon.h"
#import "KSVIPRightsFloatingView.h"
#import "KSIapGiftManager.h"
#import "KSLiveSHowEnterManager.h"

#import "JceTimeline_cell_mini_heat_card.h"
#import "JceTimeline_s_user.h"
#import "JceTimeline_cell_userinfo.h"

#define kTimelineTailShowStatusCanHit 1

@implementation KSimpleFeedManager (Action)

- (KSUserInfo*)getUserInfoFromAction:(KSDrawItemAction*)action
{
    KSUserInfo *user = nil;
    KSimpleFeed* feed = nil;
    
    if ([action.busiData isKindOfClass:[KSUserInfo class]])
    {
        user = SAFE_CAST(action.busiData, KSUserInfo);
    }
    else if ([action.busiData isKindOfClass:[KSCellUser class]])
    {
        KSCellUser *cellUser = (KSCellUser*)action.busiData;
        user = [[KSUserInfo alloc] init];
        user.userId = cellUser.userId;
        user.avatarTimestamp = cellUser.timeStamp;
        user.nickName = cellUser.nickName;
    }
    else if ([action.busiData isKindOfClass:[KSimpleFeed class]])
    {
        feed = SAFE_CAST(action.busiData, KSimpleFeed);
        user = feed.simpleUser.userinfo;
        if (feed.feedAd)
        {
            user = [KSUserInfo new];
            user.nickName = feed.feedAd.jce_advertiserInfo.jce_name;
        }
    }
    else if ([action.busiData isKindOfClass:[JceTimeline_s_user class]])
    {
        JceTimeline_s_user *sUser = (JceTimeline_s_user *)action.busiData;
        user = [[KSJceModelBridge sharedInstance] translateFeedUserToKSUser:sUser];
    }
    
    return user;
}

//点击头像
- (void)avtarDidClickWith:(KSDrawItemAction*)action finishBlk:(SingleObjectQueryCompletedBlock)finishBlk
{
    KSUserInfo *user = [self getUserInfoFromAction:action];

    if (user)
    {
        KSimpleFeed* feed = SAFE_CAST(action.busiData, KSimpleFeed);
        if (feed.feedAd)
        {
            NSInteger clickPose = (action.type == DRAWITEM_ACTION_TYPE_AVATAR)?KSFeedAdvertiseClickPose_AdIcon:KSFeedAdvertiseClickPose_AdUserInfo;
            [self didSelectedOnAdvertiseFeed:feed clickPose:clickPose];
        }
        else if (!IS_EMPTY_STR_BM(user.strRoomID) && [feed isRecCardFeed] == NO)
        {
            NSDictionary *infoDic = nil;
            if (!IS_EMPTY_STR_BM(feed.recommendItem.jce_strTraceId)) {
                infoDic = @{@"trace_id":feed.recommendItem.jce_strTraceId,
                            @"item_type":[NSString stringWithFormat:@"%d",feed.recommendItem.jce_uiItemType],
                            @"algorithm_type":[NSString stringWithFormat:@"%d",feed.recommendItem.jce_uiAlgorithmType],
                            @"algoritym_id":feed.recommendItem.jce_strAlgorithmId
                };
            }
            // 跳转直播间,大卡片还是跳个人页
            infoDic = [KSLiveShowModuleService appendFromPageNewKey:kFROM_PAGE_NEW_KEY_FEED_CREATION_LIVE_STATUS sourceDic:infoDic];
            [[KSNavigationManager sharedManager] watchLiveShowWithRoomId:user.strRoomID schemeUrl:nil anchorUserId:user.userId forcePop:YES infoDict:infoDic];
        }
        else
        {
            NSString *fromPage = nil;
            if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(getUserProfileFromPageForReport:)])
            {
                fromPage = [self.feedReportDelegate getUserProfileFromPageForReport:feed];
            }
            
            NSDictionary *extraInfo = nil;
            NSInteger eReportSource = [feed getReportSourceForContribute];
            
            if (eReportSource > 0)
            {
                extraInfo = @{@"source": @(eReportSource)};
            }
            // 跳转个人主页
            [[KSNavigationManager sharedManager] showProfileView:user.userId fromPage:fromPage withRectItme:nil extraInfo:extraInfo];
        }
        
        if (finishBlk)
        {
            finishBlk(user,nil,nil);
        }
    }
}

#pragma mark - KSLayoutableTableCellDelegate
- (void)KSLayoutableTableCell:(KSLayoutableTableCell *)tableCell didClickWithAction:(KSDrawItemAction *)action {

    if (action.busiData && [action.busiData isKindOfClass:[KSimpleFeed class]])
    {
        [KSAutoTraceReportManager traceCommonParamsForFeed:SAFE_CAST(action.busiData, KSimpleFeed)];
    }
    
    NSString *urlStr = nil;
    if (action.type == DRAWITEM_ACTION_TYPE_OPEN_WEB_URL) {
        urlStr = SAFE_CAST(action.busiData, NSString);
        if (urlStr.bm_isNumberString) {
            // 转接回原方法
            action.type = urlStr.integerValue;
            action.busiData = SAFE_CAST(tableCell.busiData, KSimpleFeed);
        }
    }
    
    if (action.type == DRAWITEM_ACTION_TYPE_AVATAR)
    {
        //点了头像
        KSUserInfo *user = [self getUserInfoWithAction:action];
        KSimpleFeed* feed = nil;
    
        if (user)
        {
            feed = SAFE_CAST(tableCell.busiData, KSimpleFeed);
            if (feed.feedAd)
            {
                NSInteger clickPose = KSFeedAdvertiseClickPose_AdIcon;
                [self didSelectedOnAdvertiseFeed:feed clickPose:clickPose];
            }
            else if ([feed isMiniHeatCardFeed]) {
                BOOL isSendNearByMsg = feed.isNearbyFeed;
                
                [self showPersonalVC:feed withUserId:user isSendNearByMsg:isSendNearByMsg];
                
                [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                    reportModel.key = @"feed_creation#xtg_recommend#but#click#0";
                    reportModel.commonInt1 = feed.miniHeatCardItem.jce_lNum;
                    reportModel.commonInt2 = 2;
                    reportModel.ugcid = feed.miniHeatCardItem.jce_strUgcid;
                    reportModel.touid = feed.miniHeatCardItem.jce_userinfo.jce_user.jce_lUid;
                    reportModel.mid = feed.songinfo.songMid;
                }];
            }
            else if (!IS_EMPTY_STR_BM(user.pushStreamLivingUrl))
            {
                NSString *liveUrl = user.pushStreamLivingUrl;
                if (!IS_EMPTY_STR_BM(feed.recommendItem.jce_strTraceId)) {
                    liveUrl = [user.pushStreamLivingUrl stringByAppendingFormat:@"&trace_id=%@&item_type=%u&algorithm_type=%u&algoritym_id=%@",feed.recommendItem.jce_strTraceId,feed.recommendItem.jce_uiItemType,feed.recommendItem.jce_uiAlgorithmType,feed.recommendItem.jce_strAlgorithmId];
                    liveUrl = [liveUrl stringByAddingPercentEncodingForWholeUrl];
                }
                liveUrl = [liveUrl appendUrlParameter:[NSString stringWithFormat:@"%@=%@",PushNotification_liveShowFromPageNew,[@"live#0#feed_following#live_feed#uploader" URLEncodedString]]];
                [[KSNavigationManager sharedManager] watchLiveShowWithRoomId:nil schemeUrl:liveUrl anchorUserId:user.userId forcePop:YES];
            }       
            else if (!IS_EMPTY_STR_BM(user.strRoomID))
            {
                
                NSDictionary *infoDic = nil;
                if (!IS_EMPTY_STR_BM(feed.recommendItem.jce_strTraceId)) {
                    infoDic = @{@"trace_id":feed.recommendItem.jce_strTraceId,
                                @"item_type":[NSString stringWithFormat:@"%d",feed.recommendItem.jce_uiItemType],
                                @"algorithm_type":[NSString stringWithFormat:@"%d",feed.recommendItem.jce_uiAlgorithmType],
                                @"algoritym_id":feed.recommendItem.jce_strAlgorithmId
                    };
                }
                infoDic = [KSLiveShowModuleService appendFromPageNewKey:@"live#0#feed_following#live_feed#uploader" sourceDic:infoDic];
                NSMutableDictionary *mutDict = [NSMutableDictionary dictionaryWithDictionary:infoDic];
                [mutDict safeSetObject:SAFE_STR_BM([feed.liveShow.mapExt safeObjectForKey:PushNotification_strCdnUrl]) forKey:PushNotification_strCdnUrl];
                [mutDict safeSetObject:SAFE_STR_BM([feed.liveShow.mapExt safeObjectForKey:PushNotification_iSupportP2P]) forKey:PushNotification_iSupportP2P];
                infoDic = [NSDictionary dictionaryWithDictionary:mutDict];
                //头像有直播态跳转直播间
                [[KSNavigationManager sharedManager] watchLiveShowWithRoomId:user.strRoomID schemeUrl:nil anchorUserId:user.userId forcePop:YES infoDict:infoDic];
            }
            else if (!IS_EMPTY_STR_BM(user.sAuthLiveUrl))
            {
                NSString *liveUrl = user.sAuthLiveUrl;
                if (!IS_EMPTY_STR_BM(feed.recommendItem.jce_strTraceId)) {
                    liveUrl = [user.sAuthLiveUrl stringByAppendingFormat:@"&trace_id=%@&item_type=%u&algorithm_type=%u&algoritym_id=%@",feed.recommendItem.jce_strTraceId,feed.recommendItem.jce_uiItemType,feed.recommendItem.jce_uiAlgorithmType,feed.recommendItem.jce_strAlgorithmId];
                    liveUrl = [liveUrl stringByAddingPercentEncodingForWholeUrl];
                }
                liveUrl = [liveUrl appendUrlParameter:[NSString stringWithFormat:@"%@=%@",PushNotification_liveShowFromPageNew,[@"live#0#feed_following#live_feed#uploader" URLEncodedString]]];
                liveUrl = [liveUrl appendUrlParameter:[NSString stringWithFormat:@"%@=%@&%@=%@",PushNotification_strCdnUrl, [SAFE_STR_BM([feed.liveShow.mapExt safeObjectForKey:PushNotification_strCdnUrl]) URLEncodedString],
                           PushNotification_iSupportP2P, [SAFE_STR_BM([feed.liveShow.mapExt safeObjectForKey:PushNotification_iSupportP2P]) URLEncodedString]]];
                [[KSNavigationManager sharedManager] watchLiveShowWithRoomId:nil schemeUrl:liveUrl anchorUserId:user.userId forcePop:YES];
            }
            else if([feed isForwardUserInKtvShowRoom]) {// 关注
                NSString *jumpUrl = feed.forwardFeed.feedUserInfo.avatarKtvInfo.jce_strJumpUrl;
                NSString *ktvFromRoute = [self getKTVBreatheAvatarStr15:feed];
                if (!IS_EMPTY_STR_BM(jumpUrl) && !IS_EMPTY_STR_BM(ktvFromRoute)) {
                    jumpUrl = [KSComHelper replaceUrl:jumpUrl queryName:@"ktvFromRoute" withValue:ktvFromRoute];
                }
                [[KSNavigationManager sharedManager] dealWithScheme:jumpUrl];
            }
            else if ([feed isUserInKtvShowRoom]){// 关注
                NSString *jumpUrl = feed.simpleUser.avatarKtvInfo.jce_strJumpUrl;
                NSString *ktvFromRoute = [self getKTVBreatheAvatarStr15:feed];
                NSString *fromUid = @(user.userId).stringValue;
                if (!IS_EMPTY_STR_BM(jumpUrl) && !IS_EMPTY_STR_BM(ktvFromRoute)) {
                    jumpUrl = [KSComHelper replaceUrl:jumpUrl queryName:@"ktvFromRoute" withValue:ktvFromRoute];
                }
                if (!IS_EMPTY_STR_BM(jumpUrl) && !IS_EMPTY_STR_BM(fromUid)) {
                    jumpUrl = [KSComHelper replaceUrl:jumpUrl queryName:PushNotification_KtvFromUid withValue:fromUid];
                }
                [[KSNavigationManager sharedManager] dealWithScheme:jumpUrl];
            }
            else
            {
                //没有直播态跳个人页
                // 来自同城需要在个人主页点击私信时自动发送来自同城消息
                BOOL isSendNearByMsg = feed.isNearbyFeed;
                
                [self showPersonalVC:feed withUserId:user isSendNearByMsg:isSendNearByMsg];
            }
            
            if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(traceReportAvatarClickedWithAction:simpleFeed:cell:)])
            {
                [self.feedReportDelegate traceReportAvatarClickedWithAction:action simpleFeed:feed cell:tableCell];
            }
            if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(onFeedAvatarClickWithAction:simpleFeed:cell:userInfo:)])
            {
                [self.feedReportDelegate onFeedAvatarClickWithAction:action simpleFeed:feed cell:tableCell userInfo:user];
            }
        }
    }
    else if (action.type == DRAWITEM_ACTION_TYPE_NICK_NAME || action.type == DRAWITEM_ACTION_TYPE_FORWARD_NICK_NAME)
    {
        //点击昵称和转发昵称
        KSUserInfo *user = [self getUserInfoWithAction:action];
        KSimpleFeed* feed = nil;
            
        if (user)
        {
            feed = SAFE_CAST(tableCell.busiData, KSimpleFeed);
            if (feed.feedAd)
            {
                NSInteger clickPose = KSFeedAdvertiseClickPose_AdUserInfo;
                [self didSelectedOnAdvertiseFeed:feed clickPose:clickPose];
            }
            else
            {
                // 跳转个人主页
                [self showPersonalVC:feed withUserId:user isSendNearByMsg:NO];
            }

            
            if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(traceReportAvatarClickedWithAction:simpleFeed:cell:)])
            {
                [self.feedReportDelegate traceReportAvatarClickedWithAction:action simpleFeed:feed cell:tableCell];
            }
            if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(onFeedAvatarClickWithAction:simpleFeed:cell:userInfo:)])
            {
                [self.feedReportDelegate onFeedAvatarClickWithAction:action simpleFeed:feed cell:tableCell userInfo:user];
            }
            
        }
    }
    else if (action.type == DRAWITEM_ACTION_TYPE_BUTTON_DELETE_FEED)
    {
        //删除动态feed
        [self showDeleteFeedAlertView:tableCell action:action];
        
        if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(traceReportDeleteFeedClickedWithAction:simpleFeed:cell:)])
        {
            [self.feedReportDelegate traceReportDeleteFeedClickedWithAction:action simpleFeed:action.busiData cell:tableCell];
        }
        
    }
    else if (action.type == DRAWITEM_ACTION_TYPE_ADVERTISE_DESC_CLICK)
    {
        //点击广告文案
        KSimpleFeed * simpleFeed = action.busiData;
        
        [self didSelectedOnAdvertiseFeed:simpleFeed clickPose:KSFeedAdvertiseClickPose_AdDesc];
    }
    else if (action.type == DRAWITEM_ACTION_TYPE_COVER)
    {
        KSimpleFeed * simpleFeed = action.busiData;
        
        // 点击封面
        if (simpleFeed.h5JumpFeed)
        {
            if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(traceReportH5CoverClickedWithSimpleFeed:simpleFeed:indexPath:)])
            {
                [self.feedReportDelegate traceReportH5CoverClickedWithSimpleFeed:KSTimelineRootVCH5CoverClick simpleFeed:simpleFeed indexPath:tableCell.indexPath];
            }
            
            if (simpleFeed.h5JumpFeed.jumpUrl.length > 0)
            {
                [[KSNavigationManager sharedManager] dealWithScheme:simpleFeed.h5JumpFeed.jumpUrl];
            }
        }
        else
        {
            if (simpleFeed.feedAd.jce_advertType == JceTimeline_enum_advertType_AdvertTypeVideo)
            {
                if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(traceReportAdvertiseClickedWithActionByNewEngine:simpleFeed:indexPath:)])
                {
                    [self.feedReportDelegate traceReportAdvertiseClickedWithActionByNewEngine:KSTimelineRootVCAdCoverClick simpleFeed:simpleFeed indexPath:tableCell.indexPath];
                }
                
                [self didSelectedOnAdvertiseFeed:simpleFeed clickPose:KSFeedAdvertiseClickPose_AdImage];
            }
            // 视频类型的广告沿用此上报
            else if (simpleFeed.feedAd.jce_advertType == JceTimeline_enum_advertType_AdvertTypePicture)
            {
                if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(traceReportAdvertiseClickedWithActionByNewEngine:simpleFeed:indexPath:)]) {
                    
                     [self.feedReportDelegate traceReportAdvertiseClickedWithActionByNewEngine:KSTimelineRootVCAdVideoClick simpleFeed:simpleFeed indexPath:tableCell.indexPath];
                }
               
                [self didSelectedOnAdvertiseFeed:simpleFeed clickPose:KSFeedAdvertiseClickPose_AdVideo];
            }
        }
    }
    else if (action.type == DRAWITEM_ACTION_TYPE_VIEW_DETAIL)
    {
        //查看广告详情
        KSimpleFeed * simpleFeed = action.busiData;
        
        if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(traceReportAdvertiseClickedWithActionByNewEngine:simpleFeed:indexPath:)]) {
            [self.feedReportDelegate traceReportAdvertiseClickedWithActionByNewEngine:KSTimelineRootVCAdDetailLinkClick simpleFeed:simpleFeed indexPath:tableCell.indexPath];
        }
        
        [self didSelectedOnAdvertiseFeed:simpleFeed clickPose:KSFeedAdvertiseClickPose_AdViewDetail];
    }
    else if (action.type == DRAWITEM_ACTION_TYPE_JOIN_COMPITITION)
    {
        // 参赛ugc
        KSimpleFeed *simpleFeed = action.busiData;
        [[KSNavigationManager sharedManager] showWebView:simpleFeed.songinfo.strCompetitionUrl];
        
        if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(onUgcFeedClickCompetitionButtonWithAction:simpleFeed:cell:)])
        {
            [self.feedReportDelegate onUgcFeedClickCompetitionButtonWithAction:action simpleFeed:simpleFeed cell:tableCell];
        }
        
    }
    else if(action.type == DRAWITEM_ACTION_TYPE_COMPETITION_NANME_JUMP)
    {
        //点击参赛ugc名字
        KSimpleFeed *simpleFeed = action.busiData;
        [[KSNavigationManager sharedManager] showWebView:simpleFeed.songinfo.strCompetitionUrl];
        
    }
    else if(action.type == DRAWITEM_ACTION_TYPE_DAOJU_COMPETITION_VOTE)
    {
        //道具大赛投票
        KSimpleFeed *simpleFeed = action.busiData;
        [self showTimelineDetailWithFeed:simpleFeed];
        
        if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(onUgcFeedClickDaoJuVoteButtonWithAction:simpleFeed:cell:)])
        {
            [self.feedReportDelegate onUgcFeedClickDaoJuVoteButtonWithAction:action simpleFeed:simpleFeed cell:tableCell];
        }
    }
    else if (action.type == DRAWITEM_ACTION_TYPE_BUTTON_Advertise)
    {
        //广告不感兴趣
        [self didClickNegativeAdEntryWithSimpleFeed:action.busiData indexPath:tableCell.indexPath];
    }
    else if (action.type == DRAWITEM_ACTION_TYPE_BUTTON_REPLY)
    {
        //点了评论/回复，包含关注热门附近tab
        if (![[KSNetStatusManager sharedManager] IsEnableInternet])
        {
            [self tempAlert:@"网络不可用，请检查网络设置"];
            return;
        }
    
        KSimpleFeed *feed = (KSimpleFeed *)action.busiData;
        
        if (feed.isMusicMoodFeed && feed.isRefUGCUnavailable)
        {
            [self tempAlert:@"作品无法查看，暂不支持此操作"];
            return;
        }
        
        self.commentSimpleFeed = feed;
        self.ugcCommentItem = nil;
        self.commentType = KSTimelineReply_UgcComment;
        //对转发feed的评论会同时发给ugc作者和当前的转发人(中间链路上的转发人不会接受评论)，或者只发给ugc作者（类似详情页的回复）
        KSUidType uid = [KSLoginManager sharedInstance].curUserInfo.userId;
        if (feed.forwardFeed && feed.forwardFeed.feedUserInfo.userinfo.userId != uid)
        {
            self.commentType = KSTimelineReply_ForwardComment;
        }
    
        [self showMessageInputView];
        
        if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(traceReportCommentClickedWithAction:simpleFeed:)])
        {
            [self.feedReportDelegate traceReportCommentClickedWithAction:action simpleFeed:feed];
        }
        
        if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(onFeedClickCommentButtonWithAction:simpleFeed:cell:)])
        {
            [self.feedReportDelegate onFeedClickCommentButtonWithAction:action simpleFeed:feed cell:tableCell];
        }

    }
    else if (action.type == DRAWITEM_ACTION_TYPE_BUTTON_SHOW_COMMENTSHOWVC)
    {
        //点了评论/回复，包含关注热门附近tab
        if (![[KSNetStatusManager sharedManager] IsEnableInternet]) {
            [self tempAlert:@"网络不可用，请检查网络设置"];
            return;
        }
        
        KSimpleFeed *feed = (KSimpleFeed *)action.busiData;
        
        if (feed.isMusicMoodFeed && feed.isRefUGCUnavailable)
        {
            [self tempAlert:@"作品无法查看，暂不支持此操作"];
            return;
        }
        
        self.commentSimpleFeed = feed;
        self.ugcCommentItem = nil;
        self.commentType = KSTimelineReply_UgcComment;
        //对转发feed的评论会同时发给ugc作者和当前的转发人(中间链路上的转发人不会接受评论)，或者只发给ugc作者（类似详情页的回复）
        KSUidType uid = [KSLoginManager sharedInstance].curUserInfo.userId;
        if (feed.forwardFeed && feed.forwardFeed.feedUserInfo.userinfo.userId != uid)
        {
            self.commentType = KSTimelineReply_ForwardComment;
        }
        
        NSString *placeHolder = [action.extraInfoDic objectForKey:@"placeHolder"];
        if (placeHolder.length > 0)
        {
            [self showTimelineCommentsView:feed placeHolder:placeHolder];
        }
        else
        {
            [self showTimelineCommentsView:feed];
        }
        
        if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(traceReportCommentClickedWithAction:simpleFeed:)])
        {
            [self.feedReportDelegate traceReportCommentClickedWithAction:action simpleFeed:feed];
        }
        if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(traceReportCommentClickedWithAction2:simpleFeed:)])
        {
            [self.feedReportDelegate traceReportCommentClickedWithAction2:action simpleFeed:feed];
        }
        
        if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(onFeedClickCommentButtonWithAction:simpleFeed:cell:)])
        {
            [self.feedReportDelegate onFeedClickCommentButtonWithAction:action simpleFeed:feed cell:tableCell];
        }
    }
    else if (action.type == DRAWITEM_ACTION_TYPE_BUTTON_UGCListREPLY || action.type == DRAWITEM_ACTION_TYPE_BUTTON_UGCListCOMMENT)
    {
        //好友Tab中评论露出3条评论，点击回复，跟普通回复分开
        if (![[KSNetStatusManager sharedManager] IsEnableInternet]) {
            [self tempAlert:@"网络不可用，请检查网络设置"];
            return;
        }
        
        self.commentSimpleFeed = SAFE_CAST(tableCell.busiData, KSimpleFeed);
       
        self.ugcCommentItem = (KSimpleFeedCommentItem *)action.busiData;
        //可以删除自己的评论，回复别人的评论
        if (self.ugcCommentItem.feedCommentUser.userId == [KSLoginManager sharedInstance].curUserInfo.userId)
        {
            [self showDeleteCommentActionSheet:self.ugcCommentItem];
        }
        else
        {
            if (action.type == DRAWITEM_ACTION_TYPE_BUTTON_UGCListREPLY)
            {
                self.commentType = KSTimelineReply_UgcReplySecond;
            }
            else if (action.type == DRAWITEM_ACTION_TYPE_BUTTON_UGCListCOMMENT)
            {
                self.commentType = KSTimelineReply_UgcReplyFirst;
                if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(onUgcFeedClickReplyCommentWithAction:simpleFeed:cell:)])
                {
                    
                    [self.feedReportDelegate onUgcFeedClickReplyCommentWithAction:action simpleFeed:self.commentSimpleFeed cell:tableCell];
                }
                
            }
            
            [self showMessageInputView];
        }
        //点击上报
        if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(traceReportClickedOnCommentList)])
        {
            [self.feedReportDelegate traceReportClickedOnCommentList];
        }
        
    }
    else if (action.type == DRAWITEM_ACTION_TYPE_BUTTON_SHOW_ALL_COMMENT)
    {
        //好友tab中查看全部评论
        if (![[KSNetStatusManager sharedManager] IsEnableInternet])
        {
            [self tempAlert:@"网络不可用，请检查网络设置"];
            return;
        }
        KSimpleFeed *feed = (KSimpleFeed *)action.busiData;
        NSString *commentId = nil;
        NSInteger count = feed.cellComment.ugcCommentLists.count;
        if (count>0)
        {
            KSimpleFeedCommentItem *item =  SAFE_CAST(feed.cellComment.ugcCommentLists[count-1], KSimpleFeedCommentItem);
            commentId = item.feedCommentId;
        }
        
        KSGlobalPlayItem *playItem = [[KSGlobalPlayItem alloc] initWithFeed:feed];
        playItem.fromPage = [KSLayoutableTimelineFeedCellV2 getFromePageAccordingCurrentVC:feed];
        playItem.playfrom = KSPlayingFromType_Detail;
        playItem.passSeekTime = 0;//全局播放进详情页都是从头播放
        BOOL isShieldFeed = NO;
        if (self.feedManagerDelegate && [self.feedManagerDelegate respondsToSelector:@selector(canShieldUserFeed)])
        {
            //是否屏蔽有个用户的feed
            isShieldFeed = [self.feedManagerDelegate canShieldUserFeed];
        }
        if (isShieldFeed)
        {
            KS_WEAK_SELF(self);
            [playItem setFeedBlockCallback:^(KSUidType blockUid) {
                KS_STRONG_SELF(self);
                
                if (self.feedManagerDelegate && [self.feedManagerDelegate respondsToSelector:@selector(onBlockUid:)])
                {
                    [self.feedManagerDelegate onBlockUid:blockUid];
                }
            }];
        }
        
        playItem.commentId = commentId;
        playItem.disableAutoPlay = YES;
        if (feed.songinfo.coverurls.allValues.count > 0)
        {
            KSImage *ksImage = [feed.songinfo.coverurls.allValues firstObject];
            playItem.coverUrl = ksImage.imageUrl;
        }
        [[KSUgcPlayManager sharedManager] playUgcWithItem:playItem];
        
        //点击上报
        if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(traceReportClickedOnCommentListViewAll)])
        {
            [self.feedReportDelegate traceReportClickedOnCommentListViewAll];
        }
        
    }
    else if (action.type == DRAWITEM_ACTION_TYPE_CHORUS_JOIN)
    {

        KSimpleFeed *simpleFeed = (KSimpleFeed *)action.busiData;
        
        // 是否是独唱作品
       [self markFromUgcIDInKeyPathBeforeReocrd:simpleFeed];
        
        if (simpleFeed.songinfo.ugcMaskExt1 & JceTimeline_Detail_KGE_UGC_MASK_EXT1_AI_GENERATED)
        {
            [KSToast showToast:@"AI合唱暂不支持加入，敬请期待"];
            return;
        }
        
        if (UGC_TYPE(simpleFeed.songinfo.ugcMask) == VIDEO_UGC)
        {
            //视频只有半成品可以加入合唱，成品是不可以加入合唱
            UIViewController *con = self.currentVC;
            if (UGC_HALF_CHORUS(simpleFeed.songinfo.ugcMask))
            {
                // 视频合唱半成品 加入合唱
                if (SAFE_CAST(con, KSNewSinglesDetailVC))
                {
                    //detailmodel用这个
                    [(KSNewSinglesDetailVC *)con didClickJoinHalfUgcChorusBtnWithAction:action simpleFeed:simpleFeed];
                }
                else
                {
                    //只要有feedmodel 用下面这个
                    [self joinChorusButtonClickWithAction:action simpleFeed:simpleFeed cell:tableCell];
                }

            }
        }
        else
        {
            //音频半成品+部分独唱成品支持合唱
            BOOL isSoloSong = [KSUgcStarChorusManager canUGCChorusWithUgcMask:simpleFeed.songinfo.ugcMask extMask:simpleFeed.songinfo.ugcMaskExt songMid:simpleFeed.songinfo.songMid];
            if (isSoloSong)
            {
                // 独唱作品
                [self joinSoloSongChorusButtonClickWithAction:action simpleFeed:simpleFeed cell:tableCell];
            }
            else
            {
                // 普通合唱半成品
                [self joinChorusButtonClickWithAction:action simpleFeed:simpleFeed cell:tableCell];
            }

        }
        
        if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(onUgcFeedClickChorusButtonWithAction:simpleFeed:cell:)])
        {
            [self.feedReportDelegate onUgcFeedClickChorusButtonWithAction:action simpleFeed:simpleFeed cell:tableCell];
        }
        
        if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(traceReportJoinChorusClicked:)])
        {
            [self.feedReportDelegate traceReportJoinChorusClicked:simpleFeed];
        }
        
        if ([KSUgcStarChorusManager isUgcStarChorusHalfContentWithUgcMask:simpleFeed.songinfo.ugcMask
                                                       ugcMaskExt:simpleFeed.songinfo.ugcMaskExt])
        {
            if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(reportStarChorusHalfContentChorusButtonWithSimpleFeed:isExpsoure:)])
            {
                [self.feedReportDelegate reportStarChorusHalfContentChorusButtonWithSimpleFeed:simpleFeed isExpsoure:NO];
            }
            
        }
 
    }
    else if (action.type == DRAWITEM_ACTION_TYPE_KSONG)
    {
        //K歌
        KSimpleFeed *feed = (KSimpleFeed *)action.busiData;
        KSRecordingNormalStrategy *strategy = [[KSRecordingNormalStrategy alloc] init];
        strategy.recordingData.song = feed.songinfo;
        
        // 话题信息
        JceTimeline_s_topic *feedTopic = feed.topicsArray.firstObject;
        if (feedTopic)
        {
            KSTopicSummaryInfo *topicInfo = [KSTopicSummaryInfo topicSummaryInfoWithTimelineFeed:feedTopic];
            strategy.topicInfo = topicInfo;
        }
        
        KSLayoutableTimelineFeedCellV2* currentCell = SAFE_CAST(tableCell, KSLayoutableTimelineFeedCellV2);
        KSNavReportModel* reportModel = [currentCell getRepordReportModelAccordingCurrentVC];
        strategy.recordStrategyReport.from_page = reportModel.fromPage;
        
        if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(traceReportKSongClickedWithSimpleFeed:)])
        {
            [self.feedReportDelegate traceReportKSongClickedWithSimpleFeed:feed];
        }
        [self markFromUgcIDInKeyPathBeforeReocrd:feed];
        [[KSNavigationManager sharedManager] singSongWithStrategy:strategy];
    }
    else if (action.type == DRAWITEM_ACTION_TYPE_KSONG2)
    {
        KSimpleFeed* feed = SAFE_CAST(action.busiData, KSimpleFeed);
        [self goToSing:feed];
        
        if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(traceReportKSongClickedWithSimpleFeed:)])
        {
            [self.feedReportDelegate traceReportKSongClickedWithSimpleFeed:feed];
        }
    }
    else if (action.type == DRAWITEM_ACTION_TYPE_CAPPELA)
    {
        //清唱
        NSString *fromPage = nil;
        
        KSLayoutableTimelineFeedCellV2* currentCell = SAFE_CAST(tableCell, KSLayoutableTimelineFeedCellV2);
        KSNavReportModel* reportModel = [currentCell getRepordReportModelAccordingCurrentVC];
        fromPage = reportModel.fromPage;
        
        KSimpleFeed *feed = (KSimpleFeed *)action.busiData;
        
        if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(traceReportKSongClickedWithSimpleFeed:)])
        {
            [self.feedReportDelegate traceReportKSongClickedWithSimpleFeed:feed];
        }
        
        [self markFromUgcIDInKeyPathBeforeReocrd:feed];
        [[KSNavigationManager sharedManager] showCappllaVCWithToRecordFromPage:0 contestId:0 writeRecordFromPage:fromPage strategyBlock:nil];
    }
    else if (action.type == DRAWITEM_ACTION_TYPE_POETRY)
    {
        //朗诵
        KSimpleFeed *feed = (KSimpleFeed *)action.busiData;
        KSPoetryRecordingStrategy *strategy = [[KSPoetryRecordingStrategy alloc] init];
        strategy.recordingData.song = feed.songinfo;
        strategy.ugcId = feed.simpleFeedCommon.strFeedId;
        
        KSLayoutableTimelineFeedCellV2* currentCell = SAFE_CAST(tableCell, KSLayoutableTimelineFeedCellV2);
        KSNavReportModel* reportModel = [currentCell getRepordReportModelAccordingCurrentVC];
        strategy.recordStrategyReport.from_page = reportModel.fromPage;
                
        if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(traceReportKSongClickedWithSimpleFeed:)])
        {
            [self.feedReportDelegate traceReportKSongClickedWithSimpleFeed:feed];
        }
        [self markFromUgcIDInKeyPathBeforeReocrd:feed];
        if (UGC_IS_QRC_POETRY(feed.songinfo.ugcMaskExt))
        {
            [[KSNavigationManager sharedManager] singSongWithStrategy:strategy];
        }
        else
        {
            [[KSNavigationManager sharedManager] recitePoetryWithStrategy:strategy];
        }
    }
    else if (action.type == DRAWITEM_ACTION_TYPE_SHORT_AUDIO)
    {
        // 跳转短音频
        KSimpleFeed *feed = (KSimpleFeed *)action.busiData;

        if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(traceReportKSongClickedWithSimpleFeed:)])
        {
            [self.feedReportDelegate traceReportKSongClickedWithSimpleFeed:feed];
        }
        
        NSString *fromPage = nil;
        
        KSLayoutableTimelineFeedCellV2* currentCell = SAFE_CAST(tableCell, KSLayoutableTimelineFeedCellV2);
        KSNavReportModel* reportModel = [currentCell getRepordReportModelAccordingCurrentVC];
        fromPage = reportModel.fromPage;
                
        [self markFromUgcIDInKeyPathBeforeReocrd:feed];
        KSRecordingShortAudioStrategy *strategy = [[KSRecordingShortAudioStrategy alloc] init];
        strategy.segmentId = feed.songinfo.strSegmentId;
        strategy.recordStrategyReport.from_page = fromPage;
        [[KSNavigationManager sharedManager] shortAudioWithStrategy:strategy];
    }
    else if (action.type == DRAWITEM_ACTION_TYPE_SEND_PRESENT || action.type == DRAWITEM_ACTION_TYPE_SEND_PRESENT_Direct || action.type == DRAWITEM_ACTION_TYPE_HEAT_CARD_DIFF)
    {
        [self didTapSendGiftWithCell:tableCell didClickWithAction:action];
    }
    else if (action.type == DRAWITEM_ACTION_TYPE_SEND_FLOWER || action.type == DRAWITEM_ACTION_TYPE_SEND_FLOWER2)
    {
        // 6.4 直接送花
        if (![[KSNetStatusManager sharedManager] IsEnableInternet]) {
            [self tempAlert:@"网络不可用，请检查网络设置"];
            return;
        }
        
        KSimpleFeed *feed = (KSimpleFeed *)action.busiData;
        if (feed.isMusicMoodFeed && feed.isRefUGCUnavailable)
        {
            [self tempAlert:@"作品无法查看，暂不支持此操作"];
            return;
        }
        self.sendGiftSimpleFeed = feed;
        [self sendFlowerGiftDirectelyTimelineSimpleFeed:feed];
        feed.sentFlowerBtn = action.actionView;
        
        if (action.type == DRAWITEM_ACTION_TYPE_SEND_FLOWER2)
        {
            if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(traceReportSendFlowerGuideClickedWithAction:simpleFeed:)])
            {
                [self.feedReportDelegate traceReportSendFlowerGuideClickedWithAction:action simpleFeed:feed];
            }
            
        }
        else
        {
            if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(traceReportSendGiftClickedWithAction:simpleFeed:)])
            {
                [self.feedReportDelegate traceReportSendGiftClickedWithAction:action simpleFeed:feed];
            }
            
            if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(onFeedClickGiftButtonWithAction:simpleFeed:cell:)])
            {
                [self.feedReportDelegate onFeedClickGiftButtonWithAction:action simpleFeed:feed cell:tableCell];
            }
            
            
        }
    }
    else if (action.type == DRAWITEM_ACTION_TYPE_SHARE_FEED)
    {
        //转发feed
        if (![[KSNetStatusManager sharedManager] IsEnableInternet]) {
            [self tempAlert:@"网络不可用，请检查网络设置"];
            return;
        }
        
        KSimpleFeed *feed = (KSimpleFeed *)action.busiData;
        
        if (feed.isMusicMoodFeed && feed.isRefUGCUnavailable)
        {
            [self tempAlert:@"作品无法查看，暂不支持此操作"];
            return;
        }        
        [self showShareVCViewFeed:feed fakeFeed:nil];
        
        if (self.feedManagerDelegate && [self.feedManagerDelegate respondsToSelector:@selector(didTapShareButton:)]) {
            [self.feedManagerDelegate didTapShareButton:feed];
        }
        if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(onFeedClickShareButtonWithAction:simpleFeed:cell:)])
        {
            [self.feedReportDelegate onFeedClickShareButtonWithAction:action simpleFeed:feed cell:tableCell];
        }
    }
    else if (action.type == DRAWITEM_ACTION_TYPE_FEED_RELAY_GAME)
    {
        //抢麦
        if (![[KSNetStatusManager sharedManager] IsEnableInternet]) {
            [self tempAlert:@"网络不可用，请检查网络设置"];
            return;
        }
        KSimpleFeed *feed = (KSimpleFeed *)action.busiData;
        
        // 设定来源 sheldonttodo
        NSString *fromPage = [NSString string];
        
        if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(getCurrentSelectVCReportString)])
        {
           fromPage = [self.feedReportDelegate getCurrentSelectVCReportString];
        }
        
        if (!IS_EMPTY_STR_BM(feed.relayGameFeed.strJumpUrl))
        {
            [[KSNavigationManager sharedManager] dealWithScheme:feed.relayGameFeed.strJumpUrl fromPage:fromPage];
        }
        else if (feed.relayGameFeed.iFeedType == JceTimeline_enum_relayFeedType_RelayFeedTypeInvite ||
                 feed.relayGameFeed.iFeedType == JceTimeline_enum_relayFeedType_RelayFeedTypeWatch)
        {
            NSInteger fromPageId =0;
            if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(getRelayGameFromPageForReport)])
            {
                fromPageId = [self.feedReportDelegate getRelayGameFromPageForReport];
            }
            [[KSNavigationManager sharedManager] enterRelayGameInviteVCWithThemeId:0 roomId:feed.relayGameFeed.strRoomId fromPageId:fromPageId];
        }
        else if(feed.relayGameFeed.iFeedType == JceTimeline_enum_relayFeedType_RelayFeedTypeNormal)
        {
            [[KSNavigationManager sharedManager] showRelayGameSquareVCFrom:fromPage];
        }
        else
        {
            [self tempAlert:@"请稍后再试"];
            return;
        }
        
        if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(reportRelayGameFeedClickWithsimpleFeed:)])
        {
            [self.feedReportDelegate reportRelayGameFeedClickWithsimpleFeed:feed];
        }
    }
    else if (action.type == DRAWITEM_ACTION_TYPE_ENTER_GIFT_RANK_VC)
    {
        //进入礼物榜
        KSimpleFeed *feed = (KSimpleFeed *)action.busiData;
        KSStatFrom statFrom = JceIapGift_CONSUME_LOCATION_E_UGC_GIFT_RANK_CONSUME;
        if (feed.soloAlbumInfo)
        {
            statFrom = JceIapGift_CONSUME_LOCATION_E_UGC_ALBUM_GIFT_RANK_CONSUME;
        }
        else
        {
            statFrom = JceIapGift_CONSUME_LOCATION_E_UGC_GIFT_RANK_CONSUME;
        }
        
        if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(onFeedClickGiftRankWithAction:simpleFeed:cell:)])
        {
            [self.feedReportDelegate onFeedClickGiftRankWithAction:action simpleFeed:feed cell:tableCell];
        }
        if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(traceReportEnterGiftListClickedWithAction:simpleFeed:)])
        {
            [self.feedReportDelegate traceReportEnterGiftListClickedWithAction:action simpleFeed:feed];
        }
        
        if (feed.liveShow)
        {
            //直播礼物榜
            KSLiveShowRoom * roomInfo= [[KSLiveShowRoom alloc] init];
            roomInfo.strRoomId = feed.liveShow.strLiveRoomId;
            roomInfo.strShowId = feed.liveShow.strShowId;
            roomInfo.liveshowRoomType = feed.liveShow.roomType;
            NSInteger shouldUseAcrossRank = [SAFE_STR_BM(feed.liveShow.mapExt[@"iUseNewRank"]) integerValue];
            if (shouldUseAcrossRank) {
                // 使用互通榜单
                [[KSNavigationManager sharedManager] openAcrossWealthRankWithOpenFrom:2
                                                                             roomType:[roomInfo getAcrossRankRoomType]
                                                                               roomId:feed.liveShow.strLiveRoomId
                                                                               showId:feed.liveShow.strShowId
                                                                             anchorId:feed.liveShow.anchorUid];
            }
            else {
                [[KSNavigationManager sharedManager] showFeedLiveShowWealthRankVC:roomInfo canSendGift:NO canJumpLiveShow:YES];
            }
        }
        else if(feed.ktvRoomShow)
        {
            {
                //单麦歌房礼物榜
                KSKTVRoomManager *ktvInfo = [[KSKTVRoomManager alloc] init];
                ktvInfo.ktvRoomInfo.roomID = feed.ktvRoomShow.jce_strRoomId;
                ktvInfo.ktvRoomInfo.showID = feed.ktvRoomShow.jce_strShowId;
                ktvInfo.ktvRoomInfo.ktvRoomType = feed.ktvRoomShow.jce_iRoomType;
                KSUserInfo *anchorUserInfo = [[KSUserInfo alloc] init];
                anchorUserInfo.userId = feed.ktvRoomShow.jce_lAnchorUid;
                ktvInfo.ktvRoomInfo.anchorInfo = anchorUserInfo;
                KSNavReportModel* reportModel = nil;
                if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(getNavigationReportModelForKTVShow)])
                {
                    reportModel = [self.feedReportDelegate getNavigationReportModelForKTVShow];
                }
                [[KSNavigationManager sharedManager] showTimelineKTVWealthGiftRank:ktvInfo canShowPersenolAdmin:NO rankTotalKBNum:feed.giftRankInfo.giftWealth fromModel:reportModel];
            }
        }
        else if (feed.ktvRoomMike)
        {
            //排麦礼物榜
            KSKTVRoomManager *ktvInfo = [[KSKTVRoomManager alloc] init];
            ktvInfo.ktvRoomInfo.roomID = feed.ktvRoomMike.jce_strRoomId;
            ktvInfo.ktvRoomInfo.showID = feed.ktvRoomMike.jce_strShowId;
            ktvInfo.ktvRoomInfo.ktvRoomType = feed.ktvRoomMike.jce_iRoomType;
            KSNavReportModel* reportModel = nil;
            if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(getNavigationReportModelForKTVShow)])
            {
                reportModel = [self.feedReportDelegate getNavigationReportModelForKTVShow];
            }
            [[KSNavigationManager sharedManager] showTimelineKTVWealthGiftRank:ktvInfo canShowPersenolAdmin:NO rankTotalKBNum:feed.giftRankInfo.giftWealth fromModel:reportModel];
        }
        else if ([feed isShowDissimilateGiftRankFeed])
        {
            // 礼物榜 5s 异化展示点击
            [self showSendGiftAlertViewWithSimpleFeed:feed];
        }
        else
        {
            //单曲礼物榜
            
            [[KSNavigationManager sharedManager] showNewTimelineGiftRankVC:feed.simpleFeedCommon.strFeedId songName:feed.songinfo.name toUser:feed.simpleUser.userinfo statFrom:statFrom withDelegate:nil extraInfo:@{@"activityId": @(feed.songinfo.activityId), @"coverImageUrl": SAFE_STR_BM(feed.songinfo.albumPicUrl)}];
        }
    }
    else if (action.type == DRAWITEM_ACTION_TYPE_ENTER_V_AUTH_INFO)
    {
        //认证Icon点击跳转
        KSimpleFeed *feed = (KSimpleFeed *)action.busiData;
        NSString * jumpUrl = [[WnsConfigManager sharedInstance].appConfig.urlConfig getBigAuthJumpUrl:feed.simpleUser.userinfo.sAuthName userId:feed.simpleUser.userinfo.userId];
        if (!jumpUrl|| [jumpUrl isEmpty])
        {
            return;
        }
        [[KSNavigationManager sharedManager] showWebView:jumpUrl];
    }
    else if (action.type == DRAWITEM_ACTION_TYPE_ENTER_V_AUTH_VIP)
    {
        // VIP Icon 点击
        KSUserInfo *userInfo = SAFE_CAST(action.busiData, KSUserInfo);
        if ([self.feedReportDelegate respondsToSelector:@selector(getVipPosid)]) {
            VipPid posid = (VipPid)[self.feedReportDelegate getVipPosid];
            NSString *fromPage = @"";
            if ([self.feedReportDelegate respondsToSelector:@selector(getVipCenterFromPageForReport)]) {
                fromPage = [self.feedReportDelegate getVipCenterFromPageForReport];
            }
            BOOL shouldJumpTpVipCenter = KSABTestManager.sharedManager.shouldVIPIconClickJumpToVIPCenter;
            NSString *url = @"";
            if (shouldJumpTpVipCenter) {
                NSDictionary *customParams = (userInfo.userVipInfo.superVipStatus == 1)? @{ @"$vip": @"svip" } : nil;
                url = [KSVipManager getVIPCenterUrlWithPosID:posid customParams:customParams];
            }
            [KSVipManager vipIconComponentClickToUser:userInfo handlePresentVip:NO posid:posid vipUrl:url];
            [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                reportModel.key = @"vip_icon#icon#null#click#0";
                reportModel.from_page = fromPage;
            }];
           
        } 
    }
    else if (action.type == DRAWITEM_ACTION_TYPE_ENTER_V_AUTH_AnchorLevel)
    {
        //等级
        KSUserInfo *userInfo = SAFE_CAST(action.busiData, KSUserInfo);
        if (userInfo)
        {
            [[KSNavigationManager sharedManager] showAnchorLevel:userInfo.userId extraFrom:@"feed" reportClickFrom:9];
        }
    }
    else if (action.type == DRAWITEM_ACTION_TYPE_ENTER_GIFT_WEALTH_INFO)
    {
//        KSUserInfo *userInfo = SAFE_CAST(action.busiData, KSUserInfo);
//        if (userInfo) {
//            NSInteger wealthLevel =[[userInfo.sMapAuth objectForKey:[NSNumber numberWithInt:JceProfile_AuthKey_AUTH_WEALTH_LEVEL]] integerValue];
//            NSInteger wealthValue =[[userInfo.sMapAuth objectForKey:[NSNumber numberWithInt:JceProfile_AuthKey_AUTH_WEALTH_VALUE]] integerValue];
//            
//            [[KSNavigationManager sharedManager] jumpToWealthGlobalRank:userInfo.userId timestamp:userInfo.avatarTimestamp wealthLevel:wealthLevel wealthCount:wealthValue];
//        }
    }
    else if (action.type == DRAWITEM_ACTION_TYPE_FESTIVAL_LEVEL_ICON)
    {
        /// 春节集成活动Icon
        KSUserInfo *userInfo = SAFE_CAST(action.busiData, KSUserInfo);
        [[KSNavigationManager sharedManager] dealWithScheme:userInfo.giftActivityInfo.jumpUrl];
        
        KSLayoutableTimelineFeedCellV2 *currentCell = SAFE_CAST(tableCell, KSLayoutableTimelineFeedCellV2);
        [KSUserAuthGiftActivityReporter reportAuthGiftActivityClickWithInfo:userInfo.giftActivityInfo
                                                                       from:[currentCell.simpleFeed isRecCardFeed] ? KSUserAuthGiftAvtivityFromBigCard : KSUserAuthGiftAvtivityFromFeed];
    }
    else if (action.type == DRAWITEM_ACTION_TYPE_BADGE_ICON)
    {
        /// 勋章icon
        KSUserInfo *userInfo = SAFE_CAST(action.busiData, KSUserInfo);
        [[KSNavigationManager sharedManager] dealWithScheme:userInfo.badgeInfo.jumpUrl];
        [KSUserAuthBadgeReporter reportAuthBadgeClickWithfrom:KSUserAuthBadgeFrom_Feed
                                                         info:userInfo.badgeInfo];
    }
    else if (action.type == DRAWITEM_ACTION_TYPE_INTIMATEFRIEND) {
        // 点击密友
        if ([action.busiData isKindOfClass:[KSTimelineDetailComment class]]) {
            KSTimelineDetailComment *model = SAFE_CAST(action.busiData, KSTimelineDetailComment);
            NSString *url = [NSString stringWithFormat:@"%@&guide=1&source=7", model.scoreInfoItem.jce_sURL];
            NSString *schemeUrl = [NSString stringWithFormat:@"qmkege://kege.com?action=hippyview&url=%@", [url URLEncodedString]];
            [[KSNavigationManager sharedManager] dealWithScheme:schemeUrl];
            
            // 埋点
            [KSUserAuthBadgeReporter reportIntimateRelationShipIconClick:model];
        } else if (action.extraInfoDic[@"jumpUrl"]) {// feed.simpleUser.userinfo.strFriendlinessJumpUrl
            NSString *jumpUrl = action.extraInfoDic[@"jumpUrl"];
            if ([jumpUrl isKindOfClass:[NSString class]] && jumpUrl.length > 0) {
                [[KSNavigationManager sharedManager] dealWithScheme:jumpUrl];
                
                KSUserInfo *userInfo = SAFE_CAST(action.busiData, KSUserInfo);
                if (userInfo) {
                    [self reportIntimateFriendIconClickWithUserInfo:userInfo];
                }
            }
        }
    }
    else if (action.type == DRAWITEM_ACTION_TYPE_BUTTON_UNKNOW_FEED)
    {
        //未知类型cell，跳appstore
        [[UIApplication sharedApplication] openURL:[NSURL URLWithString:kKSongAppstoreURL] options:@{} completionHandler:^(BOOL success) {
            
        }];
    }
    else if (action.type == DRAWITEM_ACTION_TYPE_LIVESHOWFEED)
    {
        //直播
        KSimpleFeed *feed = (KSimpleFeed*) action.busiData;
      
        if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(timelineClickedTraceReport:extraInfo:)])
        {
              [self.feedReportDelegate timelineClickedTraceReport:feed extraInfo:@{@"indexRowKey":tableCell.indexPath }];
        }
        
        if (feed.liveShow && feed.liveShow.strLiveRoomId && feed.liveShow.strLiveRoomId.length > 0)
        {
            if (feed.simpleUser.userinfo.userId == [KSLoginManager sharedInstance].curUserInfo.userId) {
                // 点击的是自己的直播间（不能以观众身份进入自己的直播间），则进入开播页
                [[KSNavigationManager sharedManager] showLiveShowEnterVC];
            } else {
                JceKG_RoomH265TransInfo* roomH265TransInfo = nil;
                if (feed.liveShow.h265TransInfo) {
                    roomH265TransInfo = [JceKG_RoomH265TransInfo new];
                    roomH265TransInfo.jce_iEnableTransform = feed.liveShow.h265TransInfo.jce_iEnableTransform;
                    roomH265TransInfo.jce_iTransformType = feed.liveShow.h265TransInfo.jce_iTransformType;
                }
                KSLiveShowRoom *room = [KSNavigationManager createLiveShowRoomInfo:feed.liveShow.strLiveRoomId
                                                                        relationID:(int)feed.liveShow.iRelationId
                                                                            status:[KSTimelineManager sharedManager].iLiveStatus
                                                                 strAVAudienceRole:feed.liveShow.strAVAudienceRole
                                                                         anchorUid:feed.liveShow.anchorUid
                                                                        anchorMuid:feed.liveShow.anchorMuid
                                                                     h265TransInfo:roomH265TransInfo];
                room.iRoomType = feed.liveShow.roomType;
                
                BOOL isFromContribution = NO;
                if (feed.recommendItem && feed.recommendItem.jce_uiItemType == JceTimeline_enum_rec_item_type_enum_rec_live_contribution)
                {
                    isFromContribution = YES;
                    
                    // 直播投稿上报
                    [KSTraceReportHelper viewActionWithBlock:^(TraceReportView *info) {
                        info.reserves = ReportL3LiveContributionFeedNearClick;
                        
                        info.touid = feed.simpleUser.userinfo.userId;
                        
                        info.commstr3 = feed.liveShow.strShowId;
                        
                        [KSTraceReportManager updateTraceViewInfo:info withFeed:feed];
                    }];
                }
                
                [KSLiveShowMonitorManager sharedManager].liveSource = KSLiveShowMonitorLiveSourceFeed;
                NSString *pageFrom = @"";
                switch (feed.feedToLiveType) {
                    case KSFeedToLiveTypeBreath:
                        pageFrom = kFROM_PAGE_NEW_KEY_FEED_CREATION_LIVE_STATUS;
                        break;
                    case KSFeedToLiveTypeBottomBtn:
                        pageFrom = kFROM_PAGE_NEW_KEY_FEED_CREATION_INDUCE_BLOCK;
                        break;
                    case KSFeedToLiveTypeFocusCover:
                        pageFrom = kFROM_PAGE_NEW_KEY_FEED_FOLLOWING_LIVE_FEED_COVER;
                        break;
                    case KSFeedToLiveTypeFocusAvatar:
                        pageFrom = @"live#0#feed_following#live_feed#uploader";
                        break;
                    default:
                        break;
                }
                if([self.feedReportDelegate isKindOfClass:NSClassFromString(@"KSUserProfileVC_V2")]){
                    pageFrom = @"live#0#homepage_guest#live_feed#null";
                }
                NSDictionary *infoDict  = @{@"trace_id":SAFE_STR_BM(feed.recommendItem.jce_strTraceId),
                                            @"item_type":[NSString stringWithFormat:@"%d",feed.recommendItem.jce_uiItemType],
                                       @"algorithm_type":[NSString stringWithFormat:@"%d",feed.recommendItem.jce_uiAlgorithmType],
                                         @"algoritym_id":SAFE_STR_BM(feed.recommendItem.jce_strAlgorithmId),
                                             PushNotification_liveShowFromPageNew:pageFrom,
                                             PushNotification_strCdnUrl:SAFE_STR_BM([feed.liveShow.mapExt safeObjectForKey:PushNotification_strCdnUrl]),
                                             PushNotification_iSupportP2P:SAFE_STR_BM([feed.liveShow.mapExt safeObjectForKey:PushNotification_iSupportP2P]),
                                            PushNotification_IsOfficialLive:SAFE_STR_BM(([feed.liveShow.mapExt safeObjectForKey:PushNotification_IsOfficialLive])),@"report_ugcId":SAFE_STR_BM(feed.simpleFeedCommon.strFeedId),@"report_ugcMask_1":[NSString stringWithFormat:@"%lu",(unsigned long)feed.songinfo.ugcMask],
                                            @"report_ugcMask_2":[NSString stringWithFormat:@"%lu",(unsigned long)feed.songinfo.ugcMaskExt]
                                            };
                [[KSNavigationManager sharedManager] watchLiveShow:feed.liveShow.strLiveRoomId roomInfo:room forcePop:YES isFromContribution:isFromContribution infoDict:infoDict];
            }
            
            if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(traceReportLiveShowClickedWithAction:simpleFeed:)])
            {
                [self.feedReportDelegate traceReportLiveShowClickedWithAction:action simpleFeed:feed];
            }
            
            if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(onLiveFeedClickCoverWithAction:simpleFeed:cell:)])
            {
                
                [self.feedReportDelegate onLiveFeedClickCoverWithAction:action simpleFeed:feed cell:tableCell];
            }            
        } else if (feed.liveShow && !IS_EMPTY_STR_BM(feed.simpleUser.userinfo.pushStreamLivingUrl)) {
            // 大卡片直播态，点击右上角直播态进直播间跳转
            
            // 大卡片直播态点击上报
            if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(onUgcFeedLivingTagViewClickWithAction:simpleFeed:cell:)])
            {
                [self.feedReportDelegate onUgcFeedLivingTagViewClickWithAction:action simpleFeed:feed cell:tableCell];
            }
            if (feed.simpleUser.userinfo.userId == [KSLoginManager sharedInstance].curUserInfo.userId) {
                [[KSNavigationManager sharedManager] showLiveShowEnterVC];
            } else {
                [[KSNavigationManager sharedManager] dealWithScheme:feed.simpleUser.userinfo.pushStreamLivingUrl];
            }
        }
        else if (!IS_EMPTY_STR_BM(feed.simpleUser.userinfo.strRoomID))
        {
            // 大卡片直播态，点击右上角直播态进直播间跳转
            
            // 大卡片直播态点击上报
            if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(onUgcFeedLivingTagViewClickWithAction:simpleFeed:cell:)])
            {
                [self.feedReportDelegate onUgcFeedLivingTagViewClickWithAction:action simpleFeed:feed cell:tableCell];
            }
            
            if (feed.simpleUser.userinfo.userId == [KSLoginManager sharedInstance].curUserInfo.userId) {
                [[KSNavigationManager sharedManager] showLiveShowEnterVC];
            } else {
                // 跳转直播间, 增加进房来源的作品名称参数
                NSString *pageFrom = @"";
                switch (feed.feedToLiveType) {
                    case KSFeedToLiveTypeBreath:
                        pageFrom = kFROM_PAGE_NEW_KEY_FEED_CREATION_LIVE_STATUS;
                        break;
                    case KSFeedToLiveTypeBottomBtn:
                        pageFrom = kFROM_PAGE_NEW_KEY_FEED_CREATION_INDUCE_BLOCK;
                        break;
                    case KSFeedToLiveTypeFocusCover:
                        pageFrom = kFROM_PAGE_NEW_KEY_FEED_FOLLOWING_LIVE_FEED_COVER;
                        break;
                    case KSFeedToLiveTypeFocusAvatar:
                        pageFrom = @"live#0#feed_following#live_feed#uploader";
                        break;
                    default:
                        break;
                }
                [[KSNavigationManager sharedManager] watchLiveShow:feed.simpleUser.userinfo.strRoomID roomInfo:nil forcePop:YES isFromContribution:NO infoDict:@{@"ugcName":SAFE_STR_BM([feed getSongName]),
                                                                                                                                                                 @"trace_id":SAFE_STR_BM(feed.recommendItem.jce_strTraceId),
                                                                                                                                                                 @"item_type":[NSString stringWithFormat:@"%d",feed.recommendItem.jce_uiItemType],
                                                                                                                                                                 @"algorithm_type":[NSString stringWithFormat:@"%d",feed.recommendItem.jce_uiAlgorithmType],
                                                                                                                                                                 @"algoritym_id":feed.recommendItem.jce_strAlgorithmId,
                                                                                                                                                                 PushNotification_liveShowFromPageNew:pageFrom,
                                                                                                                                                                 @"report_ugcId":SAFE_STR_BM(feed.simpleFeedCommon.strFeedId),@"report_ugcMask_1":[NSString stringWithFormat:@"%lu",(unsigned long)feed.songinfo.ugcMask],
                                                                                                                                                                 @"report_ugcMask_2":[NSString stringWithFormat:@"%lu",(unsigned long)feed.songinfo.ugcMaskExt]
                                                                                                                                                     }];
            }
        }
    } else if (action.type == DRAWITEM_ACTION_TYPE_LIVE_ENTERROOM_BTN) {
        KSimpleFeed *feed = (KSimpleFeed*) action.busiData;
        NSString *strRoomID = feed.simpleUser.userinfo.strRoomID;
        if (IS_EMPTY_STR_BM(strRoomID)) {
            strRoomID = [feed.asyncLiveRecInfo.jce_mapExt safeObjectForKey:@"room_id"];
        }
        if (IS_EMPTY_STR_BM(strRoomID)) {
            KLog(@"[asyncWork Live]没有找到 有效 roomID ！点击无效");
            return;
        }
        if (feed.simpleUser.userinfo.userId == [KSLoginManager sharedInstance].curUserInfo.userId) {
            [[KSNavigationManager sharedManager] showLiveShowEnterVC];
        } else {
            // 跳转直播间, 增加进房来源的作品名称参数
            NSString *pageFrom = @"live#0#feed#creation#induce_block#word_guide";
            [[KSNavigationManager sharedManager] watchLiveShow:strRoomID roomInfo:nil forcePop:YES isFromContribution:NO infoDict:@{@"ugcName":SAFE_STR_BM([feed getSongName]),
                                                                                                                                                             @"trace_id":SAFE_STR_BM(feed.recommendItem.jce_strTraceId),
                                                                                                                                                             @"item_type":[NSString stringWithFormat:@"%d",feed.recommendItem.jce_uiItemType],
                                                                                                                                                             @"algorithm_type":[NSString stringWithFormat:@"%d",feed.recommendItem.jce_uiAlgorithmType],
                                                                                                                                                             @"algoritym_id":feed.recommendItem.jce_strAlgorithmId,
                                                                                                                                    PushNotification_liveShowFromPageNew:pageFrom,@"report_str8":
                                                                                                                                        feed.asyncLiveRecInfo.jce_uRecType > 0 ? [NSString stringWithFormat:@"%d",feed.asyncLiveRecInfo.jce_uRecType] : @"-1",@"report_str9":SAFE_STR(feed.asyncLiveRecInfo.jce_strRecReason),@"report_ugcId":SAFE_STR_BM(feed.simpleFeedCommon.strFeedId),@"report_ugcMask_1":[NSString stringWithFormat:@"%lu",(unsigned long)feed.songinfo.ugcMask],@"report_ugcMask_2":[NSString stringWithFormat:@"%lu",(unsigned long)feed.songinfo.ugcMaskExt]
                                                                                                                                  }];
        }
    } else if(action.type == DRAWITEM_ACTION_TYPE_KTVROOMSHOWFEED)
    {
        //歌房
        KSimpleFeed *feed = (KSimpleFeed*) action.busiData;
        
        if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(onKTVFeedClickCoverWithAction:simpleFeed:cell:)])
                {
                    [self.feedReportDelegate onKTVFeedClickCoverWithAction:action simpleFeed:feed cell:tableCell];

                }
        if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(timelineClickedTraceReport:extraInfo:)])
        {
              [self.feedReportDelegate timelineClickedTraceReport:feed extraInfo:@{@"indexRowKey":tableCell.indexPath}];
        }
        if ([feed isInteractGameInPlayingStatus] && self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(onGameFeedClickScreenAreaWithSimpleFeed:cell:)]) {
            [self.feedReportDelegate onGameFeedClickScreenAreaWithSimpleFeed:feed cell:tableCell];
        }
        
        //lite歌房退房
        if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(exitLiteKtvRoomBeforeShowKtvRoomVCWithSimpleFeed:cell:)])
        {
            [self.feedReportDelegate exitLiteKtvRoomBeforeShowKtvRoomVCWithSimpleFeed:feed cell:tableCell];
        }
        
        [self showKtvRoomVC:feed];
    }
    else if(action.type == DRAWITEM_ACTION_TYPE_MULTIKTVROOMSHOWFEED)
    {
        //多人歌房同步到feed
        KSimpleFeed *feed = (KSimpleFeed*) action.busiData;
        
        if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(onKTVFeedClickCoverWithAction:simpleFeed:cell:)])
        {
            [self.feedReportDelegate onKTVFeedClickCoverWithAction:action simpleFeed:feed cell:tableCell];
                            
        }
        
        if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(timelineClickedTraceReport:extraInfo:)])
            {
                [self.feedReportDelegate timelineClickedTraceReport:feed extraInfo:@{@"indexRowKey":tableCell.indexPath}];
            }
    
        if (feed.ktvRoomShow && (feed.ktvRoomShow.jce_lAnchorUid || feed.ktvRoomShow.jce_strRoomId))
        {
            
            KSNavReportModel* reportModel = nil;
            if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(getNavigationReportModelForMultiKTVShow)])
            {
                reportModel = [self.feedReportDelegate getNavigationReportModelForMultiKTVShow];
            }
            
            NSString *fromRoute = @"unknown";
            UIViewController* topVC = [[KSNavigationManager sharedManager] getTopViewController];
            // 转发类的取forwardFeed，非转发类的普通，取simpleUser
            KSUidType toUid = feed.forwardFeed.strForwardId > 0 ? feed.forwardFeed.feedUserInfo.userinfo.userId : feed.simpleUser.userinfo.userId;
            if (topVC && [topVC isKindOfClass:[KSUserProfileVC_V2 class]]) {
                BOOL isGuest = (toUid != [KSLoginManager sharedInstance].curUserInfo.userId);
                NSString *key = isGuest ? @"homepage_guest#feed_tab#null" : @"homepage_me#feed_tab#null";
                fromRoute = [NSString stringWithFormat:@"%@#%lld", key, toUid];
            } else if (reportModel) {
                fromRoute = [NSString stringWithFormat:@"%@#%lld",reportModel.fromPage, toUid];
            }
            
        }
        
    }
    else if (action.type == DRAWITEM_ACTION_TYPE_SOCIALKTVROOMSHOWFEED) {
        KSimpleFeed *feed = (KSimpleFeed*) action.busiData;
        [self showSocialKtvRoomVC:feed];
        
        if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(onKTVFeedClickCoverWithAction:simpleFeed:cell:)])
        {
            
            [self.feedReportDelegate onKTVFeedClickCoverWithAction:action simpleFeed:feed cell:tableCell];
                                 
        }
        if ([feed isInteractGameInPlayingStatus] && self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(onGameFeedClickScreenAreaWithSimpleFeed:cell:)]) {
            [self.feedReportDelegate onGameFeedClickScreenAreaWithSimpleFeed:feed cell:tableCell];
        }
    }
    else if(action.type == DRAWITEM_ACTION_TYPE_KTVROOMMIKEFEED)
    {
        //排麦同步到feed
        KSimpleFeed *feed = (KSimpleFeed*) action.busiData;
        
        if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(onKTVFeedClickCoverWithAction:simpleFeed:cell:)])
        {
            
            [self.feedReportDelegate onKTVFeedClickCoverWithAction:action simpleFeed:feed cell:tableCell];
                                 
        }
        if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(timelineClickedTraceReport:extraInfo:)])
        {
            [self.feedReportDelegate timelineClickedTraceReport:feed extraInfo:@{@"indexRowKey":tableCell.indexPath}];
        }

        if (feed.ktvRoomMike && feed.ktvRoomMike.jce_strRoomId && feed.ktvRoomMike.jce_strRoomId.length>0)
        {

            
            KSNavReportModel* reportModel = nil;
                   if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(getNavigationReportModelForKTVShow)])
            {
                reportModel = [self.feedReportDelegate getNavigationReportModelForKTVShow];
            }
            
            NSString *fromRoute = @"unknown";
            UIViewController* topVC = [[KSNavigationManager sharedManager] getTopViewController];
            // 转发类的取forwardFeed，非转发类的普通，取simpleUser
            KSUidType toUid = feed.forwardFeed.strForwardId > 0 ? feed.forwardFeed.feedUserInfo.userinfo.userId : feed.simpleUser.userinfo.userId;
            if (topVC && [topVC isKindOfClass:[KSUserProfileVC_V2 class]]) {
                BOOL isGuest = (toUid != [KSLoginManager sharedInstance].curUserInfo.userId);
                NSString *key = isGuest ? @"homepage_guest#feed_tab#null" : @"homepage_me#feed_tab#null";
                fromRoute = [NSString stringWithFormat:@"%@#%lld", key, toUid];
            } else if (reportModel) {
                fromRoute = [NSString stringWithFormat:@"%@#%lld",reportModel.fromPage, toUid];
            }
            [[KSNavigationManager sharedManager] showKTVRoomVCWithOwnerUid:0
                                                                    roomid:feed.ktvRoomMike.jce_strRoomId
                                                                    passwd:@""
                                                                  forcePop:YES
                                                                   ktvFrom:reportModel.ktvFrom
                                                                  fromPage:reportModel.fromPage
                                                                      info:@{PushNotification_KtvFromRoute: fromRoute}];
        }
    }
    else if(action.type == DRAWITEM_ACTION_TYPE_MULTIKTVROOMMIKEFEED)
    {
        //多人排麦同步到feed
        KSimpleFeed *feed = (KSimpleFeed*) action.busiData;
        
        
        if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(onKTVFeedClickCoverWithAction:simpleFeed:cell:)])
        {
            
            [self.feedReportDelegate onKTVFeedClickCoverWithAction:action simpleFeed:feed cell:tableCell];
                                 
        }
        if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(timelineClickedTraceReport:extraInfo:)])
        {
            [self.feedReportDelegate timelineClickedTraceReport:feed extraInfo:@{@"indexRowKey":tableCell.indexPath}];
        }
        
        

        KSNavReportModel* reportModel = nil;
               if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(getNavigationReportModelForMultiKTVShow)])
        {
            reportModel = [self.feedReportDelegate getNavigationReportModelForMultiKTVShow];
        }

        if (feed.ktvRoomMike && feed.ktvRoomMike.jce_lAnchorUid)
        {
            NSString *fromRoute = @"unknown";
            UIViewController* topVC = [[KSNavigationManager sharedManager] getTopViewController];
            // 转发类的取forwardFeed，非转发类的普通，取simpleUser
            KSUidType toUid = feed.forwardFeed.strForwardId > 0 ? feed.forwardFeed.feedUserInfo.userinfo.userId : feed.simpleUser.userinfo.userId;
            if (topVC && [topVC isKindOfClass:[KSUserProfileVC_V2 class]]) {
                BOOL isGuest = (toUid != [KSLoginManager sharedInstance].curUserInfo.userId);
                NSString *key = isGuest ? @"homepage_guest#feed_tab#null" : @"homepage_me#feed_tab#null";
                fromRoute = [NSString stringWithFormat:@"%@#%lld", key, toUid];
            } else if (reportModel) {
                fromRoute = [NSString stringWithFormat:@"%@#%lld",reportModel.fromPage, toUid];
            }
            
        }
    }
    else if (action.type == DRAWITEM_ACTION_TYPE_UGC_BEAT_JUMP_KSONG)
    {
        //打擂UGC-跳录歌页
        KSimpleFeed *simpleFeed = SAFE_CAST(action.busiData,[KSimpleFeed class]);
        KSong *songInfo = [[KSong alloc] init];
        songInfo.name = simpleFeed.songinfo.name;
        songInfo.songMid = simpleFeed.songinfo.songMid;  //只需要mid就可以跳到录歌页
        KSRecordingNormalStrategy *normalStrategy = [[KSRecordingNormalStrategy alloc] init];
        normalStrategy.recordingData.song = songInfo;
        normalStrategy.recordingData.champFrom = ChampFromTimelineFeed;
        
        KSNavReportModel* reportModel = nil;
        if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(getNavigationReportModelForPKRecording:)])
        {
            reportModel = [self.feedReportDelegate getNavigationReportModelForPKRecording:simpleFeed];
        }
        normalStrategy.recordStrategyReport.from_page = reportModel.fromPage; 
        normalStrategy.recordStrategyReport.ugcIdStr = simpleFeed.simpleFeedCommon.strFeedId;
        normalStrategy.recordStrategyReport.otherReportParam = [NSString stringWithFormat:@"touid=%lld",simpleFeed.simpleUser.userinfo.userId];
        KSPKUserInfo *userInfo = [[KSPKUserInfo alloc] init];
        userInfo.userId = simpleFeed.simpleUser.userinfo.userId;
        userInfo.avatarTimestamp = simpleFeed.simpleUser.userinfo.avatarTimestamp;
        userInfo.nickName = simpleFeed.simpleUser.userinfo.nickName;
        userInfo.score = simpleFeed.songinfo.score;
        normalStrategy.recordingData.pkUserInfo = userInfo;
        normalStrategy.pkScenario = Champion_PKScenario;
        
        
        [[KSNavigationManager sharedManager] singSongWithStrategy:normalStrategy];
        //上报
        
        if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(traceReportJumpToRecordVCFromBeatFeed:)])
        {
            [self.feedReportDelegate traceReportJumpToRecordVCFromBeatFeed:simpleFeed];
        }
        
        if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(onUgcFeedClickChallengeButtonWithAction:simpleFeed:cell:)])
        {
            [self.feedReportDelegate onUgcFeedClickChallengeButtonWithAction:action simpleFeed:simpleFeed cell:tableCell];
        }
        
        [self markFromUgcIDInKeyPathBeforeReocrd:simpleFeed];

    }
    else if (action.type == DRAWITEM_ACTION_TYPE_FEED_UGC_Avatar_KTV_Singing)
    {
        KSimpleFeed *feed = SAFE_CAST(tableCell.busiData, KSimpleFeed);
        if([feed isForwardUserInKtvShowRoom]) { // 推荐
            NSString *jumpUrl = feed.forwardFeed.feedUserInfo.avatarKtvInfo.jce_strJumpUrl;
            if (!IS_EMPTY_STR_BM(jumpUrl)) {
                jumpUrl = [KSComHelper replaceUrl:jumpUrl queryName:@"ktvFromRoute" withValue:@"feed#creation#KTV_breathe_avatar"];
            }
            [[KSNavigationManager sharedManager] dealWithScheme:jumpUrl];
        }
        else if ([feed isUserInKtvShowRoom]){ // 推荐
            NSString *jumpUrl = feed.simpleUser.avatarKtvInfo.jce_strJumpUrl;
            if (!IS_EMPTY_STR_BM(jumpUrl)) {
                jumpUrl = [KSComHelper replaceUrl:jumpUrl queryName:@"ktvFromRoute" withValue:@"feed#creation#KTV_breathe_avatar"];
            }
            NSString *fromUid = @(feed.simpleUser.userinfo.userId).stringValue;
            if (!IS_EMPTY_STR(jumpUrl) && !IS_EMPTY_STR(fromUid)) {
                jumpUrl = [KSComHelper replaceUrl:jumpUrl queryName:PushNotification_KtvFromUid withValue:fromUid];
            }
            NSString *joinRoomDesc = feed.enterKtvRoomDetail.jce_joinRoomDesc;
            if (!IS_EMPTY_STR(jumpUrl) && !IS_EMPTY_STR(joinRoomDesc)) {
                jumpUrl = [KSComHelper replaceUrl:jumpUrl queryName:PushNotification_ugcJoinRoomDesc withValue:joinRoomDesc];
            }
            
            [[KSNavigationManager sharedManager] dealWithScheme:jumpUrl];
        }
        
        if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(onFeedKtvSingingClickWithAction:simpleFeed:cell:)])
        {
            [self.feedReportDelegate onFeedKtvSingingClickWithAction:action simpleFeed:feed cell:tableCell];
        }
    }
    else if (action.type == DRAWITEM_ACTION_TYPE_FEED_UGC_Bottom_KTV_Singing) {
        KSimpleFeed *feed = SAFE_CAST(tableCell.busiData, KSimpleFeed);
        if ([feed isUserInKtvShowRoom]){ // 推荐
            NSString *jumpUrl = feed.simpleUser.avatarKtvInfo.jce_strJumpUrl;
            NSString *fromUid = @(feed.simpleUser.userinfo.userId).stringValue;
            if (!IS_EMPTY_STR(jumpUrl) && !IS_EMPTY_STR(fromUid)) {
                jumpUrl = [KSComHelper replaceUrl:jumpUrl queryName:PushNotification_KtvFromUid withValue:fromUid];
            }
            NSString *joinRoomDesc = feed.enterKtvRoomDetail.jce_joinRoomDesc;
            if (!IS_EMPTY_STR(jumpUrl) && !IS_EMPTY_STR(joinRoomDesc)) {
                jumpUrl = [KSComHelper replaceUrl:jumpUrl queryName:PushNotification_ugcJoinRoomDesc withValue:joinRoomDesc];
            }
            [[KSNavigationManager sharedManager] dealWithScheme:jumpUrl];
        }
    }
    else if (action.type == DRAWITEM_ACTION_TYPE_LIVESHOWFEED_COPYROOMID)
    {
        //debug使用
        KSimpleFeed *feed = (KSimpleFeed*) action.busiData;
        if (feed.liveShow && feed.liveShow.strLiveRoomId && feed.liveShow.strLiveRoomId.length>0)
        {
            UIPasteboard* pasteboard = [UIPasteboard generalPasteboard];
            [pasteboard setData:[feed.liveShow.strLiveRoomId dataUsingEncoding:NSUTF8StringEncoding] forPasteboardType:@"string"];
            [self tempAlert:@"拷贝成功"];
        }
    }
    else if (action.type == DRAWITEM_ACTION_TYPE_BUTTON_SHOW_FIRST_UGC)
    {
        self.commentSimpleFeed = SAFE_CAST(action.busiData, KSimpleFeed);
        [self commentVCDidClickMore];
    }
    else if (action.type == DRAWITEM_ACTION_TYPE_BUTTON_SHOW_MBAR || action.type == DRAWITEM_ACTION_TYPE_BUTTON_SHOW_TAIL)
    {
        //小尾巴跳转
        KSimpleFeed *feed = (KSimpleFeed*) action.busiData;
        
        if ( UGC_MBAR(feed.songinfo.ugcMask) || feed.songinfo.strMbarShopId.length>0)
        {
            KLog(@"[友唱] strMbarShopId is %@",feed.songinfo.strMbarShopId);
            NSString *url = [[WnsConfigManager sharedInstance].appConfig.urlConfig mbarShopPage];
            url = [url stringByReplacingOccurrencesOfString:@"$shop" withString:feed.songinfo.strMbarShopId];
            [[KSNavigationManager sharedManager] showWebView:url];
            
            if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(traceReportYouSingMBarTailClickedWithAction:simpleFeed:)])
              {
                  [self.feedReportDelegate traceReportYouSingMBarTailClickedWithAction:action simpleFeed:feed];
              }
            
        }
        else if(feed.ktvRoomShow || feed.ktvRoomMike)
        {
            // 歌房小尾巴点击上报
            if ([feed.ktvRoomShow.jce_ktvLables count] > 0)
            {
                if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(traceReportKTVShowMBarTailClickedWithAction:simpleFeed:)])
                {
                    [self.feedReportDelegate traceReportKTVShowMBarTailClickedWithAction:action simpleFeed:feed];
                }
            
                JceTimeline_ktv_lable_item *item = feed.ktvRoomShow.jce_ktvLables[0];
                NSString *tailJump = item.jcev2_p_2_o_jumpURL;
                if (tailJump.length > 0)
                {
                    [[KSNavigationManager sharedManager] dealWithScheme:tailJump];
                }
            }
            else if ([feed.ktvRoomMike.jce_ktvLables count] > 0)
            {
                JceTimeline_ktv_lable_item *item = feed.ktvRoomMike.jce_ktvLables[0];
                
                if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(traceReportKTVMikeMBarTailClickedWithAction:simpleFeed:)])
                {
                    [self.feedReportDelegate traceReportKTVMikeMBarTailClickedWithAction:action simpleFeed:feed];
                }
                

                NSString *tailJump = item.jcev2_p_2_o_jumpURL;
                if (tailJump.length > 0) {
                    [[KSNavigationManager sharedManager] dealWithScheme:tailJump];
                }
            }
        }
        else if (feed.hcCellItem.jce_uHcFinalGiftNum > 0 && (UGC_FIN_CHORUS(feed.songinfo.ugcMask) == FIN_CHORUS_UGC))
        {
            // 礼物合唱成品，点击跳转本地的半成品列表页
            KSTimelineDetail *chorusTimeline = [[KSTimelineDetail alloc] initWithFeed:feed];
            KSUserInfo *userInfo = [[KSUserInfo alloc] init];
            KSHcExtraInfo *hcExtraInfo = [[KSHcExtraInfo alloc] init];
            hcExtraInfo.hcFollowCount = feed.HcUserInfo.followCount;
            userInfo.userId = feed.HcUserInfo.userId;
            userInfo.nickName = feed.HcUserInfo.nickName;
            userInfo.avatarTimestamp = feed.HcUserInfo.avatarTimestamp;
            chorusTimeline.ugcId = feed.songinfo.strHalfUgcId;
            chorusTimeline.userInfo = userInfo;
            chorusTimeline.hcExtraInfo = hcExtraInfo;
            chorusTimeline.coverImageUrl = feed.songinfo.albumPicUrl;
            [[KSNavigationManager sharedManager] showChorusJoinListVC:chorusTimeline];
        }
        else if ([feed.songinfo.mapTailInfo isNotNull])
        {
            NSString *intooShareID = [NSString stringWithFormat:@"%@", feed.songinfo.mapTailInfo[KSSTR_TAIL_ID]];
            KLog(@"[音兔] KSSTR_TAIL_ID is %@", intooShareID);
            if ( UGC_FROM_INTOO(feed.songinfo.ugcMaskExt) && intooShareID.length>0)
            {
                                
                if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(traceReportIntoMBarTailClickedWithAction:simpleFeed:)])
                {
                    [self.feedReportDelegate traceReportIntoMBarTailClickedWithAction:action simpleFeed:feed];
                }
                NSString *tailJump = [NSString stringWithFormat:@"%@", feed.songinfo.mapTailInfo[KSSTR_TAIL_JUMP]];
                if ([tailJump rangeOfString:@"makeIntooVideo"].location != NSNotFound)
                {
                    [[KSNavigationManager sharedManager] showIntooShortMVVC:feed.simpleFeedCommon.strFeedId];
                }
                else
                {
                    [KSIntooSchemeHelper jumpToIntooUgcDetailIfNeeded:intooShareID];
                }
                
            }
            else
            {
                // 通用小尾巴详情页跳转
                NSUInteger tailStatus = [[NSString stringWithFormat:@"%@", feed.songinfo.mapTailInfo[KSSTR_TAIL_STATUS]] intValue];
                NSString *tailJump = [NSString stringWithFormat:@"%@", feed.songinfo.mapTailInfo[KSSTR_TAIL_JUMP]];
                if ([tailJump containsString:@"ugcid=$ugcid"]) {
                    tailJump = [tailJump stringByReplacingOccurrencesOfString:@"$ugcid" withString:feed.songinfo.strHalfUgcId];
                }
                KLog(@"[小尾巴] 展示状态: %li 跳转链接: %@", (unsigned long)tailStatus, tailJump);
                if (tailStatus == kTimelineTailShowStatusCanHit)
                {
                    // 小尾巴跳转
                    if ([feed isIntooShortMVProduct])
                    {
                        [[KSNavigationManager sharedManager] showIntooShortMVVC:feed.simpleFeedCommon.strFeedId];
                    }
                    else
                    {
                        [[KSNavigationManager sharedManager] dealWithScheme:tailJump];
                    }
                    if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(traceReportMBarTailClickedWithAction:simpleFeed:)])
                    {
                        [self.feedReportDelegate traceReportMBarTailClickedWithAction:action simpleFeed:feed];
                    }
                
                }
            }
        }
    }
    else if (action.type == DRAWITEM_ACTION_TYPE_CONTRIBUTE)
    {
        KSimpleFeed *feed = (KSimpleFeed *) action.busiData;
        //附近-投稿-跳转到投稿页
        [[KSNavigationManager sharedManager] showContributeVCWithTopSource];
        
        if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(traceReportEnterContributeVCClickedWithAction:simpleFeed:)])
        {
            [self.feedReportDelegate traceReportEnterContributeVCClickedWithAction:action simpleFeed:feed];
            
        }
        if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(onUgcFeedClickVipContributionButtonWithAction:simpleFeed:cell:)])
        {
            [self.feedReportDelegate onUgcFeedClickVipContributionButtonWithAction:action simpleFeed:feed cell:tableCell];
        }

    }
    else if (action.type == DRAWITEM_ACTION_TYPE_PAYALBUME_BUY)
    {
        //付费合集feed - 购买
        KSimpleFeed *feed = (KSimpleFeed *) action.busiData;
        [self buyPayAlbumBtnDidClicked:feed];
        
        if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(onPayAlbumFeedClickBuyButtonWithAction:simpleFeed:cell:)])
        {
            [self.feedReportDelegate onPayAlbumFeedClickBuyButtonWithAction:action simpleFeed:feed cell:tableCell];
        }
        

    }
    else if (action.type == DRAWITEM_ACTION_TYPE_FEED_REC_FOLLOW)
    {
        //关注推荐人
        KSimpleFeed *feed = (KSimpleFeed *)action.busiData;
        KSUserInfo* userinfo = feed.simpleUser.userinfo;
        [self followUserWithRecSimpleFeed:feed userInfo:userinfo sceneId:KSFollowScene_Unknown completionBlock:nil];
    }
    else if (action.type == DRAWITEM_ACTION_TYPE_FEED_REC_PRIVATE_CHAT)
    {
        // 私信
        KSimpleFeed *feed = (KSimpleFeed *)action.busiData;
        
        KSMailSessionDetailViewController *vc = [[KSMailSessionDetailViewController alloc] initWithTargetUid:feed.simpleUser.userinfo.userId];
        
        vc.isFromNearBy = feed.isNearbyFeed;
        
        [KSNavigationManager.sharedManager pushVC:vc animated:YES];
        
        [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
            reportModel.key = @"feed_nearby#creation#direct_message#click#0";
            reportModel.ugcmask1 = feed.songinfo.ugcMask;
            reportModel.ugcmask2 = feed.songinfo.ugcMaskExt;
            reportModel.ugcmask3 = feed.songinfo.ugcMaskExt1;
            reportModel.ugcid = feed.simpleFeedCommon.strFeedId;
            reportModel.touid = feed.simpleUser.userinfo.userId;
            reportModel.mid =  feed.songinfo.songMid;
        }];

    }
    else if (action.type == DRAWITEM_ACTION_TYPE_FOLLOW_USER)
    {
        //关注用户
        KSimpleFeed *feed = (KSimpleFeed *)action.busiData;
        KSUserInfo* userinfo = feed.simpleUser.userinfo;
        [self followUserWithRecSimpleFeed:feed userInfo:userinfo sceneId:KSFollowScene_Unknown completionBlock:nil];
    }
    else if (action.type == DRAWITEM_ACTION_TYPE_FEED_REC_DISLIKE)
    {
        //推荐型FEED 取消类似推荐        
        KSimpleFeed *feed = (KSimpleFeed *)action.busiData;
        NSMutableArray *items = [NSMutableArray new];
        for (int i = 0; i < feed.recommendItem.jce_vecIgnoreItem.count; i ++) {
            /// (对该用户不感兴趣等) 负反馈选项后台下发
            proto_feed_webapp_IgnoreItem *ignoreInfo = [feed.recommendItem.jce_vecIgnoreItem safeObjectAtIndex:i];
            KSActionSheetItem *item = [[KSActionSheetItem alloc] init];
            item.title = ignoreInfo.jce_strDesc;
            item.tag = i;
            [items addObject:item];
        }
        KSActionSheet *actionSheet = [[KSActionSheet alloc] init];
        actionSheet.items = [items copy];
        
        KS_WEAK_SELF(self);
        [actionSheet setItemClickCallback:^(KSActionSheet * _Nonnull actionSheet, KSActionSheetItem * _Nonnull item) {
            CHECK_SELF_AND_RETURN();
            proto_feed_webapp_IgnoreItem *ignoreInfo = [feed.recommendItem.jce_vecIgnoreItem safeObjectAtIndex:item.tag];
            [self feedRetroactionWithFeed:feed ignoreInfo:ignoreInfo];
            [actionSheet close];
            
            /// 上报负反馈
            if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(onFeedClickNegativeFeedbackSimpleFeed:feedText:)]) {
                [self.feedReportDelegate onFeedClickNegativeFeedbackSimpleFeed:feed feedText:ignoreInfo.jce_strDesc];
            }
        }];
        
        [actionSheet setCancelClickCallback:^(KSActionSheet * _Nonnull actionSheet) {
            [actionSheet close];
        }];
        
        [actionSheet show];
        
        if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(onFeedNegativeMenuClickedWithSimpleFeed:cell:)]) {
            [self.feedReportDelegate onFeedNegativeMenuClickedWithSimpleFeed:feed cell:tableCell];
        }
        
    }
    else if (action.type == DRAWITEM_ACTION_TYPE_REMARK_UGC)
    {
        //点评 feed 点击 ugc
        KSimpleFeed *simpleFeed = (KSimpleFeed *)action.busiData;
        [[KSNavigationManager sharedManager] showRemarkDetail:simpleFeed.ugcRemark.jce_strTopicId];
        
        if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(traceReportForRemarkClickWithSimpleFeed:index:)])
        {
            
            [self.feedReportDelegate traceReportForRemarkClickWithSimpleFeed:simpleFeed index:tableCell.indexPath.row + 1];
        }
        
    }
    else if(action.type==DRAWITEM_ACTION_TYPE_PEROSON_TUTOR_REMARK_UGC)
    {
        //教唱邀请点评
        NSString *url = WnsUrlStringConfig(@"DianpingInviteTeacherUrl");
        NSArray* busiData=SAFE_CAST(action.retainedBusiData, NSArray);
        KSUidType teacherUid = [[busiData safeObjectAtIndex:0] integerValue];
        if(teacherUid > 0) {
            url = [url stringByReplacingOccurrencesOfString:@"${teacherid}" withString:@(teacherUid).stringValue];
            url = [url stringByReplacingOccurrencesOfString:@"$topSource" withString:[[KSTraceReportManager sharedManager] getBuyKBTopSource]];
            KSJumpToOtherSceneFrom sceneFrom = [[KSNavigationManager sharedManager] canJumpFromCurrentToOtherScene];
            if (sceneFrom != KSJumpOtherSceneFrom_NO) {
                [[KSAlertManager sharedManager] showAlertView:[[KSNavigationManager sharedManager] getJumpAlertViewText:sceneFrom]
                                                        title:nil
                                                         btns:@[KString(@"取消"),KString(@"确认")]
                                                completeBlock:^(NSInteger buttonIndex, NSString *buttonTitle) {
                    if (buttonIndex == 1) {
                        [[KSPathReportManager sharedManager] lazyAddNodeAfter:^{
                            [[KSNavigationManager sharedManager] clearLiveShowVCWhenLogout];
                            [[KSNavigationManager sharedManager] showWebView:url];
                        } completeBlock:nil];
                    }
                }];
            } else {
                if(teacherUid > 0){
                    [[KSNavigationManager sharedManager] showWebView:url];
                }
            }
        }
        
        if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(traceReportForTutorRemarkClickWithAction:)])
        {
            [self.feedReportDelegate traceReportForTutorRemarkClickWithAction:action];
        }
        
    }
    else if(action.type == DRAWITEM_ACTION_TYPE_PEROSON_TUTOR_REMARK_UGC_ASK_FOR_LEAVE)
    {
        //离开点评
        UIAlertView *alertView=[[UIAlertView alloc] initWithTitle:@"老师已休假" message:@"  老师已休假，请过两天看看" delegate:nil cancelButtonTitle:@"取消" otherButtonTitles:@"去看看",nil];
        [alertView showWithCompleteBlock:^(NSInteger buttonIndex){
            if(buttonIndex==1){
                //   DianpingSelectTutorUrl
                NSString *url = WnsUrlStringConfig(@"DianpingTeacherListUrl");
                url = [url stringByReplacingOccurrencesOfString:@"$topSource" withString:[[KSTraceReportManager sharedManager] getBuyKBTopSource]];
                [[KSNavigationManager sharedManager] showWebView:url];
            }
        }];
        [alertView show];
        
        if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(traceReportForTutorRemarkAskForLeaveClickWithAction:)])
        {
            [self.feedReportDelegate traceReportForTutorRemarkAskForLeaveClickWithAction:action];
        }
        
    }
    else if(action.type == DRAWITEM_ACTION_TYPE_PEROSON_TUTOR_REMARK_NONE_COUNT)
    {
        //点评名额没有了
        UIAlertView *alertView=[[UIAlertView alloc] initWithTitle:@"老师今日名额已售完" message:@"  老师今天已经没有名额了，你可以明天再来看看。或者去找下别的老师是否有名额" delegate:nil cancelButtonTitle:@"取消" otherButtonTitles:@"去看看",nil];
        [alertView showWithCompleteBlock:^(NSInteger buttonIndex) {
            if(buttonIndex==1){
                NSString *url = WnsUrlStringConfig(@"DianpingTeacherListUrl");
                url = [url stringByReplacingOccurrencesOfString:@"$topSource" withString:[[KSTraceReportManager sharedManager] getBuyKBTopSource]];
                [[KSNavigationManager sharedManager] showWebView:url];
            }
        }];
        [alertView show];
        
        if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(traceReportForTutorRemarkNoCountClickWithAction:)])
        {
            [self.feedReportDelegate traceReportForTutorRemarkNoCountClickWithAction:action];
        }
        
    }
    else if (action.type == DRAWITEM_ACTION_TYPE_BUTTON_PLAY)
    {
        //点击播放
        KSimpleFeed *simpleFeed = (KSimpleFeed *)action.busiData;
        
        if ([KSABTestManager sharedManager].abTestPlayListMode == 0)
        {
            
            if (WnsConfigManager.isUseTMEPlayer)
            {
                [self playCurrentFeed:simpleFeed playerView:tableCell];
            }
            else
            {
                [self playCurrentFeed:simpleFeed isShowPlayVC:NO];
            }
        }
        else
        {
            NSMutableArray* feedList = [self delegateFeedListSource];
            [self playAllListStartFromFeed:simpleFeed feedList:feedList isShowPlayVC:NO];
        }
    }
    else if (action.type == DRAWITEM_ACTION_TYPE_FEED_EXPAND_ALL_TEXT)
    {
        //展开全文
        KSimpleFeed* feed = SAFE_CAST(action.busiData, KSimpleFeed);
        feed.isExpandAllText = YES;
        feed.layoutInfo = nil;
        [self reloadTable];
        
        
    }
    else if (action.type == DRAWITEM_ACTION_TYPE_FEED_COLLAPSE_ALL_TEXT)
    {
        //收起全文
        KSimpleFeed* feed = SAFE_CAST(action.busiData, KSimpleFeed);
        feed.isExpandAllText = NO;
        feed.layoutInfo = nil;
        [self reloadTable];
        
    }
    else if (action.type == DRAWITEM_ACTION_TYPE_FEED_EXPAND_FORWARD_TEXT)
    {
        //展开转发理由
        KSimpleFeed* feed = SAFE_CAST(action.busiData, KSimpleFeed);
        feed.isExpandForwardText = YES;
        feed.layoutInfo = nil;
        [self reloadTable];
    }
    else if (action.type == DRAWITEM_ACTION_TYPE_FEED_COLLAPSE_FORWARD_TEXT)
    {
        //收起转发理由
        KSimpleFeed* feed = SAFE_CAST(action.busiData, KSimpleFeed);
        feed.isExpandForwardText = NO;
        feed.layoutInfo = nil;
        [self reloadTable];
        
    }
    else if(action.type == DRAWITEM_ACTION_TYPE_FEED_EXPOSE_FINISH)
    {
        // 曝光
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            KSimpleFeed* feed = SAFE_CAST(action.busiData, KSimpleFeed);

            NSInteger __block feedIndex = -1;
            NSMutableArray* feedList = [self delegateFeedListSource];
            [feedList enumerateObjectsUsingBlock:^(NSObject* feedObj, NSUInteger idx, BOOL * _Nonnull stop) {

                KSimpleFeed* feedItem = [KSimpleFeed safeCastFromObj:feedObj];
                if (!feedItem)
                {
                    *stop = YES;
                }
                
                if ([feedItem.simpleFeedCommon.strFeedId isEqualToString:feed.simpleFeedCommon.strFeedId])
                {
                    feedIndex = idx;
                }
            }];

            NSMutableArray*uploadTaskArray = [self delegateUploadTaskArray];
            if (![feed isRecCardFeed] && [feed isShowDissimilateGiftRankFeed] && feed.feedParkingGuideState == ksFeedParkingGuideStateNotHasShow && feedIndex >= 0 && feedIndex < feedList.count && uploadTaskArray.count <= 0)
            {
                feed.feedParkingGuideState = ksFeedParkingGuideStateShowing;
                feed.layoutInfo = nil;
                feed.isShowParkingAnim = YES;
                [self reloadTable];
            }
            else if (feed.feedAd && feed.feedParkingGuideState == ksFeedParkingGuideStateNotHasShow)
            {
                // 广告Feed的strFeedId为空，故去掉index判断
                feed.feedParkingGuideState = ksFeedParkingGuideStateShowing;
                feed.layoutInfo = nil;
                feed.isShowParkingAnim = YES;
                KLog(@"【adfeed 曝光满足列表将要刷新】");
                [self reloadTable];
            }
        });
    }
    else if(action.type == DRAWITEM_ACTION_TYPE_AVATAR_GOTO_LIVE_OR_KTV)
    {
        // 点击feed直播态头像
        KSCellUser *cellUser = (KSCellUser*)action.busiData;
        
        NSString *liveShowRoomId = cellUser.strRoomId;
        if (!IS_EMPTY_STR_BM(liveShowRoomId)) {
            // 跳转直播间
            [[KSNavigationManager sharedManager] watchLiveShowWithRoomId:liveShowRoomId schemeUrl:nil anchorUserId:cellUser.userId forcePop:YES];
        }
    }
    else if (action.type == DRAWITEM_ACTION_TYPE_LIKE)
    {
        // 点赞
        KSimpleFeed *feed = (KSimpleFeed *)action.busiData;
        [self likeFeedWithSimpleFeed:feed];
    }
    else if (action.type == DRAWITEM_ACTION_TYPE_BUTTON_SUBSCRIBE)
    {
        // 追剧
        KSimpleFeed *feed = (KSimpleFeed *)action.busiData;
        [self subscribeWithFeed:feed];
    }
    else if (action.type == DRAWITEM_ACTION_TYPE_FEED_TOPIC)
    {
        // 点击话题跳转
        KSimpleFeed *simpleFeed = (KSimpleFeed *)action.busiData;
        if (simpleFeed && [simpleFeed isKindOfClass:KSimpleFeed.class])
        {
            JceTimeline_s_topic *topic = [simpleFeed.topicsArray safeObjectAtIndex:0];
            if (topic && [topic isKindOfClass:JceTimeline_s_topic.class])
            {
                NSString *from_page = @"unknow_page#all_module#null";
                if ([self.feedReportDelegate respondsToSelector:@selector(getTopicVCFromPageForReport)]) {
                    from_page = [self.feedReportDelegate getTopicVCFromPageForReport];
                }
                [[KSNavigationManager sharedManager] showTopicVCWithTopicId:topic.jce_uTopicId fromPage:from_page];
                
                // 上报
                if ([self.feedReportDelegate respondsToSelector:@selector(traceReportTopicClickedWithAction:simpleFeed:)])
                {
                    [self.feedReportDelegate traceReportTopicClickedWithAction:action simpleFeed:simpleFeed];
                }
            }
        }
    }
    else if (action.type == DRAWITEM_ACTION_TYPE_OPEN_WEB_URL && urlStr && [urlStr hasPrefix:@"TopicId="])
    {
        // 话题ID跳转
        NSString *topicId = [urlStr stringByReplacingOccurrencesOfString:@"TopicId=" withString:@""];
        NSString *from_page = @"unknow_page#all_module#null";
        if ([self.feedReportDelegate respondsToSelector:@selector(getTopicVCFromPageForReport)]) {
            from_page = [self.feedReportDelegate getTopicVCFromPageForReport];
        }
        [[KSNavigationManager sharedManager] showTopicVCWithTopicId:[topicId integerValue] fromPage:from_page];
    }
    else if (action.type == DRAWITEM_ACTION_TYPE_OPEN_WEB_URL && urlStr && [urlStr hasPrefix:@"TopicName="])
    {
        // 话题名跳转
        NSString *topicName = [urlStr stringByReplacingOccurrencesOfString:@"TopicName=" withString:@""];
        NSString *from_page = @"unknow_page#all_module#null";
        if ([self.feedReportDelegate respondsToSelector:@selector(getTopicVCFromPageForReport)]) {
            from_page = [self.feedReportDelegate getTopicVCFromPageForReport];
        }
        [[KSNavigationManager sharedManager] showTopicVCWithTopicId:0 topicName:topicName fromPage:from_page];
    }
    else if (action.type == DRAWITEM_ACTION_TYPE_COURSEFEED_CLICK)
    {
        // 课程Feed点击
        KSimpleFeed *feed = (KSimpleFeed *)action.busiData;
        [KSTeachSingCourseManager openCourseDetailPage:feed.courseFeed.jce_strCourseId];
        if ([self.feedReportDelegate respondsToSelector:@selector(onCourseFeedClickCoverWithAction:simpleFeed:cell:)]) {
            [self.feedReportDelegate onCourseFeedClickCoverWithAction:action simpleFeed:feed cell:tableCell];
        }
    }
    else if (action.type == DRAWITEM_ACTION_TYPE_ENTER_DETAIL)
    {
        //进详情页从头播
        KSimpleFeed *feed = SAFE_CAST(action.busiData, KSimpleFeed);
        if (feed) {
            [self showTimelineDetailWithFeed:feed];
        }
    } else if (action.type == DRAWITEM_ACTION_TYPE_HC_TEXT) {
        // 点击“与xxx合唱”文案
        KSimpleFeed *simpleFeed = SAFE_CAST(action.busiData, KSimpleFeed);
        NSString *strUgcId = simpleFeed.songinfo.strHalfUgcId;
        KSGlobalPlayItem *playItem = [[KSGlobalPlayItem alloc] initWithUgcId:strUgcId ugcMask:simpleFeed.songinfo.ugcMask songName:simpleFeed.songinfo.name nickName:simpleFeed.HcUserInfo.nickName scoreRank:0];
        playItem.sourcePage = KSPlaySourcePage_UnknowSourcePageType;
        [[KSUgcPlayManager sharedManager] playUgcWithItem:playItem];
        
        // 埋点
        if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(reportClickChorusTag:)]) {
            [self.feedReportDelegate reportClickChorusTag:simpleFeed];
        }
    } else if (action.type == DRAWITEM_ACTION_TYPE_FEED_UGC_POI_INFO) {
        KSimpleFeed *simpleFeed = SAFE_CAST(action.busiData, KSimpleFeed);
        KSLocationFeedListViewController *vc = [[KSLocationFeedListViewController alloc] initWithFeed:simpleFeed];

        UIViewController *lastVC = [[KSNavigationManager sharedManager] getMainPageNavController].viewControllers.lastObject;
        if ([lastVC isKindOfClass:[KSRootTabBarController class]]) {
            KSRootTabBarController *tabbarVC = SAFE_CAST(lastVC, KSRootTabBarController);
            vc.fromPageStr = [tabbarVC getCurrentSelectVCReportString];
        }
        [[[KSNavigationManager sharedManager] getMainPageNavController] pushViewController:vc animated:YES];
    }
    else if (action.type == DRAWITEM_ACTION_TYPE_BUTTON_FEED_HEAT_TOP_RANK_TAIL)
    {
        //拉起热度卡巅峰榜单
        KSimpleFeed *feed = SAFE_CAST(action.busiData, KSimpleFeed);
        NSString *tailJumpUrl = [feed.songinfo.mapTailInfo safeObjectForKey:KSSTR_TAIL_JUMP];
        if (!IS_EMPTY_STR_BM(tailJumpUrl)) {
            [[KSNavigationManager sharedManager] dealWithScheme:tailJumpUrl];
        }
    }
    else if (action.type == DRAWITEM_ACTION_TYPE_FEED_GOODVOICERANK_TAIL) {
        // 进好声音上榜页面
        KSimpleFeed *simpleFeed = SAFE_CAST(action.busiData, KSimpleFeed);
        NSString *urlStr = simpleFeed.songinfo.goodVoiceRankJumpUrl;
        [[KSNavigationManager sharedManager] dealWithScheme:SAFE_STR_BM(urlStr)];
        
        // 上榜
        if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(traceReportMBarTailClickedWithAction:simpleFeed:)]) {
            [self.feedReportDelegate traceReportMBarTailClickedWithAction:action simpleFeed:simpleFeed];
        }
    }
    else if (action.type == DRAWITEM_ACTION_TYPE_VIP_RIGHTS) {
        // 展示VIP权益半屏
        KSimpleFeed *simpleFeed = SAFE_CAST(action.busiData, KSimpleFeed);
        KSVIPRightsFloatingView *vipRightsView = [[KSVIPRightsFloatingView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT)];
        [vipRightsView updateWithVIPRights:simpleFeed.VIPRights];
        KS_WEAK_SELF(vipRightsView);
        vipRightsView.closeBlk = ^{
            KS_STRONG_SELF(vipRightsView);
            [vipRightsView removeFromSuperview];
            vipRightsView = nil;
        };
        UIWindow *keyWindow = UIApplication.sharedApplication.keyWindow;
        [keyWindow addSubview:vipRightsView];
    } else if (action.type == DRAWITEM_ACTION_TYPE_RecFeed_GiftAndMinorHeatGuide) {
        // 点击大卡片底部送礼条
        KSimpleFeed *simpleFeed = SAFE_CAST(action.busiData, KSimpleFeed);
        if (simpleFeed.giftGuide.isMinorHeatCard) {
            // 小额热度卡场景
            [self directSendRecMinorHeatCard:simpleFeed];
        } else if (simpleFeed.giftGuide.isFreeAd) {
            // 看广告免费送
            KSLayoutableTimelineFeedCellV2 *feedCell = SAFE_CAST(tableCell, KSLayoutableTimelineFeedCellV2);
            if ([feedCell respondsToSelector:@selector(didTapFollowSpecialGiftBtn:)]) {
                [feedCell didTapFollowSpecialGiftBtn:nil];
            }
        } else {
            // 送礼异化增强场景
            [self onRecBottomSendGiftBtnClick:simpleFeed];
        }
    } else if (action.type == DRAWITEM_ACTION_TYPE_KTV_TME_TOWN) {
        // 跳转到TMETown
        KSimpleFeed *simpleFeed = SAFE_CAST(action.busiData, KSimpleFeed);
        NSString *urlStr = [simpleFeed.tmeTown.jce_mapExt safeObjectForKey:@"room_url"];
        [[KSNavigationManager sharedManager] showWebView:SAFE_STR_BM(urlStr)];
    } else if (action.type == DRAWITEM_ACTION_TYPE_Feed_HeatCard_Jump) {
        // 拉起热度卡hippy全屏购买页
        KSimpleFeed *simpleFeed = SAFE_CAST(action.busiData, KSimpleFeed);
        NSString *urlStr = simpleFeed.accompRecHeatInfo.jce_strJumpUrl;
        [[KSNavigationManager sharedManager] dealWithScheme:urlStr];
        // 热度卡标识点击上报
        if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(onFeedHeatCardClickWithAction:simpleFeed:)]) {
            // 伴奏详情页
            [self.feedReportDelegate onFeedHeatCardClickWithAction:action simpleFeed:simpleFeed];
        } else if (simpleFeed.feedVCType == KSimpleFeedSourceType_TimelineRoot) {
            // 关注feed上报
            NSString *reprotModuleKey = feed_report_tab_follow;
            if ([KSTimelineManager sharedManager].currentFiltrMask == [KSTimelineManager sharedManager].filtrMaskFollow) {
                reprotModuleKey = feed_report_tab_follow;
            } else if ([KSTimelineManager sharedManager].currentFiltrMask == [KSTimelineManager sharedManager].filtrMaskQQWXFriends) {
                reprotModuleKey = feed_report_tab_friends;
            } else if ([KSTimelineManager sharedManager].currentFiltrMask == [KSTimelineManager sharedManager].filtrMaskUgcFeed) {
                reprotModuleKey = feed_report_tab_ugc_feed;
            } else if ([KSTimelineManager sharedManager].currentFiltrMask == [KSTimelineManager sharedManager].filtrMaskUgcFeedQQWX) {
                reprotModuleKey = feed_report_tab_ugc_feed_only_qqwx;
            }
                        
            [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                reportModel.key = [NSString stringWithFormat:@"%@#creation#heat#click#0", reprotModuleKey];
                reportModel.mid = simpleFeed.songinfo.songMid;
                reportModel.ugcid = simpleFeed.simpleFeedCommon.strFeedId;
                reportModel.touid = simpleFeed.simpleUser.userinfo.userId;
                
                NSString *UIDStr = [NSString stringWithFormat:@"%u", simpleFeed.accompRecHeatInfo.jce_uHeadLinePurchaser];
                reportModel.commonStr1 = !IS_EMPTY_STR_BM(UIDStr) ? UIDStr : @"unknown";
                
                reportModel.commonInt3 = simpleFeed.accompRecHeatInfo.jce_uShowHeatFrom;
            }];
        }
    } else if(action.type == DRAWITEM_ACTION_TYPE_RecLiveFeed_LiveShow){
        KSimpleFeed *feed = SAFE_CAST(action.busiData, KSimpleFeed);
        
        NSDictionary *dict  = @{@"trace_id":feed.recommendItem.jce_strTraceId,
                                    @"item_type":[NSString stringWithFormat:@"%d",feed.recommendItem.jce_uiItemType],
                               @"algorithm_type":[NSString stringWithFormat:@"%d",feed.recommendItem.jce_uiAlgorithmType],
                                 @"algoritym_id":feed.recommendItem.jce_strAlgorithmId,
                                PushNotification_liveShowFromPageNew:kFROM_PAGE_NEW_KEY_FEED_LIVE_FEED_COVER,
                                     PushNotification_iSupportP2P:SAFE_STR_BM([feed.liveShow.mapExt safeObjectForKey:PushNotification_iSupportP2P]),
                                @"report_int14":( @"1")
                                    };
        // 跳转直播间
        NSMutableDictionary *infoDict = [NSMutableDictionary dictionaryWithDictionary:dict];
        if ([feed.liveShow isOfficialChannelRoom] == NO) {
            [infoDict addEntriesFromDictionary:@{PushNotification_strCdnUrl:SAFE_STR_BM([feed.liveShow.mapExt safeObjectForKey:PushNotification_strCdnUrl])}];
        }
        [infoDict setObject:@(KSLiveShowProxyTypeFull) forKey:kLiveShowUIModeType];
        // 进直播间后的其他操作（送道具，点赞等）
        [infoDict setObject:@(NO) forKey:@"pushAnimation"];
        
        if (feed.liveShow.productClickType != KSFeedLiveProductClick_None) {
            if (feed.liveShow.productClickType == KSFeedLiveProductClick_Normal) {
                [infoDict setObject:@(KSLiteJumpTypeEcommerceDetail) forKey:kLiveShowOperationAfterLoad];
            } else if (feed.liveShow.productClickType == KSFeedLiveProductClick_Buy) {
                [infoDict setObject:@(KSLiteJumpTypeEcommerceBuy) forKey:kLiveShowOperationAfterLoad];
            }
        }
        feed.liveShow.productClickType = KSFeedLiveProductClick_None;
        
        [KSLiveShowModeTransferContext sharedManager].toRoomIdForRecommendTab = feed.liveShow.strLiveRoomId;
        [[KSNavigationManager sharedManager] watchLiveShow:feed.liveShow.strLiveRoomId roomInfo:nil feedModule:nil forcePop:YES isFromContribution:NO infoDict:infoDict];
        // 上报
        if ([self.feedReportDelegate respondsToSelector:@selector(onLiveFeedClickWithSimpleFeed:cell:)]) {
            [self.feedReportDelegate onLiveFeedClickWithSimpleFeed:feed cell:tableCell];
        }
        
    } else if (action.type == DRAWITEM_ACTION_TYPE_FEED_BIRTHDAY_SETTING) { // 生日设置
        
        KSPersonalGiftVC* personalGiftVC = [[KSPersonalGiftVC alloc] init];
        personalGiftVC.visitorUserInfo = [KSLoginManager sharedInstance].curUserInfo;
        KSUserInfo *masterUserInfo = [[KSUserInfo alloc] init];
        masterUserInfo.userId = [KSLoginManager sharedInstance].curUserInfo.userId;
        masterUserInfo.avatarUrl = [KSLoginManager sharedInstance].curUserInfo.avatarUrl;
        personalGiftVC.masterUserInfo = masterUserInfo;
        [personalGiftVC setRankIndex:PersonalGiftSpecialRank];
        [[[KSNavigationManager sharedManager] getMainPageNavController] pushViewController:personalGiftVC animated:YES];
        
    } else if (action.type == DRAWITEM_ACTION_TYPE_INTERACTGAME_STARTMATCH) {
        KSimpleFeed *simpleFeed = SAFE_CAST(action.busiData, KSimpleFeed);
        NSString *urlStr = simpleFeed.ktvRoomWebgameStatusInfo.jce_stComm.jce_strMatchURL;
        KLog(@"[互动游戏feed] 我也要玩 匹配链接: %@", urlStr);
        if (!IS_EMPTY_STR_BM(urlStr)) {
            [[KSNavigationManager sharedManager] dealWithScheme:urlStr];
        } else {
            // 兜底逻辑：进房
            if (simpleFeed.ktvRoomShow.jce_iRoomType & KSKTVRoomType_Social) {
                [self showSocialKtvRoomVC:simpleFeed];
            }
            else {
                [self showKtvRoomVC:simpleFeed];
            }
        }
        
        if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(onGameFeedClickButtonWithSimpleFeed:cell:)]) {
            [self.feedReportDelegate onGameFeedClickButtonWithSimpleFeed:simpleFeed cell:tableCell];
        }
    } else if (action.type == DRAWITEM_ACTION_TYPE_INTERACTGAME_JOINGAME) {
        KLog(@"[互动游戏feed] 立即加入");
        KSimpleFeed *simpleFeed = SAFE_CAST(action.busiData, KSimpleFeed);
        // 转到进房逻辑
        if (simpleFeed.ktvRoomShow.jce_iRoomType & KSKTVRoomType_Social) {
            [self showSocialKtvRoomVC:simpleFeed];
        }
        else {
            [self showKtvRoomVC:simpleFeed];
        }
        
        if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(onGameFeedClickButtonWithSimpleFeed:cell:)]) {
            [self.feedReportDelegate onGameFeedClickButtonWithSimpleFeed:simpleFeed cell:tableCell];
        }
    } else if (action.type == DRAWITEM_ACTION_TYPE_SOCIALGAMESHOWFEED) {
        KSimpleFeed *simpleFeed = SAFE_CAST(action.busiData, KSimpleFeed);
        KLog(@"[异步游戏feed] 跳转游戏: %@", simpleFeed.socialGameShow.jce_strURL);
        if (!IS_EMPTY_STR_BM(simpleFeed.socialGameShow.jce_strURL)) {
            [[KSNavigationManager sharedManager] dealWithScheme:simpleFeed.socialGameShow.jce_strURL];
        }
        
        if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(onGameFeedClickScreenAreaWithSimpleFeed:cell:)]) {
            [self.feedReportDelegate onGameFeedClickScreenAreaWithSimpleFeed:simpleFeed cell:tableCell];
        }
    } else if (action.type == DRAWITEM_ACTION_TYPE_SOCIALGAMECLICKGETAWARD) {
        KSimpleFeed *simpleFeed = SAFE_CAST(action.busiData, KSimpleFeed);
        KLog(@"[异步游戏feed] 点击赢取奖励: %@", simpleFeed.socialGameShow.jce_strURL);
        if (!IS_EMPTY_STR_BM(simpleFeed.socialGameShow.jce_strURL)) {
            [[KSNavigationManager sharedManager] dealWithScheme:simpleFeed.socialGameShow.jce_strURL];
        }
        if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(onGameFeedClickButtonWithSimpleFeed:cell:)]) {
            [self.feedReportDelegate onGameFeedClickButtonWithSimpleFeed:simpleFeed cell:tableCell];
        }
    } else if (action.type == DRAWITEM_ACTION_TYPE_INTERACTGAME_ENTERGAMELOBBY) {
        KSimpleFeed *simpleFeed = SAFE_CAST(action.busiData, KSimpleFeed);
        NSString *urlStr = !IS_EMPTY_STR_BM(simpleFeed.ktvRoomWebgameStatusInfo.jce_stComm.jce_strURL)? simpleFeed.ktvRoomWebgameStatusInfo.jce_stComm.jce_strURL :[simpleFeed.socialGameShow.jce_mapExt safeObjectForKey:@"jumpURL"];
        KLog(@"[游戏feed] 底部按钮点击: %@", urlStr);
        if (!IS_EMPTY_STR_BM(urlStr)) {
            [[KSNavigationManager sharedManager] dealWithScheme:urlStr];
        }
        
        if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(onGameFeedClickDeviceLabelWithSimpleFeed:cell:)]) {
            [self.feedReportDelegate onGameFeedClickDeviceLabelWithSimpleFeed:simpleFeed cell:tableCell];
        }
    } else if (action.type == DRAWITEM_ACTION_TYPE_REC_BOTTOM_BAR) {
        // 大卡片底部热门条
        KSimpleFeed *simpleFeed = SAFE_CAST(action.busiData, KSimpleFeed);
        [[KSNavigationManager sharedManager] dealWithScheme:simpleFeed.bottomBarInfo.jce_strJumpUrl];
        
        if ([self.feedReportDelegate respondsToSelector:@selector(onUgcFeedBottomBarClickWithSimpleFeed:cell:)]) {
            [self.feedReportDelegate onUgcFeedBottomBarClickWithSimpleFeed:simpleFeed cell:tableCell];
        }
    } else if (action.type == DRAWITEM_ACTION_TYPE_MINI_HEAT_OFFICIAL_TAG) {
        // do nothing
    } else if (action.type == DRAWITEM_ACTION_TYPE_RANKLIST_TAG) {
        // MARK: 排行榜
        NSString *rankListJumpUrl = SAFE_STR_BM(action.busiData);
        if (rankListJumpUrl) {
            [[KSNavigationManager sharedManager] dealWithScheme:rankListJumpUrl];
        }
    } else if (action.type == DRAWITEM_ACTION_TYPE_FEED_CLICK_AI_TAKE_PIC) {
        // MARK: AI拍同款
        KSimpleFeed *simpleFeed = SAFE_CAST(action.busiData, KSimpleFeed);
        NSString *hippyUrl = simpleFeed.AIPicSingInfo.strTemplateJumpUrl;
        [[KSNavigationManager sharedManager] dealWithScheme:hippyUrl];
        [self checkPauseWhenShowFullHippyView:hippyUrl];
    } else if (action.type == DRAWITEM_ACTION_TYPE_MAIL_GIFT) {
        // MARK: 送礼
        [self didTapSendGiftWithCell:tableCell didClickWithAction:action];
    } else {
        // MARK: 进详情页
        KSUITableView *conTable = nil;
        if (self.feedManagerDelegate && [self.feedManagerDelegate respondsToSelector:@selector(fetchInterTableView)])
        {
            conTable = [self.feedManagerDelegate fetchInterTableView];
        }
        if (!conTable)
        {
            return;
        }

        if (conTable.delegate && [conTable.delegate respondsToSelector:@selector(tableView:didSelectRowAtIndexPath:)])
        {
            [conTable.delegate tableView:conTable didSelectRowAtIndexPath:tableCell.indexPath];
        }
    }
}

- (void)didTapSendGiftWithCell:(KSLayoutableTableCell *)tableCell didClickWithAction:(KSDrawItemAction *)action {
    KSimpleFeed *feed = (KSimpleFeed *)action.busiData;
    if (action.type == DRAWITEM_ACTION_TYPE_HEAT_CARD_DIFF) {
        if ([KSABTestManager sharedManager].heatEventType == KSABTestDiffHeatCardEventTypePopGiftContainer) {
            // 拉起礼物面板 定位到热度卡
            self.isShowHeatBtnGiftContainer = YES;
            [self reportRecCardHeatCardIconClick:feed event:1];
        }
        else if ([KSABTestManager sharedManager].heatEventType == KSABTestDiffHeatCardEventTypeBuyCard) {
            // 直接跳热度卡购买
            [[KSNavigationManager sharedManager] dealWithScheme:feed.cellExtraInfo.jce_strHeatCardBuyUrl];
            [self reportRecCardHeatCardIconClick:feed event:3];
            return;
        }
    }
        
    if (self.feedManagerDelegate && [self.feedManagerDelegate respondsToSelector:@selector(hideGiftGuideView)])
    {
        [self.feedManagerDelegate hideGiftGuideView];
    }
    //送礼(3.3后送花从送礼独立出来)
    if (![[KSNetStatusManager sharedManager] IsEnableInternet])
    {
        [self tempAlert:@"网络不可用，请检查网络设置"];
        return;
    }

    if (feed.isMusicMoodFeed && feed.isRefUGCUnavailable)
    {
        [self tempAlert:@"作品无法查看，暂不支持此操作"];
        return;
    }
    self.sendGiftSimpleFeed = feed;
    
    
    if (action.type == DRAWITEM_ACTION_TYPE_SEND_PRESENT_Direct) {
        [self shortSendGiftDirectelyTimelineSimpleFeed:feed];
    } else {
        [self showGiftContainerViewWidthTimelineSimpleFeed:feed];
    }
    
    if (self.feedManagerDelegate && [self.feedManagerDelegate respondsToSelector:@selector(didTapGiftButton:)]) {
        [self.feedManagerDelegate didTapGiftButton:feed];
    }
    
    if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(traceReportSendGiftClickedWithAction:simpleFeed:)])
    {
        [self.feedReportDelegate traceReportSendGiftClickedWithAction:action simpleFeed:feed];
    }
    
    if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(onFeedClickGiftButtonWithAction:simpleFeed:cell:)])
    {
        [self.feedReportDelegate onFeedClickGiftButtonWithAction:action simpleFeed:feed cell:tableCell];
    }
}

// 跳转歌房详情
- (void)showKtvRoomVC:(KSimpleFeed *)feed
{
    KLog(@"[跳转歌房] roomId = %@", feed.ktvRoomShow.jce_strRoomId);
    if (feed.ktvRoomShow && feed.ktvRoomShow.jce_strRoomId && feed.ktvRoomShow.jce_strRoomId.length>0)
    {
        
        KSNavReportModel* reportModel = nil;
        if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(getNavigationReportModelForKTVShow)])
             {
                  reportModel = [self.feedReportDelegate getNavigationReportModelForKTVShow];
             }
        
        NSString *fromRoute = @"unknown";
        UIViewController* topVC = [[KSNavigationManager sharedManager] getTopViewController];
        // 转发类的取forwardFeed，非转发类的普通，取simpleUser
        KSUidType toUid = feed.forwardFeed.strForwardId > 0 ? feed.forwardFeed.feedUserInfo.userinfo.userId : feed.simpleUser.userinfo.userId;
        if (topVC && [topVC isKindOfClass:[KSUserProfileVC_V2 class]]) {
            BOOL isGuest = (toUid != [KSLoginManager sharedInstance].curUserInfo.userId);
            NSString *key = isGuest ? @"homepage_guest#feed_tab#null" : @"homepage_me#feed_tab#null";
            fromRoute = [NSString stringWithFormat:@"%@#%lld", key, toUid];
        } else if (reportModel) {
            fromRoute = [NSString stringWithFormat:@"%@#%lld",reportModel.fromPage, toUid];
        }
        
        NSMutableDictionary *infoDic = [NSMutableDictionary dictionaryWithCapacity:0];
        [infoDic safeSetObject:fromRoute forKey:PushNotification_KtvFromRoute];
        [infoDic safeSetObject:NSIToString(feed.ktvRoomShow.jce_lRelationId) forKey:PushNotification_RelationId];
        [infoDic safeSetObject:feed.ktvRoomShow.jce_strShowId forKey:PushNotification_ShowId];
        BOOL isInteratGameInGroup = [[feed.ktvRoomWebgameStatusInfo.jce_stComm.jce_mapExt safeObjectForKey:@"uStatus"] integerValue] == 1;
        [infoDic safeSetObject:NSIToString(isInteratGameInGroup) forKey:PushNotification_InteractGameAutoJoin];

        [[KSNavigationManager sharedManager] showKTVRoomVCWithOwnerUid:0
                                                                roomid:feed.ktvRoomShow.jce_strRoomId
                                                                passwd:@""
                                                              forcePop:YES
                                                               ktvFrom:!IS_EMPTY_STR_BM(reportModel.ktvFrom) ? reportModel.ktvFrom : @"14"
                                                              fromPage:reportModel.fromPage
                                                                  info:infoDic];
    }
}

- (void)showSocialKtvRoomVC:(KSimpleFeed *)feed
{
    NSString *shareId = [NSString stringWithFormat:@"%lld", feed.simpleUser.userinfo.userId];
    KLog(@"[跳转欢聚歌房] shareId = %@", shareId);
    if (feed.ktvRoomShow)
    {
        KSNavReportModel* reportModel = nil;
               if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(getNavigationReportModelForKTVShow)])
        {
            reportModel = [self.feedReportDelegate getNavigationReportModelForKTVShow];
        }
        
        NSString *fromRoute = @"unknown";
        UIViewController* topVC = [[KSNavigationManager sharedManager] getTopViewController];
        // 转发类的取forwardFeed，非转发类的普通，取simpleUser
        KSUidType toUid = feed.forwardFeed.strForwardId > 0 ? feed.forwardFeed.feedUserInfo.userinfo.userId : feed.simpleUser.userinfo.userId;
        if (topVC && [topVC isKindOfClass:[KSUserProfileVC_V2 class]]) {
            BOOL isGuest = (toUid != [KSLoginManager sharedInstance].curUserInfo.userId);
            NSString *key = isGuest ? @"homepage_guest#feed_tab#null" : @"homepage_me#feed_tab#null";
            fromRoute = [NSString stringWithFormat:@"%@#%lld", key, toUid];
        } else if (reportModel) {
            fromRoute = [NSString stringWithFormat:@"%@#%lld",reportModel.fromPage, toUid];
        }
        
        BOOL isInteratGameInGroup = [[feed.ktvRoomWebgameStatusInfo.jce_stComm.jce_mapExt safeObjectForKey:@"uStatus"] integerValue] == 1;
        NSMutableDictionary *socialInfo = [[NSMutableDictionary alloc] init];
        [socialInfo safeSetObject:shareId forKey:@"shareId"];
        [socialInfo safeSetObject:fromRoute forKey:PushNotification_KtvFromRoute];
        [socialInfo safeSetObject:NSIToString(isInteratGameInGroup) forKey:PushNotification_InteractGameAutoJoin];
        
        [[KSNavigationManager sharedManager] showSocialKtvWithKtvRoomShow:feed.ktvRoomShow ktvFrom:@"20" info:socialInfo];
    }
}

/// 填充算法字段到scheme
/// 注意：需要对算法字段进行 encode 处理，防止算法字段包含保留字符，如：" ! * ' ( ) ; : @ & = + $ , / ? # [ ] "，
/// 在执行 dealWithScheme 跳转，KSPortalPreCheck 类内部会在 ksportalPreCheck 方法中将 URL 中的参数进行 decode 操作
- (NSString *)fillAlgorithmDataWithUrl:(NSString *)ktvSingJumpUrl algorithmDic:(NSDictionary *)algorithmDic
{
    NSMutableDictionary *queryDic = [NSMutableDictionary dictionaryWithCapacity:0];
    if ([ktvSingJumpUrl rangeOfString:PushNotification_ItemType].location == NSNotFound) {
        if ([algorithmDic safeObjectForKey:PushNotification_ItemType]) {
            NSString *encodeItemType = [[algorithmDic safeObjectForKey:PushNotification_ItemType] stringByAddingPercentEscapesUsingEncoding:NSUTF8StringEncoding];
            [queryDic safeSetObject:encodeItemType forKey:PushNotification_ItemType];
        }
    }
    
    if ([ktvSingJumpUrl rangeOfString:PushNotification_TraceID].location == NSNotFound) {
        if ([algorithmDic safeObjectForKey:PushNotification_TraceID]) {
            NSString *encodeTraceID = [[algorithmDic safeObjectForKey:PushNotification_TraceID] stringByAddingPercentEscapesUsingEncoding:NSUTF8StringEncoding];
            [queryDic safeSetObject:encodeTraceID forKey:PushNotification_TraceID];
        }
    }
    
    if ([ktvSingJumpUrl rangeOfString:PushNotification_AlgorithmType].location == NSNotFound) {
        if ([algorithmDic safeObjectForKey:PushNotification_AlgorithmType]) {
            NSString *encodeAlgorithmType = [[algorithmDic safeObjectForKey:PushNotification_AlgorithmType] stringByAddingPercentEscapesUsingEncoding:NSUTF8StringEncoding];
            [queryDic safeSetObject:encodeAlgorithmType forKey:PushNotification_AlgorithmType];
        }
    }
    
    if ([ktvSingJumpUrl rangeOfString:PushNotification_AlgorithmID].location == NSNotFound) {
        if ([algorithmDic safeObjectForKey:PushNotification_AlgorithmID]) {
            NSString *encodeAlgorithmID = [[algorithmDic safeObjectForKey:PushNotification_AlgorithmID] stringByAddingPercentEscapesUsingEncoding:NSUTF8StringEncoding];
            [queryDic safeSetObject:encodeAlgorithmID forKey:PushNotification_AlgorithmID];
        }
    }
    
    NSURL *theUrl = [[NSURL URLWithString:ktvSingJumpUrl] ksjs_URLByAppendingQueryComponentNamed:queryDic];
    return [theUrl absoluteString];
}

// 去录歌前记一下 ugcid
- (void)markFromUgcIDInKeyPathBeforeReocrd:(KSimpleFeed *)feed
{
    if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(markFromUgcIDInKeyPathBeforeReocrd:)])
    {
        [self.feedReportDelegate markFromUgcIDInKeyPathBeforeReocrd:feed];
    }
}

- (void)goToSing:(KSimpleFeed *)simpleFeed
{
    KSTimelineDetail * timelineDetail = [KSUgcPlayManager sharedManager].currentUgcDetail;
    KSong *song = simpleFeed.songForGotoSingAction;
    
    // 话题信息
    KSTopicSummaryInfo *topicInfo;
    JceTimeline_s_topic *jceTopic = simpleFeed.topicsArray.firstObject;
    if (jceTopic)
    {
        topicInfo = [KSTopicSummaryInfo topicSummaryInfoWithTimelineFeed:jceTopic];
    }
    
    if ([simpleFeed isKindOfAIImageFeed]) {
        /// Ai一键图文作品
        NSString *jumpURL = @"qmkege://kege.com?action=kuikly&url=https%3A%2F%2Fkg.qq.com%3Fkuikly%3DNearInfoEditPage";
        [[KSNavigationManager sharedManager] dealWithScheme:jumpURL];
    }
    else if (simpleFeed.needShowRecAITakePic) {
        /// AI拍同款
        NSString *jumpURL = simpleFeed.AIPicSingInfo.strTemplateJumpUrl;
        [[KSNavigationManager sharedManager] dealWithScheme:jumpURL];
        [self checkPauseWhenShowFullHippyView:jumpURL];
    }
    else if ((song.ugcMaskExt & JceTimeline_Detail_KGE_UGC_MASK_EXT_INTERACTIVE) == JceTimeline_Detail_KGE_UGC_MASK_EXT_INTERACTIVE)
    {
        // 玩法合唱
        KSRecordingNormalStrategy *normalStrategy = [[KSRecordingNormalStrategy alloc] init];
        normalStrategy.topicInfo = topicInfo;
        normalStrategy.recordingData.song = song;
        normalStrategy.recordStrategyReport.from_page = @"feed#creation#sing_button#click#0";

        if (simpleFeed.songinfo.isSegment)
        {
            normalStrategy.state.beginTime = simpleFeed.songinfo.chorusSegmentStart;
            normalStrategy.state.endTime = simpleFeed.songinfo.chorusSegmentEnd;
        }

        if (simpleFeed.songinfo.audioTransVideoPlayType == JceTimeline_EnumAudioTransVideoPlayType_eAudioTransVideo_type_native_tempalte_video)
        {
            KSRecordingNormalStrategy* normalStrategy = [[KSRecordingNormalStrategy alloc] initWithSong:song timelineDetail:timelineDetail];
            normalStrategy.topicInfo = topicInfo;
            normalStrategy.recordStrategyReport.from_page = @"feed#creation#sing_button#click#0";
            [[KSNavigationManager sharedManager] singSongWithStrategy:normalStrategy];
        }
        else
        {
            KSFilmingSource source;
            if (simpleFeed.songinfo.uPlayTemplateId > 0)
            {
                source = KSFilmingSourcePlayTemplate;
                normalStrategy.recordingType = FilmingMvPlayTemplete;
                
                if (timelineDetail.originTopic == nil)
                {
                    timelineDetail.originTopic = [[JceTimeline_Detail_UgcTopic alloc] init];
                }
                timelineDetail.originTopic.jce_uPlayTemplateId = (JceUInt32)simpleFeed.songinfo.uPlayTemplateId;
                timelineDetail.originTopic.jce_stPropsInfo = simpleFeed.songinfo.propsInfo;
                timelineDetail.originTopic.jce_activity_id = (JceInt32)simpleFeed.songinfo.activityId;
            }
            else
            {
                source = KSFilmingSourceMv;
                normalStrategy.recordingType = FilmingMv;
            }

            KSFilmingModel *filmingModel = [KSFilmingModel modelWithSource:source
                                                                  strategy:normalStrategy
                                                            publishContent:nil
                                                            timelineDetail:timelineDetail.copy];

            KSFilmingViewController *filmingViewController = [[KSFilmingViewController alloc] initWithFilmingModel:filmingModel];
            [[[KSNavigationManager sharedManager] getMainPageNavController] pushViewController:filmingViewController animated:NO];
        }
    }
    else if (UGC_HALF_CHORUS(song.ugcMask) == HALF_CHORUS_UGC || UGC_FAVOR_CHORUS(song.ugcMask) == FAVOR_CHORUS_UGC || UGC_FIN_CHORUS(song.ugcMask) == FIN_CHORUS_UGC)
    {
        // 加入合唱
        NSString *ugcId = simpleFeed.songinfo.strHalfUgcId;
        KSRecordingStrategyBase *joinStrategy = nil;
        if ((song.ugcMaskExt & JceTimeline_Detail_KGE_UGC_MASK_EXT_BIT_KGE_UGC_MASK_EXT_SINGLE_HC) == JceTimeline_Detail_KGE_UGC_MASK_EXT_BIT_KGE_UGC_MASK_EXT_SINGLE_HC)
        {
            // 普通ugc加入合唱作品
            ugcId = simpleFeed.songinfo.strHalfUgcId;
            joinStrategy = [KSRecordingJoinNormalUgcChorusStrategy new];
            ((KSRecordingJoinNormalUgcChorusStrategy*)joinStrategy).shouldIgnorePrivateSetting = NO;
        }
        else if (UGC_HALF_CHORUS(song.ugcMask))
        {
            // 半成品
            ugcId = simpleFeed.simpleFeedCommon.strFeedId;
            joinStrategy = [[KSRecordingJoinChorusStrategy alloc] init];
            ((KSRecordingJoinChorusStrategy*)joinStrategy).shouldIgnorePrivateSetting = YES;
        }
        else if (UGC_FIN_CHORUS(song.ugcMask) || UGC_FAVOR_CHORUS(song.ugcMask))
        {
            // 成品
            ugcId = simpleFeed.songinfo.strHalfUgcId;
            joinStrategy = [[KSRecordingJoinChorusStrategy alloc] init];
            ((KSRecordingJoinChorusStrategy*)joinStrategy).shouldIgnorePrivateSetting = NO;
        }
        else
        {
            KLog(@"Feed %@ 大卡片 加入合唱 作品UGCMASK错误", simpleFeed);
            return;
        }
        
        if (IS_EMPTY_STR_BM(ugcId))
        {
            KLog(@"Feed %@ 大卡片 加入合唱 作品UGCID错误", simpleFeed);
            return;
        }
        
        joinStrategy.topicInfo = topicInfo;
        joinStrategy.recordingData.ugc.userInfo = simpleFeed.simpleUser.userinfo;
        joinStrategy.recordingData.ugc.ugcId = ugcId;
        //[869746029]
        joinStrategy.recordStrategyReport.from_page = @"feed#creation#sing_button#click#0";

        if (UGC_TYPE(song.ugcMask) == MUSIC_VIDEO_UGC)
        {
            joinStrategy.state.chorusScenario = Video_ChorusScenario;
            // 补充同框合唱模板ID
            JceTimeline_Detail_UgcTopic *topic = joinStrategy.recordingData.ugc.originTopic;
            JceUInt32 templatID = (JceUInt32)simpleFeed.songinfo.uPlayTemplateId;
            topic.jce_uPlayTemplateId = templatID;
        }
        else
        {
            joinStrategy.state.chorusScenario = Normal_ChorusScenario;
        }
        
        joinStrategy.recordingData.song = song;
        joinStrategy.recordingData.iHaveGift = simpleFeed.hcCellItem.jce_iHasGift;
        [[KSNavigationManager sharedManager] singSongWithStrategy:joinStrategy];
    }
    else if ((song.ugcMask & JceTimeline_Detail_KGE_UGC_MASK_BIT_KGE_UGC_MASK_QC_SONG) && IS_EMPTY_STR_BM(simpleFeed.songinfo.strQcRefKSongMid))
    {
        //清唱短视频则进二次创作 判断条件：1:短视频并且是清唱ugc  2: 清唱原声伴奏标识位
        if (UGC_IS_SHORT_VIDEO(song.ugcMask) || [song isCappellaSong:song.songMid])
        {
            KSRecordingNormalStrategy *normalStrategy = [[KSRecordingNormalStrategy alloc] init];
            normalStrategy.recordingType = FilmingCappellaMv;

            KSFilmingModel *filmingModel = [KSFilmingModel modelWithSource:KSFilmingSourceCappella strategy:normalStrategy publishContent:nil timelineDetail:nil];
            KSFilmingViewController *filmingViewController = [[KSFilmingViewController alloc] initWithFilmingModel:filmingModel];
            [[[KSNavigationManager sharedManager] getMainPageNavController] pushViewController:filmingViewController animated:NO];
        }
        else
        {
            // 清唱
            [[KSNavigationManager sharedManager] showCappllaVCWithToRecordFromPage:0 contestId:0 writeRecordFromPage:@"feed#creation#sing_button#click#0" strategyBlock:nil];
        }
    }
    else if ([song isPoetry] || (song.ugcMaskExt & JceTimeline_Detail_KGE_UGC_MASK_EXT_BIT_KGE_UGC_MASK_EXT_RECITE_TXT) || (song.ugcMaskExt & JceTimeline_Detail_KGE_UGC_MASK_EXT_BIT_KGE_UGC_MASK_EXT_RECITE_QC))
    {
        // 诗朗诵
        if ([KSong isPoetryDefaultSong:song.songMid])
        {
            KSPoetryRecordingStrategy *strategy = [[KSPoetryRecordingStrategy alloc] init];
            strategy.topicInfo = topicInfo;
            strategy.recordingData.song = [KSong poetryDefaultSong];
            strategy.recordStrategyReport.from_page = @"feed#creation#sing_button#click#0";
            [[KSNavigationManager sharedManager] recitePoetryWithStrategy:strategy];
        }
        else
        {
            KSPoetryRecordingStrategy *strategy = [[KSPoetryRecordingStrategy alloc] init];
            strategy.topicInfo = topicInfo;
            strategy.recordingData.song = song;
            strategy.recordStrategyReport.from_page = @"feed#creation#sing_button#click#0";
            [[KSNavigationManager sharedManager] recitePoetryWithStrategy:strategy];
        }
    }
    else if (song.ugcMaskExt & JceTimeline_Detail_KGE_UGC_MASK_EXT_ADAPT_SONG)
    {
        // 翻唱改编跳hippy
        [[KSNavigationManager sharedManager] dealWithScheme:[NSString stringWithFormat:@"http://kg.qq.com?hippy=accompainment&mid=%@&ugcid=%@&source=1",song.songMid,simpleFeed.simpleFeedCommon.strFeedId]];
    }
    else if(simpleFeed.songinfo.propsInfo.jce_uLightPropsId > 0 || simpleFeed.songinfo.propsInfo.jce_uShangtangPropsId > 0)
    {
        KSRecordingNormalStrategy *normalStrategy = [[KSRecordingNormalStrategy alloc] init];
        normalStrategy.topicInfo = topicInfo;
        normalStrategy.recordingData.song = song;
        normalStrategy.recordingType = FilmingMv;
        normalStrategy.recordStrategyReport.from_page = @"feed#creation#sing_button#click#0";

        if (simpleFeed.songinfo.isSegment)
        {
            normalStrategy.state.beginTime = simpleFeed.songinfo.chorusSegmentStart;
            normalStrategy.state.endTime = simpleFeed.songinfo.chorusSegmentEnd;
        }
        
        if (timelineDetail.originTopic == nil)
        {
            timelineDetail.originTopic = [[JceTimeline_Detail_UgcTopic alloc] init];
        }
        
        timelineDetail.originTopic.jce_stPropsInfo = simpleFeed.songinfo.propsInfo;
        timelineDetail.originTopic.jce_activity_id = (JceInt32)simpleFeed.songinfo.activityId;
        
        KSFilmingModel *filmingModel = [KSFilmingModel modelWithSource:KSFilmingSourceMv
                                                              strategy:normalStrategy
                                                        publishContent:nil
                                                        timelineDetail:timelineDetail.copy];

        KSFilmingViewController *filmingViewController = [[KSFilmingViewController alloc] initWithFilmingModel:filmingModel];
        [[[KSNavigationManager sharedManager] getMainPageNavController] pushViewController:filmingViewController animated:NO];
    }
    else if ([simpleFeed isAddStudySingFeed])//学唱跳转
    {
        if ([simpleFeed isAlreadyStudySing])//已经加入学唱
        {
            KLog(@"Feed isAlreadyStudySing %@",simpleFeed);
            return;
        }
        [KSStudySingReport want_learn_button_click:simpleFeed];
        self.studySingManager = [KSStudySingManager manager];
        self.studySingManager.simpleFeed = simpleFeed;
        self.studySingManager.vc = self.currentVC;
        self.studySingManager.song = song;
        [self.studySingManager addMidToStudy:song.songMid];
    }
    else if ([simpleFeed isChorusWithItFeed])//跟他合唱
    {
        KSRecordingJoinNormalUgcChorusStrategy *strategy = [KSRecordingJoinNormalUgcChorusStrategy new];
        
        BOOL shouldIgnorePrivacySetting = NO;
        // isSoloSongUgc 是否是独唱作品
        // ufavorChorus 而且不能是收录作品
        if (timelineDetail.isSoloSongUgc && !timelineDetail.ufavorChorus)
        {
            // 能进这里，代表用户能看到是独唱作品的详情页
            // 如果是公开作品就应该让用户加入合唱
            // 如果是私密作品那么用户应该是通过分享渠道进入到了详情页
            // 所以这种情况下，应该放开隐私权限的校验
            shouldIgnorePrivacySetting = YES;
        }
        
        strategy.shouldIgnorePrivateSetting = shouldIgnorePrivacySetting;
        // 判断是不是一个合唱成品
        // 注意中里需要判断它是不是一个成品/收录作品  不是判断是不是半成品  因为独唱作品不会被归到成品
        strategy.recordingData.ugc.ugcId = (timelineDetail.ufinChorus || timelineDetail.ufavorChorus) ? timelineDetail.hcExtraInfo.strHcHalfUgcid : timelineDetail.ugcId;
        strategy.recordingData.ugc.userInfo = timelineDetail.userInfo;
        strategy.recordingData.iHaveGift = timelineDetail.hcGiftInfo.jce_iHaveGift;
        strategy.recordStrategyReport.from_page = @"feed#creation#sing_button#click#0";
        
        
        NSString *commonInt1 = @"1";
        NSString *touid = [NSString stringWithFormat:@"%lld", timelineDetail.userInfo.userId];
        // 根据音视频设置模式
        if (timelineDetail.ugcMask == MUSIC_VIDEO_UGC)
        {
            // 视频合唱
            strategy.state.chorusScenario = Video_ChorusScenario;
            commonInt1 = @"2";
        }
        else if (timelineDetail.ugcMask == AUDIO_UGC)
        {
            // 音频合唱
            strategy.state.chorusScenario = Normal_ChorusScenario;
        }
        
        strategy.recordStrategyReport.otherReportParam = [NSString stringWithFormat:@"commonInt1=1&touid=%@&ugcid=%@&mid=%@", touid, timelineDetail.ugcId, timelineDetail.ksongMid];
        
        KSong* song = [[KSong alloc] init];
        song.name = timelineDetail.songName;
        song.pureName = timelineDetail.songNamePure;
        song.songMid = timelineDetail.ksongMid;
        song.fileMid2 = timelineDetail.fileMid;
        song.singerName = timelineDetail.singerName;
        strategy.recordingData.song = song;
        
        [[KSNavigationManager sharedManager] singSongWithStrategy:strategy];
    }
    else
    {
        // 普通
        KSRecordingNormalStrategy* normalStrategy = [[KSRecordingNormalStrategy alloc] initWithSong:song timelineDetail:timelineDetail];
        normalStrategy.topicInfo = topicInfo;
        normalStrategy.recordStrategyReport.from_page = @"feed#creation#sing_button#click#0";
        [[KSNavigationManager sharedManager] singSongWithStrategy:normalStrategy];
    }
}

#pragma mark 获取action中的userInfo
-(KSUserInfo*)getUserInfoWithAction:(KSDrawItemAction*)action
{
    KSUserInfo *user = nil;
    KSimpleFeed* feed = nil;
    if ([action.busiData isKindOfClass:[KSUserInfo class]])
    {
        user = SAFE_CAST(action.busiData, KSUserInfo);
    }
    else if ([action.busiData isKindOfClass:[KSCellUser class]])
    {
        KSCellUser *cellUser = (KSCellUser*)action.busiData;
        user = [[KSUserInfo alloc] init];
        user.userId = cellUser.userId;
        user.avatarTimestamp = cellUser.timeStamp;
        user.nickName = cellUser.nickName;
    }
    else if ([action.busiData isKindOfClass:[KSimpleFeed class]])
    {
        feed = SAFE_CAST(action.busiData, KSimpleFeed);
        user = feed.simpleUser.userinfo;
        if (feed.feedAd)
        {
            user = [KSUserInfo new];
            user.nickName = feed.feedAd.jce_advertiserInfo.jce_name;
        }else if(feed.forwardFeed){
            user = feed.forwardFeed.feedUserInfo.userinfo;
        }
    }
    else if ([action.busiData isKindOfClass:[JceTimeline_s_user class]])
    {
        JceTimeline_s_user *sUser = (JceTimeline_s_user *)action.busiData;
        user = [[KSJceModelBridge sharedInstance] translateFeedUserToKSUser:sUser];
    }
    return user;
}

#pragma mark 跳个人页
/// isSendNearByMsg:是否在进入个人主页后点击私信自动发送来自同城消息

- (void)showPersonalVC:(KSimpleFeed*)feed withUserId:(KSUserInfo*)user isSendNearByMsg:(BOOL)isSendNearByMsg
{
    NSString *fromPage = nil;
    if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(getUserProfileFromPageForReport:)])
    {
        fromPage = [self.feedReportDelegate getUserProfileFromPageForReport:feed];
    }
    
    NSDictionary *extraInfo = nil;
    NSInteger eReportSource = [feed getReportSourceForContribute];
    
    if (eReportSource > 0)
    {
        extraInfo = @{
            @"source": @(eReportSource),
            @"isSendNearByMsg"  :@(isSendNearByMsg),
        };
        
    }
    // 跳转个人主页
    KSReportRecommendItem *recItem = [KSJceModelBridge translateToReportRecItemFromFeedRecItem:feed.recommendItem];
    [[KSNavigationManager sharedManager] showProfileView:user.userId fromPage:fromPage withRectItme:recItem extraInfo:extraInfo];
}

// 获取ktvFromRoute
- (NSString *)getKTVBreatheAvatarStr15:(KSimpleFeed *)feed
{
    NSString *ktvFromRoute = @"";
    if (feed.feedVCType == KSimpleFeedSourceType_UserProfile) {
        if (feed.isGuest) {
            ktvFromRoute = @"homepage_guest#personal_information#avatar";
        } else {
            ktvFromRoute = @"homepage_me#creation#KTV_breathe_avatar";
        }
    } else if (feed.feedVCType == KSimpleFeedSourceType_TimelineRoot) {
        ktvFromRoute = @"feed_nearby#nearby_people#avatar_name_area";
    } else if (feed.feedVCType == KSimpleFeedSourceType_LocationFeed) {
        ktvFromRoute = @"feed_nearby#creation#uploader";
    }
    return ktvFromRoute;
}

#pragma mark -

/// 异化看视频送礼物按钮点击
- (void)freeAdGiftButtonDidClickWithFeed:(KSimpleFeed *)feed {
    KLog(@"feed freeAd click");
    
    // TOOD: @neo 判断rec或follow
    KSAdFreeGiftScene *scene = [KSAdFreeGiftScene sceneWithType:KSAdFreeGiftSceneType_FollowFeed];
    scene.vc = [[KSNavigationManager sharedManager] getTopViewController];
    if ([self.feedReportDelegate respondsToSelector:@selector(getCurrentSelectVCReportString)]) {
        scene.from_page = [self.feedReportDelegate getCurrentSelectVCReportString];
    }
    KS_WEAK_SELF(self);
    scene.sendBlock = ^(KSIapGift *gift, NSInteger num) {
        CHECK_SELF_AND_RETURN();
        self.currentQuickGiftFeed = feed;
        KSTimelineDetail *timelineDetail = [[KSTimelineDetail alloc] init];
        timelineDetail.ugcId = feed.simpleFeedCommon.strFeedId;
        self.giftBridge.timelineDetail = [timelineDetail toGiftTimelineDetail];
        
        KSBaseSendGiftInfo *info = [KSBaseSendGiftInfo createWithGift:gift giftCount:num sendStyle:KSGiftBridgeSendStyleDirectly receiverUid:feed.simpleUser.userinfo.userId statFrom:JceIapGift_CONSUME_LOCATION_E_FEED_CONSUME];
        [KSGiftBridgeHelper fillSponsorUserInfoForGift:gift];
        info.shouldIgnoreKBBanlance = NO; // 送礼前忽略余额检查
        if ([feed isFollowTabFeed]) {
            info.reportPosid = BuyKBPid_FEED_QUICK_SENDGIFT_GUIDE;
        }
        UIViewController *topVC = [[KSNavigationManager sharedManager] getTopViewController];
        [self.giftBridge sendGiftWithInfo:info atView:topVC.view];
    
        if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(onFeedQuickGiftClickWithSimpleFeed:quickGiftGuide:)]) {
            [self.feedReportDelegate onFeedQuickGiftClickWithSimpleFeed:feed quickGiftGuide:feed.giftGuide];
        }
        
        // 发送礼成功通知，刷新feed
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            NSDictionary *params = @{
                @"ugcId": SAFE_STR_BM(feed.simpleFeedCommon.strFeedId),
                @"iapGift": gift,
                @"count": @(gift.giftNum),
                @"giftType": @(gift.type),
                @"isAnonymous": @([KSIapGiftManager defaultManager].curSendGiftAnonymousStatus)
            };
            KLog(@"feed freeAd send noti:%@",params);
            [[NSNotificationCenter defaultCenter] postNotificationName:KS_IAP_GIFT_SEND_GIFT_SUCC_NOTIFICATION object:nil userInfo:params];
        });
    };
    scene.endBlock = ^{
        CHECK_SELF_AND_RETURN();
        self.adFreeGiftManager = nil;
    };
    // 跳转看广告
    self.adFreeGiftManager = [KSAdFreeGiftManager managerWithScene:scene];
    [self.adFreeGiftManager jumpToShowAd];
}

- (void)quickGiftButtonDidClickWithFeed:(KSimpleFeed *)feed
{
    self.currentQuickGiftFeed = feed;
    
    KSTimelineDetail *timelineDetail = [[KSTimelineDetail alloc] init];
    timelineDetail.ugcId = feed.simpleFeedCommon.strFeedId;
    self.giftBridge.timelineDetail = [timelineDetail toGiftTimelineDetail];
    
    //直接走作品送礼
    KSStatFrom statFrom = JceIapGift_CONSUME_LOCATION_E_FEED_CONSUME;
    KSIapGift *gift = nil;
    BOOL isbonusGift = NO;
    // 主人态没有优先级1
    if (feed.giftGuide.backpackGift) {
        gift = [[KSIapGift alloc] initWithProtoLotteryGift:feed.giftGuide.backpackGift];
    } else if (feed.giftGuide.kbGift) {
        gift = [[KSIapGift alloc] initWithProtoGift:feed.giftGuide.kbGift];
    } else if (feed.giftGuide.bonusGift) {
        gift = [[KSIapGift alloc] initWithProtoGift:feed.giftGuide.bonusGift];
        isbonusGift = YES;
    } else {
        KINFO(@"[QGB] 礼物数据缺失");
        return;
    }
    
    if (isbonusGift) {
        showReportSceneType scenType = 0;
        if ([self.feedReportDelegate respondsToSelector:@selector(getRewardSendGiftSource)]) {
            scenType = [self.feedReportDelegate getRewardSendGiftSource];
        }
        proto_new_gift_Gift *bonusGift = feed.giftGuide.bonusGift;
        JceIapGift_BonusConsumeUgc *consumeUgc = [[JceIapGift_BonusConsumeUgc alloc] init];
        consumeUgc.jce_strUgcId = feed.simpleFeedCommon.strFeedId;
        self.rewardSendGiftVC.QUICK_SCENE = proto_quick_gift_webapp_QUICK_SCENE_EN_ASYNC_UGC_DETAIL_FEED;
        [self.rewardSendGiftVC showRewardsAlertViewWithShowType:RewardsDirectSendGiftType
                                                  sendGiftUfrom:JceIapGift_CONSUME_LOCATION_E_FEED_CONSUME
                                                    consumeType:JceIapGift_emBonusConsumeType_BONUS_CONSUME_GIFTUGC
                                                      sceneType:scenType
                                                   bonusConsume:consumeUgc
                                                          toUid:feed.simpleUser.userinfo.userId
                                                     simpleFeed:nil];
        self.rewardSendGiftVC.configGift = bonusGift;
        [self.rewardSendGiftVC updateGiftID:bonusGift.jce_uGiftId
                                    logoStr:bonusGift.jce_strLogo
                                    kbPrice:bonusGift.jce_uPrice
                                 bonusPrice:bonusGift.jce_uBonusPrice];
        if (!IS_EMPTY_STR_BM(feed.giftGuide.buttoneStrategyStr)) {
            // 更新送礼按钮文案
            [self.rewardSendGiftVC updateGiftDescText:feed.giftGuide.buttoneStrategyStr];
        }
        
        KS_WEAK_SELF(self);
        [self.rewardSendGiftVC queryDefaultRewards:^(BOOL isDefault) {
            CHECK_SELF_AND_RETURN();
            if (isDefault) {
                [self.rewardSendGiftVC rewardGiftBtnDidClick:nil];
            } else {
                [KSendRewardGiftAlertViewController showRewardsAlertView:self.rewardSendGiftVC];
            }
        }];
    } else {
        KSBaseSendGiftInfo *info = [KSBaseSendGiftInfo createWithGift:gift giftCount:1 sendStyle:KSGiftBridgeSendStyleDirectly receiverUid:feed.simpleUser.userinfo.userId statFrom:statFrom];
        [KSGiftBridgeHelper fillSponsorUserInfoForGift:gift];
        info.shouldIgnoreKBBanlance = NO; // 送礼前忽略余额检查
        if ([feed isFollowTabFeed]) {
            info.reportPosid = BuyKBPid_FEED_QUICK_SENDGIFT_GUIDE;
        }
        UIViewController *topVC = [[KSNavigationManager sharedManager] getTopViewController];
        [self.giftBridge sendGiftWithInfo:info atView:topVC.view];
    }
    
    if (self.feedReportDelegate && [self.feedReportDelegate respondsToSelector:@selector(onFeedQuickGiftClickWithSimpleFeed:quickGiftGuide:)]) {
        [self.feedReportDelegate onFeedQuickGiftClickWithSimpleFeed:feed quickGiftGuide:feed.giftGuide];
    }
    
    // 发送礼成功通知，刷新feed
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        NSDictionary *params = @{
            @"ugcId": SAFE_STR_BM(feed.simpleFeedCommon.strFeedId),
            @"iapGift": gift,
            @"count": @(gift.giftNum),
            @"giftType": @(gift.type),
            @"isAnonymous": @([KSIapGiftManager defaultManager].curSendGiftAnonymousStatus)
        };

        [[NSNotificationCenter defaultCenter] postNotificationName:KS_IAP_GIFT_SEND_GIFT_SUCC_NOTIFICATION object:nil userInfo:params];
    });
}

@end
