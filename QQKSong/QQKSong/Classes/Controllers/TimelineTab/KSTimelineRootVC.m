//
//  KSTimelineRootVC.m
//  QQKSong
//
//  Created by <PERSON><PERSON><PERSON> on 14-5-16.
//  Copyright (c) 2014年 Tencent. All rights reserved.
//
#import "KSNotificationDefines.h"
#import "KSUIItem.h"
#import "KSTimelineRootVC.h"
#import "KSRootTabBarController.h"
#import "KSAppDelegate+MainUI.h"
#import "KSLiveShowManager.h"
#import "KSRootTabBarController+Badge.h"
#import "KSTimelineRootVC+IapGift.h"
#import "KSTimelineRootVC+Recommand.h"
#import "KSTimelineRootVC+NewReport.h"
#import "KSTimelineRootVC+TabView.h"
#import "KSTimelineRootVC+ActivityTab.h"
#import "KSPerformanceService.h"
#import "KSTimelineRootVC+Upload.h"
#import "KSTimelineRootVC+Statistic.h"
#import "KSTimelineRootVC+NewReport.h"
#import "KSTimelineRootVC+VideoAutoPlay.h"
#import "KSTimelineRootVC+WaterFlowFeed.h"
#import "KSChampionRankListViewController.h"
#import "KSNavigationManager+UIStrategy.h"
#import "KSCommonConfigDataKey.h"
#import "KSBadgeUpdateManager.h"
#import "KSUgcPlayManager.h"
#import "KSUgcPlayManager+Hippy.h"
#import "KSUgcPlayManager+Report.h"
#import "KSUgcPlayManager+ReportV2.h"
#import "KSTimelineRootVC+HeaderView.h"
#import "KSFlowerManager.h"
#import "KShortUgcObj.h"
#import "KSTimelineDetailManager.h"
#import "KSPackageDataManager.h"
#import "KSInterestedPeopleVC.h"
#import "KSABTestManager.h"
#import "KSTimelineRootVC+ABTest.h"
#import "KSWelcomeGiftWebAlertView.h"
#import "JceBadgeUpdate_GetPromptUrlReq.h"
#import "JceBadgeUpdate_GetPromptUrlRsp.h"
#import "JceBadgeUpdate_emPromptUrlType.h"
#import "KSGuideBubbleManager.h"
#import "KSPathReportManager.h"
#import "KSTraceReprotHelper_V2+Feed.h"
#import "KSInviteToSingManager.h"
#import "KSRelationUserInfo.h"
#import "KSBlankDiscoverVC.h"
#import "KSNotificationViewController.h"
#import "KSHippyPopupView.h"
#import "JceTimeline_cell_ugc_dianping.h"
#import "UIAlertView+Block.h"
#import <CoreLocation/CLError.h>
#import <tgmath.h>
#import "JceKG_AbtestRspItem.h"
#import "JceProfile_PROFILE_MASK.h"
#import "JceTimeline_cell_ktv.h"
#import "JceTimeline_cell_ktv_mike.h"
#import "KSAssetDownloadManager.h"
#import "KSBadgeView.h"
#import "KSBuyPayAlbumView.h"
#import "KSCommonModel.h"
#import "KSEntertainmentView.h"
#import "KSGlobalPlayItem.h"
#import "KSIapGift.h"
#import "KSLoginManager.h"
#import "KSMailSessionDetailViewController.h"
#import "KSMultiGiftBridgeManager.h"
#import "KSNewUserGuideAndGiftManager.h"
#import "KSNotificationTabButton.h"
#import "KSPageableList.h"
#import "KSPaySoloAlbumManager.h"
#import "KSPerformanceReportHelper.h"
#import "KSProtocolCommands.h"
#import "KSRecordStrategyReport.h"
#import "KSRecordingStrategies.h"
#import "KSRegionRankCityModel.h"
#import "KSRelationManager.h"
#import "KSShortVideoRankCell.h"
#import "KSShareTopicListCell.h"
#import "KSTimelineForward.h"
#import "KSTimelineForwardInfo.h"
#import "KSTimelineManager.h"
#import "KSTraceReportEnumV2.h"
#import "KSTraceReportHelper.h"
#import "KSHookViewController+Report.h"
#import "KSTraceReportManager+PayAlbum.h"
#import "KSTraceReportModel_V2.h"
#import "KSUIABTestConstants.h"
#import "KSUIABTestManager.h"
#import "KSUnreadNumberView.h"
#import "KSUserInfo.h"
#import "KSong+Common.h"
#import "KSNetStatusManager.h"
#import "UIScrollView+DragTableSafeAnimation.h"
#import "UIView+PDExtension.h"
#import "KSUgcPlayMgr.h"
#import "KSTimelineRootVC+NewUserGift.h"
#import "KSTimelineRootVC+Search.h"
#import "KSTimelineRootVC+Data.h"
#import "JceTimeline_enum_filter_mask.h"
#import "KSAnimCommon.h"
#import "KSDynamicAnimResManager.h"
#import "KSFeedAdManager+KSFeed.h"
#import "KSimpleFeedManager+Advertisement.h"
#import "KSimpleFeedManager+CellFactory.h"
#import "KSimpleFeedManager+Gift.h"
#import "KSimpleFeedManager+Comment.h"
#import "KSimpleFeedManager+Share.h"
#import "KSStyleButton.h"
#import "KSCleanManager.h"
#import "KSDrawItemAction.h"
#import "KSUIScrollView.h"
#import "KSHookViewController+MemoryTrace.h"
#import "KSTMEAdHelper.h"
#import "KSFeedFilterResult.h"
#import "KSTimelineRootVC+Debug.h"
// 全局播放浮窗
#import "KSGlobalPlayFloatingWindowManager.h"
#import "KSRecommendBizManager.h"
#import "KSFeedFilterResult.h"
#import "KSLayoutUIManagerTimeline+RecFeed.h"
#import "KSTimelineRootVC+Recommand.h"
#import "KSLayoutUIManagerTimeline+RecFeed.h"
#import "KSFilmingViewController.h"
#import "CALayer+Extension.h"
#import "KSimpleFeedManager+audioPrdVideolize.h"
#import "KSUgcPlayManager+AudioPrdVideolize.h"
#import "KSTeenModeManager.h"
#import "KSTeachSingCourseManager.h"
#import "KSTimelineRootVC+Observer.h"
#import "KSLogUploadManager.h"
#import "TMEKSCrashReportHelper+KSong.h"
#import "KSLayoutableTimelineFeedCellV2+RecFeed.h"
#import "KSLayoutableTimelineFeedCellV2+LayoutV3.h"
#import "KSLayoutableTimelineFeedCellV2+AutoPlay.h"
#import "KSimpleFeedManager+Preload.h"
#import "KSTimelineManager+RefreshConflict.h"
#import "KSFontSizeManager.h"
// 推荐子标签
#import "KSCommonGuideView.h"
#import "KSProductUploadManagerBridge.h"
#import "KSUgcPlayManager+AudioPrdVideolize.h"
#import "KSLocalDataManager.h"
#import "KSCustomAMSBigCardAdView.h"
#import "KSWorkBindPics.h"

#import "KSTimelineRecUserFeedModel.h"
#import "JceTimeline_GetFeedsFlag.h"
#import "KSTimelineLoadMoreHistoryCell.h"
#import "KSTimelineRootVC+Advertise.h"
#import "KSTagsModel.h"
#import "UIButton+HitTestArea.h"
#import "proto_main_page_webapp_GetTaskEntranceRsp.h"
#import "KSFloatWindowManager.h"

#import "KSLoginManager+ThirdPartyAuthDialog.h"

#import "JceProtoLbs_GetLbsCityByIpReq.h"
#import "JceProtoLbs_GetLbsCityByIpRsp.h"
#import "JceProtoLbs_GeoInfo.h"
#import "JceProtoLbs_GetGeoInfoReq.h"
#import "JceProtoLbs_GetGeoInfoRsp.h"
#import "KSLifeFrozenData.h"
#import "KSDownloadEnum.h"
#import "KSAnimResDownloadItem.h"
#import "KSAnimResDownloadCenter.h"
#import "KSRecfeedTechReportManager.h"
#import "KSResourceDownloadBusinessConfig.h"
#import "KSLiveStreamPreloadManager.h"
#import "KSTaskCenterManager.h"
#import "KSTimelineRootVC+LastFeed.h"
#import "KSUgcPlayManager_Private.h"
#import "KSPhoneCardManager.h"
#import "KSUIABTestManager.h"
#import "KSRedPackedDialog.h"
#import "KSTimelineRootVC+Action.h"
#import <KSCommonAudioScore/KSCommonAudioScoreHeader.h>
#import "KSAudioScoreBusinessConfiger.h"
#import "KSPermissionManager.h"
#import "KSTimelineRootVC+TaskEntrance.h"
#import "proto_main_page_webapp_GetEntranceInfoRsp.h"
#import "KSFeedBannerEntryItemDataModel.h"
#import "KSImgProcAssetHelperManager.h"
#import "KSSplashAdManager+OperateOneShot.h"
#import "KSCalendarEventHandler.h"
#import "KSOneShotBigCardView.h"
#import "KSVoiceStatusRequester.h"
#import "KSVoiceStatusUtility.h"
#import "JceTimeline_VoiceInfo.h"
#import "KSTimelineFollowFilterView.h"
#import "KSRecFeedBottomGuideFrequencyControlMgr.h"
#import "KSNearbyQueryModel.h"
#import "KSProtoLbsManager.h"
#import "KSSysVolumeHelper+TimelineRootBiz.h"

//Siri Suggestion
#import <Intents/INPlayMediaIntent.h>
#import "KSSiriMediaIntentManager.h"

#ifdef INTERNALBUILD
#import "KSDebugPreloadTaskManager.h"
#import <mach-o/dyld.h>
#endif

#import "KSTimelineRootVC+NearBy.h"
#import "KSTimelineRootVC+QuickSinging.h"

#import "proto_feed_webapp_ENUM_REC_FEED_TYPE.h"
#import "KSAudioSession.h"
#import "KSAudioCommentSwitcher.h"
#import "KSTimelineRootVC+FeedAblum.h"
#import "KSBoxManager.h"
#import "KSPublishVCProvider.h"
#import "KSVideoDisplayView.h"
#import "KSPersonalGiftManager.h"
#import "KSBigCardDiagnostor.h"
#import "KSUserPortraitStategyManager.h"
#import "proto_ad_channel_user_pay_task_QueryPayTaskStatusRsp.h"
#import <KSUIKit/UIApplication+KSStatusBarStyle.h>
#import "KSUserVip.h"
#import "KSUserInfo+Auth.h"
#import "KSUgcPlayManager+PreloadV2.h"
#import "KSPrivateProtectLocationManager.h"
#import "KSAudioTunerExperienceTipsView.h"
#import "KSPIPManager.h"
#import "KSEntertainmentView.h"
#import "proto_feed_webapp_cell_tme_town_feed.h"

#import "KSNearbyUserTableViewCell.h"
#import "KSShareTopicListCell.h"
#import "KSNearbyTabbarView.h"
#if __has_include(<TXLiteAVSDK_Professional/TRTCCloud.h>)
#import <TXLiteAVSDK_Professional/TXLiveBase.h>
#else
#import <TXLiteAVSDK_Live/TXLiveBase.h>
#endif
#import "KSLiveShowModuleService.h"
#import "KSLiveShowMonitorManager.h"
#import "KSLayoutableTimelineFeedCellV2+KtvRoomCard.h"
#import "KSSearchPageFactory.h"
#import "KSNewDiscoverHippyVC.h"
#import "KSGMUtility.h"
#import "KSLiteKTVRoomManager.h"
#import "KSFollowKTVRoomFeedContainer.h"
#import "KSCloudCompositeManager.h"
#import "proto_cloud_game_stream_ENUM_AVATAR_COMPOSE_TASK_STATUS.h"
#import "KSTimeRootUploadShareCellV2.h"
#import "KSKTVRoomCardPreRunManager.h"

// 大合唱
#import "JceFriendKtv_GetFastPlayKtvRoomRsp.h"
#import "JceLiveHome_FeedBannerItem.h"
// 草稿箱
#import "KSDraftsManager.h"
#import "KSTimelineRootVC+FeedRewardAd.h"

#import "KSRewardAdManager.h"
#import "KSRewardAdFloatView.h"
#import "KSInteractiveGameDataCenter.h"
#import "KSEcommerceTabView.h"
#import "KSPlayletConstants.h"
#import "KSBannerAdManager.h"
#import "KSTMEAdConstants.h"
#import "KSBigCardSwitchUtil.h"
#import "KSAppDelegate+PlayConflict.h"

#import "KSHippyManager+Event.h"
#import "KSHippyManager+Property.h"
#import "KSBoxManager.h"
#import "KSGiftProtocalReportModel.h"
#import "KSConfigBanner.h"
#import "KSFeedPlayBarV4.h"
#import "KSLiveRoomPreLoadManager.h"
#import "KSReaderConstants.h"
#import "KSHippyViewController.h"
#import "KSFeedVMissionTransition.h"
#import "KSFeedVMissionGestureRecognizer.h"
#import "KSFeedVMissionSlideGuideView.h"
#import <KSBasicUI/UIImage+Gif.h>
#import "proto_interact_ecommerce_page_ThemeConfigItem.h"
#import "proto_interact_ecommerce_page_ThemeConfigTab.h"
#import "KSTimelineRootVCTabIndexManager.h"
#import "JceTimeline_cell_mini_heat_card.h"
#import "JceTimeline_s_user.h"
#import "JceTimeline_cell_userinfo.h"
#import "KSAdFreeHotCardManager.h"

#import "KSTimelineTabHippyVC.h"
#import "KSLiveSHowEnterManager.h"
#import <FireEye/FireEye+FPSMonitor.h>

#import "JceKG_recommend_activity_webapp_TableInfo.h"
#import "JceKG_recommend_activity_webapp_RecommendTabListRsp.h"
#import "KSTimelineRootVC+SubTab.h"
#import "TLMAudioSessionSharedManager.h"
#import "KSImagePickerVC.h"
#import "KSTimelineRootVC+TabTitle.h"
#import "KSTimelineManager+Gift.h"

#define kTimelineChangedY 1.0

#define kTimelineNavButtonWidth 50

static NSString *const kTimelineRootVCDidCancelSubcribeViewTimes = @"kTimelineRootVCDidCancelSubcribeViewTimes";
static NSString *const kTimelineRootVCDidCancelSubcribeViewTimeStamp = @"kTimelineRootVCDidCancelSubcribeViewTimeStamp";

static NSString * const kFeedSlideMissionGuideCount = @"KSFeedSlideMissionGuideCount";

const static NSInteger kLocationReqOverTime = 10;  //请求地理位置信息的超时时间
CGFloat const kFollowTagsContainerHeight = 40.f;   // 关注Tab 标签栏高度


@interface KSTimelineRootVC ()
<CLLocationManagerDelegate,
EmptyViewDelegate,
KSOneShotBigCardDelegate,
KSUgcPlayManagerDelegate,
KSPIPShowPendingVCDelegate,
KSRewardAdManagerDelegate,
KSRewardAdFloatViewDelegate>

@property (nonatomic, strong) CLLocationManager *locationManager;
@property (nonatomic, assign) CLLocationCoordinate2D  locationCoordinate;
@property (nonatomic, assign) NSTimeInterval locationReqTimes;         //请求位置请求次数

@property (nonatomic, assign) BOOL friendsLoadFirstTime;
@property (nonatomic, assign) BOOL hotLoadFirstTime;
@property (nonatomic, assign) BOOL nearbyLoadFirstTime;
@property (nonatomic, assign) BOOL QQWXLoadFirstTime;
@property (nonatomic, assign) BOOL recommendLoadFirstTime;
@property (nonatomic, assign) BOOL recommendNearByLoadFirstTime;

@property (nonatomic, assign) BOOL hasShownWelcomeGift;

@property (nonatomic,assign) BOOL isAuthGroup;

@property (nonatomic,assign) CGFloat tableView1Y;
@property (nonatomic,assign) CGFloat tableView2Y;
@property (nonatomic,assign) CGFloat tableView3Y;
@property (nonatomic,assign) CGFloat tableView4Y;

@property (nonatomic,assign) BOOL hasHandledPretchEvent; // 是否已经处理完大卡片预加载数据


@property (nonatomic,assign) BOOL shouldShowEmptyView;
@property (nonatomic,assign) BOOL shouldNotRefresh;

@property (nonatomic,assign) BOOL shouldReloadWhenNetworkChange;
@property (nonatomic,strong) NSDictionary *reloadWhenNetworkChangeExtraInfo;

@property (nonatomic, assign) NSInteger lastRecFeedIndex;

@property (nonatomic, strong) KSLiteKTVRoomManager *liteKTVRoomManager;

@property (nonatomic, strong) KSFeedVMissionGestureRecognizer *vMissionGestureRecognizer;

@property (nonatomic,assign) BOOL shouldNotShowVMissionGuide;

@property (nonatomic, assign) BOOL hasLocationUpdated;  // 记录系统位置是否第一次更新

@end


@implementation KSTimelineRootVC {
    BOOL isLoadingView;
}

KS_HIPPY_BRIDGE_PORTAL_REGISTER() {
    // 收到hippy直播tab刷新成功的通知，消除直播tab的红点
    [KSHippyBridgePortal registerBridgeWithHandle:^(NSDictionary *params, HippyBridgeCompleteBlock complete) {
        [[NSNotificationCenter defaultCenter] postNotificationName:KSNotification_UpdateBadgeNotification_ForFeedLive object:nil];
    } module:@"KGInterfaceModule" method:@"karaHippyBridge" action:@"removeFeedLiveRedBadge"];
    // 收到hippy通知 更新下一个信息流金币三期广告
    [KSHippyBridgePortal registerBridgeWithHandle:^(NSDictionary *params, HippyBridgeCompleteBlock complete) {
        KSFilterFeedsInfo *recFeedsInfo = [KSTimelineManager sharedManager].feedsInfo_Recommend;
        KSTimelineRootVC *rootVc = [[KSNavigationManager sharedManager] getTimelineRootVC];
        KLog(@"FeedAdGold showNextAd");
        if (complete) {
            [rootVc sendFeedAdEventWithInfo:recFeedsInfo hasAdBlock:^(BOOL hasAd) {
                if (hasAd) {
                    complete(@{@"code":@"0"});
                }else {
                    complete(@{@"code":@"-1"});
                }
            }];
        }
        
    } module:@"KGInterfaceModule" method:@"karaHippyBridge" action:@"showNextAd"];
    
    //收到hippy通知处理 信息流三期toast
    [KSHippyBridgePortal registerBridgeWithHandle:^(NSDictionary *params, HippyBridgeCompleteBlock complete) {
        KSTimelineRootVC *rootVc = [[KSNavigationManager sharedManager] getTimelineRootVC];
        KLog(@"FeedAdGold FeedGoldAdComplete");
        rootVc.needHiddenFeedAdtoast = YES;
        
    } module:@"KGInterfaceModule" method:@"karaHippyBridge" action:@"feedGoldAdComplete"];
    
    //收到hippy通知 信息流三期挂件状态改变
    [KSHippyBridgePortal registerBridgeWithHandle:^(NSDictionary *params, HippyBridgeCompleteBlock complete) {
        KSTimelineRootVC *rootVc = [[KSNavigationManager sharedManager] getTimelineRootVC];
        KLog(@"FeedAdGold adStatusChange");
        rootVc.needHiddenFeedAdtoast = NO;
        
    } module:@"KGInterfaceModule" method:@"karaHippyBridge" action:@"adStatusChange"];
}

#pragma mark - Init

- (id)init {
    if (self = [super init]) {
        self.lastRecFeedIndex = -1;
        self.friendsLoadFirstTime = YES;
        self.nearbyLoadFirstTime = YES;
        self.viewFirstAppear = YES;
        self.hotLoadFirstTime = YES;
        self.QQWXLoadFirstTime = YES;
        self.recommendLoadFirstTime = NO;
        self.recommendNearByLoadFirstTime = YES;
        self.isViewDisappear = NO;
        self.feedManager = [[KSimpleFeedManager alloc] initWithFeedManagerDelegate:self feedReportDelegate:self currentVC:self queryKbAccount:YES];
        [KSLiveStreamPreloadManager shareInstance].feedManagerDelegate = self;
        self.loadingRequests = [[NSMutableArray alloc] init];
        //这里的数据跟默认tab相关，否则不一定是feedsInfo_ForFollow
        self.currentFilterFeedsInfo = [KSTimelineManager sharedManager].feedsInfo_Recommend;
        self.isSendingReq = NO;
        [self loadFollowTabCurFiltrMask];
        self.shareSimpleFeeds = [[NSMutableArray alloc] init];
        self.removedUGCArr = [[NSMutableArray alloc] init];
        self.nearByUserArray = [[NSMutableArray alloc] init];
        self.hasReportSearchIcon = NO;
        self.socialBindRefreshPhase = FollowFeedBindSocialStatusNone;
        self.curRecFeedRowIndex = 0;
        self.hasShowAutoPlayBottomTip = NO;
        self.curCityName = @"同城";
        self.curRecFeedRowCheckIndex = -1;
        self.hasHandledPretchEvent = NO;
        self.shouldNotRefresh = NO;
        self.shouldReloadWhenNetworkChange = NO;
        //刷新任务可领取鲜花数
        self.shouldRefreshEntrace = YES;
        
        [[KSRecFeedBottomGuideFrequencyControlMgr sharedManager] configWithBizType:KSGuideFrequencyControlBizType_UGCFollow];
    }
    return self;
}

#pragma mark - Life Cycle

- (void)loadView
{
    //计时上报，一定要在开头和结尾
    [[KSPerformanceReportHelper sharedInstance] startTraceLoad:self andSelector:_cmd];
    
    [KSRecfeedTechReportManager sharedManager].viewLoadBeginTime = CACurrentMediaTime();

    [[UIApplication sharedApplication] setStatusBarHidden:NO withAnimation:UIStatusBarAnimationNone];
    
    [super loadView];
    
    self.lastSelectTabIndex = [KSTimelineRootVC getLastSelectedTabIndex];
    // 做了越界处理
    if (self.lastSelectTabIndex >= [KSTimelineRootVCTabIndexManager.sharedManager getTabArr].count || self.lastSelectTabIndex < 0) {
        self.lastSelectTabIndex = 0;
    }
    
    isLoadingView = YES;
    
    [self setupNavigationBarStyle:KSongNavigationBarStyleFeedGray];
    [self createNavBarButton];
    
    [self createContainerScrollViewIfNeed];

    [self createTabTitleViewIfNeed];
    
    [self addNavBarAccessibilityElements];
    
    if ([KSTimelineManager sharedManager].currentFiltrMask == [KSTimelineManager sharedManager].filtrMaskFollow ||
        [KSTimelineManager sharedManager].currentFiltrMask == [KSTimelineManager sharedManager].filtrMaskQQWXFriends ||
        [KSTimelineManager sharedManager].currentFiltrMask == [KSTimelineManager sharedManager].filtrMaskUgcFeed ||
        [KSTimelineManager sharedManager].currentFiltrMask == [KSTimelineManager sharedManager].filtrMaskUgcFeedQQWX||
        [KSTimelineManager sharedManager].currentFiltrMask == [KSTimelineManager sharedManager].filtrMaskTopic)
    {
        self.contentTableView = self.mFollowTableView;
    }
    else if ([KSTimelineManager sharedManager].currentFiltrMask == [KSTimelineManager sharedManager].filtrMaskHot ||
             [KSTimelineManager sharedManager].currentFiltrMask == [KSTimelineManager sharedManager].filtrMaskRecommend)
    {
        self.contentTableView = self.mHotTableView;
        
        self.recCardFirstAppearTime = CACurrentMediaTime();
        
    } else if ([KSTimelineManager sharedManager].currentFiltrMask == [KSTimelineManager sharedManager].filtrMaskRecommendNearBy) {
        
        if (self.nearbySubIndex ==  NearbyTabBarSubIndex_Dynamic) // 附近动态
        {
            self.contentTableView = self.mNearbyFeedTableView;
            
        } else if (self.nearbySubIndex ==  NearbyTabBarSubIndex_User) { // 附近的人
            
            self.contentTableView = self.mNearbyUserTableView;
        }
        
    } else if ([KSTimelineManager sharedManager].currentFiltrMask == [KSTimelineManager sharedManager].filtrMaskLive) {
        self.contentCollectionView = self.entertainmentView.collectionView;
    }
    else if ([KSTimelineManager sharedManager].currentFiltrMask == [KSTimelineManager sharedManager].filtrMaskShop) {
        self.contentTableView = self.mShopTableView;
    }
    
    if(!self.ExtraInfoDic){
        self.ExtraInfoDic = [[NSMutableDictionary alloc] init];
    }
    
    [KSRecfeedTechReportManager sharedManager].viewLoadEndTime = CACurrentMediaTime();
        
    // =========================
    // ⚠️ 计时上报，一定要在开头和结尾
    [[KSPerformanceReportHelper sharedInstance] endTraceLoad:self andSelector:_cmd];
}


- (void)viewDidLoad
{
    //计时上报，一定要在开头和结尾
    [[KSPerformanceReportHelper sharedInstance] startTraceLoad:self andSelector:_cmd];
    
    [KSRecfeedTechReportManager sharedManager].viewDidLoadBeginTime = CACurrentMediaTime();

    self.abTest_SplashAdSceneMask = EM_AD_SCENE_Feed;
    
    self.ksTraceReportName = @"TimeLineFeed";
    self.enableScrollFrameMonitor = YES;
    
    self.syncMultiTaskArray = [[NSMutableArray alloc] init];
    
    [super viewDidLoad];
    self.view.backgroundColor = [UIColor ks_whiteColor];

    isLoadingView = NO;
    self.locationCoordinate = kCLLocationCoordinate2DInvalid;
    
    [self registerNotificationObserver];
        
    self.multiGiftBridgeManager = [KSMultiGiftBridgeManager new];
            
    
    [self requestTaskTips];
    
    [self checkLaunchUseUnReadFeedsIfNeed];

    [self performSelector:@selector(timeWelcomeGift) withObject:nil afterDelay:1];

    KS_WEAK_SELF(self);
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        CHECK_SELF_AND_RETURN();
        [self updateUploadTaskArrayIfNeeded];
    });
    
    // 页面置灰配置
    NSArray *pageArray = WnsStringArrayConfig(@"AppTint", @"AppTintGrayColor");
    if ([pageArray containsObject:@"1001"]) {
        self.view.layer.grayscale = YES;
    }

    if ([[KSSplashAdManager sharedManager] isInOneShotProcess]) {
        [self setTransparencyForOneShot];
        [[KSSplashAdManager sharedManager] addViewNeedsFadeIn:self.view];
    }
    
    // 拉取广告免费送礼礼物
    [self loadFeedADFreeGift:[KSTimelineManager sharedManager].filtrMaskRecommend];
    
    //拉取热门动态数据
    if (WnsSwitchBoolConfig(@"SiriMediaIntentDonation"))
    {
        KLog(@"Siri WNS SiriMediaIntentDonation = %@", @(WnsSwitchBoolConfig(@"SiriMediaIntentDonation")));
        //wns开关 防止crash
        if (@available(iOS 13.0, *))
        {
            [[KSSiriMediaIntentManager shareInstance] loadSiriTrendingData];
        }
    }
    else
    {
        if (@available(iOS 13.0, *))
        {
            [[KSSiriMediaIntentManager shareInstance] deleteAllDonateInteractions];
        }
    }
    
    //插入成功 展示floatview
    if ([KSTimelineManager sharedManager].feedNativeAdReadyButNoVC) {
        KLog(@"FeedNativeAd, rootVC didload,self.feedRewardAdFloatView = %@",self.feedRewardAdFloatView);
        if (self.feedRewardAdFloatView && self.feedRewardAdFloatView.hidden == NO) {
            [self updateFeedRewardAdFloatView];
        }else {
            [self showFeedRewardAdFloatView];
        }
        [KSTimelineManager sharedManager].feedNativeAdReadyButNoVC = NO;
    }
        
    [KSRecfeedTechReportManager sharedManager].viewDidLoadEndTime = CACurrentMediaTime();

    // =========================
    // ⚠️ 计时上报，一定要在开头和结尾
    [[KSPerformanceReportHelper sharedInstance] endTraceLoad:self andSelector:_cmd];

}

- (void)viewWillAppear:(BOOL)animated
{
    //计时上报，一定要在开头和结尾
    [[KSPerformanceReportHelper sharedInstance] startTraceLoad:self andSelector:_cmd];
    [KSRecfeedTechReportManager sharedManager].viewWillAppearBeginTime = CACurrentMediaTime();

    [super viewWillAppear:animated];
    
    [self resumeOnViewWillAppear];
    
    if (self.contentTableView == self.mNearbyUserTableView) {
        [self.mNearbyUserTableView reloadData];// 呼吸态动画需要刷新重新展示 @filedjiang
    }
    
    [KSRecfeedTechReportManager sharedManager].viewWillAppearEndTime = CACurrentMediaTime();
    [self memoryTrace_startTrace];

    // =========================
    // ⚠️ 计时上报，一定要在开头和结尾
    [[KSPerformanceReportHelper sharedInstance] endTraceLoad:self andSelector:_cmd];
}

- (BOOL)memoryTrace_disableAutoTrace {
    return YES;
}

- (void)viewDidAppear:(BOOL)animated
{
    KDEBUG(@"[大卡片技术上报]动态页DidAppear");
    //计时上报，一定要在开头和结尾
    [[KSPerformanceReportHelper sharedInstance] startTraceLoad:self andSelector:_cmd];
    [KSRecfeedTechReportManager sharedManager].viewDidAppeardBeginTime = CACurrentMediaTime();

    [super viewDidAppear:animated];
    
    [self createTabIfNeed];
    [self resumeOnViewDidAppear];
    [self checkFeedRewardIsExist];
    [self viewWillAppearWithReward];
    
    /// 可以延后执行的任务放这里
    [self runTaskAfterDidAppear];
    
    // 任务中心侧滑引导动画
    [self showVMissionSlideGuideAnim];
    
    // 红点治理
    [self reportAllTabRedDot];
    [[KSBadgeUpdateManager sharedManager] reportRootTabBadge];
    
    [KSRecfeedTechReportManager sharedManager].viewDidAppeardEndTime = CACurrentMediaTime();

    // =========================
    // ⚠️ 计时上报，一定要在开头和结尾
    [[KSPerformanceReportHelper sharedInstance] endTraceLoad:self andSelector:_cmd];
    // 添加逻辑请到KSPerformanceReportHelper函数之前
    // 添加逻辑请到KSPerformanceReportHelper函数之前
    // 添加逻辑请到KSPerformanceReportHelper函数之前
    // 请不要在这里添加逻辑
}

- (void)viewDidFirstAppear:(BOOL)animated
{
    [super viewDidFirstAppear:animated];
    [self reportSiriShortCut];
}

- (void)viewWillDisappear:(BOOL)animated
{
    [super viewWillDisappear:animated];
    [[KSLoginManager sharedInstance] cancelShowAuthGuide];
    
    [[UIApplication sharedApplication] setIdleTimerDisabled:NO];//关掉息屏

    [self pauseOnViewWillDisappear];
    
    [self viewWillDisappearWithReward];
    
    //取消直播预拉数据
    [[KSLiveRoomPreLoadManager shareInstance] cleanPrePullStreamSource];
    
    if ([self.feedManager isSharing]) {
        [self.feedManager closeShareVC];
        self.feedManager.shareVC = nil;
    }
    
    if (self.feedManager.sendGiftContainerView.shown) {
        [self.feedManager.sendGiftContainerView dismiss];
        self.feedManager.sendGiftContainerView = nil;
    }
    [self memoryTrace_stopTrace];
}


- (void)viewDidDisappear:(BOOL)animated
{
    [super viewDidDisappear:animated];
    
    [self pauseOnViewDidDisappear];
}

- (void)dealloc
{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
    _locationManager.delegate = nil;
    self.navigateTipView.ksNavigateTipViewDelegate  = nil;
    self.payAlbumView.delegate = nil;
    self.scrollView = nil;
    [self.mFollowTableView safeRemoveObserver:self.feedManager forKeyPath:@"contentSize" context:(void *)self.mFollowTableView];
    [self.mHotTableView safeRemoveObserver:self.feedManager forKeyPath:@"contentSize" context:(void *)self.mHotTableView];
    [self.mHotCollectionView safeRemoveObserver:self.feedManager forKeyPath:@"contentSize" context:(void *)self.mHotCollectionView];

    [self.multiGiftBridgeManager clearAll];
    self.multiGiftBridgeManager = nil;
 
    KLog(@"[动态Retain] timelineroot dealloc Count = %ld\n",CFGetRetainCount((__bridge CFTypeRef)(self)));
}

#pragma mark - Task
/// didAppear再延迟执行的任务
- (void)runTaskAfterDidAppear
{
    
}

#pragma mark ********* 点击附近tabbar 回调 ********
- (void)didNearbyTabbarViewClickWithIndex:(NSInteger)index {
    
    if (self.nearbySubIndex == NearbyTabBarSubIndex_User && index == NearbyTabBarSubIndex_Dynamic && !self.isHasFirstSwitchToNearDynamic) {
        
        self.isHasFirstSwitchToNearDynamic = YES;
        [self.mNearbyFeedTableView reloadData];
    }
    self.nearbySubIndex = index;

    CGPoint offset = CGPointMake(kScreenWidthBM() * self.nearbySubIndex, 0);
    dispatch_async(dispatch_get_main_queue(), ^{
        [self.nearbyScrollContainerView setContentOffset:offset animated:YES];
    });
    if (self.nearbySubIndex == NearbyTabBarSubIndex_Dynamic)
    {
        self.contentTableView = self.mNearbyFeedTableView;
    }
    else if (self.nearbySubIndex == NearbyTabBarSubIndex_User) // 附近的人
    {
        // 停止当前列表的media播放
       [self processStopMediaPlayIgnoreExpose:YES];
       self.contentTableView = self.mNearbyUserTableView;
        
        [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
            reportModel.key = @"feed_nearby#nearby_people#null#exposure#0";
            // int1=曝光来源： 1默认曝光 2 点击进入；
            reportModel.commonInt1 = 2;
        }];
    }
    
    [self updateEmptyView];
}


#pragma mark override
- (void)autoExecBlockWhenContentTableChanged:(KSUITableView *)contentTableView {
    KLog(@"[动态切换]tableSetUp[%p] x=%f",contentTableView,contentTableView.x);
    self.refreshHeaderView = self.contentTableView.headerView;
    self.loadMoreFooterView = self.contentTableView.footerView;
    self.scrollView = (UIScrollView *)self.contentTableView;
}

#pragma mark override
- (void)autoExecBlockWhenContentCollectionChanged:(KSUICollectionView *)contentCollectionView
{
    self.refreshHeaderView = self.contentCollectionView.headerView;
    self.loadMoreFooterView = self.contentCollectionView.footerView;
    self.scrollView = (UIScrollView *)self.contentCollectionView;
}

- (void)addNavBarAccessibilityElements {
    NSMutableArray *accessibilityElements = [NSMutableArray array];
    if (self.navBar.leftNavBarBtn) {
        [accessibilityElements safeAddObject:self.navBar.leftNavBarBtn];
    }
    for (int index = 0; index < self.centerSegControl.tabViews.count; index++) {
        [accessibilityElements safeAddObject:self.centerSegControl.tabViews[index]];
    }
    [accessibilityElements safeAddObject:self.navBar.rightNavBarBtn];
    self.navBar.accessibilityElements = [accessibilityElements copy];
}


#pragma mark - Refresh

//异化策略生效时，强制进入推荐，进入时需要带着jce_strLabelTraceId重新请求
- (void)uiStrategyJumpRecommendRefresh
{
    if ([KSTimelineManager sharedManager].uiStrategyJumpRecommend == 0)
    {
        return;
    }
    if (!self.bSelectTabEntry && [KSTimelineManager sharedManager].uiStrategyJumpRecommend == 2)
    {
        KLog(@"命中异化推荐但不是手点进入");
        return;
    }
    KLog(@"命中推荐及流量分发强跳推荐");
    NSString * traceid = [[KSNavigationManager sharedManager] recParamForStrategyIndex:0];
    if (!traceid)
    {
        traceid = @"";
        KLog(@"命中推荐traceid空了");
        [KSTimelineManager sharedManager].uiStrategyJumpRecommend = 0;
    }
    [[KSNavigationManager sharedManager] dealWithScheme:[NSString stringWithFormat:@"qmkege://kege.com?action=feed&tab=recommend&trace_id=%@",[traceid URLEncodedString]]];
}


- (void)showTopFreshButton:(BOOL)show
{
    if (show && self.contentTableView == self.mFollowTableView)
    {
        // 全部动态更新数
        [self.followFilterView updateIncomingNewFeedsWithCount:[KSBadgeUpdateManager sharedManager].feedNum];
    }
    
    if ([KSTimelineManager sharedManager].currentFiltrMask & [KSTimelineManager sharedManager].filtrMaskUgcFeed ||
        [KSTimelineManager sharedManager].currentFiltrMask & [KSTimelineManager sharedManager].filtrMaskUgcFeedQQWX)
    {
        // 勾选“只看作品”时 不展示红点
        [self.followFilterView updateFilterButtonBadge:NO];
    }
    else
    {
        // 作品红点
        [self.followFilterView updateFilterButtonBadge:[KSBadgeUpdateManager sharedManager].feedUgcNum > 0];
    }
}

- (void)refreshDidFinishedWithreshString:(NSString*)freshText
{
    if (freshText.length > 0)
    {
        [self.refreshHeaderView endLoading:self.scrollView shouldChangeContentInset:YES freshString:freshText];
        
        self.topFreshBar.hidden = NO;
        self.topFreshLabel.text = freshText;
        
        CGSize size = self.topFreshBar.size;
        self.topFreshBarBG.frame = CGRectMake(size.width / 2, 0, 0, size.height);
        self.topFreshBar.alpha = 0;
        
        [UIView animateWithDuration:0.2 animations:^{
            self.topFreshBar.alpha = 1;
            self.topFreshBarBG.frame = CGRectMake(0, 0, size.width, size.height);
        } completion:^(BOOL finished) {
            [self performSelector:@selector(hiddenTopFreshLabel:) withObject:nil afterDelay:3];
        }];
    }
    else
    {
        [self.refreshHeaderView endLoading:self.scrollView shouldChangeContentInset:YES];
    }
}

- (void)hiddenTopFreshLabel:(UIScrollView *)scrollView
{
    [UIView animateWithDuration:0.3 animations:^{
        self.topFreshBar.alpha = 0;
    } completion:^(BOOL finished) {
        self.topFreshBar.hidden = YES;
        self.topFreshBar.alpha = 1;
    }];
}

- (void)cloudCompositeUpdate:(KSPublishContent*)publishContent progressPercent:(NSInteger)percent
{
    NSArray *array = KSCloudCompositeManager.defaultManager.validTaskPublishContentArray;
    for (NSInteger i = 0; i < array.count; ++i)
    {
        KSPublishContent *pc = [array safeObjectAtIndex:i];
        if ([pc.productMid isEqualToString:publishContent.productMid])
        {
            NSIndexPath *path = [NSIndexPath indexPathForRow:i inSection:CollectionSectionTypeCloudComposite];
            UITableViewCell *cell = [self.contentTableView cellForRowAtIndexPath:path];
            if ([cell isKindOfClass:[KSTimeRootUploadShareCellV2 class]])
            {
                KSTimeRootUploadShareCellV2 *uploadCell = (KSTimeRootUploadShareCellV2 *)cell;
                [uploadCell updatePublishContent:publishContent];
            }
        }
    }
}
#pragma mark -（table和collectionview）kvo重设footer位置，否则数据刷新和加载逻辑有问题

- (void)observeValueForKeyPath:(NSString *)keyPath ofObject:(id)object change:(NSDictionary *)change context:(void *)context
{
    if (object == self.entertainmentView.collectionView) {
        // KSEntertainmentView 内部自己做了监听
        return;
    }
    if ([keyPath isEqualToString:@"contentSize"])
    {
        
        UIScrollView *scrollContentView = (__bridge UIScrollView *)context;
        
        if (scrollContentView)
        {
            NSValue *pointValue = change[@"new"];
            CGSize contentSize = [pointValue CGSizeValue];
            CGFloat dragFooterMinY = max(contentSize.height, scrollContentView.height);
            
            KSongLoadMoreScrollViewFooterView *footerView = [scrollContentView getFooterView];
            UILabel *noMoreLabe = [scrollContentView getNoMoreLabel];            
            if (footerView)
            {
                footerView.frame = CGRectMake(0, dragFooterMinY, scrollContentView.width, scrollContentView.height);
            }
            if (noMoreLabe)
            {
                noMoreLabe.frame = CGRectMake(0, dragFooterMinY, scrollContentView.width, 45);
            }
        }
    }
    else if ([keyPath isEqualToString:@"contentOffset"]) {
        KLog(@"observeValueForKeyPath contentOffset");
    }
    else if ([keyPath isEqualToString:@"tableHeaderView"]) {
        KLog(@"observeValueForKeyPath tableHeaderView");
    }
}


- (void)refreshScrollViewHeaderDidTriggerRefreshWithView:(KSongRefreshScrollViewHeaderView *)headerView usrInfo:(NSDictionary*)usrInfo
{
    if (headerView && [headerView.superview isKindOfClass:[UIScrollView class]])
    {
        KSongLoadMoreScrollViewFooterView* footerView = [headerView getFooterView];
        if (footerView && footerView.isLoading) {
            [footerView endLoading:(UIScrollView *)headerView.superview shouldChangeContentInset:YES];
            [self cancelLoadMore];
        }
       
        [self checkNewUserGiftUpdate];
        
        self.refreshUsrInfo = usrInfo;
        
        if ([self isRecCardFeedTab]) {
            BOOL isPrefetching = [KSTimelineManager sharedManager].isPrefetchingRecFeedData;
            if (!isPrefetching) {
                [KSABTestManager sharedManager].isRecFeedAutoPlayFirstUGCNotPlaying = NO;//刷新就对齐下滑的情况

                /// 是否使用未读feeds
                if (![self checkRefreshUseUnReadFeedsIfNeed]) {
                    [self didTriggerRefresh];
                }
            }
        } else {
            [self didTriggerRefresh];
        }
        
        self.refreshUsrInfo = nil;
    }
}

- (void)loadMoreScrollViewFooterDidTriggerLoadMoreWithView:(KSongLoadMoreScrollViewFooterView *)footerView
{
    if (footerView && [footerView.superview isKindOfClass:[UIScrollView class]])
    {
        KSongRefreshScrollViewHeaderView *headerView = [footerView getHeaderView];
        
        if (headerView && headerView.isLoading)
        {
            [headerView endLoading:(UIScrollView *)footerView.superview shouldChangeContentInset:YES];
            [self cancelRefresh];
        }
        
        [self didTriggerLoadMore];
    }
    else {
        [self cancelLoadMore];
    }
}

#pragma mark override
- (BOOL)enableFooterView
{
    if ([[KSTimelineManager sharedManager] shouldShowRecUserFeedWithFiltrMask:self.currentFilterFeedsInfo.filterMask])
    {
        // 浅关系链需要根据推荐数据来判断
        KSTimelineRecUserFeedModel *currentRecUserFeedModel;
        if ([KSTimelineManager sharedManager].recFeedType == proto_feed_webapp_ENUM_REC_FEED_TYPE_ENUM_REC_FEED_TOPIC)
        {
            currentRecUserFeedModel = [KSTimelineManager sharedManager].recHotSpotFeed;
        }
        else
        {
            if (self.currentFilterFeedsInfo.filterMask == [KSTimelineManager sharedManager].filtrMaskFollow)
            {
                currentRecUserFeedModel = [KSTimelineManager sharedManager].recUserFeed_ForFollow;
            }
            else if (self.currentFilterFeedsInfo.filterMask == [KSTimelineManager sharedManager].filtrMaskQQWXFriends)
            {
                currentRecUserFeedModel = [KSTimelineManager sharedManager].recUserFeed_ForQQWXFriends;
            }
            else if (self.currentFilterFeedsInfo.filterMask == [KSTimelineManager sharedManager].filtrMaskUgcFeed)
            {
                currentRecUserFeedModel = [KSTimelineManager sharedManager].recUserFeed_ForUgcFeed;
            }
            else if (self.currentFilterFeedsInfo.filterMask == [KSTimelineManager sharedManager].filtrMaskUgcFeedQQWX)
            {
                currentRecUserFeedModel = [KSTimelineManager sharedManager].recUserFeed_ForUgcFeedQQWX;
            }
        }
        
        return currentRecUserFeedModel.hasMoreData;
    }
    else
    {
        return self.currentFilterFeedsInfo.hasMore;
    }
}

- (void)setEnableFooterView:(BOOL)enableFooterView
{
    KSongLoadMoreScrollViewFooterView *footerView = nil;
    UILabel *noMoreLabel = nil;
    
    if ([self.scrollView isKindOfClass:[UITableView class]])
    {
        UIScrollView *scrollView = self.contentTableView;
        KSUITableView *tableScrollView = SAFE_CAST(scrollView, [KSUITableView class]);
        footerView = tableScrollView.footerView;
        noMoreLabel = tableScrollView.noMoreLabel;
    }
    else
    {
        UIScrollView *scrollView = self.contentCollectionView;
        KSUICollectionView *collectionScrollView = SAFE_CAST(scrollView, [KSUICollectionView class]);
        footerView = collectionScrollView.footerView;
        noMoreLabel = collectionScrollView.noMoreLabel;
    }
    
    if (footerView)
    {
        footerView.hidden = !enableFooterView;
    }
     
    if (noMoreLabel)
    {
        noMoreLabel.hidden = enableFooterView;
    }
}

- (void)updateNearByUserAudioPlayStateForUgcId:(NSString *)ugcId isPlay:(BOOL)isPlay
{
    for (int i = 0; i < self.nearByUserArray.count; i++)
    {
        KSNearByUserInfoModel *user = self.nearByUserArray[i];
        JceProtoLbs_UserInfo *userInfo = user.userInfo;
        BOOL isTarget = [userInfo.jce_new_ugc_info.jce_ugcId isEqualToString:ugcId];
        if (isTarget)
        {
            user.isPlayMuisc = isPlay;
            // 采用reload 会导致头像、相片刷新影响体验
            [self changePlayState:isPlay withIndexPath:[NSIndexPath indexPathForRow:i inSection:0]];
        }
        else // 未点击播放操作的数据
        {
            if (user.isPlayMuisc) // 之前在播放
            {
                user.isPlayMuisc = NO;
                // 采用reload 会导致头像、相片刷新影响体验
                [self changePlayState:NO withIndexPath:[NSIndexPath indexPathForRow:i inSection:0]];

            }
        }
    }
}

- (void)changePlayState:(BOOL)isPlay withIndexPath:(NSIndexPath *)indexPath
{
    if (indexPath.row >= self.nearByUserArray.count)
    {
        return;
    }
    KSNearbyUserTableViewCell *cell = [self.mNearbyUserTableView cellForRowAtIndexPath:indexPath];
    if (cell)
    {
        [cell updateAudioPlayState:isPlay];
    }
}

- (void)setTransparencyForOneShot
{
    self.view.backgroundColor = [UIColor clearColor];
    self.contentTableView.backgroundColor = UIColor.clearColor;
}

- (void)resetTransparencyAfterOneShot
{
    [self setupMainVCViewBackgroundColor];

}

- (void)setupFirstLoginLocation
{
    
    if ([CLLocationManager locationServicesEnabled])
    {
        BOOL isLocationAuthorizationStatusDenied = [self isLocationAuthorizationStatusDenied];
        BOOL isLocationAuthorizationStatusNotDetermined = [self isLocationAuthorizationStatusNotDetermined];
        if (isLocationAuthorizationStatusDenied || isLocationAuthorizationStatusNotDetermined)
        {
            KLog(@"[同城获取位置权限]setupFirstLoginLocation isLocationAuthorizationStatusDenied : %@ isLocationAuthorizationStatusNotDetermined : %@",@(isLocationAuthorizationStatusDenied),@(isLocationAuthorizationStatusNotDetermined));
            //DO NOTHING
        }
        else
        {
            if (!self.locationManager)
            {
                self.locationManager = [[CLLocationManager alloc] init];
            }
            if (@available(iOS 14.0, *))
            {
                if (_locationManager.authorizationStatus == kCLAuthorizationStatusAuthorizedWhenInUse || _locationManager.authorizationStatus == kCLAuthorizationStatusAuthorizedAlways)
                {
                    _locationManager.delegate = self;
                    _locationManager.desiredAccuracy = kCLLocationAccuracyBest;
                    _locationManager.distanceFilter = 10.0f;
                    [_locationManager startUpdatingLocation];
                    
                }
            }
            else
            {
                if ([CLLocationManager authorizationStatus] == kCLAuthorizationStatusAuthorizedWhenInUse || [CLLocationManager authorizationStatus] == kCLAuthorizationStatusAuthorizedAlways)
                {
                    _locationManager.delegate = self;
                    _locationManager.desiredAccuracy = kCLLocationAccuracyBest;
                    _locationManager.distanceFilter = 10.0f;
                    [_locationManager startUpdatingLocation];
                }
            }
        }
       
    }
}

- (void)registerNotificationObserver
{

    //红点更新
    [[NSNotificationCenter defaultCenter] removeObserver:self name:KSNotification_UpdateBadgeNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(didReceiveBadgeUpdate:)
                                                 name:KSNotification_UpdateBadgeNotification object:nil];
    //7.0直播红点更新
    [[NSNotificationCenter defaultCenter] removeObserver:self name:KSNotification_UpdateBadgeNotification_ForFeedLive object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(checkShouldResetFeedLiveNum)
                                                 name:KSNotification_UpdateBadgeNotification_ForFeedLive object:nil];
    

    //网络状态变化，出现无网络引导条
    [[NSNotificationCenter defaultCenter] removeObserver:self name:kKSongReachabilityChangedNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(reachabilityChanged:)
                                                 name:kKSongReachabilityChangedNotification
                                               object:nil];
    
    //热启动
    [[NSNotificationCenter defaultCenter] removeObserver:self name:KSNotification_HotRelaunchDidFinishedShowAd object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(hotRelaunchDidFinishedShowAd)
                                                 name:KSNotification_HotRelaunchDidFinishedShowAd
                                               object:nil];
    
    //新版任务中心红点需更新
    [[NSNotificationCenter defaultCenter] removeObserver:self name:KSNotification_MissionEntranceChange object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(setUpShouldRefreshFlag:)
                                                 name:KSNotification_MissionEntranceChange
                                               object:nil];
    
    //新版任务中心红点需更新
    [[NSNotificationCenter defaultCenter] removeObserver:self name:KSNotification_PrefetchRecfeedDataFinished object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(handlePrefetchFinishedEvent:)
                                                 name:KSNotification_PrefetchRecfeedDataFinished
                                               object:nil];
    
    [[NSNotificationCenter defaultCenter] removeObserver:self name:KSNotification_ScrollViewWillBeginDragging object:nil];
    
    [[NSNotificationCenter defaultCenter] removeObserver:self name:KSNotification_PIPWindowShowStateChange object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(pipWindowStatusChanged:) name:KSNotification_PIPWindowShowStateChange object:nil];
   
    //后台关闭了推荐的通知
    [[NSNotificationCenter defaultCenter] removeObserver:self name:KSNotification_SeverEnableRecOrNot object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self 
                                             selector:@selector(recRightDidChangeNotification:)
                                                 name:KSNotification_SeverEnableRecOrNot
                                               object:nil];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(didABTestUpdate)
                                                 name:KSUIABTestUpdateNotification
                                               object:nil];
    
    [[NSNotificationCenter defaultCenter] addObserverForName:UIApplicationWillResignActiveNotification 
                                                      object:nil
                                                       queue:nil
                                                  usingBlock:^(NSNotification * _Nonnull note) {
        /// 退后台，保存未读大卡片
        [[KSTimelineManager sharedManager] saveRecomWithLastUnreadCacheFeed];
    }];
    
    KS_WEAK_SELF(self);
    [[NSNotificationCenter defaultCenter] addObserverForName:UIApplicationDidEnterBackgroundNotification object:nil queue:nil usingBlock:^(NSNotification * _Nonnull note) {
        KS_STRONG_SELF(self);
        if (self) {
            [self traceReportFeedRefreshAndPull];
        }
    }];
    
    [[NSNotificationCenter defaultCenter] addObserverForName:LMNotification_Logout object:nil queue:[NSOperationQueue mainQueue] usingBlock:^(NSNotification * _Nonnull note) {
        CHECK_SELF_AND_RETURN();
        [self removeFloatViewAndRewardAd];
        [KSTimelineManager.sharedManager.bannerAdManager consumeAd];
        [KSInteractiveGameDataCenter sharedManager].xxlEntryPassback = nil;
    }];

    [[NSNotificationCenter defaultCenter] addObserverForName:kKSongReachabilityChangedNotificationInternal object:nil queue:nil usingBlock:^(NSNotification * _Nonnull note) {
        CHECK_SELF_AND_RETURN();
        if (self.shouldReloadWhenNetworkChange && [[KSNetStatusManager sharedManager] IsEnableInternet] && [KSNavigationManager sharedManager].getTopViewController == self) {
            self.shouldReloadWhenNetworkChange = NO;
            [self loadFeedsListFromRemote:self.reloadWhenNetworkChangeExtraInfo];
        }
    }];
    
    
    [[NSNotificationCenter defaultCenter] addObserverForName:KSNotification_ShareOutsideReportSuccess object:nil queue:nil usingBlock:^(NSNotification * _Nonnull note) {
        KS_STRONG_SELF(self);
        NSString *contentId = [note.userInfo objectForKey:@"contentId"];
        if ([contentId isKindOfClass:[NSString class]] && contentId.length > 0) {
            for (KSimpleFeed *simpleFeed in self.currentFilterFeedsInfo.feedsList) {
                if ([simpleFeed.simpleFeedCommon.strFeedId isEqualToString:contentId] || [simpleFeed.payAlbumInfo.strAlbumId isEqualToString:contentId]
                     || [simpleFeed.soloAlbumInfo.strAlbumId isEqualToString:contentId]) {
                    if (!simpleFeed.forwardInfo) {
                        simpleFeed.forwardInfo = [[KSTimelineForwardInfo alloc] init];
                    }
                    KLog(@"[动态]ugcId=%@ 分享数=%@ forwardId=%@",simpleFeed.simpleFeedCommon.strFeedId,NSIToString(simpleFeed.forwardInfo.forwardNum),simpleFeed.forwardFeed.strForwardId);
                    simpleFeed.forwardInfo.forwardNum ++ ;
                    simpleFeed.layoutInfo = nil; // 重新排版
                }
            }
            [self reloadTable];
        }
    }];
    
    // 自动播放设置改变时刷新cell
    [[NSNotificationCenter defaultCenter] addObserverForName:KSNotification_FeedAutoPlaySettingChange object:nil queue:nil usingBlock:^(NSNotification * _Nonnull note) {
        CHECK_SELF_AND_RETURN()
        
        dispatch_async(dispatch_get_main_queue(), ^{
            [self.mFollowTableView reloadData];
            [self.mNearbyFeedTableView reloadData];
        });
    }];
    
    [NSNotificationCenter.defaultCenter addObserverForName:KSNotification_VoiceStatusInfoDidFetch object:nil queue:NSOperationQueue.mainQueue usingBlock:^(NSNotification * _Nonnull note) {
        CHECK_SELF_AND_RETURN()
        KINFO(@"did recv notification VoiceStatusInfoDidFetch %@", note.object);
        RUN_ON_UI_THREAD_ASYNC(^{
            if (note.object)
            {
                KSTimelineManager.sharedManager.voiceStatusInfo = SAFE_CAST(note.object, JceTimeline_VoiceInfo);
                
                if (self.contentTableView == self.mFollowTableView)
                {
                    [self.contentTableView reloadData];
                }
            }
        });
    }];
    
    // 退出游戏化的通知
    [[NSNotificationCenter defaultCenter] addObserverForName:@"KSGMGameMasterDidExitNotificaiton" object:nil queue:nil usingBlock:^(NSNotification * _Nonnull note) {
        CHECK_SELF_AND_RETURN();
        if (self.mFollowTableView) {
            KSFilterFeedsInfo *filterFeedsInfo = [self getFeedDataListByScollView:self.mFollowTableView];
            NSArray <KSimpleFeed *> *feedsArray = filterFeedsInfo.feedsList;
            for (KSimpleFeed *feed in feedsArray) {
                feed.layoutInfo = nil;
            }
        }
        [self reloadTable];
    }];
    
    [[NSNotificationCenter defaultCenter] addObserverForName:KSNotification_PopLayer_Ad_Show object:nil queue:nil usingBlock:^(NSNotification * _Nonnull note) {
        CHECK_SELF_AND_RETURN();
        [self showMaskAdBanner];
    }];
    
    [[NSNotificationCenter defaultCenter] addObserverForName:KSNotification_CloudCompositeProgress object:nil queue:nil usingBlock:^(NSNotification * _Nonnull note) {
        CHECK_SELF_AND_RETURN();
        NSDictionary *dict = [note userInfo];
        NSArray *productInfos = [dict objectForKey:@"productInfos"];
        // 处理进度回调
        for (NSDictionary *dictionary in productInfos)
        {
            int status = [[dictionary objectForKey:@"status"] intValue];
            if (status <= proto_cloud_game_stream_ENUM_AVATAR_COMPOSE_TASK_STATUS_ENUM_AVATAR_COMPOSE_TASK_RUNNING)
            {
                KSPublishContent *publishContent = [dictionary objectForKey:@"publishContent"];
                NSInteger progress = [[dictionary objectForKey:@"progress"] integerValue];
                [self cloudCompositeUpdate:publishContent progressPercent:progress];
            }
        }
        // 处理发布成功事件
        BOOL needReload = NO;
        for (NSDictionary *dictionary in productInfos)
        {
            int status = [[dictionary objectForKey:@"status"] intValue];
            if (status == proto_cloud_game_stream_ENUM_AVATAR_COMPOSE_TASK_STATUS_ENUM_AVATAR_COMPOSE_TASK_SUCC)
            {
                KSPublishContent *publishContent = [dictionary objectForKey:@"publishContent"];
                [self manager:nil fileUpload:nil publishContent:publishContent finishedWithResult:kUploadResult_Success];
                return;
            }
            else if (status > proto_cloud_game_stream_ENUM_AVATAR_COMPOSE_TASK_STATUS_ENUM_AVATAR_COMPOSE_TASK_SUCC)
            {
                needReload = YES;
            }
        }
        
        if  (needReload)
        {
            [self reloadTable];
        }
    }];
    
    [self addNearByNotification];
}

- (void)reloadTable
{
    KDEBUG(@"[自动播]--reloadTable force play");
    if ([self.scrollView isKindOfClass:[UITableView class]]) {
        if ([self isPreviewFeedWillChange]) {
            [self processStopMediaPlayIgnoreExpose:YES when:KSTimelineStopVideoWhenDefault];
        }
        [KSLiveStreamPreloadManager shareInstance].isReload = YES;//直播Feed区分刷新和滑动加蒙层
        self.isReloadTable = YES;
        [self.contentTableView reloadData];
        [self.visibleFeedsIndex removeAllObjects];
                
        //恢复自动播放
        BOOL isAppear = (self.viewState == viewStateAppearing) || (self.viewState == viewStateAppeared);
        if (isAppear) {
            if (([[KSUgcPlayManager sharedManager] isUgcPlaying] || [KSUgcPlayManager sharedManager].isRecFeedPauseByDeleteFeed)) {
                [KSUgcPlayManager sharedManager].isRecFeedPauseByDeleteFeed = NO;
                [self processStartMediaPlayIgnoreExpose:YES otherParams:@{@"autoPlayCaller":@"reloadTable"}];
            }
            else {
                KS_WEAK_SELF(self);
                dispatch_async(dispatch_get_main_queue(), ^{
                    KS_STRONG_SELF(self);
                    [self processStartMediaPlayIgnoreExpose:YES otherParams:@{@"autoPlayCaller":@"reloadTable"}];
                });
            }
        }
        
    } else {
        [self.contentCollectionView reloadData];
        
        // 高光时刻自动播放
        [self.contentCollectionView layoutIfNeeded];
        KS_WEAK_SELF(self);
        dispatch_async(dispatch_get_main_queue(), ^{
            KS_STRONG_SELF(self);
            [self processStartMediaPlayIgnoreExpose:YES otherParams:@{@"autoPlayCaller":@"reloadTable"}];
        });
        
    }
}

- (NSString *)currentDateStr{
    NSDate *currentDate = [NSDate date];//获取当前时间，日期
    NSDateFormatter *dateFormatter = [[NSDateFormatter alloc] init];// 创建一个时间格式化对象
    [dateFormatter setDateFormat:@"YYYY/MM/dd"];//设定时间格式,这里可以设置成自己需要的格式
    NSString *dateString = [dateFormatter stringFromDate:currentDate];//将时间转化成字符串
    return dateString;
}

- (void)contentViewReloadData:(UIScrollView *)scrollView {
    KDEBUG(@"[EmptyFeed]Cnt[自动播][%p][%f]%@",scrollView,scrollView.x,[self getLogStrWithCurrentTable]);
    
    if ([scrollView isKindOfClass:[UICollectionView class]]) {
        UICollectionView *currentCollection = SAFE_CAST(scrollView, UICollectionView);
        [currentCollection reloadData];
        
        // 高光时刻自动播放
        [currentCollection layoutIfNeeded];
        KS_WEAK_SELF(self);
        dispatch_async(dispatch_get_main_queue(), ^{
            KS_STRONG_SELF(self);

            if ([self isRecCardTable:self.scrollView]) {
                CGFloat rowHeight = scrollView.height;
                [scrollView setContentOffset:CGPointMake(0, rowHeight * self.curRecFeedRowIndex) animated:YES];
            }
            [self processStartMediaPlayIgnoreExpose:YES otherParams:@{@"autoPlayCaller":@"ReloadData"}];
        });
    } else {
        
        UITableView *currentTable = SAFE_CAST(scrollView, UITableView);
        if (currentTable) {
            KLog(@"【adfeed 网络数据刷新upate列表】");
            [KSLiveStreamPreloadManager shareInstance].isReload = YES;//直播Feed区分刷新和滑动加蒙层
            
            //获取推荐页刷次次数
            NSString *currentTime = [self currentDateStr];
            NSString *localSaveTime = [KSLocalConfigManager stringConfig:@"recommendRefreshCountTime" isGolbal:YES];
            NSInteger refreshCount = [KSLocalConfigManager integerConfig:@"recommendRefreshCount" isGolbal:NO];
            if (![currentTime isEqualToString:localSaveTime]) {
                //如果当前时间和上次记录的不一样（天为单位），刷次计数清零
                refreshCount = 0;
                [KSLocalConfigManager saveLocalConfig:@"recommendRefreshCountTime" commonValue:currentTime isGolbal:YES];
            }
            refreshCount ++;
            [KSLocalConfigManager saveLocalConfig:@"recommendRefreshCount" integerValue:refreshCount isGolbal:NO];
            
            KSimpleFeed *lastPreviewFeed = SAFE_CAST(self.videoPreviewPlayCurCell.busiData, KSimpleFeed);
            // 如果果欢聚歌房的feed，刷新后需要先停掉原来的
            if ([self isPreviewFeedWillChange] || [lastPreviewFeed isPublicSingleKtvFeed]) {
                [self processStopMediaPlayIgnoreExpose:YES when:KSTimelineStopVideoWhenDefault];
            }
 
            KDEBUG(@"[EmptyFeed]Cnt reload");
            [currentTable reloadData];
            [self.visibleFeedsIndex removeAllObjects];
            // 高光时刻自动播放
            [currentTable layoutIfNeeded];
            KS_WEAK_SELF(self);
            dispatch_async(dispatch_get_main_queue(), ^{
                KS_STRONG_SELF(self);
                [self processStartMediaPlayIgnoreExpose:YES otherParams:@{@"autoPlayCaller":@"ReloadData"}];
            });
        }
    }
}

- (BOOL)isPreviewFeedWillChange
{
    KSimpleFeed *lastPreviewFeed = SAFE_CAST(self.videoPreviewPlayCurCell.busiData, KSimpleFeed);
    KSimpleFeed *curPreviewFeed = [self getFeedByTableView:self.contentTableView indexPath:self.videoPreviewPlayCurCell.indexPath];
    BOOL isChange = ![lastPreviewFeed isSameWithSimpleFeed:curPreviewFeed];
    return isChange;
}

- (void)processStartMediaPlayWhenAppear
{
    if (self.disablePlayWhenAppear) {
        self.disablePlayWhenAppear = NO;
        return;
    }
    
    if([KSAppDelegate shareInstance].isShowingSplashAd &&
       self.centerSegControl.selectIndex == KSTimelineTab_Rec &&
       [[KSABTestManager sharedManager] isRecfeedCardStyle]) {
        //正在展示广告就不播放等展示完 KSNotification_HotRelaunchDidFinishedShowAd 这个通知回调
    }
    else {
        [self setAudioSessionCategoryIfNeed];
        if (self.centerSegControl.selectIndex == KSTimelineTab_Follow) {
            // 解决bug，在关注页面刷新后立即切到其他页面，再切回来，歌词没有正常展示的问题
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [self processStartMediaPlayIgnoreExpose:YES otherParams:@{@"autoPlayCaller":@"viewVillAppear"}];
            });
        } else {
            [self processStartMediaPlayIgnoreExpose:YES otherParams:@{@"autoPlayCaller":@"viewVillAppear"}];
        }
    }
}

- (void)createTabIfNeed {
    NSInteger index = self.centerSegControl.selectIndex;
    if (index == KSTimelineTab_Shop) {
        [self createShopPageTabViewIfNeed];
    }
    else if (index == KSTimelineTab_Novel) {
        [self createNovelPageTabViewIfNeed];
    }
    else if (index == KSTimelineTab_ShortPlay) {
        [self createShortPlayTabViewIfNeed];
    }
}

- (void)resumeOnViewDidAppear {
    [[KSUIABTestManager sharedManager] fireUIABTestModule:ksUIABTest_Module_LiveFeed strategyBlock:^(JceKG_AbtestRspItem *item) {
        self.abTestLiveFeedStyle = [item.jce_mapParams[ksUIABTest_Module_LiveFeed_Style] integerValue];
    }];
#ifdef DEBUG
    self.abTestLiveFeedStyle = 2;
#endif
    
    // 曝光上报
    [self traceReportGetFlowerExpose];
    [self missionCenterExposeReport];
    [self traceReportForExposeSearch];
    
    /// 动态插入活动Tab
    [self checkActivityTabIfNeed];
    
    [self informHippyVCAppear];
    
    //刷新鲜花数量
    [[KSFlowerManager sharedManager] loadFlowerFromRemoteWithFroceStore:YES completion:nil reportModel:[[KSGiftProtocalReportModel alloc] initWithScene:uni_scene_context_emScenePos_SCENE_POS_ASYN_DEFAULT actionType:uni_scene_context_TriggerActionType_SYSTEM_TRIGGERED]];
    
    //刷新任务可领取鲜花数
    [self refreshTaskInfo];
        
    // 评论键盘
    if ([self.feedManager respondsToSelector:@selector(recoverCommentIfNeeded)])
    {
        [self.feedManager recoverCommentIfNeeded];
    }
    
    [self showDebugToast];
    
    if ([self isRecCardTable:self.scrollView]) {
        [[UIApplication sharedApplication] setIdleTimerDisabled:YES];
    }

    BOOL isFeedListNull = (self.currentFilterFeedsInfo.feedsList.count == 0);
    if (isFeedListNull)
    {
        // currentFilterFeedsInfo 为空但是推荐用户这里有单独推荐数据源，这里做个保护
        if ([[KSTimelineManager sharedManager] checkIsFollowBizWithFilterMask:self.currentFilterFeedsInfo.filterMask])
        {
            KSFilterFeedsInfo *feedsInfo = [[KSTimelineManager sharedManager] getFeedInfoWithFilterMask:self.currentFilterFeedsInfo.filterMask];
            if (feedsInfo.feedsList.count > 0)
            {
                isFeedListNull = NO;
            }
        }
    }
    
    if ([KSTimelineManager sharedManager].currentFiltrMask == [KSTimelineManager sharedManager].filtrMaskLive)
    {
        // 重新加载动态直播 hippy
        [self.entertainmentView reloadHippyIfNeeded];
    }
    
    if (isFeedListNull || self.viewFirstAppear)
    {
        ///直播页
        if ([KSTimelineManager sharedManager].currentFiltrMask != [KSTimelineManager sharedManager].filtrMaskLive)
        {
            [self loadFeedsListFromLocal];
        }
        
        if ([KSTimelineManager sharedManager].currentFiltrMask == [KSTimelineManager sharedManager].filtrMaskRecommendNearBy)
        {
            [self loadFeedsNear];
        }
        else if ([KSTimelineManager sharedManager].currentFiltrMask == [KSTimelineManager sharedManager].filtrMaskLive)
        {
            // 直播页的数据请求由EntertainmentView控制，因此不在此处进行数据加载
        }
        else
        {
            /// 动态大卡片
            BOOL isRecTab = [KSTimelineManager sharedManager].currentFiltrMask == [KSTimelineManager sharedManager].filtrMaskRecommend;
            BOOL hasRecData = [KSTimelineManager sharedManager].feedsInfo_Recommend.feedsList.count > 0;
            BOOL isPrefetching = [KSTimelineManager sharedManager].isPrefetchingRecFeedData;
            if (hasRecData || isPrefetching) {
                /// 有数据或者在预加载中，不再重复刷
                self.recommendLoadFirstTime = NO;
            }
            
            if (self.viewFirstAppear && isRecTab && hasRecData && !isPrefetching) {
                //如果已经预拉取了那么直接刷新
                [self handleRecFeedDataPrefetchEvent];
            }
            else if ((isRecTab && !isPrefetching && !hasRecData) || !isRecTab) {
                /// 拉取feed
                NSMutableDictionary* extarInfo = [NSMutableDictionary new];
                KSFeedFreshInfo* freshInfo = [[KSFeedFreshInfo alloc] initWithTiming:TimelineRefreshTiming_ViewDidAppearPrefetchNotBack];
                [extarInfo setObject:freshInfo forKey:KSRefreshSource];
                [self loadFeedsListFromRemote:extarInfo];
            }
        }
    }
    else if (self.socialBindRefreshPhase == FollowFeedBindSocialStatusFeed)
    {
        if (self.centerSegControl.selectIndex == KSTimelineTab_Follow)
        {
            // 浅关系链绑定社交关系后返回刷新
            NSMutableDictionary* extarInfo = [NSMutableDictionary new];
            KSFeedFreshInfo* freshInfo = [[KSFeedFreshInfo alloc] initWithTiming:TimelineRefreshTiming_FriendshipRefrsh];
            [extarInfo setObject:freshInfo forKey:KSRefreshSource];
            [self loadFeedsListFromRemote:extarInfo];
            
        }
    }
   
    //这里在self.viewFirstAppear = NO前执行
    KSTimelineRootVCExposeSource reportSource = [self calculateExposeSourceWhileVCAppear];
    
    KS_WEAK_SELF(self);
    if (self.viewFirstAppear) {
        //动态关注动画 首次创建appear之后再启动下载
        [KSResourceDownloadBusinessConfig checkAssetsWhen:KSAssetDownloadCheckWhenForce 
                                                  forType:KSAssetDownloadTypeLocalConfig
                                               resourceID:KSAssetDownloadLocalIDFeedLottieFollow
                                         downloadDelegate:nil];
        
        /// 下载歌神卡片资源
        [KSResourceDownloadBusinessConfig checkAssetsWhen:KSAssetDownloadCheckWhenForce
                                                  forType:KSAssetDownloadTypeLocalConfig
                                               resourceID:KSAssetDownloadLocalIDSongGodCardVoiceLottie
                                         downloadDelegate:nil];
        
        [[KSRelationManager sharedManager] getBindInfo:[KSLoginManager sharedInstance].curUserInfo.userId
                                       completionBlock:^(id ksObject, NSDictionary *customInfo, NSError *error) {
            CHECK_SELF_AND_RETURN()
            [self updateTableViewHeaderInfo];
        }];
        
        [self setupFirstLoginLocation];
        
        /// Ktv引导放到appear处理
        [self initKtvEnterRoomGuide];
    }
    
    self.isViewDisappear = NO;//进入vc后置为NO;
    self.viewFirstAppear = NO;
    
    [[WnsConfigManager sharedInstance].appConfig.kSongSetting mobilePattern];
    
    [self updateMutilFormsCommentPics];
    
    [self loadUserInfo];
    
    [self requestRecFeedWhenBackFromSingleDetailVC];
        
    [self resetTraceReportFeedShowDurationWhenAppear];
    
    
    if (self.isBackFromClickNearbyMsg) // 在私聊中点击同城返回该界面在私聊内上报此处不做处理
    {
        self.isBackFromClickNearbyMsg = NO;
    }
    else
    {
       [self onFeedTableViewExposeWithType:reportSource];
    }
        
    
    [self loadAVSdkRole];
    

    //查看是否需要加载新人礼入口
    [self checkNewUserGiftUpdate];
    
    //页面显示的时候处理下视频预览自动播放

    KDEBUG(@"[自动播]--viewappear force play");
    [self processStartMediaPlayWhenAppear];
    
    [self.feedManager beginFlowerExposeTimerWhileAppear];
    [self.feedManager onVCDidAppear];
    
    if (self.centerSegControl.selectIndex == KSTimelineTab_Live) {
        // 在直播tab停留5秒后开始预加载
        [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(preloadAnimationResources) object:nil];
        [self performSelector:@selector(preloadAnimationResources) withObject:nil afterDelay:5];
    } else if (self.centerSegControl.selectIndex == KSTimelineTab_Activity) {
        [self informActivityPageHippyVCAppear];
    }

    if ([self isRecCardTable:self.scrollView]) {
        //大卡片显示或者隐藏全局播放浮窗
        [[KSGlobalPlayFloatingWindowManager sharedManager] showOrHidePlayFloatingWindow];
        [[KSFloatWindowManager sharedManager] distoryFloatWindowIfExsit];
        if ([[KSPIPManager sharedManager] isPictureInPictureActive]){
            [[KSPIPManager sharedManager] destroyPIPWindowIfExist];
        }
    }
    
    [self uiStrategyJumpRecommendRefresh];//进入页面时判断一下是否命中流量分发跳推荐

    [self checkRecfeedLastExposedTimeStampOverTwoHours];
    
    [self saveAppDefaultLocationTypeShowTimestamp:self.centerSegControl.selectIndex];

    if ([[KSSplashAdManager sharedManager] isInOneShotProcess]) {
        [[KSSplashAdManager sharedManager] startTransAnimationAndComplete:^{
            [self resetTransparencyAfterOneShot];
            [[KSNavigationManager sharedManager].getRootTabBarController dealWithPasteboard];
        }];
    } else {
        dispatch_async(dispatch_get_main_queue(), ^{// 延迟到下一循环
            [[KSLoginManager sharedInstance] showAuthGuideFrom:KSThirdAuthGuideFrom_TimelineRoot];
        });
    }
    
    // 启动音频动态轮询
    [KSVoiceStatusRequester.sharedRequester fireAtOnce];
    [KSVoiceStatusRequester.sharedRequester poll];
    
    if (![TLMAudioSessionSharedManager sharedManager].hasRegister) {
        if (KSAudioCommentSwitcher.sharedSwitcher.enableReplaceTRTCAudioSessionForTimelineRoot)
        {
            [TXLiveBase setAudioSessionDelegate:[KSAudioSession.sharedInstance getTXLiveAudioSessionDelegate]];
        }
    } else {
        //todo:danecai,跟phenli确认上面代码其实已经没有跑，为了保险，等新方案稳定之后整块删除
    }
    
    
    // 恢复小游戏浮窗
    [[KSGlobalPlayFloatingWindowManager sharedManager] recoverMiniGameWindowIfNeeded];
    
    // viewDidAppear
    [self informSquareHippyVCAppear];
    
    [self informNovelPageHippyVCAppear];
    
    if (self.centerSegControl.selectIndex == KSTimelineTab_Shop) {
        [self.ecommerceTabView ecommerceViewDidAppear];
        [self.navBar setBackgroundColor:[UIColor clearColor]];
    }
    
#ifdef INTERNALBUILD
    //内部打印下基础地址，方便privateProtect做翻译查找
    uint32_t count = _dyld_image_count();
    KLog(@"[PrivateProtect]Dyld image count %d", count);
    char *image_name = (char *)_dyld_get_image_name(0);
    const struct mach_header *mh = _dyld_get_image_header(0);
    intptr_t vmaddr_slide = _dyld_get_image_vmaddr_slide(0);
    KLog(@"[PrivateProtect]Image name %s at address 0x%llx and ASLR slide 0x%lx.\n", image_name, (mach_vm_address_t)mh, vmaddr_slide);
#endif
}

- (void)pauseOnViewWillDisappear {
    [self.feedManager onVCWillDisappear];
    
    [[KSFeedPreviewManager sharedManager] pausePreview];

    // 关闭评论
    [self.feedManager dismissAddcommentView];
    
    [self hideShareFeedGuideBar];
    [self hidePlayingGuideView];
    
    [self stopVisibleCellsPlayVideoAnimation];
    if (self.maskView)
    {
        [self.maskView removeFromSuperview];
        self.maskView = nil;
    }
    
    if ([KSAnimCommon isWifi] || [KSPhoneCardManager isEnableCellularAndKingCard])
    {
        if ([WnsSwitchStringConfig(@"DownloadAnimResWhenLeaveFeed") integerValue] > 0 && !LocalBoolConfig(kCleanManagerHasCleanedGiftResKey)) {
            // WNS配置了打开，并且用户没手动删除过动画资源时，才能在这里触发预加载
            [self preloadAnimationResources];
        }
    }

    // 下载语音分析模型文件 （打分使用）
    [KSCommonAudioScore preDownloadVoiceAnalysisFileWithProtocolClass:[KSAudioScoreBusinessConfiger class]];
    
    //话题tab的位置信息置nil
    [self saveAppDefaultLocationTypeShowTimestamp:self.centerSegControl.selectIndex];
    
    // 结束轮询
    [KSVoiceStatusRequester.sharedRequester hold];
    if (self.centerSegControl.selectIndex == KSTimelineTab_Live)
    {
        [self.entertainmentView informLiveTabDisappear];
    }
    
    if (![TLMAudioSessionSharedManager sharedManager].hasRegister) {
        if (KSAudioCommentSwitcher.sharedSwitcher.enableReplaceTRTCAudioSessionForTimelineRoot)
        {
            [TXLiveBase setAudioSessionDelegate:nil];
        }
    } else {
        //todo:danecai,跟phenli确认上面代码其实已经没有跑，为了保险，等新方案稳定之后整块删除
    }

    
    [[KSLiveStreamPreloadManager shareInstance] pauseAllWrapperPlayers];
    
    if  (self.centerSegControl.selectIndex == KSTimelineTab_Live) {
        [self.entertainmentView entertainmentViewWillDisAppear];
    }
    
    if (self.centerSegControl.selectIndex == KSTimelineTab_Shop) {
        [self.ecommerceTabView ecommerceViewWillDisAppear];
    }
    
    [[KSKTVRoomCardPreRunManager sharedManager] forceCleanStatus];
    // viewWillDisappear
    [self informSquareHippyVCDisappear];
    [self informNovelPageHippyVCDisappear];
}

- (void)pauseOnEnterAVCallChatting {// `-viewWillDisappear`
    [self pauseOnViewWillDisappear];
    [self pauseOnViewDidDisappear];
}

- (void)resumeOnRejectAVCallChatting {
    //计时上报，一定要在开头和结尾
    [[KSPerformanceReportHelper sharedInstance] startTraceLoad:self andSelector:@selector(viewWillAppear:)];
    
    [self resumeOnViewWillAppear];
    
    [[KSPerformanceReportHelper sharedInstance] startTraceLoad:self andSelector:@selector(viewDidAppear:)];
    
    [self resumeOnViewDidAppear];
}

- (void)startVisibleCellsPlayVideoAnimation
{
    for (KSLayoutableTimelineFeedCellV2 *cell in self.contentTableView.visibleCells) {
        if ([cell isKindOfClass:[KSLayoutableTimelineFeedCellV2 class]]) {
            [cell startVideoAnimationPlay];
        }
    }
}

// 停止播放所有可见cell的视频动画
- (void)stopVisibleCellsPlayVideoAnimation
{
    for (KSLayoutableTimelineFeedCellV2 *cell in self.contentTableView.visibleCells) {
        if ([cell isKindOfClass:[KSLayoutableTimelineFeedCellV2 class]]) {
            [cell stopVideoAnimationPlay];
        }
    }
}

- (void)saveAppDefaultLocationTypeShowTimestamp:(KSTimelineTabSelectIndex)selecIndex
{
    if (!IsValidTimelineTabIndex(selecIndex)) {
        NSAssert(NO, @"传入的TabIndex不合法");
        return;
    }
    KSABTestAppDefaultLocationExperimentType type = KSABTestAppDefaultLocationExperimentType_None;
    if (selecIndex == KSTimelineTab_Rec)
    {
        type = KSABTestAppDefaultLocationExperimentType_FeedTabRecommend;
    }
    else if (selecIndex == KSTimelineTab_Follow)
    {
        type = KSABTestAppDefaultLocationExperimentType_FeedTabFollow;
    }
    else if (selecIndex == KSTimelineTab_Live)
    {
        type = KSABTestAppDefaultLocationExperimentType_FeedTabLive;
    }
    else if (selecIndex == KSTimelineTab_RecNearBy)
    {
        //合规问题，暂时先不支持到附近-Tab,这里兜底去推荐
        type = KSABTestAppDefaultLocationExperimentType_FeedTabRecommend;
    }
    else if (selecIndex == KSTimelineTab_Novel)
    {
        type = KSABTestAppDefaultLocationExperimentType_Novel;
    }
    else if (selecIndex == KSTimelineTab_ShortPlay)
    {
        type = KSABTestAppDefaultLocationExperimentType_Party;
    }
    else if (selecIndex == KSTimelineTab_Shop)
    {
        type = KSABTestAppDefaultLocationExperimentType_Shop;
    }
    else if (selecIndex == KSTimelineTab_Activity)
    {
        /// 不需要保存
        return;
    }
    else if (selecIndex == KSTimelineTab_GameCenter)
    {
        type = KSABTestAppDefaultLocationExperimentType_Game;
    }
    else
    {
        // 如果selectIndex在合法范围内但是又没有匹配上对应的index，1. 说明开发者这里忘记加上新的tab了。2. 或者就是这里越界了（一般不会，因为使用的地方传的都是真实的index）
        NSAssert(NO, @"[saveAppDefaultLocationTypeShowTimestamp] 未知的Tab类型");
    }
    
    [[KSABTestManager sharedManager] saveAppDefaultLocationTypeShowTimestamp:type];
}



- (void)preloadAnimationResources
{
   
    KSAnimResDownloadItem *downloadItem = [KSAnimResDownloadItem downloadItemWithResourceId:KSAssetDownloadLocalIDBubbleLight type:KSAnimResDownloadTypeLocalAsset priority:KSDownloadPriorityLow downloadBlock:nil];
    [[KSAnimResDownloadCenter sharedInstance] download:downloadItem];

    [[KSDynamicAnimResManager sharedInstance] checkResourceList:nil forceFetch:NO downloadPriority:KSDownloadPriorityLow];
}


- (void)pauseOnViewDidDisappear {
    [self traceReportFeedShowDurationWhenDisappear];
    
    [self traceReportFeedRefreshAndPull];
    
    self.isViewDisappear = YES;
    
    if (self.centerSegControl.selectIndex == KSTimelineTab_Shop) {
        [self.ecommerceTabView ecommerceViewDidDisAppear];
    }
    [self informHippyVCDisappear];
    
    KINFO(@"process StopMediaPlay When Disappear")
    [self processStopMediaPlayIgnoreExpose:YES when:KSTimelineStopVideoWhenDisappear];
    [[KSFeedPreviewManager sharedManager] pausePreview];
   
    KSHippyPopupView *popupView = [KSHippyPopupView getSharedInstance];
    if (popupView) {
        [popupView destroy];
    }
    [self.feedManager onVCDidDisappear];
    [self.feedManager stopFlowerExposeTimerWhileDisappear];
    self.lastRecfeedExposeTS = [[NSDate date] timeIntervalSince1970];
    [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(preloadAnimationResources) object:nil];
    
    [KSUgcPlayManager sharedManager].isRecFeedWillEndDraging = NO;
    
    if (self.centerSegControl.selectIndex == KSTimelineTab_Activity) {
        [self informActivityPageHippyVCDisappear];
    }
}

- (void)resumeOnViewWillAppear {
    BOOL isPresentImagePickVC = [self.presentedViewController isKindOfClass:[KSImagePickerVC class]];
    BOOL isPresentCameraVC = [self.presentedViewController isKindOfClass:[UIImagePickerController class]];
    self.disablePlayWhenAppear = isPresentImagePickVC || isPresentCameraVC;
    
    if ([self.feedManager.curSimpleFeed isKindOfLiveShowFeed]) {
        [self processStartMediaPlayWhenAppear];
    }
    
    [[KSProductUploadManagerBridge sharedManager] registerDelegate:self forScene:KSUploadTaskDisplaySceneTimeline];
    
    [KSSoloAlbumCreateManager sharedManager].delegate = self;
    
    [self updateUploadTaskArray];
        
    // 刷新头部
    [self updateTableViewHeaderInfo];
    
    [self processVideoView];

    
    if ([[KSSplashAdManager sharedManager] isInOneShotProcess] && self.centerSegControl.selectIndex != KSTimelineTab_Rec) {
        //当是来自于OneShot的情况下，需要强制切到大卡片,这里做一下检测
        [self.centerSegControl setSelectIndex:KSTimelineTab_Rec];
        KLog(@"[OneShot]动态页强制切换到大卡片");
    }
    
    if (self.centerSegControl.selectIndex == KSTimelineTab_Live)
    {
        [self.entertainmentView checkReload];
        [self.entertainmentView informLiveTabAppear];
    }
    
    if (self.centerSegControl.selectIndex == KSTimelineTab_Follow) {
        [self requestNewerListFeedBanner:YES];
    }
    
    if (self.centerSegControl.selectIndex != KSTimelineTab_Live ||
        [self isInLiveBigCardTab:self.centerSegControl.selectIndex]) {
        if ([[KSPIPManager sharedManager] isPictureInPictureActive]){
            [[KSPIPManager sharedManager] destroyPIPWindowIfExistImmediatly:YES];
        }
    } else {
        if ([[KSPIPManager sharedManager] isPictureInPictureActive]) {
            [self.entertainmentView notifyNativePIPStatus:YES];
        }
    }
    
    if (self.centerSegControl.selectIndex == KSTimelineTab_Live) {
        [self.entertainmentView entertainmentViewWillAppear];
        if (self.entertainmentView && ![self.entertainmentView isLiteCardVCShowing]) {
            [self.navBar setBackgroundColor:UIColor.blackColor];
        }
    }
}

- (void)informHippyVCAppear {
    if (self.centerSegControl.selectIndex == KSTimelineTab_GameCenter) {
        [self informGamePageHippyVCAppear];
    }
}

- (void)informHippyVCDisappear {
    if (self.centerSegControl.selectIndex == KSTimelineTab_GameCenter) {
        [self informGamePageHippyVCDisappear];
    }
}

- (void)processVideoView
{
    if ([[KSUgcPlayManager sharedManager] isUgcPlaying])
    {
        for (UITableViewCell *cell in self.contentTableView.visibleCells) {
            if ([cell isKindOfClass:[KSLayoutableTimelineFeedCellV2 class]])
            {
                [((KSLayoutableTimelineFeedCellV2*)cell) didVCWillAppear];
            }
        }
    }
}

- (void)appEnterForground
{
    KDEBUG(@"[自动播]--EnterForeground force play");
    if([KSAppDelegate shareInstance].isShowingSplashAd &&
       self.centerSegControl.selectIndex == KSTimelineTab_Rec) {
        //正在展示广告就不播放等展示完 KSNotification_HotRelaunchDidFinishedShowAd 这个通知回调
    }
    else {
        [self setAudioSessionCategoryIfNeed];
        [self processStartMediaPlayIgnoreExpose:YES otherParams:@{@"autoPlayCaller":@"appEnterForground"}];
    }
    
    [self.feedManager beginFlowerExposeTimerWhileAppear];
    [self loadUserInfo];
    [self resetTraceReportFeedShowDurationWhenAppear];
    [[KSRelationManager sharedManager] getBindInfo:[KSLoginManager sharedInstance].curUserInfo.userId completionBlock:nil];
    
    [self checkRecfeedLastExposedTimeStampOverTwoHours];
    
    [self startVisibleCellsPlayVideoAnimation];
    
    if (self.centerSegControl.selectIndex == KSTimelineTab_Activity) {
        [self informActivityPageHippyVCAppear];
    }
    [self informHippyVCAppear];
}

- (void)appEnterBackground
{
    [self processStopMediaPlayIgnoreExpose:YES when:KSTimelineStopVideoWhenEnterBg];
    
    [self.feedManager stopFlowerExposeTimerWhileDisappear];
    
    [self traceReportFeedShowDurationWhenDisappear];
    
    self.lastRecfeedExposeTS = [[NSDate date] timeIntervalSince1970];
    
    [self stopVisibleCellsPlayVideoAnimation];
    
    [[KSLiveRoomPreLoadManager shareInstance] cleanPrePullStreamSource];
    
    if (self.centerSegControl.selectIndex == KSTimelineTab_Activity) {
        [self informActivityPageHippyVCDisappear];
    }
    [self informHippyVCDisappear];
}

- (void)createNavBarButton
{
    // 搜索按钮
    KSBaseButton *searchBtn = [KSBaseButton buttonWithType:UIButtonTypeCustom];
    searchBtn.imageSizeModel = KSSizeMode_Mid;
    [searchBtn setImage:[UIImage imageNamed:@"ic_timeline_nav_search_black"] forState:UIControlStateNormal];
    [searchBtn.imageView setContentMode:UIViewContentModeCenter];
    [searchBtn addTarget:self action:@selector(searchBtnDidClick) forControlEvents:UIControlEventTouchUpInside];
    [self.navBar setUpRightButtonWithButton:searchBtn];
    self.navBar.rightNavBarBtn.right = SCREEN_WIDTH - (IS_IPHONE5 ? KSMargin_5 : KSMargin_15);
    searchBtn.accessibilityLabel = @"搜索";
    
    // 任务中心
    [self setUpNavBarMissionBtnWithBtn];
    
}

// 任务中心
- (void)setUpNavBarMissionBtnWithBtn
{
    KSNotificationTabButton *btn = [KSNotificationTabButton buttonWithType:UIButtonTypeCustom];
    [self.navBar setUpLeftNavBarButtonWithButton:btn];
    // 动态配置任务中心入口大小
    NSInteger entranceSize = WnsSwitchIntegerConfig(@"TaskEntranceSize");
    // 为了让Btn里子元素撑满
    btn.contentHorizontalAlignment = UIControlContentHorizontalAlignmentFill;
    btn.contentVerticalAlignment = UIControlContentVerticalAlignmentFill;
    
    btn.frame = CGRectMake(20, 20, _size_mid(entranceSize), _size_mid(entranceSize));
    [btn setImage:[UIImage imageNamed:@"V7Feed_task_new_style"] forState:UIControlStateNormal];
    [btn addTarget:self action:@selector(leftNavBarBtnDidClick) forControlEvents:UIControlEventTouchUpInside];
    btn.accessibilityLabel = @"任务中心";
    btn.enabled = YES;
    self.badgeBtn = btn;
    
    self.navBar.leftNavBarBtn.hitTestEdgeInsets = UIEdgeInsetsMake(-13, -13, -13, -13);
    self.badgeBtn.centerY = self.navBar.rightNavBarBtn.centerY;
    self.badgeBtn.left = DYNAMIC_VALUE_UNIVERSAL_IPHONEX(10, 10, 20, 20, 20, 20);
}

- (void)setUpShouldRefreshFlag:(NSNotification *)noti
{
    self.shouldRefreshEntrace = YES;
    if ([noti.object[@"refreshNow"] boolValue]) {
        [self refreshTaskInfo];
        KINFO(@"feed页接收到任务完成push，即将刷新红点");
    }
}



- (void)leftNavBarBtnDidClick
{
    //判断当前是否是信息流二期广告滑动过，若首次则阻止跳转
    if ([self isFeedNativaFirstShow]) {
        return;
    }
    self.badgeBtn.messageBadgeView.hidden = YES;
    [KSBadgeUpdateManager sharedManager].taskCenterNum = 0;
    
    //发送状态改变通知
    [[NSNotificationCenter defaultCenter] postNotificationName:KSNotification_MissionEntranceChange object:nil];

    NSString *jumpUrl;
    if (!self.feedTaskEntranceRsp || self.feedTaskEntranceRsp.jce_strJumpUrl.length == 0) {
        jumpUrl = WnsUrlStringConfig(@"TaskHome");
        [self refreshTaskInfo];
    } else {
        jumpUrl = self.feedTaskEntranceRsp.jce_strJumpUrl;
    }
    [[KSNavigationManager sharedManager] showWebView:jumpUrl from:KSWebOpenFrom_TimelineTask];
    
    [self missionCenterClickReport];
    [self traceReportGetFlowerClicked];
}

- (void)searchBtnDidClick
{
    KSSearchPageVCConfig *cfg = [self didTapSearchBar];

    [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
        reportModel.key = @"feed_following#search_button#null#click#0";
        //int1=1-带搜索词，2-不带搜索词，取不到值报-1
        reportModel.commonInt1 = cfg.hotWord ? 1 : 2;
    }];
}

- (void)showHotTabAtIndex:(KSHotTabSelectIndex)index frompage:(NSString *)frompage
{
    KLog(@"[SegTab]show tab = hot");
    [[self centerSegControl] setSelectIndex:KSTimelineTab_Rec];
    [self refreshVC];
    
    NSString *from_page_str = @"push_page_virtual#push_click#null";
    if ([frompage isEqualToString:from_page_str]) {
        [self onFeedTableViewExposeWithType:KSTimelineRootVCExposeSource_PushRecommend];
    }
}


#pragma mark - ksSegmentedControlDelegate 关注热门好友附近切换
- (void)KSSegmentedControl:(KSSegmentedControl *)tabView didSelectTabAtIndex:(NSUInteger)index extraInfo:(NSDictionary *)extraInfo {
    KINFO(@"tabView didSelectTabAtIndex %ld", (long)index);
    
    [self.navBar setBackgroundColor:UIColor.clearColor];
    [self traceReportClickCurrentTab:index];
    
    if (!WnsLocalServerBoolConfig(@"disableRecommendDarkMode")) {
        self.rootTabBarController.darkMode = index == KSTimelineTab_Rec;
    }
    
    if (index == KSTimelineTab_Rec) {
        [self createRecommendTabViewIfNeed];
    }
    else if (index == KSTimelineTab_Follow) {
        [self createFollowTabViewIfNeed];
    }
    else if (index == KSTimelineTab_Live) {
        [self createLiveTabViewIfNeed];
    }
    else if (index == KSTimelineTab_Shop) {
        [self createShopPageTabViewIfNeed];
    }
    else if (index == KSTimelineTab_Novel) {
        [self createNovelPageTabViewIfNeed];
    }
    else if (index == KSTimelineTab_ShortPlay) {
        [self createShortPlayTabViewIfNeed];
    }
    else if (index == KSTimelineTab_RecNearBy) {
        [self createNearByTabViewIfNeed];
    }
    else if (index == KSTimelineTab_Activity) {
        [self createActivityTabViewIfNeed:NO];
    }
    else if (index == KSTimelineTab_GameCenter) {
        [self createGameCenterTabViewIfNeed];
    }

    /// 清除红点
    [self.centerSegControl clearUnreadBadgeWithIndex:index];
    
    if ([KSABTestManager.sharedManager enableSlideMission] && !self.vMissionGestureRecognizer) {
        [self addVMissionGesture];
    }
    
    if (index == KSTimelineTab_Follow) { // 点击关注
        [self refreshFinishedWithTableView:self.mFollowTableView];
        [[KSLoginManager sharedInstance] queryCanPullSocialRelation:^(id ksObject, NSDictionary *customInfo, NSError *error) {
            if (ksObject) {
                [[KSLoginManager sharedInstance] showFriendAuthRemind:@"timelineroot_follow"];
            }
        }];
    }
    
    // 点击“关注”或“同城”拉取评论面板动态表情包组信息
    if (index == KSTimelineTab_Follow || index == KSTimelineTab_RecNearBy) {
        [self.feedManager loadStickerGroupInfo];
    }
    
    if (tabView.lastSelectIndex == index && index == KSTimelineTab_Follow &&
        (([KSTimelineManager sharedManager].currentFiltrMask == [KSTimelineManager sharedManager].filtrMaskFollow &&
          [KSBadgeUpdateManager sharedManager].feedNum > 0) ||
         ([KSTimelineManager sharedManager].currentFiltrMask == [KSTimelineManager sharedManager].filtrMaskQQWXFriends &&
          [KSBadgeUpdateManager sharedManager].feedFriendNum > 0)))
    {
        KSFeedFreshInfo* freshInfo = [[KSFeedFreshInfo alloc] initWithTiming:TimelineRefreshTiming_TabDidSelect];
        NSMutableDictionary* temp = [NSMutableDictionary new];
        [temp setObject:freshInfo forKey:KSRefreshSource];
        [self performSelector:@selector(forceRefreshWithUserInfo:) withObject:temp afterDelay:0.2];
        KINFO(@"tabViewIndex=%ld currentMask=%ld", (long)index,(long)[KSTimelineManager sharedManager].currentFiltrMask );
        return;
    }
    
    // view还没出来不要去save，影响其他底tab的记忆逻辑
    if (!self.isViewFirstAppear) {
        [self saveAppDefaultLocationTypeShowTimestamp:index];
    }
        
    if ([self isRecCardTable:self.scrollView]) {
        //scrollView.height为0时候要保护下
        if (self.scrollView.height > 0)
        {
            self.curRecFeedRowIndex = (NSInteger)(self.scrollView.contentOffset.y / self.scrollView.height);
        }
        else
        {
            self.curRecFeedRowIndex = 0;
        }
    }
    
    [self traceReportVCExposeDurationWhenDisappear];
    [[UIApplication sharedApplication] setIdleTimerDisabled:NO];
    NSInteger lastFilterMask = [KSTimelineManager sharedManager].currentFiltrMask;
    NSInteger selectedIndex = index;
    
    if (self.navigateTipView && self.navigateTipView.type != KSTimelineNavigateTip_NoNetwork) {
        [self hideNavigateTipView];
    }
    
    [self traceReportFeedRefreshAndPull]; // 必须放在currentFiltrMask修改之前，否则会上报错误的id
    
    // normanliu,更新contentTableView前停止当前列表的media播放
    if (tabView.lastSelectIndex != index)
    {
        [self processStopMediaPlayIgnoreExpose:YES];
    }
    
    //切换tab取消预拉
    if (tabView.lastSelectIndex == KSTimelineTab_Rec && tabView.lastSelectIndex != index) {
        if([KSLiveRoomPreLoadManager shareInstance].appLNCHLoadLiveTabDone) {
            [[KSLiveRoomPreLoadManager shareInstance] cleanPrePullStreamSource];
        }
    }
    
    if (selectedIndex != KSTimelineTab_Follow && tabView.lastSelectIndex == KSTimelineTab_Follow) {
        [self stopVisibleCellsPlayVideoAnimation];
    }
    
    BOOL isViewAppear = self.viewState == viewStateAppearing || self.viewState == viewStateAppeared;
    [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(preloadAnimationResources) object:nil];
    
    if (selectedIndex == KSTimelineTab_Follow)
    {
        // 关注Tab
        self.contentTableView = self.mFollowTableView;
        KLog(@"[feed] followTabCurMask=%ld",(long)self.followTabCurFiltrMask);
        if (self.followTabCurFiltrMask == [KSTimelineManager sharedManager].filtrMaskQQWXFriends)
        {
            /// 好友动态
            [KSTimelineManager sharedManager].currentFiltrMask = [KSTimelineManager sharedManager].filtrMaskQQWXFriends;
            self.currentFilterFeedsInfo = [KSTimelineManager sharedManager].feedsInfo_ForQQWXFriends;
        }
        else if (self.followTabCurFiltrMask == [KSTimelineManager sharedManager].filtrMaskUgcFeed)
        {
            /// 全部作品
            [KSTimelineManager sharedManager].currentFiltrMask = [KSTimelineManager sharedManager].filtrMaskUgcFeed;
            self.currentFilterFeedsInfo = [KSTimelineManager sharedManager].feedsInfo_ForUgcFeed;
        }
        else if (self.followTabCurFiltrMask == [KSTimelineManager sharedManager].filtrMaskUgcFeedQQWX)
        {
            /// 好友作品
            [KSTimelineManager sharedManager].currentFiltrMask = [KSTimelineManager sharedManager].filtrMaskUgcFeedQQWX;
            self.currentFilterFeedsInfo = [KSTimelineManager sharedManager].feedsInfo_ForUgcFeedQQWX;
        }
        else if (self.followTabCurFiltrMask == [KSTimelineManager sharedManager].filtrMaskTopic)
        {
            /// 话题
            [KSTimelineManager sharedManager].currentFiltrMask = [KSTimelineManager sharedManager].filtrMaskTopic;
            self.currentFilterFeedsInfo = [KSTimelineManager sharedManager].feedsInfo_ForTopicFeed;
        }
        else
        {
            /// 全部动态
            [KSTimelineManager sharedManager].currentFiltrMask = [KSTimelineManager sharedManager].filtrMaskFollow;
            self.currentFilterFeedsInfo = [KSTimelineManager sharedManager].feedsInfo_ForFollow;
        }
        
        [self.rootTabBarController processFeedOffset:self.mFollowTableView.contentOffset.y];
    }
    else if (selectedIndex == KSTimelineTab_Live)
    {
        // 直播
        self.contentCollectionView = self.entertainmentView.collectionView;
        
        [KSTimelineManager sharedManager].currentFiltrMask = [KSTimelineManager sharedManager].filtrMaskLive;
        
        // 每次从其他tab切回直播tab的时候，检查是否需要重新加载hippy包
        if (isViewAppear) {
            [self.entertainmentView checkReload];
        }
        
        // 7.0 附近页被动态直播页替换了
        // 在直播tab停留5秒后开始预加载
        [self performSelector:@selector(preloadAnimationResources) withObject:nil afterDelay:5];
        
        [self.rootTabBarController processFeedOffset:self.contentCollectionView.contentOffset.y];
    }
    else if (selectedIndex == KSTimelineTab_Rec)
    {
        // 推荐
        if ([KSABTestManager sharedManager].recFeedStyle == KSABTestRecFeedUIType_Collection)
        {
            self.contentCollectionView = self.mHotCollectionView;
        }
        else
        {
            self.contentTableView = self.mHotTableView;
        }
        
        if ([self isRecCardTable:self.scrollView]) {
            [[UIApplication sharedApplication] setIdleTimerDisabled:YES];
        }
        
        [KSTimelineManager sharedManager].currentFiltrMask = [KSTimelineManager sharedManager].filtrMaskRecommend;
        self.currentFilterFeedsInfo = [KSTimelineManager sharedManager].feedsInfo_Recommend;
        
        //显示或者隐藏全局播放浮窗
        [[KSGlobalPlayFloatingWindowManager sharedManager] showOrHidePlayFloatingWindow];
        
        //点击推荐后需要将红点隐藏
        if ([KSBadgeUpdateManager sharedManager].feedRecommend != 0) {
            [KSBadgeUpdateManager sharedManager].feedRecommend = 0;
        }
    } else if (selectedIndex == KSTimelineTab_RecNearBy) {
        // 同城
        if (self.nearbySubIndex == NearbyTabBarSubIndex_Dynamic)
        {
            self.contentTableView = self.mNearbyFeedTableView;
        }
        else if (self.nearbySubIndex == NearbyTabBarSubIndex_User)
        {
            self.contentTableView = self.mNearbyUserTableView;
        }
        
        [KSTimelineManager sharedManager].currentFiltrMask = [KSTimelineManager sharedManager].filtrMaskRecommendNearBy;
        self.currentFilterFeedsInfo = [KSTimelineManager sharedManager].feedsInfo_RecommendIntraCity;
        
        //显示或者隐藏全局播放浮窗
        [[KSGlobalPlayFloatingWindowManager sharedManager] showOrHidePlayFloatingWindowIsCloseMiniProgram:NO];
        
        CGFloat offset = self.contentTableView.contentOffset.y;
        [self.rootTabBarController processFeedOffset:offset];
        
        // 有红点时点击“同城”，需要拉取最新数据，并冒泡标签有动画效果
        if ([KSBadgeUpdateManager sharedManager].nearbyNum != 0 && tabView.lastSelectIndex != KSTimelineTab_RecNearBy) {
            self.nearbyTabHasRedPoint = YES;
            self.nearbyTabHasRedPointForTrace = YES;
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_MSEC)), dispatch_get_main_queue(), ^{
                [self refreshVC];
            });
        }
        [KSBadgeUpdateManager sharedManager].nearbyNum = 0;

    } else if (selectedIndex == KSTimelineTab_Novel) { // 小说
        self.contentTableView = self.novelPageTableView;
        [KSTimelineManager sharedManager].currentFiltrMask = [KSTimelineManager sharedManager].filtrMaskNovelPage;
        //显示或者隐藏全局播放浮窗
        [[KSGlobalPlayFloatingWindowManager sharedManager] showOrHidePlayFloatingWindowIsCloseMiniProgram:NO];
        [self.rootTabBarController processFeedOffset:0];
        
        if (isViewAppear) {
            [self informNovelPageHippyVCAppear];
        }
        
    } else if (selectedIndex == KSTimelineTab_ShortPlay) { // 短剧
        // 点击短剧的时候再创建hippyVC，否则会有多余的曝光上报
        self.contentTableView = self.shortPlayTableView;
        [KSTimelineManager sharedManager].currentFiltrMask = [KSTimelineManager sharedManager].filtrMaskShortPlay;
        
        //显示或者隐藏全局播放浮窗
        [[KSGlobalPlayFloatingWindowManager sharedManager] showOrHidePlayFloatingWindowIsCloseMiniProgram:NO];
        
        [self.rootTabBarController processFeedOffset:0];
        
        // 点击短剧
        if (isViewAppear) {
            [self informSquareHippyVCAppear];
        }
        // 9.8版本特性提示功能下线，清理缓存。再过几个版本可以删除这个清理逻辑。
        RemoveLocalConfig(@"kTimelineTabPlayetRedNumShowed");
        // 清除红点
        [KSBadgeUpdateManager sharedManager].playletRedNum = 0;
        
    }
    else if (selectedIndex == KSTimelineTab_Shop) { // 商城
        self.contentTableView = self.mShopTableView;
        self.navBar.rightNavBarBtn.hidden = YES;
        [KSTimelineManager sharedManager].currentFiltrMask = [KSTimelineManager sharedManager].filtrMaskShop;
        if (isViewAppear) {
            if (tabView.lastSelectIndex != KSTimelineTab_Shop) {
                [self.ecommerceTabView ecommerceViewDidAppear];
                [self.navBar setBackgroundColor:[UIColor clearColor]];
            }
        }
        
        [self.ecommerceTabView refreshTabViewList];
        
        [self.rootTabBarController processFeedOffset:0];
    }
    else if (selectedIndex == KSTimelineTab_Activity) { // 活动
        [KSTimelineManager sharedManager].currentFiltrMask = [KSTimelineManager sharedManager].filtrMaskActivity;
        self.contentTableView = self.mActivityTableView;
        [self informActivityPageHippyVCAppear];
        
        [self traceReportActivityTabViewExpose];
    }
    else if (selectedIndex == KSTimelineTab_GameCenter) { // 游戏
        [KSTimelineManager sharedManager].currentFiltrMask = [KSTimelineManager sharedManager].filtrMaskGameCenter;
        self.contentTableView = self.mGameCenterTableView;
        if (isViewAppear) {
            [self informGamePageHippyVCAppear];
        }
        
        SaveLocalBoolConfig(@"kTimelineTabGameRedNumShowed", YES);
    }
    
    if (isViewAppear && (selectedIndex == KSTimelineTab_Live && tabView.lastSelectIndex != KSTimelineTab_Live)) {
        [self.entertainmentView entertainmentViewWillAppear];
        if (self.entertainmentView && ![self.entertainmentView isLiteCardVCShowing]) {
            [self.navBar setBackgroundColor:UIColor.blackColor];
        }
    } else if (selectedIndex != KSTimelineTab_Live && tabView.lastSelectIndex == KSTimelineTab_Live) {
        [self.entertainmentView entertainmentViewWillDisAppear];
    }
    
    if (selectedIndex != KSTimelineTab_Shop && tabView.lastSelectIndex == KSTimelineTab_Shop) {
        [self.ecommerceTabView ecommerceViewWillDisAppear];
        self.navBar.rightNavBarBtn.hidden = NO;
    }
    
    if (self.centerSegControl.selectIndex != selectedIndex ) {
        KLog(@"[SegTab]did clicked=%ld",(long)selectedIndex);
        self.centerSegControl.selectIndex = selectedIndex;
    }
    
    self.rootTabBarController.enableToTopAnimation = index != KSTimelineTab_Rec;
    
    // Tab 切换时收起feed更多按钮面板
    [[NSNotificationCenter defaultCenter] postNotificationName:KSNotification_DidChangeFeedSelectTab object:self];
    
    //数据上报
    [self traceReportClickedOnTab];
    
    [self setEnableFooterView:self.currentFilterFeedsInfo.hasMore];
    [self reloadTable];
    
    // 链路节点
    if ([KSAppDelegate shareInstance].rootTabBarController) {
        // currentFiltrMask 重新赋值后就可以刷新节点了
        [[KSPathReportManager sharedManager] updateTabBarControllerNode:[KSAppDelegate shareInstance].rootTabBarController];
    }
    else {
        // 首次启动，[KSAppDelegate shareInstance].rootTabBarController 还没赋值，所以这里直接添加节点
        [[KSPathReportManager sharedManager] addKeyPathNode:self moduleId:nil pageExtra:nil moduleExtra:nil];
    }
    
    // 直播页不在此处进行数据请求
    if ([KSTimelineManager sharedManager].currentFiltrMask != [KSTimelineManager sharedManager].filtrMaskLive && self.currentFilterFeedsInfo.feedsList.count == 0)
    {
        [self loadFeedsListFromLocal];
    }
    
    if ([KSTimelineManager sharedManager].currentFiltrMask != lastFilterMask)
    {
        //动画切换
        [self setupMainVCViewBackgroundColor];
        
        if (self.mContainerScrollView.contentOffset.x != selectedIndex * self.view.width)
        {
            // 这里的判断不要轻易修改，涉及到数据上报。（用于判断是否为滑动进入）loying
            BOOL tmp = isLoadingView;
            [UIView animateWithDuration:0.3 animations:^{
                self.mContainerScrollView.contentOffset = CGPointMake(selectedIndex * self.view.width, self.mContainerScrollView.contentOffset.y);
            } completion:^(BOOL finished) {
                [self onABTestSelectIndex:selectedIndex];
                
                if (selectedIndex == KSTimelineTab_Follow && [KSBadgeUpdateManager sharedManager].feedNum > 0)
                {
                    // 关注tab且红点数大于0时，强制刷新“全部动态”数据
                    [KSTimelineManager sharedManager].currentFiltrMask = [KSTimelineManager sharedManager].filtrMaskFollow;
                    
                    self.followTabCurFiltrMask = [KSTimelineManager sharedManager].filtrMaskFollow;
                    [self saveFollowTabCurFiltrMask];
                    
                    self.currentFilterFeedsInfo = [KSTimelineManager sharedManager].feedsInfo_ForFollow;
                    
                    [self.scrollView setContentOffset:CGPointMake(self.scrollView.contentOffset.x, REFRESH_TRIGGER_HEIGHT - self.refreshHeaderView.initTopContentInset)];
                    KSFeedFreshInfo *freshInfo = [[KSFeedFreshInfo alloc] initWithTiming:TimelineRefreshTiming_RedDot];
                    NSMutableDictionary *temp = [NSMutableDictionary new];
                    [temp setObject:freshInfo forKey:KSRefreshSource];
                    [self performSelector:@selector(forceRefreshWithUserInfo:) withObject:temp afterDelay:0.3];
                }
                else if ([KSTimelineManager sharedManager].currentFiltrMask == [KSTimelineManager sharedManager].filtrMaskQQWXFriends &&
                         [KSBadgeUpdateManager sharedManager].feedFriendNum > 0)
                {
                    [self.scrollView setContentOffset:CGPointMake(self.scrollView.contentOffset.x, REFRESH_TRIGGER_HEIGHT - self.refreshHeaderView.initTopContentInset)];
                    KSFeedFreshInfo* freshInfo = [[KSFeedFreshInfo alloc] initWithTiming:TimelineRefreshTiming_RedDot];
                    NSMutableDictionary* temp = [NSMutableDictionary new];
                    [temp setObject:freshInfo forKey:KSRefreshSource];
                    [self performSelector:@selector(forceRefreshWithUserInfo:) withObject:temp afterDelay:0.3];
                }
                else if ([KSTimelineManager sharedManager].currentFiltrMask == [KSTimelineManager sharedManager].filtrMaskUgcFeed &&
                         [KSBadgeUpdateManager sharedManager].feedUgcNum > 0)
                {
                    [self.scrollView setContentOffset:CGPointMake(self.scrollView.contentOffset.x, REFRESH_TRIGGER_HEIGHT - self.refreshHeaderView.initTopContentInset)];
                    KSFeedFreshInfo* freshInfo = [[KSFeedFreshInfo alloc] initWithTiming:TimelineRefreshTiming_RedDot];
                    NSMutableDictionary* temp = [NSMutableDictionary new];
                    [temp setObject:freshInfo forKey:KSRefreshSource];
                    [self performSelector:@selector(forceRefreshWithUserInfo:) withObject:temp afterDelay:0.3];
                }
                else if ([KSTimelineManager sharedManager].currentFiltrMask == [KSTimelineManager sharedManager].filtrMaskUgcFeedQQWX &&
                         [KSBadgeUpdateManager sharedManager].feedUgcNum > 0)
                {
                    [self.scrollView setContentOffset:CGPointMake(self.scrollView.contentOffset.x, REFRESH_TRIGGER_HEIGHT - self.refreshHeaderView.initTopContentInset)];
                    KSFeedFreshInfo* freshInfo = [[KSFeedFreshInfo alloc] initWithTiming:TimelineRefreshTiming_RedDot];
                    NSMutableDictionary* temp = [NSMutableDictionary new];
                    [temp setObject:freshInfo forKey:KSRefreshSource];
                    [self performSelector:@selector(forceRefreshWithUserInfo:) withObject:temp afterDelay:0.3];
                }
                else if ([KSTimelineManager sharedManager].currentFiltrMask == [KSTimelineManager sharedManager].filtrMaskLive &&
                         [KSBadgeUpdateManager sharedManager].feedLiveNum > 0)
                {
                    //直播红点数大于0，直播tab强制刷新
                    [self.entertainmentView forceRefresh];
                }
                else
                {
                    [self onAutoRefresh];
                }
                
                if ([KSTimelineManager sharedManager].currentFiltrMask != [KSTimelineManager sharedManager].filtrMaskRecommend)
                {
                    self.lastRecfeedExposeTS = [[NSDate date] timeIntervalSince1970];
                }
                
                if (!tmp)
                {
                    // 产品要求，第一次打开app不重复报曝光，于是这里添加isLoadingView作为判断，因为block执行存在延迟，需要添加一个临时变量tmp来保存
                    KSUIControlTrigger triggerSource = [[extraInfo objectForKey:kSegmentedTriggerKey] integerValue];
                    KSTimelineRootVCExposeSource repotSource = (triggerSource == KSUIControlTrigger_User)? KSTimelineRootVCExposeSource_TopTabClick:KSTimelineRootVCExposeSource_Other;
                    if (!self.needChangeRootTabbarAndTimeLineTabbar) {
                        [self onFeedTableViewExposeWithType:repotSource];
                    } else {
                        self.needChangeRootTabbarAndTimeLineTabbar = NO;
                    }
                }
                [self onSelectedIndex:selectedIndex];
            }];
        }
        else
        {
            [self onABTestSelectIndex:selectedIndex];
            if (self.viewFirstAppear && (([KSTimelineManager sharedManager].currentFiltrMask & JceTimeline_enum_filter_mask_ENUM_FILTER_MASK_FEED_RECOMM_CARD) == JceTimeline_enum_filter_mask_ENUM_FILTER_MASK_FEED_RECOMM_CARD) && [KSTimelineManager sharedManager].feedsInfo_Recommend.feedsList.count > 0)
            {
                
            }
            else
            {
                [self onAutoRefresh];
            }
            
            [self onFeedTableViewExposeWithType:KSTimelineRootVCExposeSource_TopTabScroll];
            
            [self onSelectedIndex:selectedIndex];
        }
    }
    
    if (index == KSTimelineTab_Follow) {
        [self updateTableViewHeaderInfo];
    }
    
    // 好友更新请求
    // 这里将self.centerSegControl.selectIndex改为index，因为首次加载时，centerSegControl还没有被赋值，导致这里数据错误
    //    if (self.centerSegControl.selectIndex == KSTimelineTab_Follow)
    if (index == KSTimelineTab_Follow)
    {
        KLog(@"[reccard]requestNewerListFeedBanner");
        [self requestNewerListFeedBanner:YES];
        [[NSNotificationCenter defaultCenter] postNotificationName:KSNotification_DidChangeFeedSelectTab object:self];
    }
    
    if (selectedIndex != KSTimelineTab_Live || [self isInLiveBigCardTab:selectedIndex]) {
        [[KSPIPManager sharedManager] destroyPIPWindowIfExist];
    } else {
        if (isViewAppear && [[KSPIPManager sharedManager] isPictureInPictureActive]) {
            [self.entertainmentView notifyNativePIPStatus:YES];
        }
    }
    
    //切换tab重新计算曝光时间
    [self resetTraceReportVCExposeDurationWhenAppear];
    
    if (selectedIndex == KSTimelineTab_Follow) {
        if ([self shouldShowShareGuideBar]) {
            [self showShareFeedGuideBar];
        } else {
            [self hideShareFeedGuideBar];
        }
    } else {
        [self hideShareFeedGuideBar];
    }
    
    [KSTimelineRootVC refreshFeedLiveIfNeeded];
    
//    if (index != KSTimelineTab_Rec)
//    {
//        [[[KSNavigationManager sharedManager] getRootTabBarController] hideBottomTip];
//    }
    
    //隐藏PopLayer
    static NSInteger lastIndex = 0;
    if (index != lastIndex) {
        [[KSBoxManager sharedManager] dimissPopLayer:self];
        lastIndex = index;
    }
    
    [[KSFontSizeManager sharedManager] showAlertIfNeedInVC:self];
    
    // 任务中心侧滑引导动画
    [self showVMissionSlideGuideAnim];
}

- (void)KSSegmentedControl:(KSSegmentedControl *)tabView willSelectTabAtIndex:(NSUInteger)index {
    
    if ([self isFeedNativaFirstShow]) {
        return;
    }
    
    if (index != tabView.lastSelectIndex) {
        [FireEye fps_customPageWillDisappear:self
                                      pageId:[self tabTypeStringFromIndex:tabView.lastSelectIndex]];
        
        [FireEye fps_customPageDidAppear:self
                                  pageId:[self tabTypeStringFromIndex:index]];
    }

    [self updateTabTitleColorWithIndex:index];
    
    [self topBarChanged:index];
    [self updateAdViewPlayStatus:index];
}

- (void)KSSegmentedControl:(KSSegmentedControl*)tabView willUnselectTabAtIndex:(NSUInteger)index
{
    if (index == KSTimelineTab_Live)
    {
        [self.entertainmentView informLiveTabDisappear];
    }
    
    if (index == KSTimelineTab_Rec) {
        [[KSLiveStreamPreloadManager shareInstance] pauseAllWrapperPlayers];
    }
    
    if (index == KSTimelineTab_ShortPlay) {
        // 派对被unselect
        [self informSquareHippyVCDisappear];
    }
    
    if (index == KSTimelineTab_Novel) {
        [self informNovelPageHippyVCDisappear];
    }
    
    if (index == KSTimelineTab_Activity) {
        // 活动页面
        [self informActivityPageHippyVCDisappear];
    }
    
    [self informHippyVCDisappear];
}

- (void)pipWindowStatusChanged:(NSNotification *)noti
{
    if (self.centerSegControl.selectIndex == KSTimelineTab_Live &&
        [[[KSNavigationManager sharedManager] getTopViewController] isEqual:self]) {
        BOOL pipWindowShow = [[noti.userInfo safeObjectForKey:@"status"] boolValue];
        [self.entertainmentView notifyNativePIPStatus:pipWindowShow];
    }
}

// “全部动态”或“好友动态”Tab选中时 刷新
- (void)onRealRefresh:(KSFeedFreshInfo*)freshInfo
{
    if ([[KSTimelineManager sharedManager] shouldShowRecUserFeedWithFiltrMask:self.currentFilterFeedsInfo.filterMask])
    {
        // 浅关系链需要根据推荐数据来判断
        KSTimelineRecUserFeedModel *currentRecUserFeedModel;
        if ([KSTimelineManager sharedManager].recFeedType == proto_feed_webapp_ENUM_REC_FEED_TYPE_ENUM_REC_FEED_TOPIC)
        {
            currentRecUserFeedModel = [KSTimelineManager sharedManager].recHotSpotFeed;
        }
        else
        {
            if (self.currentFilterFeedsInfo.filterMask == [KSTimelineManager sharedManager].filtrMaskFollow)
            {
                currentRecUserFeedModel = [KSTimelineManager sharedManager].recUserFeed_ForFollow;
            }
            else if (self.currentFilterFeedsInfo.filterMask == [KSTimelineManager sharedManager].filtrMaskQQWXFriends)
            {
                currentRecUserFeedModel = [KSTimelineManager sharedManager].recUserFeed_ForQQWXFriends;
            }
            else if (self.currentFilterFeedsInfo.filterMask == [KSTimelineManager sharedManager].filtrMaskUgcFeed)
            {
                currentRecUserFeedModel = [KSTimelineManager sharedManager].recUserFeed_ForUgcFeed;
            }
            else if (self.currentFilterFeedsInfo.filterMask == [KSTimelineManager sharedManager].filtrMaskUgcFeedQQWX)
            {
                currentRecUserFeedModel = [KSTimelineManager sharedManager].recUserFeed_ForUgcFeedQQWX;
            }
        }
        
        [self setEnableFooterView:currentRecUserFeedModel.hasMoreData];
    }
    else
    {
        [self setEnableFooterView:self.currentFilterFeedsInfo.hasMore];
    }
    
    [self reloadTable];
    
    if (self.currentFilterFeedsInfo.feedsList.count == 0)
    {
        [self loadFeedsListFromLocal];
    }
    
    //动画切换
    [self setupMainVCViewBackgroundColor];
    
    // 这里的0.4秒是经验值，因为动画是0.2秒，如果写0.2秒会runloop重叠，导致刷新不生效
    NSMutableDictionary* temp = [NSMutableDictionary new];
    [temp safeSetObject:freshInfo forKey:KSRefreshSource];
    [self performSelector:@selector(forceRefreshWithUserInfo:) withObject:temp afterDelay:0.4];
    
    [self updateTableViewHeaderInfo];
    
    if (self.centerSegControl.selectIndex == KSTimelineTab_Follow)
    {
        KLog(@"[reccard]requestNewerListFeedBanner");
        [self requestNewerListFeedBanner:YES];
        
        if ([self shouldShowShareGuideBar])
        {
            [self showShareFeedGuideBar];
        } else {
            [self hideShareFeedGuideBar];
        }
    } else {
        [self hideShareFeedGuideBar];
    }
    
}

- (void)onSelectedIndex:(NSInteger)index
{    
    KDEBUG(@"[自动播]--selectTabChanged force play");
    [self setAudioSessionCategoryIfNeed];
    
    [self processStartMediaPlayIgnoreExpose:YES otherParams:@{@"autoPlayCaller":@"onSelectedIndex"}];
}

- (void)onAutoRefresh
{
    //只有第一次会刷新，其他时候切换不刷新,否则账号切换的时候,拉到上一个账号的数据，因为初始化的时候不会刷新所有tab数据
    KLog(@"[feed自动刷新]curFilterMask=%ld firstTimeFlag hot=%ld,near=%ld,qqwx=%ld,follow=%ld,recommend=%ld,recommendNearby=%ld",(long)[KSTimelineManager sharedManager].currentFiltrMask,(long)self.hotLoadFirstTime,(long)self.nearbyLoadFirstTime,(long)self.QQWXLoadFirstTime,(long)self.friendsLoadFirstTime,(long)self.recommendLoadFirstTime,(long)self.recommendNearByLoadFirstTime);
    if (self.hotLoadFirstTime && [KSTimelineManager sharedManager].currentFiltrMask == [KSTimelineManager sharedManager].filtrMaskHot)
    {
        KLog(@"[feed自动刷新]=Hot");
        [self didTriggerRefresh];
    }
    if (self.QQWXLoadFirstTime && [KSTimelineManager sharedManager].currentFiltrMask  == [KSTimelineManager sharedManager].filtrMaskQQWXFriends)
    {
        KLog(@"[feed自动刷新]=QQWXFriends");
        [self didTriggerRefresh];
    }
    if (self.friendsLoadFirstTime && [KSTimelineManager sharedManager].currentFiltrMask  == [KSTimelineManager sharedManager].filtrMaskFollow)
    {
        KLog(@"[feed自动刷新]=Follow");
        [self didTriggerRefresh];
    }
    if (self.recommendLoadFirstTime &&  [KSTimelineManager sharedManager].currentFiltrMask  == [KSTimelineManager sharedManager].filtrMaskRecommend)
    {
        KLog(@"[feed自动刷新]=Recommend");
        [self didTriggerRefresh];
    }
    if (self.recommendNearByLoadFirstTime &&  [KSTimelineManager sharedManager].currentFiltrMask  == [KSTimelineManager sharedManager].filtrMaskRecommendNearBy)
    {
        KLog(@"[feed自动刷新]=Recommend_Intra_City");
        [self didTriggerRefresh];
    }
    
    [self checkRecfeedLastExposedTimeStampOverTwoHours];
}

// ugc在详情页回来的时候被更新了
- (void)ugcUpdated:(NSNotification *)data
{
    KShortUgcObj *ugcObj = data.object;
    
    NSArray *feedList = self.currentFilterFeedsInfo.feedsList;
    for (KSimpleFeed *feed in feedList) {
        if ([feed.simpleFeedCommon.strFeedId isEqualToString:ugcObj.ugcId]) { // 更新数据
            if (![ugcObj.coverImageURL isEmpty]) { //非空才赋值，不然本来有图的，被刷没了
                KSImage *cover = [[KSImage alloc] init];
                cover.imageUrl = ugcObj.coverImageURL;
                feed.songinfo.coverurls = @{ @(200): cover };
            }
            
            feed.comment.number = ugcObj.numOfComment;
            feed.forwardInfo.forwardNum = ugcObj.numOfForward;
            //            feed.recivedFlowerInfo.number = ugcObj.numOfGift; //详情页回来更新
            feed.listenerInfo.number = ugcObj.numOfPlay;
            feed.layoutInfo = nil;
            feed.songinfo.songDesc = ugcObj.content;

            [self reloadTable];
            
            break;
        }
    }
}

- (NSString *)tabTypeStringFromIndex:(KSTimelineTabSelectIndex)index {
    NSDictionary *mapping = @{
        @(KSTimelineTab_Rec): @"tl_feed_rec",
        @(KSTimelineTab_Follow): @"tl_feed_follow",
        @(KSTimelineTab_Live): @"tl_live",
        @(KSTimelineTab_Novel): @"tl_novel",
        @(KSTimelineTab_Shop): @"tl_shop",
        @(KSTimelineTab_ShortPlay): @"tl_shortplay",
        @(KSTimelineTab_RecNearBy): @"tl_rec_nearby",
        @(KSTimelineTab_QQWXFriends): @"tl_qqwxfriends",
        @(KSTimelineTab_UgcFeed): @"tl_feed_ugc",
        @(KSTimelineTab_UgcFeedQQWX): @"tl_feed_ugc_qqwx",
        @(KSTimelineTab_TopicFeed): @"tl_feed_topic",
        @(KSTimelineTab_Activity): @"tl_activity",
        @(KSTimelineTab_GameCenter): @"tl_game",
    };
    
    return mapping[@(index)] ?: @"unknown";
}

#pragma mark - 重载父类方法
- (void)deleteSyncMultiTask:(id)Task
{
    if ([self.syncMultiTaskArray containsObject:Task]){
        [self.syncMultiTaskArray removeObject:Task];
    }
    
    if ([self.uploadTaskArray containsObject:Task]) {
        [self.uploadTaskArray removeObject:Task];
    }
}

- (void)forceRefresh
{
    BOOL useUnReadFeeds = [self checkRefreshUseUnReadFeedsIfNeed];
    if (useUnReadFeeds) {
        return;
    }
    
    if ([KSBigCardSwitchUtil enableEndDragingPlay]) {
        if ([self isRecCardTable:self.scrollView]) {
            if (self.feedManager.currentIndexPath.row != 0) {
                /// 刷新暂停当前播放
                [self processStopMediaPlayIgnoreExpose:YES when:KSTimelineStopVideoWhenDefault];
            }
        }
    }

    /// 走原刷新逻辑
    [super forceRefresh];
}

- (void)refreshVC {
    
    if ([KSTimelineManager sharedManager].currentFiltrMask == [KSTimelineManager sharedManager].filtrMaskLive) {
        [self.entertainmentView forceRefresh];
    } else if ([KSTimelineManager sharedManager].currentFiltrMask == [KSTimelineManager sharedManager].filtrMaskNovelPage) {
        // 刷新小说界面
        if (self.novelPageHippyVC.hippyView.loadState == KSHippyWrapperViewLoadStateLoaded) {
            [self.novelPageHippyVC.hippyView sendEvent:@"refresh" params:@{}];
        }
    } else if ([KSTimelineManager sharedManager].currentFiltrMask == [KSTimelineManager sharedManager].filtrMaskShortPlay) {
        // 刷新短剧hippy页面
        if (self.shortPlayHippyVC.hippyView.loadState == KSHippyWrapperViewLoadStateLoaded) {
            [self.shortPlayHippyVC.hippyView sendEvent:@"refresh" params:@{}];
        }
    } else if ([KSTimelineManager sharedManager].currentFiltrMask == [KSTimelineManager sharedManager].filtrMaskShop) {
        // 刷新商城tab页面
        [self.ecommerceTabView refreshTabViewList];
    } else {
        self.feedManager.isForceFresh = YES;
        [self forceRefresh];
        self.feedManager.isForceFresh = NO;
    }
    if ([self isRecCardTable:self.scrollView]) {
        self.isRefreshing = YES;
        [self.feedManager resetVideoMakerState];
        [[KSUgcPlayManager sharedManager] resetRecFeedRecentListenerDict];
        self.curRecFeedRowIndex = 0;
    }
}

- (void)didTriggerRefresh {
    KINFO(@"didTriggerRefresh %ld", (long)[KSTimelineManager sharedManager].currentFiltrMask);
    self.curRecFeedRowIndex = 0;
    if ([KSTimelineManager sharedManager].currentFiltrMask == [KSTimelineManager sharedManager].filtrMaskRecommendNearBy) {
        self.locationLoadType = TimelineNearbyLoadTypeRefresh;
        [self loadFeedsNear];
        
        if (self.hasLocationUpdated) {
            [self loadRecFeedNearByFeed];
        }
        if (self.hasFirstRequestNearByUserList) {
            [self loadNearbyUserFromRemoteForIsMore:NO];
        }
    } else {
        self.isRefreshing = YES;
        
        NSMutableDictionary* extarInfo = [NSMutableDictionary dictionaryWithDictionary:self.refreshUsrInfo];
        
        KSFeedFreshInfo* freshInfo = [extarInfo objectForKey:KSRefreshSource];
        if (!freshInfo)
        {
            freshInfo = [[KSFeedFreshInfo alloc] init];
        }
        [freshInfo addTiming:TimelineRefreshTiming_TriggerFresh];
        [extarInfo setObject:freshInfo forKey:KSRefreshSource];
        [self loadFeedsListFromRemote:extarInfo];
    }
    
    if (![KSRelationManager sharedManager].requestedBindInfo)
    {
        [[KSRelationManager sharedManager] getBindInfo:[KSLoginManager sharedInstance].curUserInfo.userId completionBlock:nil];
    }
    
    if (self.centerSegControl.selectIndex == KSTimelineTab_Follow)
    {
        KLog(@"[reccard]requestNewerListFeedBanner");
        // 主动刷新恢复状态
        self.isJumpFromClickedUser = NO;
        [self requestNewerListFeedBanner:YES];
    }
    else if (self.centerSegControl.selectIndex == KSTimelineTab_Rec)
    {        
        if ([KSABTestManager sharedManager].recFeedStyle != KSABTestRecFeedUIType_Collection)
        {
            [[KSSplashAdManager sharedManager] didTriggerFresh];
            // 清掉所有的已经预热的播放器
            [[KSUgcPlayManager sharedManager] cleanAllPreheatedPlayer];
            
            [KSLiveStreamPreloadManager shareInstance].hasInitializeLiveStreamPreloadManager = NO;
            [self reloadTable];
        }

    }
    
    [self requestTaskTips];
}

- (void)didTriggerLoadMore
{
    KINFO(@"didTriggerLoadMore %ld", (long)[KSTimelineManager sharedManager].currentFiltrMask);
    if ([KSTimelineManager sharedManager].currentFiltrMask == [KSTimelineManager sharedManager].filtrMaskRecommendNearBy)
    {
        self.locationLoadType = TimelineNearbyLoadTypeLoadMore;
        if (self.nearbySubIndex == NearbyTabBarSubIndex_Dynamic) // 附近动态
        {
            [self loadFeedsNearMorebyFromRemote];
            
        } else if (self.nearbySubIndex == NearbyTabBarSubIndex_User) { // 附近的人
            [self loadNearbyUserFromRemoteForIsMore:YES];
        }
    }
    else
    {
        [self loadFeedsMoreListFromRemote];
    }
}

- (void)scrollTableviewTotop
{
    [self.scrollView setContentOffset:CGPointMake(0, 0) animated:NO];
}

- (void)languageDidChange
{
    [super languageDidChange];
    
    [self updateEmptyView];
    self.navBar.rightNavBarBtn.accessibilityLabel = @"搜索歌曲或歌手";
    self.navBar.leftNavBarBtn.accessibilityLabel = @"K币账户";
    [self didTriggerRefresh];
    [[[KSNavigationManager sharedManager] getRootTabBarController] updateTabBarItemTitle];
}

- (NSInteger)globalPlayBtnClickFromPage
{
    return 1;
}

// 筛选上传task，是否携带poi
// 这里hasPoiInfo赋予了新的含义。
// 后来新增需求，如果是从同城发布页发布的，都要进同城上传，所以即使没有poi信息，也认为hasPoiInfo = YES
// getShareSimpleFeedsWithPoiInfo 下同
- (NSArray *)getUploadTaskWithPoiInfo:(BOOL)hasPoiInfo {
    NSMutableArray *result = [[NSMutableArray alloc] init];

    for (id task in self.uploadTaskArray) {
        if (hasPoiInfo) {
            if (![task isKindOfClass:[ProductUploadTask class]]) {
                continue;
            }
            ProductUploadTask *uploadTask = (ProductUploadTask *)task;
            if (uploadTask.publishContent.hasPoiInfo) {
                [result safeAddObject:uploadTask];
            }
        } else {
            if (![task isKindOfClass:[ProductUploadTask class]]) {
                [result safeAddObject:task];
                continue;
            }
            ProductUploadTask *uploadTask = (ProductUploadTask *)task;
            if (!uploadTask.publishContent.hasPoiInfo) {
                [result safeAddObject:uploadTask];
            }
        }
    }
    return result;
}

- (NSArray *)getShareSimpleFeedsWithPoiInfo:(BOOL)hasPoiInfo {
    NSMutableArray *resultWithPoi = [[NSMutableArray alloc] init];
    NSMutableArray *resultNoPoi = [[NSMutableArray alloc] init];

    for (KSimpleFeed *simpleFeed in self.shareSimpleFeeds) {
        if (simpleFeed.publishContent.hasPoiInfo) {
            [resultWithPoi safeAddObject:simpleFeed];
        } else {
            [resultNoPoi safeAddObject:simpleFeed];
        }

    }
    if (hasPoiInfo) {
        return resultWithPoi;
    } else {
        return resultNoPoi;
    }
}

#pragma mark - 未读Feeds优化
/// 下拉刷新，用未读feed先填充
- (BOOL)checkRefreshUseUnReadFeedsIfNeed
{
    if (![self isRecCardFeedTab]) {
        return NO;
    }
    
    /// 刷新上报
    [KSBeaconManager trackWithEventId:@"kg_ios_rec_card_refresh_action" andBlock:nil];
    
    /// 使用已拉取的未读卡片
    BOOL useCache = [[KSTimelineManager sharedManager] reloadRecomWithCurrentUnreadFeeds];
    if (useCache) {
        /// 回到顶部
        [self scrollTableviewTotop];
        
        /// 刷新UI
        [self contentViewReloadData:self.mHotTableView];
        
        /// 同时拉取后台数据
        [self didTriggerRefresh];
        
        /// 结束loading态
        dispatch_async(dispatch_get_main_queue(), ^{
            [self refreshFinishedWithTableView:self.mHotTableView];
        });
    }

    return useCache;
}

/// 冷启，用未读feed来填充
- (void)checkLaunchUseUnReadFeedsIfNeed
{
    if (![self isRecCardFeedTab]) {
        return;
    }
    
    /// 使用已拉取的未读卡片
    BOOL useCache = [[KSTimelineManager sharedManager] reloadRecomWithLastUnreadCacheFeed];
    KINFO(@"check Launch Use UnRead Feeds :%d", useCache);
    if (useCache) {
        [self contentViewReloadData:self.mHotTableView];
    }    
}

#pragma mark - 滑动返回手势
- (BOOL)gestureRecognizerShouldBegin:(UIGestureRecognizer *)gestureRecognizer
{
    if([self.navigationController respondsToSelector:@selector(interactivePopGestureRecognizer)] && gestureRecognizer == self.navigationController.interactivePopGestureRecognizer)
    {
        return NO;
    }
    else
    {
        return YES;
    }
}

#pragma mark - UITableViewDataSource<NSObject>
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    // CollectionSectionType 中定义
    
    if (tableView == self.mNearbyUserTableView)
    {
        // 附近的人列表
        return 1;
    }
    return CollectionSectionTypeTotalCount;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    
    if (tableView == self.mNearbyUserTableView)
    {
        // 附近的人列表
        return self.nearByUserArray.count;
    }
    //话题tab不显示假feed
    BOOL isTopicTabSelected = self.currentFilterFeedsInfo == [KSTimelineManager sharedManager].feedsInfo_ForTopicFeed;
    if (section == CollectionSectionTypeFollowFilter)
    {
        // 动态关注筛选器
        if (tableView == self.mFollowTableView)
        {
            return 1;
        }
    }
    else if (section == CollectionSectionTypeUpload)
    {
        if (tableView == self.mFollowTableView && !isTopicTabSelected) {
            // 关注 显示不带poi信息的上传任务
            NSArray *arr = [self getUploadTaskWithPoiInfo:NO];
            return [arr count];
        } else if (tableView == self.mNearbyFeedTableView) {
            // 同城 显示带poi信息的上传任务
            NSArray *arr = [self getUploadTaskWithPoiInfo:YES];
            return [arr count];
        } else {
            return 0;
        }
    }
    else if (section == CollectionSectionTypeCloudComposite)
    {
        NSInteger count = [[KSCloudCompositeManager defaultManager] validTaskCount];
        return count;
    }
    else if (section == CollectionSectionTypeShare)
    {
        if (tableView == self.mFollowTableView && !isTopicTabSelected) {
            // 关注 显示不带poi信息的上传任务
            NSArray *arr = [self getShareSimpleFeedsWithPoiInfo:NO];
            return [arr count];
        } else if (tableView == self.mNearbyFeedTableView) {
            // 同城 显示带poi信息的上传任务
            NSArray *arr = [self getShareSimpleFeedsWithPoiInfo:YES];
            return [arr count];
        } else {
            return 0;
        }
    }
    else if (section == CollectionSectionTypeCell)
    {
        //feed内容
        KSFilterFeedsInfo *filterFeedsInfo = [self getFeedDataListByScollView:tableView];
        NSInteger ret = filterFeedsInfo.feedsList.count;
        // 检查列表是否存在破窗广告
        if (tableView == self.mFollowTableView) {
            // 关注列表检测是否需要清除浮层视图
            [self checkNeedCleanFloatView:filterFeedsInfo.feedsList];
        }
        if (ret == 0 && ![[KSNetStatusManager sharedManager] IsEnableInternet] && self.mHotTableView == tableView) {
            self.shouldShowEmptyView = YES;
            return 1;
        }
        else {
            KDEBUG(@"[EmptyFeed]Cnt=%ld mask=%ld tablex=%f",ret,filterFeedsInfo.filterMask,tableView.frame.origin.x);
            return ret;
        }
        
    }
    else if (section == CollectionSectionTypeRecUser)
    {
        // 折叠后拉取的推荐用户Feed,话题feed不需要，直接返回0
        KSFilterFeedsInfo *filterFeedsInfo = [self getFeedDataListByScollView:tableView];
        
        NSInteger count = 0;
        // 1.展示推荐话题feed 2.展示普通feed
        if ([KSTimelineManager sharedManager].recFeedType == proto_feed_webapp_ENUM_REC_FEED_TYPE_ENUM_REC_FEED_TOPIC)
        {
            if ([self shouldShowFoldWithFilterFeedsInfo:filterFeedsInfo])
            {
                count++;
            }
            NSUInteger recHotSpotFeedCount = [KSTimelineManager sharedManager].recHotSpotFeed.feedsList.count;
            if ([[KSTimelineManager sharedManager] shouldShowRecUserFeedWithFiltrMask:filterFeedsInfo.filterMask] && recHotSpotFeedCount > 0)
            {
                count = count + 1 + recHotSpotFeedCount;
            }
        }
        else
        {
            if (filterFeedsInfo.filterMask == [KSTimelineManager sharedManager].filtrMaskFollow)
            {
                // 关注/全部Tab
                if ([self shouldShowFoldWithFilterFeedsInfo:filterFeedsInfo])
                {
                    // 展示更多折叠
                    count++;
                }
                
                NSUInteger recFeedCount = [KSTimelineManager sharedManager].recUserFeed_ForFollow.feedsList.count;
                if ([[KSTimelineManager sharedManager] shouldShowRecUserFeedWithFiltrMask:filterFeedsInfo.filterMask] && recFeedCount > 0)
                {
                    // 推荐用户数据 Feed
                    count = count + 1 + recFeedCount; // 更多 + 标题 + Feed
                }
            }
            else if (filterFeedsInfo.filterMask == [KSTimelineManager sharedManager].filtrMaskQQWXFriends)
            {
                // 关注/全部Tab “只看好友”
                if ([self shouldShowFoldWithFilterFeedsInfo:filterFeedsInfo])
                {
                    // 展示更多折叠
                    count++;
                }
                
                NSUInteger recFeedCount = [KSTimelineManager sharedManager].recUserFeed_ForQQWXFriends.feedsList.count;
                if ([[KSTimelineManager sharedManager] shouldShowRecUserFeedWithFiltrMask:filterFeedsInfo.filterMask] && recFeedCount > 0)
                {
                    // 推荐用户数据 Feed
                    count = count + 1 + recFeedCount; // 更多 + 标题 + Feed
                }
            }
            else if (filterFeedsInfo.filterMask == [KSTimelineManager sharedManager].filtrMaskUgcFeed)
            {
                // 关注/作品Tab
                if ([self shouldShowFoldWithFilterFeedsInfo:filterFeedsInfo])
                {
                    // 展示更多折叠
                    count++;
                }
                
                NSUInteger recFeedCount = [KSTimelineManager sharedManager].recUserFeed_ForUgcFeed.feedsList.count;
                if ([[KSTimelineManager sharedManager] shouldShowRecUserFeedWithFiltrMask:filterFeedsInfo.filterMask] && recFeedCount > 0)
                {
                    // 推荐用户数据 Feed
                    count = count + 1 + recFeedCount; // 更多 + 标题 + Feed
                }
            }
            else if (filterFeedsInfo.filterMask == [KSTimelineManager sharedManager].filtrMaskUgcFeedQQWX)
            {
                // 关注/作品Tab “只看好友”
                if ([self shouldShowFoldWithFilterFeedsInfo:filterFeedsInfo])
                {
                    // 展示更多折叠
                    count++;
                }
                
                NSUInteger recFeedCount = [KSTimelineManager sharedManager].recUserFeed_ForUgcFeedQQWX.feedsList.count;
                if ([[KSTimelineManager sharedManager] shouldShowRecUserFeedWithFiltrMask:filterFeedsInfo.filterMask] && recFeedCount > 0)
                {
                    // 推荐用户数据 Feed
                    count = count + 1 + recFeedCount; // 更多 + 标题 + Feed
                }
            }
        }
        
        return count;
    }
    return 0;
}

- (KSFilterFeedsInfo*)getFeedDataListByScollView:(UIScrollView *)scrollview
{
    KSFilterFeedsInfo *filterFeedsInfo = nil;
    if (scrollview == self.mFollowTableView)
    {
        if (self.followTabCurFiltrMask == [KSTimelineManager sharedManager].filtrMaskQQWXFriends)
        {
            // 关注/全部 (好友)
            filterFeedsInfo = [KSTimelineManager sharedManager].feedsInfo_ForQQWXFriends;
        }
        else if (self.followTabCurFiltrMask == [KSTimelineManager sharedManager].filtrMaskUgcFeed)
        {
            // 作品
            filterFeedsInfo = [KSTimelineManager sharedManager].feedsInfo_ForUgcFeed;
        }
        else if (self.followTabCurFiltrMask == [KSTimelineManager sharedManager].filtrMaskUgcFeedQQWX)
        {
            filterFeedsInfo = [KSTimelineManager sharedManager].feedsInfo_ForUgcFeedQQWX;
        }
        else if (self.followTabCurFiltrMask == [KSTimelineManager sharedManager].filtrMaskTopic)
        {
            filterFeedsInfo = [KSTimelineManager sharedManager].feedsInfo_ForTopicFeed;
        }
        else
        {
            // 关注/全部
            filterFeedsInfo = [KSTimelineManager sharedManager].feedsInfo_ForFollow;
        }
        
        if ([WnsConfigManager sharedInstance].isPGSH) {
            
            NSMutableArray<KSimpleFeed *> *filterKtvRoomFeedsArray = [NSMutableArray array];
            [filterFeedsInfo.feedsList enumerateObjectsUsingBlock:^(KSimpleFeed * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
                if (obj.isKtvFeed) {
                    [filterKtvRoomFeedsArray safeAddObject:obj];
                }
            }];
            
            [filterFeedsInfo.feedsList removeObjectsInArray:filterKtvRoomFeedsArray];
        }
        
    }
    else if (scrollview == self.mHotTableView)
    {
        // 推荐有 abtest，这是 testB
        filterFeedsInfo = [KSTimelineManager sharedManager].feedsInfo_Recommend;
    }
    else if (scrollview == self.mHotCollectionView)
    {
        // 推荐有 abtest，这是 testA
        filterFeedsInfo = [KSTimelineManager sharedManager].feedsInfo_Recommend;
    }
    else if (scrollview == self.mNearbyFeedTableView)
    {
        filterFeedsInfo = [KSTimelineManager sharedManager].feedsInfo_RecommendIntraCity;
    }
    else if (scrollview == self.mNearbyUserTableView)
    {
        
    }
    return filterFeedsInfo;
}

//这里几个tableview和collectionview会一起初始化，不能用currentFeedsInfo来进行数据绑定，需要根据各自table/collection返回各自的数据
- (KSimpleFeed *) getFeedByTableView:(UITableView *)tableView indexPath:(NSIndexPath *)indexPath
{
    KSFilterFeedsInfo *filterFeedsInfo = [self getFeedDataListByScollView:tableView];
    NSArray <KSimpleFeed *> *feedsArray = filterFeedsInfo.feedsList;
    KSimpleFeed *simpleFeed = [KSComHelper getObjectInArray:feedsArray byIndex:indexPath.row ofClassType:[KSimpleFeed class] defaultValue:nil];
    return simpleFeed;
}

// 防止越界访问
- (BOOL)judgeArrayIndexInBounds:(NSIndexPath *)indexPath tableView:(UITableView *)tableView
{
    
    if (indexPath.section == CollectionSectionTypeUpload) {
        if (tableView == self.mFollowTableView) {
            // 关注 显示不带poi信息的上传任务
            NSArray *arr = [self getUploadTaskWithPoiInfo:NO];
            if (indexPath.row >= [arr count]) {
                return NO;
            }
        } else if (tableView == self.mNearbyFeedTableView) {
            // 同城 显示带poi信息的上传任务
            NSArray *arr = [self getUploadTaskWithPoiInfo:YES];
            if (indexPath.row >= [arr count]) {
                return NO;
            }
        }
    }
    else if (indexPath.section == CollectionSectionTypeCloudComposite) {
        if (tableView == self.mFollowTableView) {
            // 关注 显示不带poi信息的上传任务
            if (indexPath.row >= [KSCloudCompositeManager defaultManager].validTaskCount)
            {
                return NO;
            }
        } else if (tableView == self.mNearbyFeedTableView) {
            // 同城 显示带poi信息的上传任务
            if (indexPath.row >= [KSCloudCompositeManager defaultManager].validTaskCount)
            {
                return NO;
            }
        }
    }
    else if (indexPath.section == CollectionSectionTypeShare) {
        if (tableView == self.mFollowTableView) {
            // 关注
            NSArray *arr = [self getShareSimpleFeedsWithPoiInfo:NO];
            if (indexPath.row >= [arr count]) {
                return NO;
            }
        } else if (tableView == self.mNearbyFeedTableView) {
            // 同城
            NSArray *arr = [self getShareSimpleFeedsWithPoiInfo:YES];
            if (indexPath.row >= [arr count]) {
                return NO;
            }
        }
    }
    else if (indexPath.section == CollectionSectionTypeCell)
    {
        KSFilterFeedsInfo *filterFeedsInfo = [self getFeedDataListByScollView:tableView];
        NSInteger ret = filterFeedsInfo.feedsList.count;
        if (indexPath.row >= ret) {
            return NO;
        }
    }
    return YES;
}

#pragma mark - UITableViewDataSource & UITableViewDelegate <NSObject>

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    if (tableView == self.mNearbyUserTableView)
    {
        // 附近的人列表
        BOOL isOutOfRange = indexPath.row >= self.nearByUserArray.count;
        if (isOutOfRange)
        {
            UITableViewCell *cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"NilCell"];
            return cell;
        }
        
        KSNearbyUserTableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:KSNearbyUserTableViewCell.reuseId];
        KSNearByUserInfoModel *userInfo = self.nearByUserArray[indexPath.row];
        cell.nearByUserModel = userInfo;
        cell.isHiddenLocationInfo = [self isLocationAuthorizationStatusUnabled];

        KS_WEAK_SELF(self);
        cell.audioPlayBlock = ^(JceProtoLbs_NewSongInfo * _Nonnull songInfo, BOOL isPlay) {
          
            CHECK_SELF_AND_RETURN()
            
            [self updateNearByUserAudioPlayStateForUgcId:songInfo.jce_ugcId isPlay:isPlay];

            if (isPlay) {
                KSGlobalPlayItem *playItem = [[KSGlobalPlayItem alloc] initWithUgcId:songInfo.jce_ugcId ugcMask:0 songName:songInfo.jce_name nickName:@"" scoreRank:0];
                playItem.coverUrl = songInfo.jce_strCover;
                
                // 判断seek的时刻合法性
                if (0 < songInfo.jce_segmentStart && songInfo.jce_segmentStart < songInfo.jce_durationSec)
                {
                    playItem.passSeekTime = songInfo.jce_segmentStart;
                } else {
                   playItem.passSeekTime = 0;
                }

                //点击后台播放
                [KSUgcPlayManager sharedManager].delegate = self;
                [[KSUgcPlayManager sharedManager] playUgcWithItem:playItem showPlayVC:NO];
                
                // 0音量提示
                [[KSSysVolumeHelper sharedInstance] checkThenNeedShowVolumeTipWithBizType:KSZeroVolumeTipBiz_Timeline_NearBy_User];
            } else {
                [[KSGlobalPlayFloatingWindowManager sharedManager] floatingViewControlPlayOrPause];
            }
        };
         return cell;
    }
    
    BOOL indexInRange = [self judgeArrayIndexInBounds:indexPath tableView:tableView];
    if (!indexInRange) {
        UITableViewCell *cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"NilCell"];
        if (self.shouldShowEmptyView) {
            //特殊情况，需要展示一个无网络界面
            [cell.contentView addSubview:[self noNetworkEmptyViewWithDarkMode:YES]];
            self.shouldShowEmptyView = NO;
        }
        return cell;
    }
    
    // 按照section区分
    if (indexPath.section == CollectionSectionTypeFollowFilter)
    {
        // 动态关注筛选器
        if (tableView == self.mFollowTableView)
        {
            UITableViewCell *cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"NilCell"];
            return cell;
        }
    }
    else if (indexPath.section == CollectionSectionTypeUpload)
    {
        id task;
        BOOL hideFakeFeed = NO;
        
        if (self.uploadTaskArray) {
            if (tableView == self.mFollowTableView) {
                // 关注 显示不带poi信息的上传任务
                NSArray *arr = [self getUploadTaskWithPoiInfo:NO];
                task = [arr safeObjectAtIndex:indexPath.row];
            } else if (tableView == self.mNearbyFeedTableView) {
                // 同城 显示带poi信息的上传任务
                NSArray *arr = [self getUploadTaskWithPoiInfo:YES];
                task = [arr safeObjectAtIndex:indexPath.row];
            }
        }
        
        if (self.syncMultiTaskArray && [self.syncMultiTaskArray containsObject:task])
        {
            hideFakeFeed = YES;
        }
        
        if ([task isKindOfClass:[ProductUploadTask class]])
        {
            ProductUploadTask *uploadTask = (ProductUploadTask*)task;
            KSimpleFeed *simpleFeed = [KSimpleFeed feedWithPublishContent:uploadTask.publishContent];
            if (tableView == self.mNearbyFeedTableView) {
                // 带poi信息，进入同城展示，打上同城的mask
                simpleFeed.feedFilterMask = simpleFeed.feedFilterMask | JceTimeline_enum_filter_mask_ENUM_FILTER_MASK_INTRA_CITY_FEED;
            }
            NSString *playModelStr = [uploadTask.mapExtDic safeObjectForKey:@"is_mv_play"];
            if ([playModelStr isEqualToString:@"1"])
            {
                simpleFeed.songinfo.ugcMaskExt = JceTimeline_Detail_KGE_UGC_MASK_EXT_BIT_KGE_UGC_MASK_EXT_MV_PLAY_MODEL;
            }
            simpleFeed.hideFakeFeed = hideFakeFeed;
            simpleFeed.isFakeFeed = YES;
            simpleFeed.isShowUploading = YES;
            simpleFeed.uploadTask = uploadTask;

            return [self.feedManager createUploadCellForTableView:tableView cellForRowAtIndexPath:indexPath WithSimpleFeed:simpleFeed];
            
        }
        else if ([task isKindOfClass:[KSSoloAlbumCreateTaskInfo class]])
        {
            KSSoloAlbumCreateTaskInfo *soloAlbumTask = (KSSoloAlbumCreateTaskInfo*)task;
            KSimpleFeed *simpleFeed = [KSimpleFeed feedWithSoloAlbumTask:soloAlbumTask];
            simpleFeed.isFakeFeed = YES;
            simpleFeed.isShowUploading = YES;
            simpleFeed.soloAlbumTask = soloAlbumTask;
            
            return [self.feedManager createUploadAlbumCellForTableView:tableView cellForRowAtIndexPath:indexPath WithSimpleFeed:simpleFeed];
        }
        else
        {
            return [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"NilCell"];
        }
    }
    else if (indexPath.section == CollectionSectionTypeCloudComposite)
    {
        KSPublishContent *publishContent = nil;
        if (tableView == self.mFollowTableView || tableView == self.mNearbyFeedTableView) {
            NSArray *arr = [KSCloudCompositeManager defaultManager].validTaskPublishContentArray;
            publishContent = [arr safeObjectAtIndex:indexPath.row];
        }
        
        if (publishContent != nil)
        {
            KSimpleFeed *simpleFeed = [KSimpleFeed feedWithPublishContent:publishContent];
            if (tableView == self.mNearbyFeedTableView) {
                // 带poi信息，进入同城展示，打上同城的mask
                simpleFeed.feedFilterMask = simpleFeed.feedFilterMask | JceTimeline_enum_filter_mask_ENUM_FILTER_MASK_INTRA_CITY_FEED;
            }
            
            simpleFeed.hideFakeFeed = NO;
            simpleFeed.isFakeFeed = YES;
            simpleFeed.isShowUploading = YES;
            KSTimeRootUploadShareCellV2 *cell = (KSTimeRootUploadShareCellV2 *)[self.feedManager createUploadCellForTableView:tableView cellForRowAtIndexPath:indexPath WithSimpleFeed:simpleFeed];
            [cell updatePublishContent:publishContent];
            return cell;
        }
        else
        {
            return [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"NilCell"];
        }
    }
    else if (indexPath.section == CollectionSectionTypeShare)
    {
        KSimpleFeed *simpleFeed;
        if (tableView == self.mFollowTableView) {
            // 关注
            NSArray *arr = [self getShareSimpleFeedsWithPoiInfo:NO];
            simpleFeed = [arr safeObjectAtIndex:indexPath.row];
        } else if (tableView == self.mNearbyFeedTableView) {
            // 同城
            NSArray *arr = [self getShareSimpleFeedsWithPoiInfo:YES];
            simpleFeed = [arr safeObjectAtIndex:indexPath.row];
            // 带poi信息，进入同城展示，打上同城的mask
            simpleFeed.feedFilterMask = simpleFeed.feedFilterMask | JceTimeline_enum_filter_mask_ENUM_FILTER_MASK_INTRA_CITY_FEED;
        }
        UITableViewCell *cell = [self.feedManager createShareCellForTableView:tableView cellForRowAtIndexPath:indexPath WithSimpleFeed:simpleFeed];
        return cell;
    } else if (indexPath.section == CollectionSectionTypeCell) {
        // MARK: Feed UGC/直播/歌房等cell
        KSimpleFeed *simpleFeed = [self getFeedByTableView:tableView indexPath:indexPath];
        simpleFeed.giftBtnDiffInfo = [KSUserPortraitStategyManager sharedManager].recCardGiftDiffRsp;
        UITableViewCell *cell = nil;
        if ([self isRecCardTable:tableView]) {
            // MARK: 大卡片Feed
            if (indexPath.row == 0 && ![KSLiveStreamPreloadManager shareInstance].hasInitializeLiveStreamPreloadManager) {
                //直播feed替换逻辑
                [KSLiveStreamPreloadManager shareInstance].hasInitializeLiveStreamPreloadManager = YES;
                [[KSLiveStreamPreloadManager shareInstance] startPreloadLivePlayersWhenScrollTo:0];
            }
            if ([simpleFeed isKindOfLiveShowFeed]) {
                simpleFeed.liveShow.liveFeedSourceType = LiveFeedBigCardSourceType_RECOMMEND;
            }
            [self.timeSliceManager removeTimeSliceWithName:[NSString stringWithFormat:@"feedCellShareBreath"]];
            cell = [self.feedManager createRecCardFeedCellForTableView:tableView rowIndexPath:indexPath withKSimpleFeed:simpleFeed];
            if ([cell isKindOfClass:KSLayoutableTimelineFeedCellV2.class]) {
                KSLayoutableTimelineFeedCellV2 *feedCell = SAFE_CAST(cell, KSLayoutableTimelineFeedCellV2.class);
                if (feedCell) {
                    feedCell.parentVC = self;
                    if (!self.pageManager) {
                        self.pageManager = [[KSKTVRoomCardDataManager alloc] init];
                    }
                    feedCell.pageManager = self.pageManager;
                    feedCell.currentPositionDelegate = self;
                    feedCell.collectionDelegate = self;
                    feedCell.previewPlayDelegate = self;
                }
            }
            // 可以确认下要是业务废弃了就应该删除
            [self.feedManager preloadSongGodCouponWithTable:tableView curRowIndexPath:indexPath];
        } else {
            // MARK: 普通Feed
            cell = [self.feedManager createFeedCellForTableView:tableView cellForRowAtIndexPath:indexPath WithSimpleFeed:simpleFeed];
            if ([cell isKindOfClass:KSLayoutableTimelineFeedCellV2.class]) {
                KSLayoutableTimelineFeedCellV2 *feedCell = SAFE_CAST(cell, KSLayoutableTimelineFeedCellV2.class);
                feedCell.previewPlayDelegate = self;
            }
        }
        
        return cell;
    }
    else if (indexPath.section == CollectionSectionTypeRecUser)
    {
        // 浅关系链推荐用户feed
        KSFilterFeedsInfo *filterFeedsInfo = [self getFeedDataListByScollView:tableView];
        NSInteger currentFilterMask = filterFeedsInfo.filterMask;
        
        if ([[KSTimelineManager sharedManager] checkIsFollowBizWithFilterMask:currentFilterMask])
        {
            // 有折叠
            BOOL foldFlag = [self shouldShowFoldWithFilterFeedsInfo:filterFeedsInfo];
            
            if (indexPath.row == 0)
            {
                if (foldFlag)
                {
                    // 更多历史
                    KSTimelineLoadMoreHistoryCell *cell = [tableView dequeueReusableCellWithIdentifier:kTimelineLoadMoreHistoryCellID forIndexPath:indexPath];
                    if (currentFilterMask & [KSTimelineManager sharedManager].filtrMaskUgcFeed || currentFilterMask & [KSTimelineManager sharedManager].filtrMaskUgcFeedQQWX)
                    {
                        [cell updateWithTitle:@"查看更多历史作品" currentFilterMask:currentFilterMask];
                    }
                    else
                    {
                        [cell updateWithTitle:@"查看更多历史动态" currentFilterMask:currentFilterMask];
                    }
                    
                    // 曝光上报
                    KS_WEAK_SELF(self);
                    [cell judgeExpose3Immediately:nil
                                        cellModel:cell.cellModel
                                      exposeBlock:^{
                        CHECK_SELF_AND_RETURN();
                        [self traceReportExposeFoldHistorycellWithCurrentFilterMask:currentFilterMask];
                    }];
                    
                    return cell;
                }
                else
                {
                    // 标题
                    KSTimelineRecUserHeaderCell *cell = [tableView dequeueReusableCellWithIdentifier:kTimelineRecUserHeaderCellID forIndexPath:indexPath];
                    return cell;
                }
            }
            else
            {
                if (foldFlag && indexPath.row == 1)
                {
                    // 标题
                    KSTimelineRecUserHeaderCell *cell = [tableView dequeueReusableCellWithIdentifier:kTimelineRecUserHeaderCellID forIndexPath:indexPath];
                    return cell;
                }
                else
                {
                    // 推荐用户feed
                    NSInteger idx = foldFlag ? indexPath.row - 2 : indexPath.row - 1; // 偏移 折叠(可能没有) 标题
                    
                    KSimpleFeed *simpleFeed;
                    
                    if ([KSTimelineManager sharedManager].recFeedType == proto_feed_webapp_ENUM_REC_FEED_TYPE_ENUM_REC_FEED_TOPIC)
                    {
                        simpleFeed = [KSComHelper getObjectInArray:[KSTimelineManager sharedManager].recHotSpotFeed.feedsList byIndex:idx ofClassType:[KSimpleFeed class] defaultValue:nil];
                    }
                    else
                    {
                        if (currentFilterMask == [KSTimelineManager sharedManager].filtrMaskFollow)
                        {
                            simpleFeed = [KSComHelper getObjectInArray:[KSTimelineManager sharedManager].recUserFeed_ForFollow.feedsList byIndex:idx ofClassType:[KSimpleFeed class] defaultValue:nil];
                        }
                        else if (currentFilterMask == [KSTimelineManager sharedManager].filtrMaskQQWXFriends)
                        {
                            simpleFeed = [KSComHelper getObjectInArray:[KSTimelineManager sharedManager].recUserFeed_ForQQWXFriends.feedsList byIndex:idx ofClassType:[KSimpleFeed class] defaultValue:nil];
                        }
                        else if (currentFilterMask == [KSTimelineManager sharedManager].filtrMaskUgcFeed)
                        {
                            simpleFeed = [KSComHelper getObjectInArray:[KSTimelineManager sharedManager].recUserFeed_ForUgcFeed.feedsList byIndex:idx ofClassType:[KSimpleFeed class] defaultValue:nil];
                        }
                        else if (currentFilterMask == [KSTimelineManager sharedManager].filtrMaskUgcFeedQQWX)
                        {
                            simpleFeed = [KSComHelper getObjectInArray:[KSTimelineManager sharedManager].recUserFeed_ForUgcFeedQQWX.feedsList byIndex:idx ofClassType:[KSimpleFeed class] defaultValue:nil];
                        }
                    }
                    
                    if (simpleFeed)
                    {
                        UITableViewCell *cell = [self.feedManager createFeedCellForTableView:tableView cellForRowAtIndexPath:indexPath WithSimpleFeed:simpleFeed];
                        if ([cell isKindOfClass:KSLayoutableTimelineFeedCellV2.class])
                        {
                            KSLayoutableTimelineFeedCellV2 *feedCell = SAFE_CAST(cell, KSLayoutableTimelineFeedCellV2.class);
                            if (feedCell)
                            {
                                feedCell.previewPlayDelegate = self;
                                
                                if (([simpleFeed isRecCardFeed] == NO) && [self isFeedAutoPlay])
                                {
                                    // Feed自动播放场景
                                    BOOL shouldHide = [KSFeedPreviewManager sharedManager].currentPreviewPlayingType != PreviewPlayingTypeNone && [[KSFeedPreviewManager sharedManager] isPlayingCurrentFeed:simpleFeed];
                                               
                                    [feedCell setUgcInfoSubviewAlpha:shouldHide ? 0 : 1];
                                    
                                    feedCell.playButton.selected = shouldHide;
                                    
                                    [feedCell.videoPlayView removeFromSuperview];
                                    feedCell.videoPlayView = nil;
                                    KLog(@"[视频view]setup nil");
                                    [[KSUgcPlayManager sharedManager] setPlaybackView:nil];
                                }
                            }
                        }
                        
                        return cell;
                    }
                }
            }
        }
    }
    
    return [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"NilCell"];
}

- (void)playerChangeStatus:(KSPlayStatus)toStatus playItem:(KSGlobalPlayItem *)playItem {

    KLog(@"KSNearByUserListAudioPlay status: %zi ugcId: %@", toStatus, playItem.ugcId);
    
    if (toStatus == KSPlayStatus_Playing ||
        toStatus == KSPlayStatus_Stop ||
        toStatus == KSPlayStatus_Pause)
    {
        
        BOOL isPlay = (toStatus == KSPlayStatus_Playing);
        [self updateNearByUserAudioPlayStateForUgcId:playItem.ugcId isPlay:isPlay];
    }
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath
{
    if (tableView == self.mNearbyUserTableView)
    {
        // 附近的人列表
        if (indexPath.row < self.nearByUserArray.count)
        {
            KSNearByUserInfoModel *user = self.nearByUserArray[indexPath.row];
            BOOL isShowPhotos = (user.userInfo.jce_vctPicItem.count > 0);
            return [KSNearbyUserTableViewCell nearByUserCellHeightWithShowPhoto:isShowPhotos];
        }
        return CGFLOAT_MIN;
    }
    
    NSInteger section = indexPath.section;
    if (section == CollectionSectionTypeFollowFilter)
    {
        // 动态关注筛选器
        if (tableView == self.mFollowTableView)
        {
            return kTimelineFollowFilterViewHeight;
        }
    }
    else if (section == CollectionSectionTypeUpload)
    {
        // 上传section 是否展示取决于是不是有上传任务
        id task;
        BOOL hideFakeFeed = NO;
        
        if (self.uploadTaskArray)
        {
            if (tableView == self.mFollowTableView) {
                // 关注 显示不带poi信息的上传任务
                NSArray *arr = [self getUploadTaskWithPoiInfo:NO];
                task = [arr safeObjectAtIndex:indexPath.row];
            } else if (tableView == self.mNearbyFeedTableView) {
                // 同城 显示带poi信息的上传任务
                NSArray *arr = [self getUploadTaskWithPoiInfo:YES];
                task = [arr safeObjectAtIndex:indexPath.row];
            }
        }
        
        if (self.syncMultiTaskArray && [self.syncMultiTaskArray containsObject:task])
        {
            hideFakeFeed = YES;
        }

        if ([task isKindOfClass:[ProductUploadTask class]])
        {
            ProductUploadTask *uploadTask = (ProductUploadTask*)task;
            KSimpleFeed *simpleFeed = [KSimpleFeed feedWithPublishContent:uploadTask.publishContent];
            NSString *playModelStr = [uploadTask.mapExtDic safeObjectForKey:@"is_mv_play"];
            if ([playModelStr isEqualToString:@"1"])
            {
                simpleFeed.songinfo.ugcMaskExt = JceTimeline_Detail_KGE_UGC_MASK_EXT_BIT_KGE_UGC_MASK_EXT_MV_PLAY_MODEL;
            }
            simpleFeed.hideFakeFeed = hideFakeFeed;
            simpleFeed.isFakeFeed = YES;
            simpleFeed.isShowUploading = YES;
            if (tableView == self.mFollowTableView) {
                simpleFeed.feedFilterMask = simpleFeed.feedFilterMask | JceTimeline_enum_filter_mask_ENUM_FILTER_MASK_INTRA_CITY_FEED;
            }
            CGFloat height = [KSLayoutUIManagerTimeline currentHeightOfTimelineFeedInfo:simpleFeed];
            return height;
        }
        else if ([task isKindOfClass:[KSSoloAlbumCreateTaskInfo class]])
        {
            KSSoloAlbumCreateTaskInfo *soloAlbumTask = (KSSoloAlbumCreateTaskInfo*)task;
            KSimpleFeed *simpleFeed = [KSimpleFeed feedWithSoloAlbumTask:soloAlbumTask];
            simpleFeed.isFakeFeed = YES;
            simpleFeed.isShowUploading = YES;
            CGFloat height = [KSLayoutUIManagerTimeline currentHeightOfTimelineFeedInfo:simpleFeed];
            return height;
        }
        return 0;
    }
    else if (indexPath.section == CollectionSectionTypeCloudComposite)
    {
        KSPublishContent *publishContent = nil;
        if (tableView == self.mFollowTableView || tableView == self.mNearbyFeedTableView) {
            NSArray *arr = [KSCloudCompositeManager defaultManager].validTaskPublishContentArray;
            publishContent = [arr safeObjectAtIndex:indexPath.row];
        }
        
        if (publishContent != nil)
        {
            KSimpleFeed *simpleFeed = [KSimpleFeed feedWithPublishContent:publishContent];
            if (tableView == self.mNearbyFeedTableView) {
                // 带poi信息，进入同城展示，打上同城的mask
                simpleFeed.feedFilterMask = simpleFeed.feedFilterMask | JceTimeline_enum_filter_mask_ENUM_FILTER_MASK_INTRA_CITY_FEED;
            }
            
            simpleFeed.hideFakeFeed = NO;
            simpleFeed.isFakeFeed = YES;
            simpleFeed.isShowUploading = YES;
            if (tableView == self.mFollowTableView) {
                simpleFeed.feedFilterMask = simpleFeed.feedFilterMask | JceTimeline_enum_filter_mask_ENUM_FILTER_MASK_INTRA_CITY_FEED;
            }
            CGFloat height = [KSLayoutUIManagerTimeline currentHeightOfTimelineFeedInfo:simpleFeed];
            return height;
        }
    }
    else if (section == CollectionSectionTypeShare)
    {
        // 分享section 是否展示取决于是否有刚上传的ugc
        KSimpleFeed *simpleFeed;
        if (tableView == self.mFollowTableView) {
            // 关注
            NSArray *arr = [self getShareSimpleFeedsWithPoiInfo:NO];
            simpleFeed = [arr safeObjectAtIndex:indexPath.row];
        } else if (tableView == self.mNearbyFeedTableView) {
            // 同城
            NSArray *arr = [self getShareSimpleFeedsWithPoiInfo:YES];
            simpleFeed = [arr safeObjectAtIndex:indexPath.row];
            // 带poi信息，进入同城展示，打上同城的mask
            simpleFeed.feedFilterMask = simpleFeed.feedFilterMask | JceTimeline_enum_filter_mask_ENUM_FILTER_MASK_INTRA_CITY_FEED;
        }
        //6.11.0 产品seancuxu要求取消展示这个分享条，先用这个BOOL值隐藏，预防产品加回来
        // 作品关联相册，而且图片上传失败的要显示错误UI
        simpleFeed.isShowShare = (simpleFeed.publishContent.workPics.imgs.count > 0 && simpleFeed.publishContent.isWorkPicUploadFailed);
        
        CGFloat height = [KSLayoutUIManagerTimeline currentHeightOfTimelineFeedInfo:simpleFeed];
        return height;
    }
    else if (section == CollectionSectionTypeCell)
    {
        if ([KSTimelineManager sharedManager].currentFiltrMask == [KSTimelineManager sharedManager].filtrMaskHot)
        {
            return [KSShortVideoRankCell getFeedCellHeight];
        }
        else
        {
            KSimpleFeed *simpleFeed = [self getFeedByTableView:tableView indexPath:indexPath];
            
            if (simpleFeed.feedAd)
            {
                KSUnifiedNativeAdType adType = [[KSFeedAdManager sharedManager] getCurrentFeedAdType];
                if (!simpleFeed.tmeFeedAd && [[KSFeedAdManager sharedManager] canFillFeedAdButHaveNotReplaceData:simpleFeed adType:adType]) {
                    if ([simpleFeed isRecCardFeed]) {
                        BOOL enabelZeroHeight = WnsLocalServerBoolConfig(@"kEnabelRecCardAdCellZeroheight");
                        if (enabelZeroHeight) {
                            return 0;
                        }else{
                            KLog(@"[TMEAdFeed]recomm ad cell height can't use 0");
                        }
                    } else {
                        return 0;
                    }
                }
            }
            //如果要更改大卡片高度后，请同步修改广告删除的高度判断逻辑，不然可能会导致播放 & 展示错误
            //本类中搜索关键词 延迟检查填充广告。by zhongqishi
            if ([self isRecCardTable:tableView]) {
                CGFloat height = tableView.height;
                return height;
            }
            else
            {
                CGFloat height = [KSLayoutUIManagerTimeline currentHeightOfTimelineFeedInfo:simpleFeed];
                return height;
            }
        }
    }
    else if (section == CollectionSectionTypeRecUser)
    {
        // 关注Tab 推荐用户数据
        KSFilterFeedsInfo *filterFeedsInfo = [self getFeedDataListByScollView:tableView];
        NSInteger currentFilterMask = filterFeedsInfo.filterMask;
        if ([[KSTimelineManager sharedManager] checkIsFollowBizWithFilterMask:currentFilterMask])
        {
            // 有折叠
            BOOL foldFlag = [self shouldShowFoldWithFilterFeedsInfo:filterFeedsInfo];
            
            if (indexPath.row == 0)
            {
                if (foldFlag)
                {
                    // 查看更多历史动态
                    return kTimelineLoadMoreHistoryCellHeight;
                }
                else
                {
                    // 标题
                    return kTimelineRecUserHeaderCellHeight;
                }
            }
            else
            {
                if (foldFlag && indexPath.row == 1)
                {
                    // 标题
                    return kTimelineRecUserHeaderCellHeight;
                }
                else
                {
                    // 推荐用户 feed
                    NSInteger idx = foldFlag ? indexPath.row - 2 : indexPath.row - 1; // 偏移 折叠(可能没有) 标题
                    
                    KSimpleFeed *simpleFeed;
                    if ([KSTimelineManager sharedManager].recFeedType ==proto_feed_webapp_ENUM_REC_FEED_TYPE_ENUM_REC_FEED_TOPIC)
                    {
                        // 浅关系链下热点话题数据
                        simpleFeed = [KSComHelper getObjectInArray:[KSTimelineManager sharedManager].recHotSpotFeed.feedsList byIndex:idx ofClassType:[KSimpleFeed class] defaultValue:nil];
                    }
                    else
                    {
                        if (currentFilterMask == [KSTimelineManager sharedManager].filtrMaskFollow)
                        {
                            // 全部
                            simpleFeed = [KSComHelper getObjectInArray:[KSTimelineManager sharedManager].recUserFeed_ForFollow.feedsList byIndex:idx ofClassType:[KSimpleFeed class] defaultValue:nil];
                        }
                        else if (currentFilterMask == [KSTimelineManager sharedManager].filtrMaskQQWXFriends)
                        {
                            // 全部（好友）
                            simpleFeed = [KSComHelper getObjectInArray:[KSTimelineManager sharedManager].recUserFeed_ForQQWXFriends.feedsList byIndex:idx ofClassType:[KSimpleFeed class] defaultValue:nil];
                        }
                        else if (currentFilterMask == [KSTimelineManager sharedManager].filtrMaskUgcFeed)
                        {
                            // 作品
                            simpleFeed = [KSComHelper getObjectInArray:[KSTimelineManager sharedManager].recUserFeed_ForUgcFeed.feedsList byIndex:idx ofClassType:[KSimpleFeed class] defaultValue:nil];
                        }
                        else if (currentFilterMask == [KSTimelineManager sharedManager].filtrMaskUgcFeedQQWX)
                        {
                            // 作品（好友）
                            simpleFeed = [KSComHelper getObjectInArray:[KSTimelineManager sharedManager].recUserFeed_ForUgcFeedQQWX.feedsList byIndex:idx ofClassType:[KSimpleFeed class] defaultValue:nil];
                        }
                    }
                    
                    if (simpleFeed.feedAd)
                    {
                        return CGFLOAT_MIN;
                    }
                    
                    CGFloat height = [KSLayoutUIManagerTimeline currentHeightOfTimelineFeedInfo:simpleFeed];
                    
                    return height;
                }
            }
        }
    }
    return CGFLOAT_MIN;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    NSInteger section = indexPath.section;
    
    if (tableView == self.mNearbyUserTableView)
    {
        if (indexPath.row < self.nearByUserArray.count)
        {
            KSNearByUserInfoModel *user = self.nearByUserArray[indexPath.row];
            // 进入个人主页
            [[KSNavigationManager sharedManager] showProfileView:user.userInfo.jce_uid fromPage:@"feed_nearby#person_nearby" withRectItme:nil];
        }
        return;
    }
    
    
    if (section == CollectionSectionTypeUpload)
    {
        
    }
    else if (indexPath.section == CollectionSectionTypeCloudComposite)
    {
        
    }
    else if (section == CollectionSectionTypeShare)
    {
        KSimpleFeed *feed = nil;
        if (tableView == self.mFollowTableView) {
            // 关注 显示不带poi信息的上传任务
            NSArray *arr = [self getShareSimpleFeedsWithPoiInfo:NO];
            feed = [arr safeObjectAtIndex:indexPath.row];
        } else if (tableView == self.mNearbyFeedTableView) {
            // 同城 显示带poi信息的上传任务
            NSArray *arr = [self getShareSimpleFeedsWithPoiInfo:YES];
            feed = [arr safeObjectAtIndex:indexPath.row];
        }
        
        if (feed.soloAlbumInfo)
        {
            [[KSNavigationManager sharedManager] showSongListDetailVC:feed.soloAlbumInfo.strAlbumId fromType:KSSongListDetailFromType_Feed];
        }
        else
        {
            if (!feed.isFakeFeed && !IS_EMPTY_STR_BM(feed.simpleFeedCommon.strFeedId))
            {
                [self showTimelineDetailWithFeed:feed];
            }
            else
            {
                // 有可能假Feed没有刷掉，这里让用户重试下
                [KSToast showToast:@"发布状态同步中，请刷新后重试"];
            }
        }
        
    }
    else if (section == CollectionSectionTypeCell)
    {
        if ([self getCurTab] == KSTimelineTab_Rec && [KSTimelineManager sharedManager].currentFiltrMask != [KSTimelineManager sharedManager].filtrMaskRecommend)
        {
            //做异常保护，热门点击空白cell也不跳转
            return;
        }
        
        if ([self getCurTab] == KSTimelineTab_Follow ||
            [self getCurTab] == KSTimelineTab_QQWXFriends) {
            
            [[KSGuideBubbleManager sharedManager] preConditionShowGiftGuide];
        }
        
        KSimpleFeed *feed = [self getFeedByTableView:tableView indexPath:indexPath];
        
        // 动态点击上报
        [self timelineClickedTraceReport:feed extraInfo:@{@"indexRowKey":indexPath}];
        
        if (feed.songinfo || feed.simpleFeedCommon.uTypeid == FEED_TYPE_UGC)
        {
            [self onUgcFeedClickWithSimpleFeed:feed indexPath:indexPath];
        }
        
        if (feed.competitionFeed)
        {
            [[KSNavigationManager sharedManager] dealWithScheme:feed.competitionFeed.strJumpUrl];
            [self onCompetitionFeedClickCoverWithSimpleFeed:feed indexPath:indexPath];
        }
        else if (feed.feedAd)
        {
            //广告feed。确保每个分支要上报点击数据。如果跳转的是jumprl，直接上报了。否则目标链接不是jumpurl.这时候需要主动发送个Http请求告诉广点通点击上报.注意这里上报只能上报一次，避免重复上报
            [self.feedManager didSelectedOnAdvertiseFeed:feed clickPose:KSFeedAdvertiseClickPose_AdOtherEmptyZone];
        }
        else if (feed.soloAlbumInfo)
        {
            [self onSonglistFeedClickCoverWithSimpleFeed:feed indexPath:indexPath];
            [[KSNavigationManager sharedManager] showSongListDetailVC:feed.soloAlbumInfo.strAlbumId fromType:KSSongListDetailFromType_Feed];
        }
        else if (feed.payAlbumInfo)
        {
            [[KSNavigationManager sharedManager] showPayAlbumView:feed.payAlbumInfo.strAlbumId andUgcId:feed.simpleFeedCommon.strFeedId];
      
            
            KSUserPayRight *payRight = [[KSPaySoloAlbumManager sharedManager] getPayRightContentID:feed.payAlbumInfo.strAlbumId andPayType:KSUserPayTypeOptionAlbum];
            switch ([[KSPaySoloAlbumManager sharedManager] getPayIconMask:payRight.mapRight]) {
                case KSPayIconPriorityVIP: {
                    
                    NSUInteger pid = 0;
                    KSTimelineTabSelectIndex curTab = [self getCurTab];
                    if (curTab == KSTimelineTab_Follow)
                    {
                        pid = VipPid_PayAlbumFeed_p3;
                        
                    }
                    else if (curTab == KSTimelineTab_QQWXFriends)
                    {
                        pid = VipPid_PayAlbumFeed_p6;
                        
                    }
                    else if (curTab == KSTimelineTab_Rec)
                    {
                        pid = VipPid_PayAlbumFeed_p9;
                        
                    }
                    else if (curTab == KSTimelineTab_Live)
                    {
                        pid = VipPid_PayAlbumFeed_p12;
                    }
                                        
                    [[KSVipTraceReportManager sharedManager] vipPayAlbumClickReportWithPosId:pid contentId:feed.payAlbumInfo.strAlbumId];
                    
                    
                    break;
                }
                    
                case KSPayIconPriorityPay: {
                    BOOL isForword = NO;
                    if (feed.forwardFeed && feed.forwardFeed.strForwardId) {
                        isForword = YES;
                    }
                    NSUInteger pid = 0;
                    KSTimelineTabSelectIndex curTab = [self getCurTab];
                    if (curTab == KSTimelineTab_Follow)
                    {
                        pid = PayAlbum_Feed_Follow_P3;
                    }
                    else if (curTab == KSTimelineTab_QQWXFriends)
                    {
                        pid = PayAlbum_Feed_Friend_P3;
                    }
                    else if (curTab == KSTimelineTab_Rec)
                    {
                        pid = PayAlbum_Feed_Hot_P3;
                    }
                    else if (curTab == KSTimelineTab_Live)
                    {
                        pid = PayAlbum_Feed_Near_P3;
                    }
                    [[KSTraceReportManager sharedManager] payAlbumClickPosid:pid
                                                                       ugcId:@""
                                                                     albumId:feed.payAlbumInfo.strAlbumId
                                                                   isForward:isForword
                                                                       toUid:feed.simpleUser.userinfo.userId
                                                                     shareId:0];
                    break;
                }
                default:
                    break;
            }
        }
        else if (feed.liveShow && feed.liveShow.strLiveRoomId.length>0)
        {
            NSString *activityUrl = [feed.liveShow.mapExt safeObjectForKey:@"share_url"];
            if (feed.liveShow.isPushStreamLive && !IS_EMPTY_STR_BM(activityUrl) ) {
                // 运营推流直播
                if (!IS_EMPTY_STR_BM(feed.recommendItem.jce_strTraceId)) {
                    activityUrl = [activityUrl stringByAppendingFormat:@"&trace_id=%@&item_type=%u&algorithm_type=%u&algoritym_id=%@",feed.recommendItem.jce_strTraceId,feed.recommendItem.jce_uiItemType,feed.recommendItem.jce_uiAlgorithmType,feed.recommendItem.jce_strAlgorithmId];
                    activityUrl = [activityUrl stringByAddingPercentEncodingForWholeUrl];
                }
                [[KSNavigationManager sharedManager] dealWithScheme:activityUrl];
            } else {
                if (feed.simpleUser.userinfo.userId == [KSLoginManager sharedInstance].curUserInfo.userId) {
                    [[KSNavigationManager sharedManager] showLiveShowEnterVC];
                } else {
                    // 如果是高光回放推荐，需要补充上高光时刻的参数
                    NSMutableDictionary *params = [NSMutableDictionary new];
                    if (!IS_EMPTY_STR_BM(feed.recommendItem.jce_strTraceId)) {
                        params[@"trace_id"] = feed.recommendItem.jce_strTraceId;
                        params[@"item_type"] = [NSString stringWithFormat:@"%d",feed.recommendItem.jce_uiItemType];
                        params[@"algorithm_type"] = [NSString stringWithFormat:@"%d",feed.recommendItem.jce_uiAlgorithmType];
                        params[@"algoritym_id"] = feed.recommendItem.jce_strAlgorithmId ?: @"";
                    }
                    
                    NSDictionary *liveMapExt = feed.liveShow.mapExt;
                    /// 热度卡推荐使用
                    if ([liveMapExt safeObjectForKey:PushNotification_uFromSource]) {
                        params[PushNotification_uFromSource] = [liveMapExt safeObjectForKey:PushNotification_uFromSource];
                    }
                    if ([liveMapExt safeObjectForKey:PushNotification_iHeatCardStatus]) {
                        params[PushNotification_iHeatCardStatus] = [liveMapExt safeObjectForKey:PushNotification_iHeatCardStatus];
                        params[PushNotification_HeatEntrance] = @"4"; //用于上报
                    }

                    [params safeSetObject:SAFE_STR_BM([feed.liveShow.mapExt safeObjectForKey:PushNotification_strCdnUrl]) forKey:PushNotification_strCdnUrl];
                    [params safeSetObject:SAFE_STR_BM([feed.liveShow.mapExt safeObjectForKey:PushNotification_iSupportP2P]) forKey:PushNotification_iSupportP2P];
                    
                    NSDictionary *infoDic = [KSLiveShowModuleService appendFromPageNewKey:kFROM_PAGE_NEW_KEY_FEED_LIVE_FEED_COVER sourceDic:[params copy]];
                    [[KSNavigationManager sharedManager] watchLiveShow:feed.liveShow.strLiveRoomId roomInfo:nil forcePop:NO isFromContribution:NO infoDict:infoDic];
                }
            }
            KSLayoutableTimelineFeedCellV2 *cell = [tableView cellForRowAtIndexPath:indexPath];
            [self onLiveFeedClickWithSimpleFeed:feed cell:cell];
           
            return;
        }
        else if((feed.ktvRoomShow && feed.ktvRoomShow.jce_strRoomId.length>0))
        {
            if ([feed isRecCardFeed]) {
                if ((feed.ktvRoomShow.jce_iRoomType & KSKTVRoomType_Multi) == KSKTVRoomType_Multi) {
                    NSString *fromRoute = @"feed#song_room_big_card#song_room";
                    
                } else {
                    NSString *fromRoute = @"feed#song_room_big_card#song_room";
                    NSMutableDictionary *params = [NSMutableDictionary new];
                    params[PushNotification_KtvFromRoute] = fromRoute;
                    params[@"item_type"] = [NSString stringWithFormat:@"%u" ,feed.recommendItem.jce_uiItemType];
                    params[@"trace_id"] = feed.recommendItem.jce_strTraceId;
                    params[@"algorithm_type"] = [NSString stringWithFormat:@"%u" ,feed.recommendItem.jce_uiAlgorithmType];
                    params[@"algoritym_id"] = feed.recommendItem.jce_strAlgorithmId;
                    [[KSNavigationManager sharedManager] showKTVRoomVCWithOwnerUid:feed.ktvRoomShow.jce_lAnchorUid
                                                                            roomid:feed.ktvRoomShow.jce_strRoomId
                                                                            passwd:@""
                                                                          forcePop:YES
                                                                           ktvFrom:NSIToString([self watchKTVReport])
                                                                          fromPage:@"feed#song_room_big_card#song_room"
                                                                              info:params];
                }
                KSLayoutableTimelineFeedCellV2 *cell = [tableView cellForRowAtIndexPath:indexPath];
                [self onRecKTVRoomFeedClickWithSimpleFeed:feed cell:cell index:0];
            }
            return;
        } else if ([feed isKtvRoomCard]) {
            KSLayoutableTimelineFeedCellV2 *cell = [tableView cellForRowAtIndexPath:indexPath];
            [self onRecKTVRoomFeedClickWithSimpleFeed:feed cell:cell index:0];
            return;
        }
        else if (feed.ktvRoomMike && feed.ktvRoomMike.jce_strRoomId.length>0)
        {
            
            if (feed.ktvRoomMike && feed.ktvRoomMike.jce_strRoomId && feed.ktvRoomMike.jce_strRoomId.length>0)
            {
                if ((feed.ktvRoomMike.jce_iRoomType & KSKTVRoomType_Multi) == KSKTVRoomType_Multi) {//进入多人歌房
                    
                }else{
                    [[KSNavigationManager sharedManager] showKTVRoomVCWithOwnerUid:0
                                                                            roomid:feed.ktvRoomMike.jce_strRoomId
                                                                            passwd:@""
                                                                          forcePop:YES
                                                                           ktvFrom:NSIToString([self watchKTVReport])];
                }
            }
        }
        else if ([feed isKindOfProductCardFeed]) 
        {
            return;
        }
        else if (feed.socialGameShow) // 异步社交游戏
        {
            return;
        }
        else if (feed.secretaryFeed) //小秘书
        {
            return;
        }
        else if (feed.oneShotAd) {
            return;
        }
        else if (feed.shortVideoFeed) // 推荐短视频
        {
            return;
        }
        else if (feed.relayGameFeed)
        {
            return;
        }
        else if (feed.musicMoodFeed)
        {
            [self showTimelineDetailWithFeed:feed];
            return;
        }
        else if (feed.singleSendGiftFeed || feed.friendsBirthdaySendGiftFeed) // 生日送礼点击无响应
        {
            return;
        }
        else if (feed.recSongsCellData)//猜你喜欢
        {
            [[KSNavigationManager sharedManager] dealWithScheme:@"qmkege://kege.com?action=guess_you_like_list"];
            return;
        }
        else if (feed.familyUgc ||feed.rectopicFeed || feed.topicListFeed) // 家族feed中 单条ugc以外的空白处点击无响应
        {
            return;
        }
        else if (feed.beatListFeed)//好友擂台
        {
            KSChampionRankListViewController *vc = [[KSChampionRankListViewController alloc] init];
            [[[KSNavigationManager sharedManager] getMainPageNavController] pushViewController:vc animated:YES];
            return;
        }
        else if (feed.recFriendsFeed)//关系链好友推荐
        {
            //跳转到我感兴趣的人页面
            KSUserInfo *userInfo = [KSLoginManager sharedInstance].curUserInfo;
            KSInterestedPeopleVC *interestedPeopleVC = [[KSInterestedPeopleVC alloc] initWithUserinfo:userInfo];
            [self.navigationController pushViewController:interestedPeopleVC animated:YES];
        }
        else if (feed.isRemoved) //已删除feed不跳转
        {
            KINFO(@"已删除不跳转,feed:%@", feed);
            return;
        }
        else if (feed.h5JumpFeed)
        {
            if (feed.h5JumpFeed.jumpUrl.length > 0)
            {
                [[KSNavigationManager sharedManager] dealWithScheme:feed.h5JumpFeed.jumpUrl];
            }
            
            [self onH5FeedClickWithSimpleFeed:feed indexPath:indexPath];
        }
        else if (feed.forwardFeed.strForwardId.length > 0)
        {
            [self traceReportPayAlbum:feed isForward:YES extraInfo:nil];
            
            [self showTimelineDetailWithFeed:feed];
        }
        else if (feed.giftWorksRank)
        {
            // 好友榜统一跳转
            [[KSNavigationManager sharedManager] dealWithScheme:feed.giftWorksRank.strJumpUrl];

            int64_t commonInt6 = -1;
            switch (feed.giftWorksRank.rankType) {
                case proto_feed_webapp_em_frined_ugc_rank_type_em_friend_rank:
                    commonInt6 = 4;
                    break;
                case proto_feed_webapp_em_frined_ugc_rank_type_em_record_rank:
                    commonInt6 = 1;
                    break;
                case proto_feed_webapp_em_frined_ugc_rank_type_em_score_rank:
                    commonInt6 = 2;
                    break;
                case proto_feed_webapp_em_frined_ugc_rank_type_em_listen_rank:
                    commonInt6 = 3;
                    break;
                default:
                    break;
            }
            [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                reportModel.key = @"feed_following#friends_list_entrance#null#click#0";
                reportModel.commonInt6 = commonInt6;
            } simpleFeed:feed];
        }
        else if (feed.ugcRemark)
        {
            [self traceReportForRemarkClick:feed index:indexPath.row + 1];
            [[KSNavigationManager sharedManager] showRemarkDetail:feed.ugcRemark.jce_strTopicId];
        }
        else if (feed.courseFeed) {
            // 课程Feed点击
            [KSTeachSingCourseManager openCourseDetailPage:feed.courseFeed.jce_strCourseId];
            KSDrawItemAction *action = [KSDrawItemAction actionWithType:DRAWITEM_ACTION_TYPE_COURSEFEED_CLICK];
            [self onCourseFeedClickCoverWithAction:action simpleFeed:feed cell:nil];
        }
        else if ([feed isKindOfTmeTownFeed]) {
            // 跳转TMETown
            NSString *urlStr = [feed.tmeTown.jce_mapExt safeObjectForKey:@"room_url"];
            [[KSNavigationManager sharedManager] showWebView:SAFE_STR_BM(urlStr)];
        }
        else if (feed.followMultiSendGiftModel) {
            // 关注多人送礼
            return;
        }
        else if ([feed isMiniHeatCardFeed]) {
            [self handleFeedTouchEvent:feed];
            
            [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                reportModel.key = @"feed_creation#xtg_recommend#but#click#0";
                reportModel.commonInt1 = feed.miniHeatCardItem.jce_lNum;
                reportModel.commonInt2 = 3;
                reportModel.ugcid = feed.miniHeatCardItem.jce_strUgcid;
                reportModel.touid = feed.miniHeatCardItem.jce_userinfo.jce_user.jce_lUid;
                reportModel.mid = feed.songinfo.songMid;
            }];
        }
        else
        {
            [self traceReportPayAlbum:feed isForward:NO extraInfo:nil];
            // 如果是VIP营销feed，点击后暂停/恢复播放，不进详情页。且为了对齐点击全局播放按钮的体验，延迟0.5s
            if ([feed isKindOfVipMarketingFeed])
            {
                KSLayoutableTimelineFeedCellV2 *cell = [tableView cellForRowAtIndexPath:indexPath];
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    [cell playOrPauseRecFeed:feed];
                });
            }
            else if ([feed isRecCardFeed] && ![feed.simpleFeedCommon.strFeedId isEqualToString:self.feedManager.currentRecPrdFeed.simpleFeedCommon.strFeedId] && self.feedManager.currentRecPrdFeed.simpleFeedCommon.strFeedId.length > 0)
            {
                //目前测试 11.4回调的indexPath row没问题 但是13.3、13.7、14以上回调row会有问题 导致feed数据不对，具体复现路径 大卡片-》个人主页——》点击一个作品播放-》返回大卡片滑动到其他大卡片点击描述地方的文案进详情页
                [self handleFeedTouchEvent:self.feedManager.currentRecPrdFeed];
            }
            else
            {
                [self handleFeedTouchEvent:feed];
            }
            
        }
    }
    else if (section == CollectionSectionTypeRecUser)
    {
        // 推荐用户 Feed
        KSFilterFeedsInfo *filterFeedsInfo = [self getFeedDataListByScollView:tableView];
        
        if ([[KSTimelineManager sharedManager] checkIsFollowBizWithFilterMask:filterFeedsInfo.filterMask])
        {
            // 有折叠
            BOOL foldFlag = [self shouldShowFoldWithFilterFeedsInfo:filterFeedsInfo];
            
            if (indexPath.row == 0)
            {
                if (foldFlag)
                {
                    // 加载被折叠的关注动态Feed
                    [self loadMoreFollowFoldedFeedsListFromRemote];
                    
                    // 点击上报
                    [self traceReportClickFoldHistorycellWithCurrentFilterMask:filterFeedsInfo.filterMask];
                }
                else
                {
                    // 标题不处理
                }
            }
            else
            {
                if (foldFlag && indexPath.row == 1)
                {
                    // 标题不处理
                }
                else
                {
                    // 推荐用户feed
                    NSInteger idx = foldFlag ? indexPath.row - 2 : indexPath.row - 1; // 偏移 折叠(可能没有) 标题
                    
                    [self didClickFeedWithFilterMask:filterFeedsInfo.filterMask indexPath:[NSIndexPath indexPathForRow:idx inSection:indexPath.section]];
                }
            }
        }
    }
}

#pragma mark UITableView Cell 相关

//处理点击事件
- (void)handleFeedTouchEvent:(KSimpleFeed *)feed
{
    if ([feed isRecCardFeed])
    {
        [self.feedManager stopCurrentVideolize];
        if ([feed isSameWithPlayItem:[KSUgcPlayManager sharedManager].passPlayItem])
        {
            [KSUgcPlayManager sharedManager].passPlayItem.disablePlayHistoryReport = NO;  //手动播放的时候进播放历史
        }
        feed.isClickPlayForReport = YES;
    }
    
    if ([feed isRecCardFeed] && [feed isShortPlay]) {
        /// 跳转短剧播放页
        [self showPlayletDetailWithFeed:feed];
        
    } else {
        /// 跳转详情页
        [self showTimelineDetailWithFeed:feed];
    }
    
    [self setCurrentRecFeed:feed];
}
 
- (void)didClickFeedWithFilterMask:(NSInteger)filterMask indexPath:(NSIndexPath *)indexPath
{
    NSArray <KSimpleFeed *> *feedsArray;
    if ([KSTimelineManager sharedManager].recFeedType ==proto_feed_webapp_ENUM_REC_FEED_TYPE_ENUM_REC_FEED_TOPIC)
    {
        feedsArray = [KSTimelineManager sharedManager].recHotSpotFeed.feedsList;
    }
    else
    {
        if (filterMask == [KSTimelineManager sharedManager].filtrMaskFollow)
        {
            feedsArray = [KSTimelineManager sharedManager].recUserFeed_ForFollow.feedsList;
        }
        else if (filterMask == [KSTimelineManager sharedManager].filtrMaskQQWXFriends)
        {
            feedsArray = [KSTimelineManager sharedManager].recUserFeed_ForQQWXFriends.feedsList;
        }
        else if (filterMask == [KSTimelineManager sharedManager].filtrMaskUgcFeed)
        {
            feedsArray = [KSTimelineManager sharedManager].recUserFeed_ForUgcFeed.feedsList;
        }
        else if (filterMask == [KSTimelineManager sharedManager].filtrMaskUgcFeedQQWX)
        {
            feedsArray = [KSTimelineManager sharedManager].recUserFeed_ForUgcFeedQQWX.feedsList;
        }
        else
        {
            return;
        }
    }

    KSimpleFeed *feed = [KSComHelper getObjectInArray:feedsArray byIndex:indexPath.row ofClassType:[KSimpleFeed class] defaultValue:nil];
    
    // 动态点击上报
    [self timelineClickedTraceReport:feed extraInfo:@{@"indexRowKey":indexPath}];
    
    if (feed.songinfo || feed.simpleFeedCommon.uTypeid == FEED_TYPE_UGC)
    {
        [self onUgcFeedClickWithSimpleFeed:feed indexPath:indexPath];
    }
    
    if (feed.competitionFeed)
    {
        [[KSNavigationManager sharedManager] dealWithScheme:feed.competitionFeed.strJumpUrl];
        [self onCompetitionFeedClickCoverWithSimpleFeed:feed indexPath:indexPath];
    }
    else if(feed.feedAd)
    {
        //广告feed。确保每个分支要上报点击数据。如果跳转的是jumprl，直接上报了。否则目标链接不是jumpurl.这时候需要主动发送个Http请求告诉广点通点击上报.注意这里上报只能上报一次，避免重复上报
        [self.feedManager didSelectedOnAdvertiseFeed:feed clickPose:KSFeedAdvertiseClickPose_AdOtherEmptyZone];
    }
    else if (feed.soloAlbumInfo)
    {
        [self onSonglistFeedClickCoverWithSimpleFeed:feed indexPath:indexPath];
        [[KSNavigationManager sharedManager] showSongListDetailVC:feed.soloAlbumInfo.strAlbumId fromType:KSSongListDetailFromType_Feed];
    }
    else if (feed.payAlbumInfo)
    {
        [[KSNavigationManager sharedManager] showPayAlbumView:feed.payAlbumInfo.strAlbumId andUgcId:feed.simpleFeedCommon.strFeedId];
        
        
        KSUserPayRight *payRight = [[KSPaySoloAlbumManager sharedManager] getPayRightContentID:feed.payAlbumInfo.strAlbumId andPayType:KSUserPayTypeOptionAlbum];
        switch ([[KSPaySoloAlbumManager sharedManager] getPayIconMask:payRight.mapRight]) {
            case KSPayIconPriorityVIP: {
                
                NSUInteger pid = 0;
                KSTimelineTabSelectIndex curTab = [self getCurTab];
                if (curTab == KSTimelineTab_Follow)
                {
                    pid = VipPid_PayAlbumFeed_p3;
                }
                else if (curTab == KSTimelineTab_QQWXFriends)
                {
                    pid = VipPid_PayAlbumFeed_p6;
                }
                else if (curTab == KSTimelineTab_Rec)
                {
                    pid = VipPid_PayAlbumFeed_p9;
                }
                else if (curTab == KSTimelineTab_Live)
                {
                    pid = VipPid_PayAlbumFeed_p12;
                }
                
                [[KSVipTraceReportManager sharedManager] vipPayAlbumClickReportWithPosId:pid contentId:feed.payAlbumInfo.strAlbumId];
                
                
                break;
            }
            case KSPayIconPriorityPay: {
                BOOL isForword = NO;
                if (feed.forwardFeed && feed.forwardFeed.strForwardId) {
                    isForword = YES;
                }
                NSUInteger pid = 0;
                KSTimelineTabSelectIndex curTab = [self getCurTab];
                if (curTab == KSTimelineTab_Follow)
                {
                    pid = PayAlbum_Feed_Follow_P3;
                }
                else if (curTab == KSTimelineTab_QQWXFriends)
                {
                    pid = PayAlbum_Feed_Friend_P3;
                }
                else if (curTab == KSTimelineTab_Rec)
                {
                    pid = PayAlbum_Feed_Hot_P3;
                }
                else if (curTab == KSTimelineTab_Live)
                {
                    pid = PayAlbum_Feed_Near_P3;
                }
                
                [[KSTraceReportManager sharedManager] payAlbumClickPosid:pid
                                                                   ugcId:@""
                                                                 albumId:feed.payAlbumInfo.strAlbumId
                                                               isForward:isForword
                                                                   toUid:feed.simpleUser.userinfo.userId
                                                                 shareId:0];
                break;
            }
            default:
                break;
        }
    }
    else if(feed.liveShow && feed.liveShow.strLiveRoomId.length>0)
    {
        return;
    }
    else if(feed.ktvRoomShow && feed.ktvRoomShow.jce_strRoomId.length>0)
    {
        return;
    }
    else if(feed.ktvRoomMike && feed.ktvRoomMike.jce_strRoomId.length>0)
    {
        
        if (feed.ktvRoomMike && feed.ktvRoomMike.jce_strRoomId && feed.ktvRoomMike.jce_strRoomId.length>0)
        {
            
            if ((feed.ktvRoomMike.jce_iRoomType & KSKTVRoomType_Multi) == KSKTVRoomType_Multi) {//进入多人歌房
                
            }else{
                [[KSNavigationManager sharedManager] showKTVRoomVCWithOwnerUid:0
                                                                        roomid:feed.ktvRoomMike.jce_strRoomId
                                                                        passwd:@""
                                                                      forcePop:YES
                                                                       ktvFrom:NSIToString([self watchKTVReport])];
            }
        }
    }
    else if (feed.secretaryFeed) //小秘书
    {
        return;
    }
    else if (feed.shortVideoFeed) // 推荐短视频
    {
        return;
    }
    else if (feed.relayGameFeed)
    {
        return;
    }
    else if (feed.musicMoodFeed)
    {
        [self showTimelineDetailWithFeed:feed];
        return;
    }
    else if(feed.recSongsCellData)//猜你喜欢
    {
        [[KSNavigationManager sharedManager] dealWithScheme:@"qmkege://kege.com?action=guess_you_like_list"];
        return;
    }
    else if (feed.familyUgc ||feed.rectopicFeed || feed.topicListFeed) // 家族feed中 单条ugc以外的空白处点击无响应
    {
        return;
    }
    else if(feed.beatListFeed)//好友擂台
    {
        KSChampionRankListViewController *vc = [[KSChampionRankListViewController alloc] init];
        [[[KSNavigationManager sharedManager] getMainPageNavController] pushViewController:vc animated:YES];
        return;
    }
    else if(feed.recFriendsFeed)//关系链好友推荐
    {
        //跳转到我感兴趣的人页面
        KSUserInfo *userInfo = [KSLoginManager sharedInstance].curUserInfo;
        KSInterestedPeopleVC *interestedPeopleVC = [[KSInterestedPeopleVC alloc] initWithUserinfo:userInfo];
        [self.navigationController pushViewController:interestedPeopleVC animated:YES];
    }
    else if (feed.isRemoved) //已删除feed不跳转
    {
        return;
    }
    else if (feed.forwardFeed.strForwardId.length > 0)
    {
        [self traceReportPayAlbum:feed isForward:YES extraInfo:nil];
        
        [self showTimelineDetailWithFeed:feed];
    }
    else if (feed.giftWorksRank)
    {
        if ([self getCurTab] == KSTimelineTab_Follow)
        {
            [[KSNavigationManager sharedManager] showGiftWorksRankVCAtTab:KSWeekOfRankTabOptionAttention regionCode:nil];
        }
        else if ([self getCurTab] == KSTimelineTab_QQWXFriends)
        {
            [[KSNavigationManager sharedManager] showGiftWorksRankVCAtTab:KSWeekOfRankTabOptionFriend regionCode:nil];
        }
        else if ([self getCurTab] == KSTimelineTab_Live)
        {
            [[KSNavigationManager sharedManager] showGiftWorksRankVCAtTab:KSWeekOfRankTabOptionRegion regionCode:[[KSRegionCodeModel alloc] initWithRegionCode:feed.giftWorksRank.strDistrictCode]];
        }
        
        [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
            reportModel.key =[self getCurrentFeedClickKeyWithFeedType:feed_report_feedtype_receive_gifts_list itemType:feed_report_itemtype_null];
            reportModel.commonInt3 = indexPath.row + 1;
        } simpleFeed:feed];
    }
    else if (feed.ugcRemark)
    {
        [self traceReportForRemarkClick:feed index:indexPath.row + 1];
        [[KSNavigationManager sharedManager] showRemarkDetail:feed.ugcRemark.jce_strTopicId];
    }
    else
    {
        [self traceReportPayAlbum:feed isForward:NO extraInfo:nil];
        [self handleFeedTouchEvent:feed];
    }
}

/// feed 是否展示展示折叠（仅限关注Tab下使用）
- (BOOL)shouldShowFoldWithFilterFeedsInfo:(KSFilterFeedsInfo *)filterFeedsInfo
{
    // 展示折叠的规则是 浅关系链用户 && 当前feed有更多数据 && 下发了折叠标识 && 当前feed数据 > 0
    return ([KSTimelineManager sharedManager].currentUserFlag & JceTimeline_GetFeedsFlag_GetFeedsFlagFriendless) == JceTimeline_GetFeedsFlag_GetFeedsFlagFriendless && filterFeedsInfo.hasMore && filterFeedsInfo.hasFold && filterFeedsInfo.feedsList.count > 0;
}

#pragma mark 如果从推荐进详情页的时候，记录点击的feed,听过超过10s会拉取类似feed
- (void)setCurrentRecFeed:(KSimpleFeed*)simpleFeed
{
    if (([KSTimelineManager sharedManager].currentFiltrMask & JceTimeline_enum_filter_mask_ENUM_FILTER_MASK_FEED_RECOMM) ==JceTimeline_enum_filter_mask_ENUM_FILTER_MASK_FEED_RECOMM && simpleFeed.songinfo.songMid.length>0)
    {
        self.feedManager.curSimpleFeed = simpleFeed;
    }
}

- (BOOL)isFeedNativaFirstShow
{
    if (self.feedRewardAdFloatView.superview && self.feedRewardAdFloatView.hidden == NO && [[KSABTestManager sharedManager] feedRewardStyleConfig] == 2 && ![self.feedRewardAdFloatView getFeedRewardGoldAd]) {
        if (self.feedManager.curSimpleFeed.tmeFeedAd && [self.feedManager.curSimpleFeed.tmeFeedAd.placementId isEqualToString:kTMEAdSDKFeedGoldAd]) {
            if (![self getFeedNativeAdFirstRefresh]) {
                KLog(@"FeedNativeAd 获取当前是首刷，更新首刷标记，并弹toast提示用户");
                [self setFeedNativeAdFirstRefresh:YES];
                [KSToast showToast:@"离开当前页面会导致奖励发放失败"];
                return YES;
            } else {
                return NO;
            }
        }else {
            return NO;
        }
    }else {
        return NO;
    }
}
#pragma mark - Scroll View Delegate
- (void)scrollViewWillBeginDragging:(UIScrollView *)scrollView
{      
    [super scrollViewWillBeginDragging:scrollView];
    if ([KSUgcPlayManager sharedManager].isRecFeedWillEndDraging) {
        /// 上一个未滑动停止，就继续滑动，暂停上一个
        [KSUgcPlayManager sharedManager].isRecFeedWillEndDraging = NO;
        [self processStopMediaPlayIgnoreExpose:YES when:KSTimelineStopVideoWhenDefault];
    }
    
    //停止gif动画播放
    [self pauseVisibleCellAnimatingGif];
    [KSLiveStreamPreloadManager shareInstance].isReload = NO;//直播Feed区分刷新和滑动加直播feed蒙层
    self.isReloadTable = NO;
    
    [[NSNotificationCenter defaultCenter] postNotificationName:KSNotification_RemoveFeedNegativeMenu object:KSNotification_FeedNegativeMenuDisappearAnimateDuration];
    
    // 只有大卡片才会整页翻转
    if (scrollView.pagingEnabled == NO && [self isRecCardTable:scrollView]) {
        scrollView.pagingEnabled = YES;
    }
    
    if ((scrollView == self.mFollowTableView && self.contentTableView == self.mFollowTableView && [self isFeedAutoPlay])
        || (scrollView == self.mNearbyFeedTableView && self.contentTableView == self.mNearbyFeedTableView && [self isFeedAutoPlay])) {
        [[KSFeedPreviewManager sharedManager] triggerFeedPreviewByScroll];
    }
    if (self.centerSegControl.selectIndex == KSTimelineTab_Follow && scrollView == self.mFollowTableView)
    {
        //当前tab是关注时才出发奥运挂角滚动逻辑
        [[NSNotificationCenter defaultCenter] postNotificationName:KSNotification_ViewController_scroll object:nil userInfo:nil];
    }
}

#pragma mark 点击状态栏到顶部的时候系统不会调用EndDecelerating,这里主动调用触发播放
- (void)scrollViewDidScrollToTop:(UIScrollView *)scrollView
{
    if ([self isRecCardTable:scrollView]) {
        [self processStopMediaPlayIgnoreExpose:YES when:KSTimelineStopVideoWhenCellEndDisplay];
        
        [self scrollViewDidEndDecelerating:scrollView];
    }
}

/// 在这个时机去更新实际的index
- (void)scrollViewDidEndDecelerating:(UIScrollView *)scrollView
{
    //恢复gif动画播放
    [self resumVisilbeCellAnimatingGif];
    
    if (scrollView == self.mContainerScrollView)
    {
        int index = self.mContainerScrollView.contentOffset.x / self.view.width;
        if (index != self.centerSegControl.selectIndex) {
            [self.centerSegControl setSelectIndex:index];
        }
    }
    else if (scrollView == self.nearbyScrollContainerView) // 附近动态和附近的人滚动视图
    {
        int index = self.nearbyScrollContainerView.contentOffset.x / self.view.width;
        if (index != self.nearbyTabBarView.selectedIndex)
        {
            self.nearbySubIndex = index;
            if (self.nearbySubIndex == NearbyTabBarSubIndex_User)
            {
                [self processStopMediaPlayIgnoreExpose:YES];
            }
            if (index == NearbyTabBarSubIndex_Dynamic && !self.isHasFirstSwitchToNearDynamic) {
                
                self.isHasFirstSwitchToNearDynamic = YES;
                [self.mNearbyFeedTableView reloadData];
            }
            [self.nearbyTabBarView scrollToIndex:index animated:YES];
        }
        [self updateEmptyView];
    }
    else if (scrollView == self.mHotCollectionView)
    {
        [super scrollViewDidEndDecelerating:scrollView];
        
        // 处理 header 和 footer
        KSUICollectionView *collectionScrollView = SAFE_CAST(scrollView, [KSUICollectionView class]);
        
        KSongRefreshScrollViewHeaderView *headerView = collectionScrollView.headerView;
        if (headerView) {
            [headerView refreshScrollViewDidEndDragging:scrollView];
        }
        
        KSongLoadMoreScrollViewFooterView *footerView = collectionScrollView.footerView;
        if (footerView && [self enableFooterView]) {
            [footerView loadMoreScrollViewDidEndDragging:scrollView];
        }
        
    } else {
        [super scrollViewDidEndDecelerating:scrollView];
        KSongLoadMoreScrollViewFooterView *footerView = [scrollView getFooterView];
        if (footerView && [self enableFooterView]) {
            [footerView loadMoreScrollViewDidEndDragging:scrollView];
        }
    }

    if ([self isRecCardTable:scrollView]) {
        NSInteger rowIndex = (NSInteger)(scrollView.contentOffset.y / scrollView.height + 0.5);
        BOOL isScrollUp = self.lastRecFeedIndex < rowIndex;
        for (KSLayoutableTimelineFeedCellV2 *cell in self.mHotTableView.visibleCells) {
            if ([cell isKindOfClass:[KSLayoutableTimelineFeedCellV2 class]]) {
                cell.direction = isScrollUp ? KSRecFeedCardScrollDirectionUp : KSRecFeedCardScrollDirectionDown;
            }
        }
        
        if (!isScrollUp) {
            [[KSKTVRoomCardPreRunManager sharedManager] downSlideOutCleanStauts];
        }
        
        if (rowIndex != self.lastRecFeedIndex) {
            self.lastRecFeedIndex = rowIndex;
            // 总浏览卡片次数+1
            [[KSRecFeedBottomGuideFrequencyControlMgr sharedManager] didShowFeedCard];
        }
        self.curRecFeedRowIndex = rowIndex;
        self.isRecPageChanged = YES;
        if (self.curRecFeedRowIndex >= 0 && self.curRecFeedRowIndex < self.currentFilterFeedsInfo.feedsList.count)
        {
            KSimpleFeed *curloadFeed = [self.currentFilterFeedsInfo.feedsList safeObjectAtIndex:self.curRecFeedRowIndex];
            self.feedManager.curSimpleFeed = curloadFeed;
            self.feedManager.currentIndexPath = [NSIndexPath indexPathForRow:self.curRecFeedRowIndex inSection:CollectionSectionTypeCell];
            
            //处理信息流三期金币 发送hippy消息
            [self handleFeedAdGold];
            
            //处理当前引导挂件浮窗UI
            if (self.feedRewardAdFloatView.superview && self.feedRewardAdFloatView.hidden == NO && [[KSABTestManager sharedManager] feedRewardStyleConfig] == 2 && ![self.feedRewardAdFloatView getFeedRewardGoldAd]) {
                [self startTimer]; //开始计时控制挂件展示收起状态
                
                if (curloadFeed.tmeFeedAd && [curloadFeed.tmeFeedAd.placementId isEqualToString:kTMEAdSDKFeedGoldAd]) {
                    if (self.feedRewardAdFloatView.isFloatViewPackUped) {
                        [self.feedRewardAdFloatView recoverFloatViewExpandStyle];
                    }
                    //判断当前信息流自渲染广告是否已经完成过激励，若未完成则开启倒计时奖励
                    BOOL rewarded = [curloadFeed.tmeFeedAd.ExtraInfoDic[@"rewarded"] boolValue];
                    if (!rewarded) {
                        KLog(@"FeedNativeAd startCountDownTimer 1");
                        [self.feedRewardAdFloatView startCountDownTimer:(curloadFeed.tmeFeedAd.rewardTime / 1000)];
                        [self.feedRewardAdFloatView updateGoldWithDataObject:curloadFeed.tmeFeedAd];
                    }else {
                        if(!self.feedRewardAdFloatView.timer) {                        
                            [self.feedRewardAdFloatView updateSubViewsWithData];
                        }
                    }
                }else {
                    KLog(@"FeedNativeAd 当前滑到非信息流自渲染广告 ，停止倒计时");
                    [self.feedRewardAdFloatView stopCountDownTimer];
                    KLog(@"FeedNativeAd stopCountDownTimer ");
                    if (self.feedRewardAdFloatView.isFloatViewPackUped) {
                        //内容卡片下判断是否为收起态 若为收起态 更新收起态UI
                        [self.feedRewardAdFloatView packUpFloatViewWithNewStyle];
                    }else {                    
                        [self.feedRewardAdFloatView updateSubViewsWithData];
                    }
                }
            }
        }
    }

    /// 起播
    [self processStartMediaPlayIgnoreExpose:NO otherParams:@{@"autoPlayCaller":@"DidEndDecelerating"}];
    
    if ([self isRecCardTable:scrollView]) {
        if (!self.isRefreshing && [self isNeedPreloadFeedListWithIndex:self.curRecFeedRowIndex]) {
            /// 滑动停止，预请求下一页推荐列表
            [self loadFeedsMoreListFromRemote];
        }
        
        /// 预加载
        [self.feedManager starPreloadNextFeedFromCurrentIndex:self.curRecFeedRowIndex];
        [self.feedManager resetPreloadStateIfNeed];

        [KSUgcPlayManager sharedManager].isRecFeedWillEndDraging = NO;
        [KSUgcPlayManager sharedManager].endDeceleratingTime = CACurrentMediaTime();
        /// 秒播上报
        [[KSUgcPlayManager sharedManager] recCardFeedPlayReportTotalCost];
        /// 流媒体上报
        [[KSUgcPlayManager sharedManager] traceReportLastTMEPlayerMedia];
    }
}

- (void)scrollViewDidEndScrollingAnimation:(UIScrollView *)scrollView
{

}

- (void)scrollViewDidScroll:(UIScrollView *)scrollView
{
    [self hideGiftGuideView];
    [self recFeedHideAutoPlayBottomTip:scrollView];

    if (scrollView == self.mContainerScrollView) {
        if (self.centerSegControl.selectIndex == KSTimelineTab_Follow) {
            [KSToast clean];
        }
        
        [self.centerSegControl updateByContentOffset:scrollView.contentOffset.x width:self.view.width];
    } else {
        [super scrollViewDidScroll:scrollView];
        
        KSongRefreshScrollViewHeaderView *headerView = [scrollView getHeaderView];
        KSongLoadMoreScrollViewFooterView *footerView = [scrollView getFooterView];

        if (headerView) {
            // 如果 loading 中上滑则取消 loading 动画，避免推荐频道位置异常
            if (headerView.state == KSongPullRefreshLoading && scrollView.contentOffset.y > REFRESH_TRIGGER_HEIGHT)
            {
//                [headerView endLoading:scrollView needAnimation:NO];
            } else {
                [headerView refreshScrollViewDidScroll:scrollView];
            }
        }
        
        if (footerView && [self enableFooterView]) {
            [footerView loadMoreScrollViewDidScroll:scrollView];
        }
        // 是否需要更新广告视图
        [self checkNeedUpdateAdViewDidScroll:scrollView];
    }
    
    // 滑动时高光时刻不停止播放，仅处理tableview
    if ([self.scrollView isKindOfClass:[UITableView class]]) {
        if ([self isRecCardTable:scrollView]) {
            BOOL enableDragingPlay = [KSBigCardSwitchUtil enableEndDragingPlay];
            if (!enableDragingPlay) {
                /// 松手播放过程中，不需要调用Stop
                [self processStopMediaPlayIgnoreExpose:NO when:KSTimelineStopVideoWhenScroll];
            }
        } else {
            [self processStopMediaPlayIgnoreExpose:NO when:KSTimelineStopVideoWhenScroll];
        }
    }
    
    if (self.centerSegControl.selectIndex == KSTimelineTab_Follow && scrollView == self.mFollowTableView) {
        [self.rootTabBarController processFeedOffset:scrollView.contentOffset.y];
        
        if (self.lastFollowContentOffset > scrollView.contentOffset.y) {
            self.followFilterTopScrollConstraint.constant = COM_NAV_HEIGHT + STATUS_BAR_HEIGHT;
        } else if (self.lastFollowContentOffset < scrollView.contentOffset.y) {
            self.followFilterTopScrollConstraint.constant = COM_NAV_HEIGHT + STATUS_BAR_HEIGHT - 39;
        }
        
        // 更新 lastContentOffset 为当前滚动位置
        self.lastFollowContentOffset = scrollView.contentOffset.y;
        
    } else if ((self.centerSegControl.selectIndex == KSTimelineTab_Live && scrollView == self.contentCollectionView) ||
               (self.centerSegControl.selectIndex == KSTimelineTab_RecNearBy && scrollView == self.contentCollectionView)) {
        [self.rootTabBarController processFeedOffset:scrollView.contentOffset.y];
    }
    
    if (self.contentTableView == self.mHotTableView)
    {
        //调整frame
        if (self.curRecFeedRowIndex == 0 && scrollView.contentOffset.y > 0) {
            if (!scrollView.pagingEnabled)
            {
                scrollView.pagingEnabled = YES;
            }
        }
    }

    //如果要更改大卡片高度后，请同步修改这里的删除逻辑，不然可能会导致播放 & 展示错误
    //本类中搜索关键词 延迟检查填充广告 by zhongqishi
    if ([self isRecCardTable:scrollView])
    {
        //这里延迟检查是否需要删除广告，curRecFeedRowCheck是初始化值是 -1
        //延迟检查填充广告
        if (self.curRecFeedRowCheckIndex != self.curRecFeedRowIndex){
            NSInteger currentOffsetY = scrollView.contentOffset.y * 10;
            NSInteger cardHieght = scrollView.height * 10;
            NSInteger mod = currentOffsetY % cardHieght;
            if (mod <= 100){
                self.curRecFeedRowCheckIndex = self.curRecFeedRowIndex;
                [self checkFillAdTableView:scrollView
                                  forIndex:self.curRecFeedRowCheckIndex];
            }
            else if (self.curRecFeedRowIndex - self.curRecFeedRowCheckIndex > 1){
                //处理滑动过快的情况
                self.curRecFeedRowCheckIndex = self.curRecFeedRowCheckIndex + 1;
                [self checkFillAdTableView:scrollView
                                  forIndex:self.curRecFeedRowCheckIndex];
            }
            //大卡片广告预加载
            [self checkPreLoadAdTableView:scrollView forIndex:self.curRecFeedRowIndex];
        }
    }
}

- (BOOL)scrollViewShouldScrollToTop:(UIScrollView *)scrollView
{
    if (self.centerSegControl.selectIndex == KSTimelineTab_Rec){
        [[KSUgcPlayManager sharedManager] resetRecFeedRecentListenerDict];
        self.curRecFeedRowIndex = 0;
    }
    return YES;
}

- (void)scrollViewDidEndDragging:(UIScrollView *)scrollView willDecelerate:(BOOL)decelerate
{
    
    if ([self isRecCardTable:scrollView]) {
        if (scrollView.contentOffset.y < -60)
        {
            if (scrollView.pagingEnabled)
            {
                scrollView.pagingEnabled = NO;
            }
        }
        else
        {
            if (!scrollView.pagingEnabled)
            {
                scrollView.pagingEnabled = YES;
            }
        }
    }
    
    if (scrollView != self.mContainerScrollView)
    {
        [super scrollViewDidEndDragging:scrollView willDecelerate:decelerate];
        
        KSongRefreshScrollViewHeaderView *headerView = [scrollView getHeaderView];
        KSongLoadMoreScrollViewFooterView *footerView = [scrollView getFooterView];

        if (headerView)
        {
            [headerView refreshScrollViewDidEndDragging:scrollView];
        }
        if (footerView && [self enableFooterView])
        {
            [footerView loadMoreScrollViewDidEndDragging:scrollView];
        }
    }
    
    if (!decelerate)
    {
        if ([self isRecCardTable:scrollView])
        {
            NSInteger rowIndex = (NSInteger)(scrollView.contentOffset.y / scrollView.height + 0.5);
            if (rowIndex != self.lastRecFeedIndex) {
                self.lastRecFeedIndex = rowIndex;
                // 总浏览卡片次数+1
                [[KSRecFeedBottomGuideFrequencyControlMgr sharedManager] didShowFeedCard];
                
            }
            self.curRecFeedRowIndex = rowIndex;
            self.isRecPageChanged = YES;
            KSimpleFeed* curloadFeed = nil;
            if (self.curRecFeedRowIndex>=0 && self.curRecFeedRowIndex< self.currentFilterFeedsInfo.feedsList.count)
            {
                curloadFeed = [self.currentFilterFeedsInfo.feedsList objectAtIndex:self.curRecFeedRowIndex];                
                self.feedManager.curSimpleFeed = curloadFeed;
                self.feedManager.currentIndexPath = [NSIndexPath indexPathForRow:self.curRecFeedRowIndex inSection:CollectionSectionTypeCell];
            }
        }
        KDEBUG(@"[自动播]--DidEndDrag NO force play");
        //IgnoreExpose 不能轻易改成yes,会影响推荐table样式的播放逻辑
        [self processStartMediaPlayIgnoreExpose:NO otherParams:@{@"autoPlayCaller":@"DidEndDragging"}];
    }
}

- (void)scrollViewWillEndDragging:(UIScrollView *)scrollView
                     withVelocity:(CGPoint)velocity
              targetContentOffset:(inout CGPoint *)targetContentOffset {

    // // 大卡片诊断逻辑
    BOOL isScrollRec = scrollView == self.mHotTableView && [self isRecCardTable:self.mHotTableView];
    if (!isScrollRec) {
        return;
    }

    NSInteger stopIndex = targetContentOffset->y / scrollView.height;
    if (self.curRecFeedRowIndex != stopIndex) {
        self.curRecFeedRowIndex = stopIndex;
        self.isRecPageChanged = YES;
    }
    else {
        self.isRecPageChanged = NO;
    }
    
    if (self.isRecPageChanged) {
        [KSUgcPlayManager sharedManager].startPlayingTime = 0;
        [KSUgcPlayManager sharedManager].endDeceleratingTime = 0;
        [KSUgcPlayManager sharedManager].isRecFeedWillEndDraging = YES;
        [KSUgcPlayManager sharedManager].willEndDragingTime = CACurrentMediaTime();
    }
    
    KSimpleFeed *lastSimpleFeed = self.feedManager.curSimpleFeed;
    KSimpleFeed *currentSimpleFeed = nil;
    self.feedManager.currentIndexPath = [NSIndexPath indexPathForRow:self.curRecFeedRowIndex inSection:CollectionSectionTypeCell];
    if (self.curRecFeedRowIndex >= 0 && self.curRecFeedRowIndex < self.currentFilterFeedsInfo.feedsList.count) {
        currentSimpleFeed = [self.currentFilterFeedsInfo.feedsList objectAtIndex:self.curRecFeedRowIndex];
        self.feedManager.curSimpleFeed = currentSimpleFeed;
        
        if (self.isRecPageChanged) {
            /// 松手起播
            BOOL needNextDragingPlay = NO;
            if ([KSBigCardSwitchUtil enableEndDragingPlay]) {
                needNextDragingPlay = [lastSimpleFeed isKindOfUgcTypeFeed] && [currentSimpleFeed isRecCardVideoFeed];
                if (needNextDragingPlay) {
                    /// 松手起播前，先暂停上一个播放
                    [self processStopMediaPlayIgnoreExpose:YES when:KSTimelineStopVideoWhenDefault];
                }
            }

            [KSABTestManager sharedManager].isRecFeedAutoPlayFirstUGCNotPlaying = NO;
            KINFO(@"[自动切换开始] 下一张卡片UGCID：%@",currentSimpleFeed.simpleFeedCommon.strFeedId);
            
            NSDictionary *infos = @{@"ugcid":[NSString stringWithFormat:@"%@",currentSimpleFeed.simpleFeedCommon.strFeedId],
                                    @"needPlay" : @(needNextDragingPlay)};
            [[NSNotificationCenter defaultCenter] postNotificationName:KSNotification_RecFeedUserChangeCard object:infos];
            
            //直播feed替换逻辑
            [[KSLiveStreamPreloadManager shareInstance] startPreloadLivePlayersWhenScrollTo:self.curRecFeedRowIndex];
            KLog(@"[scrollViewWillEndDragging] End");
        }
    }
}

- (void)handleFeedAdGold
{
    //处理信息流三期金币 发送hippy消息
    BOOL isFeedAdGold = NO;
    if ([[KSABTestManager sharedManager] feedAdGoldWidgetStyle] > 0 && [[KSABTestManager sharedManager] feedAdGoldHippyUrl].length > 0) {
        isFeedAdGold = YES;
    }
    if (isFeedAdGold) {
        
        KSFilterFeedsInfo *recFeedsInfo = [KSTimelineManager sharedManager].feedsInfo_Recommend;
        //提示滑动太快toast
        
        if (self.curRecFeedRowIndex > 0 && [[KSABTestManager sharedManager] feedAdGoldWidgetStyle] == 1 && [[KSBoxManager sharedManager] getFeedGoldHippyViewIsShow]) {
            KSimpleFeed *lastFeed = [recFeedsInfo.feedsList safeObjectAtIndex:self.curRecFeedRowIndex-1];
            if (lastFeed.tmeFeedAd && ![lastFeed.tmeFeedAd.ExtraInfoDic[@"willExpose"] boolValue] && !self.needHiddenFeedAdtoast) {
                //上一个卡片是广告 且未曝光
                [KSToast showToast:[[KSABTestManager sharedManager] feedAdGoldToast]];
                lastFeed.tmeFeedAd.ExtraInfoDic[@"hasJump"] = @(YES); //被跳过的广告不会在使用到领奖
            }
        }
        [self sendFeedAdEventWithInfo:recFeedsInfo hasAdBlock:^(BOOL hasAd) {
            
        }];
    }
}


- (void)sendFeedAdEventWithInfo:(KSFilterFeedsInfo *)recFeedsInfo hasAdBlock:(void (^)( BOOL hasAd))block
{
    //处理信息流三期金币挂角hippy页发消息
    __block BOOL hasFeedAd = NO;
    __block NSInteger adIndex = 0;
    __block NSInteger rewardGold = 0;
    __block NSString *adToken = @"";
    KS_WEAK_SELF(self);
    [recFeedsInfo.feedsList enumerateObjectsUsingBlock:^(KSimpleFeed *feed, NSUInteger idx, BOOL * _Nonnull stop) {
        CHECK_SELF_AND_RETURN()
        if (feed.tmeFeedAd && feed.feedAd) {
            //判断是为曝光的广告 返回index
            BOOL isExpose = [feed.tmeFeedAd.ExtraInfoDic[@"willExpose"] boolValue];
            BOOL hasJump = [feed.tmeFeedAd.ExtraInfoDic[@"hasJump"] boolValue];
            if (!isExpose && (self.curRecFeedRowIndex <= idx) && !hasJump) {
                adIndex = idx;
                rewardGold = feed.tmeFeedAd.rewardGold;
                hasFeedAd = YES;
                adToken = feed.tmeFeedAd.adToken;
                *stop = YES;
            }
        }
    }];
    KLog(@"FeedAdGold hasFeedAd = %d,self.curRecFeedRowIndex = %ld,ad idx = %ld",hasFeedAd,self.curRecFeedRowIndex,adIndex);
    if (block) {
        block(hasFeedAd);
    }
    if (hasFeedAd) {
        [[KSBoxManager sharedManager] showTopHippyView:YES];
        
    }else {
        [[KSBoxManager sharedManager] showTopHippyView:NO];
    }
    [[KSHippyManager sharedManager] sendEvent:@"hippy.pop.pageSelected" params:@{@"action": @"onPageSelected",
                                                                                 @"currentIndex":@(self.curRecFeedRowIndex),
                                                                                 @"adIndex":@(adIndex),
                                                                                 @"coinCount":@(rewardGold),
                                                                                 @"adToken":adToken
                                                                               } toHippyProject:@"vMissionFloat"];
}

#pragma mark 判断每个tab是否首次刷新
- (void)updateEachTabIsFirstLoad
{
    if (self.hotLoadFirstTime && [KSTimelineManager sharedManager].currentFiltrMask == [KSTimelineManager sharedManager].filtrMaskHot)
    {
        self.hotLoadFirstTime = NO;
    }
//    if (self.nearbyLoadFirstTime && [KSTimelineManager sharedManager].currentFiltrMask  == [KSTimelineManager sharedManager].filtrMaskNearBy)
//    {
//        self.nearbyLoadFirstTime = NO;
//    }
    if (self.QQWXLoadFirstTime && [KSTimelineManager sharedManager].currentFiltrMask  == [KSTimelineManager sharedManager].filtrMaskQQWXFriends)
    {
        self.QQWXLoadFirstTime = NO;
    }
    if (self.friendsLoadFirstTime && [KSTimelineManager sharedManager].currentFiltrMask  == [KSTimelineManager sharedManager].filtrMaskFollow)
    {
        self.friendsLoadFirstTime = NO;
    }
    if (self.recommendLoadFirstTime &&  [KSTimelineManager sharedManager].currentFiltrMask  == [KSTimelineManager sharedManager].filtrMaskRecommend)
    {
        self.recommendLoadFirstTime = NO;
    }
    if (self.recommendNearByLoadFirstTime &&  [KSTimelineManager sharedManager].currentFiltrMask  == [KSTimelineManager sharedManager].filtrMaskRecommendNearBy)
    {
        self.recommendNearByLoadFirstTime = NO;
    }
}


#pragma mark - Load Feed List

/// 动态 Feed的 refresh 方法
- (void)loadFeedsListFromRemote:(NSDictionary*)extraInfo
{
    if (![[KSNetStatusManager sharedManager] IsEnableInternet])
    {
        [self tempAlert:@"网络不可用，请检查网络设置"];
        [self performSelector:@selector(refreshDidFinished) withObject:nil afterDelay:0];
        [self cancelLoadMore];
        self.shouldReloadWhenNetworkChange = YES;
        self.reloadWhenNetworkChangeExtraInfo = extraInfo;
        return;
    }
    self.reloadWhenNetworkChangeExtraInfo = nil;
    NSUInteger currentFilterMask = [KSTimelineManager sharedManager].currentFiltrMask;
    if (self.shouldNotRefresh)
    {
        self.shouldNotRefresh = NO;
        
        self.recSubDict = [NSMutableDictionary dictionary];
        NSString *recUgcId = [self.passUserInfo safeObjectForKey:PushNotification_Ugcid];
        if (recUgcId.length > 0) {
            [self.recSubDict setObject:@[recUgcId] forKey:KSRecommendUgcIds];
        }
        self.curRecFeedRowIndex = 0;
        [self performSelector:@selector(refreshDidFinished) withObject:nil afterDelay:0];
        [self cancelLoadMore];
        KLog(@"[feed][EmptyFeed]Cnt请求去重rockyshouldnotrefresh-%lu",(unsigned long)currentFilterMask);
        return;
    }
    
    //数据更新刷新页面时停止大卡片视频预览的自动播放
    if ([self isRecCardFeedTab])
    {
        [self processStopMediaPlayIgnoreExpose:YES when:KSTimelineStopVideoWhenCellEndDisplay];
        if (self.recSubDict)
        {
            extraInfo = [self.recSubDict copy];
            self.recSubDict = nil;
        }
        //7.25流量分发强制跳推荐后需要忽略预加载，带着traceid刷一次
        if (![[KSTimelineManager sharedManager] isNeedForceRelaod:extraInfo] &&
            [KSTimelineManager sharedManager].isPrefetchingRecFeedData)
        {
            if (!self.isLoading && [self isRecCardTable:self.scrollView])
            {
                [self.refreshHeaderView triggerLoading:self.scrollView];
            }
            KLog(@"[feed][EmptyFeed]Cnt请求去重预加载-%lu",(unsigned long)currentFilterMask);
            return;
        }
    }
        
    //bug=77020599
    if ((currentFilterMask & [KSTimelineManager sharedManager].filtrMaskFollow) == [KSTimelineManager sharedManager].filtrMaskFollow && self.followTabCurFiltrMask == [KSTimelineManager sharedManager].filtrMaskQQWXFriends)
    {
        //当前是好友，拉的是关注，强切成好友
        [KSTimelineManager sharedManager].currentFiltrMask = [KSTimelineManager sharedManager].filtrMaskQQWXFriends;
        self.currentFilterFeedsInfo = [KSTimelineManager sharedManager].feedsInfo_ForQQWXFriends;
        currentFilterMask = [KSTimelineManager sharedManager].filtrMaskQQWXFriends;
    }
    
    if ((currentFilterMask & [KSTimelineManager sharedManager].filtrMaskQQWXFriends) == [KSTimelineManager sharedManager].filtrMaskQQWXFriends && self.followTabCurFiltrMask == [KSTimelineManager sharedManager].filtrMaskFollow)
    {
        //当前是关注，拉的是好友，强切成关注
        [KSTimelineManager sharedManager].currentFiltrMask = [KSTimelineManager sharedManager].filtrMaskFollow;
        self.currentFilterFeedsInfo = [KSTimelineManager sharedManager].feedsInfo_ForFollow;
        currentFilterMask = [KSTimelineManager sharedManager].filtrMaskFollow;
    }
    
    [self flushMemoryTraceReprortData:currentFilterMask];
    KLog(@"%@拉关注是否从未读池拉 mask = %lu centerIndex =%ld",LogFeed,(unsigned long)currentFilterMask,(long)self.centerSegControl.selectIndex);
    
    //如果是点击频道刷新这边要拦截一下，不然会连续发两次
    if ([self.loadingRequests containsObject:[NSString stringWithFormat:@"%lu_refresh",(unsigned long)currentFilterMask]])
    {
        //去重，避免重复多次请求,wns层做更好，这里业务层先自己做
        KLog(@"[feed][EmptyFeed]Cnt请求去重-%lu %@",(unsigned long)currentFilterMask,[extraInfo objectForKey:KSRefreshSource]);
        return;
    }
    
    if ([self.loadingRequests containsObject:[NSString stringWithFormat:@"%lu_refresh",(unsigned long)currentFilterMask]])
    {
        //去重，避免重复多次请求,wns层做更好，这里业务层先自己做
        KLog(@"[feed][EmptyFeed]Cnt请求去重-%lu",(unsigned long)currentFilterMask);
        return;
    }
    
    UIScrollView* scrollView = [self getTableViewWithFilterMask:currentFilterMask];
    if (!scrollView)
    {
        KLog(@"[feed][EmptyFeed]Cnt请求去重-%lu tableview不存在",(unsigned long)currentFilterMask);
        return;
    }
    //7.25流量分发强制跳推荐后需要忽略预加载，带着traceid刷一次
    if ([[KSTimelineManager sharedManager] isRepeatReqWithPrefetchRecFeed:currentFilterMask extraInfo:extraInfo])
    {
        //冷启动离线push大卡片置顶ugc，这里过滤第2次feed请求
        KLog(@"[feed][EmptyFeed]Cnt请求去重-%lu,已经预加载过推荐大卡片%ld",(unsigned long)currentFilterMask,(long)self.refreshHeaderView.isLoading);
        [self refreshFinishedWithTableView:scrollView];
        return;
    }
    
    [self.loadingRequests safeAddObject:[NSString stringWithFormat:@"%lu_refresh",(unsigned long)currentFilterMask]];
    
    [self traceReportAdvertiseRequestWithFiltrMask:currentFilterMask isBeginRequest:YES isRefresh:YES]; // 请求 - 开始，上报
    
    // 预加载广告
    [[KSFeedAdManager sharedManager] preLoadFeedAdWithFilterMask:currentFilterMask];
    if (![KSTMEAdHelper feedsNeedShowTMEAdWithFilterMask:currentFilterMask]) {
        // 上报
        [KSTMEAdHelper reportFeedAdReqWithFilterMask:currentFilterMask pageNum:1 isPreload:NO isAdload:NO];
    }
        
    [self calculateLastFeedOfFilterFeedInfoWithMask:currentFilterMask];
    
    // 关闭合集弹框
    [self hideFeedAblumPopPanelIfNeed];

    KDEBUG(@"[EmptyFeed]Cnt beginload mask=%ld",(long)currentFilterMask);
    // 请求 K 歌 feed
    KS_WEAK_SELF(self);
    [[KSTimelineManager sharedManager] getFriendsFeedsListFromRemoteWithFilterMask:currentFilterMask location:self.locationCoordinate extraInfo:extraInfo completion:^(NSArray *list, BOOL canRefresh, BOOL hasMore, NSDictionary *customInfo, NSError *error) {
        CHECK_SELF_AND_RETURN()
        KDEBUG(@"[EmptyFeed]Cnt list=%ld mask=%ld",list.count,(long)currentFilterMask);

        [self.loadingRequests removeObject:[NSString stringWithFormat:@"%lu_refresh",(unsigned long)currentFilterMask]];
        
        // 统计刷新是否成功
        NSInteger reserves = self.friendsLoadFirstTime? ReportRefreshType_First:ReportRefreshType_DragLoad;
        [self traceReportPullRefresh:reserves error:error];
        ++self.mPullCount;
        
        if (IS_EMPTY_STR_BM(self.mPullCountString)) {
            self.mPullCountString = [NSString stringWithFormat:@"%d", (int)list.count];
        } else {
            self.mPullCountString = [NSString stringWithFormat:@"%@_%d", self.mPullCountString, (int)list.count];
        }
        
        //记录是否首次刷新过
        [self updateEachTabIsFirstLoad];

        //提示刷新了多少条feed
        NSInteger feedFilterMask = [[customInfo safeObjectForKey:kFeedReqFilterMask] intValue];
        NSInteger refreshCount = [self calculateRefreshFeedCount:list feedsFilterMask:feedFilterMask];
        KSFeedFilterResult *result = [self getCurrentFiltterFeedInfoWithMask:feedFilterMask];        
        if (refreshCount > 0) {
            if ([[KSTimelineManager sharedManager] checkIsFollowBizWithFilterMask:feedFilterMask]) {
                // 动态关注新筛选器样式(仅限全部动态)
                if ((currentFilterMask & [KSTimelineManager sharedManager].filtrMaskFollow) == [KSTimelineManager sharedManager].filtrMaskFollow) {
                    [self.followFilterView animatingUpdatedNewFeedsWithTips:nil count:refreshCount];
                }
                
                // 停止顶部刷新动画
                [self.refreshHeaderView endLoading:self.scrollView shouldChangeContentInset:YES];
            } else {
                [self refreshDidFinishedWithreshString:[NSString stringWithFormat:result.refreshText, refreshCount]];
            }
        } else {
            // 动态关注新筛选器样式(仅限全部动态)
            if ([[KSTimelineManager sharedManager] checkIsFollowBizWithFilterMask:feedFilterMask] &&
                (currentFilterMask & [KSTimelineManager sharedManager].filtrMaskFollow) == [KSTimelineManager sharedManager].filtrMaskFollow) {
                // 动态关注且新筛选器样式
                [self.followFilterView animatingUpdatedNewFeedsWithTips:nil count:0];
            }
            
            [self refreshFinishedWithTableView:scrollView];
        }
        
        if (!error) {
            self.loadFeedTS = [[NSDate date] timeIntervalSince1970];
            
            // 处理红点隐藏逻辑
            if (currentFilterMask == [KSTimelineManager sharedManager].filtrMaskQQWXFriends) {
                // 好友动态
                [KSBadgeUpdateManager sharedManager].feedFriendNum = 0;
                //这里好友+关注合并成一个tab（关注）之后，之刷新好友数据的时候也消掉tab红点 http://tapd.oa.com/10088941/bugtrace/bugs/view?bug_id=1010088941075138365
                [KSBadgeUpdateManager sharedManager].feedNum = 0;
                [KSBadgeUpdateManager sharedManager].feedAll = 0;
                
                [self showTopFreshButton:[KSBadgeUpdateManager sharedManager].feedFriendNum > 0];
            } else if (currentFilterMask == [KSTimelineManager sharedManager].filtrMaskFollow) {
                // 全部动态
                [KSBadgeUpdateManager sharedManager].feedNum = 0;
                [KSBadgeUpdateManager sharedManager].feedAll = 0;
                
                [self showTopFreshButton:[KSBadgeUpdateManager sharedManager].feedNum > 0];
                
                // 浅关系链
                if (self.socialBindRefreshPhase == FollowFeedBindSocialStatusFeed) {
                    // 绑定关系后首次刷新不展示绑定视图（后台关系链计算有1～2秒延时，这里可能无法立即更新还是返回用户为浅关系）
                    self.socialBindRefreshPhase = FollowFeedBindSocialStatusNone;
                } else if (([KSTimelineManager sharedManager].currentUserFlag & JceTimeline_GetFeedsFlag_GetFeedsFlagFriendless) == JceTimeline_GetFeedsFlag_GetFeedsFlagFriendless) {
                    self.socialBindRefreshPhase = FollowFeedBindSocialStatusShow;
                }
            } else if (currentFilterMask == [KSTimelineManager sharedManager].filtrMaskUgcFeed ||
                       currentFilterMask == [KSTimelineManager sharedManager].filtrMaskUgcFeedQQWX) {
                // 全部作品/好友作品
                [KSBadgeUpdateManager sharedManager].feedUgcNum = 0;
                [KSBadgeUpdateManager sharedManager].feedFriendNum = 0;
                [KSBadgeUpdateManager sharedManager].feedNum = 0;
                [KSBadgeUpdateManager sharedManager].feedAll = 0;
                
                [self showTopFreshButton:[KSBadgeUpdateManager sharedManager].feedUgcNum > 0];
            }
            
            //更新顶部关注+好友，底部动态tab红点信息
            [[[KSNavigationManager sharedManager] getRootTabBarController] updateFeedTabRedPoint];
            
            if (canRefresh) {
                //上传过程中刷新做去重
                [self removeSameFeedFromFeedListCompareWithShareFeed:self.currentFilterFeedsInfo.feedsList];
                
                if (self.centerSegControl.selectIndex == KSTimelineTab_Follow) {
                    // 刷新关注页面，去除关注页面里的假feed
                    NSArray *arr = [self getShareSimpleFeedsWithPoiInfo:NO];
                    [self.shareSimpleFeeds removeObjectsInArray:arr];
                }
                
                // 更新下当前 Filter 数据来源类型，标记为下发数据
                self.currentFiltrRemoteDataMask |= currentFilterMask;
                
                [self traceReportFeedShowDurationWhenDisappear];
                                
                // 判断是否需要请求（需要折叠30天前历史Feed是唯一判断标准）
                if ([[KSTimelineManager sharedManager] shouldShowRecUserFeedWithFiltrMask:currentFilterMask]) {
                    // 推荐用户数据
                    [self loadRecommendUserFeedsThenRefreshWithFiltrMask:currentFilterMask inScrollView:scrollView];
                } else {
                    // 刷新
                    [self updateTableViewHeaderInfo];

                    [self contentViewReloadData:scrollView];
                    [self setEnableFooterView:self.currentFilterFeedsInfo.hasMore];
                    // 下拉刷新 更新一下浏览卡片张数
                    [[KSRecFeedBottomGuideFrequencyControlMgr sharedManager] didShowFeedCard];
                    
                    [self updateEmptyView];
                }
            } else {
                // 没有数据，如果是关注则判断是否为浅关系链（新用户/小号）
                // 判断浅关系链
                if ([[KSTimelineManager sharedManager] shouldShowRecUserFeedWithFiltrMask:currentFilterMask]) {
                    // 推荐用户数据
                    [self loadRecommendUserFeedsThenRefreshWithFiltrMask:currentFilterMask inScrollView:scrollView];
                } else {
                    [self updateEmptyView];
                }
            }
            
            /// 是否需要展开合集
            [self showFeedAblumPopPanelIfNeed:extraInfo];
            
            [self handleTipsForNoRefresh];
            [self traceReportAdvertiseRequestWithFiltrMask:currentFilterMask isBeginRequest:NO isRefresh:YES]; // 请求 - 结束，上报
            
            //抛通知，carplay需要知道数据是否刷新
            [[NSNotificationCenter defaultCenter] postNotificationName:KSNotification_TimelineRootFeedListRefreshed object:@(currentFilterMask)];
        } else {
            [self onMgrError:error];
            
            if ([KSErrorHandler isNetWorkProblemError:error]) {
                self.shouldReloadWhenNetworkChange = YES;
                self.reloadWhenNetworkChangeExtraInfo = extraInfo;
            }
            
            [self updateEmptyView];
        }
       
        self.isRefreshing = NO;
    }];
    
    // 拉取广告免费送礼礼物
    [self loadFeedADFreeGift:currentFilterMask];
}

- (void)handleRecFeedDataPrefetchEvent
{
    self.hasHandledPretchEvent = YES;
    NSInteger currentFilterMask = [KSTimelineManager sharedManager].filtrMaskRecommend;
    [self.loadingRequests removeObject:[NSString stringWithFormat:@"%lu_refresh",(unsigned long)currentFilterMask]];
    
    //记录是否首次刷新过
    [self updateEachTabIsFirstLoad];
    
    UIScrollView* scrollView = [self getTableViewWithFilterMask:currentFilterMask];
    
    KDEBUG(@"[EmptyFeed]Cnt scroll=%@ list=%ld",scrollView,[KSTimelineManager sharedManager].feedsInfo_Recommend.feedsList.count);
    [self updateEmptyView];
    [self refreshFinishedWithTableView:scrollView];//这里setcontentInset会触发collectionview的cellfor函数，如果cellfor里面设置frame，会有个奇怪动画，这里提前
    
    [self stopActivityIndicatorAnimation];
    [self refreshDidFinished];
    
    self.loadFeedTS = [[NSDate date] timeIntervalSince1970];
    
    
    //更新顶部关注+好友，底部动态tab红点信息
    [[[KSNavigationManager sharedManager] getRootTabBarController] updateFeedTabRedPoint];
    
    //上传过程中刷新做去重
    [self removeSameFeedFromFeedListCompareWithShareFeed:self.currentFilterFeedsInfo.feedsList];
    
    if (self.centerSegControl.selectIndex == KSTimelineTab_Follow)
    {
        [self.shareSimpleFeeds removeAllObjects];
    }
    
    // 更新下当前 Filter 数据来源类型，标记为下发数据
    self.currentFiltrRemoteDataMask |= currentFilterMask;
    
    [self traceReportFeedShowDurationWhenDisappear];
        
    // 刷新
    [self updateTableViewHeaderInfo];
    [self contentViewReloadData:scrollView];
    [self setEnableFooterView:self.currentFilterFeedsInfo.hasMore];
    
    [self handleTipsForNoRefresh];
    [self traceReportAdvertiseRequestWithFiltrMask:currentFilterMask isBeginRequest:NO isRefresh:YES]; // 请求 - 结束，上报
    
    //抛通知，carplay需要知道数据是否刷新
    [[NSNotificationCenter defaultCenter] postNotificationName:KSNotification_TimelineRootFeedListRefreshed object:@(currentFilterMask)];
    
    /// 补充预加载
    [self.feedManager starPreloadNextFeedFromCurrentIndex:self.curRecFeedRowIndex];
    
    self.isRefreshing = NO;
}

/// 动态 Feed 的 loadMore 方法
- (void)loadFeedsMoreListFromRemote
{
    if (![[KSNetStatusManager sharedManager] IsEnableInternet])
    {
        [self tempAlert:@"网络不可用，请检查网络设置"];
        [self performSelector:@selector(refreshDidFinished) withObject:nil afterDelay:0];
        [self cancelLoadMore];
        
        return;
    }
    
    KS_WEAK_SELF(self);
    NSUInteger currentFilterMask = [KSTimelineManager sharedManager].currentFiltrMask;
    
    if ([self.loadingRequests containsObject:[NSString stringWithFormat:@"%lu_more",(unsigned long)currentFilterMask]])
    {
        //去重，避免重复多次请求,wns层做更好，这里业务层先自己做
        KLog(@"[feed]请求去重-%lu",(unsigned long)currentFilterMask);
        return;
    }
       
    [self.loadingRequests safeAddObject:[NSString stringWithFormat:@"%lu_more",(unsigned long)currentFilterMask]];
    
    UIScrollView *scrollView = [self getTableViewWithFilterMask:currentFilterMask];
    
    // 推荐 频道
    NSDictionary *extraInfo = nil;

    if ([[KSTimelineManager sharedManager] shouldShowRecUserFeedWithFiltrMask:currentFilterMask])
    {
        // 拉取推荐用户Feed
        [self loadRecommendUserFeedsThenRefreshWithFiltrMask:currentFilterMask inScrollView:[self getTableViewWithFilterMask:currentFilterMask]];
    }
    else
    {
        [self traceReportAdvertiseRequestWithFiltrMask:currentFilterMask isBeginRequest:YES isRefresh:NO]; // 请求 - 开始，上报
        
        // 预加载广告
        [[KSFeedAdManager sharedManager] preLoadFeedAdWithFilterMask:currentFilterMask];
        if (![KSTMEAdHelper feedsNeedShowTMEAdWithFilterMask:currentFilterMask]) {
            // 上报
            KSFilterFeedsInfo *feedsInfo = [[KSTimelineManager sharedManager] getFeedInfoWithFilterMask:currentFilterMask];
            [KSTMEAdHelper reportFeedAdReqWithFilterMask:currentFilterMask pageNum:feedsInfo.pageNum + 1 isPreload:NO isAdload:NO];
        }
        // 请求 K 歌 feed
        [[KSTimelineManager sharedManager] getMoreFriendsFeedsListFromRemoteWithFilterMask:currentFilterMask location:self.locationCoordinate extraInfo:extraInfo completion:^(NSArray *list, BOOL canRefresh, BOOL hasMore, NSDictionary *customInfo, NSError *error) {
            CHECK_SELF_AND_RETURN();
            
            self.needReportFeedDismiss = NO;
            [self.loadingRequests removeObject:[NSString stringWithFormat:@"%ld_more",currentFilterMask]];
            
            // 统计刷新是否成功
            [self traceReportLoadMore:NO error:error];
            ++self.mLoadCount;
            
            if (!error)
            {
                // 判断是否需要请求（需要折叠30天前历史Feed是唯一判断标准）
                if ([[KSTimelineManager sharedManager] shouldShowRecUserFeedWithFiltrMask:currentFilterMask])
                {
                    // 推荐用户Feed
                    [self loadRecommendUserFeedsThenRefreshWithFiltrMask:currentFilterMask inScrollView:scrollView];
                }
                else
                {
                    // 刷新
                    if (list.count > 0) {
                        [self contentViewReloadData:scrollView];
                        
                        /// 加载更多完成，补充预加载
                        [self.feedManager starPreloadNextFeedFromCurrentIndex:self.curRecFeedRowIndex];
                    }
                    [self setEnableFooterView:self.currentFilterFeedsInfo.hasMore];
                    [self traceReportAdvertiseRequestWithFiltrMask:currentFilterMask isBeginRequest:NO isRefresh:NO]; // 请求 - 结束，上报
                }
            }
            else
            {
                [self onMgrError:error];
            }
            
            [self updateEmptyView];
            if (self.centerSegControl.selectIndex == KSTimelineTab_Rec) {
                // 推荐tab中
                if (!scrollView.isDragging && !scrollView.isDecelerating && !scrollView.isTracking) {
                    // 判断uiscrollview已经静止时再调用footer的消失逻辑(scrollViewDidEndDecelerating时机会调用),防止预加载的时候,数据回来此时还是列表处于回弹过程中,如果此时dismiss footerview会重新设置scrollview的contentinset,从而会强制强contentoffset提前设置为回弹中点,而回弹此时则又会继续,从而是的最终的回弹位置错位,显示了上个cell
                    [self loadMoreFinishedWithTableView:scrollView];
                }
            } else {
                [self loadMoreFinishedWithTableView:scrollView];
            }
        }];
    }
}

/// 拉取加载折叠的关注动态
- (void)loadMoreFollowFoldedFeedsListFromRemote
{
    if (![[KSNetStatusManager sharedManager] IsEnableInternet])
    {
        [self tempAlert:@"网络不可用，请检查网络设置"];
        [self performSelector:@selector(refreshDidFinished) withObject:nil afterDelay:0];
        [self cancelLoadMore];
        
        return;
    }

    NSUInteger currentFilterMask = [KSTimelineManager sharedManager].currentFiltrMask;

    // 请求 AMS 信息流广告
    [self traceReportAdvertiseRequestWithFiltrMask:currentFilterMask isBeginRequest:YES isRefresh:NO];
    
    // 预加载广告
    [[KSFeedAdManager sharedManager] preLoadFeedAdWithFilterMask:currentFilterMask];
    if (![KSTMEAdHelper feedsNeedShowTMEAdWithFilterMask:currentFilterMask]) {
        // 上报
        KSFilterFeedsInfo *feedsInfo = [[KSTimelineManager sharedManager] getFeedInfoWithFilterMask:currentFilterMask];
        [KSTMEAdHelper reportFeedAdReqWithFilterMask:currentFilterMask pageNum:feedsInfo.pageNum + 1 isPreload:NO isAdload:NO];
    }
    
    // 请求 K 歌 feed
    KS_WEAK_SELF(self);
    [[KSTimelineManager sharedManager] getMoreFoldedFollowFeedsListFromRemoteWithFilterMask:currentFilterMask location:self.locationCoordinate completion:^(NSArray *list, BOOL canRefresh, BOOL hasMore, NSDictionary *customInfo, NSError *error) {
        CHECK_SELF_AND_RETURN();
        
        [self.loadingRequests removeObject:[NSString stringWithFormat:@"%lu_more",(unsigned long)currentFilterMask]];
        
        // 统计刷新是否成功
        UIScrollView *scrollView = [self getTableViewWithFilterMask:currentFilterMask];
        [self traceReportLoadMore:NO error:error];
        ++self.mLoadCount;
        
        if (!error)
        {
            // 刷新
            [self contentViewReloadData:scrollView];
            [self setEnableFooterView:self.currentFilterFeedsInfo.hasMore];
            
            // 请求 - 结束，上报
            [self traceReportAdvertiseRequestWithFiltrMask:currentFilterMask isBeginRequest:NO isRefresh:NO];
        }
        else
        {
            [self onMgrError:error];
        }
        
        [self updateEmptyView];
        [self loadMoreFinishedWithTableView:scrollView];
    }];
}

- (void)stopLoadNearByUserListTableViewForMore:(BOOL)isMore
{
    // 不使用dispatch_async(dispatch_get_main_queue() 即使在主线程contentInset 也可能设置失败 奇怪！
    dispatch_async(dispatch_get_main_queue(), ^{
        if (isMore) {
            [self loadMoreFinishedWithTableView:self.mNearbyUserTableView];
        } else {
            [self refreshFinishedWithTableView:self.mNearbyUserTableView];
        }
    });
}

// 同城页面：拉取附近的人
- (void)loadNearbyUserFromRemoteForIsMore:(BOOL)isMore
{
    if (self.isNearByUserLoading)
    {
        return;
    }
    
    if (![[KSNetStatusManager sharedManager] IsEnableInternet]) {
        [self tempAlert:@"网络不可用，请检查网络设置"];
        [self performSelector:@selector(refreshDidFinished) withObject:nil afterDelay:0];
        return;
    }
    //发协议的时候加个锁，防止locationManager update的时候快速发两次协议
    self.isNearByUserLoading = YES;
    
    KSNearbyQueryModel *queryModel = [KSNearbyQueryModel new];
    queryModel.gender = self.nearbySettingModel.genderType;
    queryModel.age_begin = self.nearbySettingModel.ageFloor;
    queryModel.age_end = self.nearbySettingModel.ageUpper;
    
    if (isMore && self.nearByUserHasNoMoreData) // 加载更多
    {
        self.isNearByUserLoading = NO;
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [self stopLoadNearByUserListTableViewForMore:isMore];
        });
        return;
    }
    
       NSString *passback = (isMore ? self.nearByUserDataPassback : @"");

       KLog(@"【KSNearByUserRequest】lon:%.4f lat:%.4f  passback: %@ gender: %d, age: [%zi-%zi] online:%d isMore:%d", self.locationCoordinate.longitude, self.locationCoordinate.latitude, passback, queryModel.gender, queryModel.age_begin, queryModel.age_end, queryModel.online, isMore);
    
       [[KSProtoLbsManager sharedManager] getNearPersonList:self.locationCoordinate
                                                passback:passback
                                                  maxNum:20
                                              queryModel:queryModel
                                              completion:^(JceProtoLbs_GetLbsPersonNearRsp *rsp) {
      

        NSString *nextPassback = rsp.jce_pass_back;
        self.nearByUserHasNoMoreData = (rsp.jce_iHasMore <= 0);
        self.nearByUserDataPassback = nextPassback;
        
        NSArray *resultArray = rsp.jce_vecUserInfo;
        NSMutableArray *list = [NSMutableArray array];
        for (JceProtoLbs_UserInfo *userInfo in resultArray)
        {
            KSNearByUserInfoModel *info = [[KSNearByUserInfoModel alloc] init];
            info.userInfo = userInfo;
            [list addObject:info];
        }
        
        KLog(@"【KSNearByUserResponse】passback:%@ count:%zi nextPassback:%@ hasMore:%d", passback, rsp.jce_vecUserInfo.count, nextPassback, rsp.jce_iHasMore);

        
        if (!isMore)
        {
           [self.nearByUserArray removeAllObjects];
        }

        if (list && list.count > 0)
        {
            [self.nearByUserArray addObjectsFromArray:list];
        }
           
       dispatch_async(dispatch_get_main_queue(), ^{
           [self stopNearByLoading];
           [self.mNearbyUserTableView reloadData];
           [self stopLoadNearByUserListTableViewForMore:isMore];
           [self updateEmptyView];
       });
        
        self.isNearByUserLoading = NO;

    }];
}

// 同城页面：拉取数据
- (void)loadNearbyListFromRemote {
    if (self.isLoading) {
        return;
    }
    
    if (![[KSNetStatusManager sharedManager] IsEnableInternet]) {
        [self tempAlert:@"网络不可用，请检查网络设置"];
        [self performSelector:@selector(refreshDidFinished) withObject:nil afterDelay:0];
        return;
    }

    self.isLoading = YES;//发协议的时候加个锁，防止locationManager update的时候快速发两次协议
    
    KS_WEAK_SELF(self);
    NSUInteger currentFilterMask = [KSTimelineManager sharedManager].currentFiltrMask;
    [self traceReportAdvertiseRequestWithFiltrMask:currentFilterMask isBeginRequest:YES isRefresh:YES]; // 请求 - 开始，上报
    if ([self.loadingRequests containsObject:[NSString stringWithFormat:@"%lu_refresh",(unsigned long)currentFilterMask]]) {
        //去重，避免重复多次请求,wns层做更好，这里业务层先自己做
        KLog(@"[同城feed]请求去重-%lu",(unsigned long)currentFilterMask);
        return;
    }
    // 预加载广告
    [[KSFeedAdManager sharedManager] preLoadFeedAdWithFilterMask:currentFilterMask];
    
    BOOL hasRedPoint = self.nearbyTabHasRedPoint;
    self.nearbyTabHasRedPoint = NO;
    // 请求 K 歌 feed
    NSDictionary *extraInfo;
    if (self.nearByTopUgcIdArr.count > 0) {
        extraInfo = @{KSNearByUgcIds : self.nearByTopUgcIdArr};
        self.nearByTopUgcIdArr = nil;
    }
    [[KSTimelineManager sharedManager] getFeedsNearbyFromRemoteBylocation:self.locationCoordinate
                                                                extraInfo:extraInfo
                                                              hasRedPoint:hasRedPoint
                                             locationPositionRightUnabled:[self isLocationAuthorizationStatusUnabled]
                                                            nearbySetting:self.nearbySettingModel
                                                           withCompletion:^(NSArray *list,
                                                                            BOOL canRefresh,
                                                                            BOOL hasMore,
                                                                            NSDictionary *customInfo,
                                                                            NSError *error) {
        KS_STRONG_SELF(self);
        if (!self) return;
        [self.loadingRequests removeObject:[NSString stringWithFormat:@"%lu_refresh",(unsigned long)currentFilterMask]];
        NSInteger reserves = self.recommendNearByLoadFirstTime ? ReportRefreshType_First : ReportRefreshType_DragLoad;
        [self traceReportPullRefresh:reserves error:error];
        self.recommendNearByLoadFirstTime = NO;
        ++self.mPullCount;
        self.isLoading = NO;
        if (!error) {
            if (canRefresh) {
                if (self.centerSegControl.selectIndex == KSTimelineTab_RecNearBy) {
                    // 刷新同城页面，去除同城页面的假feed
                    NSArray *arr = [self getShareSimpleFeedsWithPoiInfo:YES];
                    [self.shareSimpleFeeds removeObjectsInArray:arr];
                }
                
                [self reloadTable];
                // 更新下当前 Filter 数据来源类型，标记为下发数据 (为了让同城feed能够自动播放而添加这行代码)
                self.currentFiltrRemoteDataMask |= currentFilterMask;
            }

            if (hasRedPoint) {
                // 有红点时点击“同城”，需要拉取最新数据，并冒泡标签有动画效果
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    for (UITableViewCell *cell in self.contentTableView.visibleCells) {
                        if ([cell isKindOfClass:[KSLayoutableTimelineFeedCellV2 class]]) {
                            KSLayoutableTimelineFeedCellV2 *feedCell = (KSLayoutableTimelineFeedCellV2 *)cell;
                            if ([feedCell.simpleFeed isNearbyBubbleFeed]) {
                                // todo 这里delay一直不起作用，先用dispatch_after，具体原因待排查
                                [feedCell.nearbyBubbleView doBubbleViewAnimationWithDelay:1];
                            }
                        }
                    }
                });
            }
            
            UIScrollView *scrollView = [self getTableViewWithFilterMask:currentFilterMask];

            [self contentViewReloadData:scrollView];
            [self setEnableFooterView:self.currentFilterFeedsInfo.hasMore];

            [self traceReportAdvertiseRequestWithFiltrMask:currentFilterMask isBeginRequest:NO isRefresh:YES]; // 请求 - 结束，上报
        } else {
            [self onMgrError:error];
        }
        
        [self updateEmptyView];
       
        [self refreshFinishedWithTableView:self.mNearbyFeedTableView];
    }];
}

-(BOOL)isLocationAuthorizationStatusDenied
{
    BOOL isLocationAuthorizationStatusDenied = NO;
    
    if (@available(iOS 14.0, *))
    {
        isLocationAuthorizationStatusDenied = _locationManager.authorizationStatus == kCLAuthorizationStatusDenied?YES:NO;
    }
    else
    {
        isLocationAuthorizationStatusDenied = [CLLocationManager authorizationStatus] == kCLAuthorizationStatusDenied?YES:NO;
    }
    KLog(@"[同城当前位置权限]isLocationAuthorizationStatusDenied :%@",@(isLocationAuthorizationStatusDenied));
    return isLocationAuthorizationStatusDenied;
}

-(BOOL)isLocationAuthorizationStatusNotDetermined
{
    BOOL isLocationAuthorizationStatusNotDetermined = NO;
    
    if (@available(iOS 14.0, *))
    {
        isLocationAuthorizationStatusNotDetermined = _locationManager.authorizationStatus == kCLAuthorizationStatusNotDetermined?YES:NO;
    }
    else
    {
        isLocationAuthorizationStatusNotDetermined = [CLLocationManager authorizationStatus] == kCLAuthorizationStatusNotDetermined?YES:NO;
    }
    KLog(@"[同城当前位置权限]isLocationAuthorizationStatusNotDetermined :%@",@(isLocationAuthorizationStatusNotDetermined));
    return isLocationAuthorizationStatusNotDetermined;
}

-(BOOL)isLocationAuthorizationStatusUnabled
{
    BOOL isLocationAuthorizationStatusUnabled = NO;
    
    if (@available(iOS 14.0, *))
    {
        switch (_locationManager.authorizationStatus) {
            case kCLAuthorizationStatusNotDetermined:
                isLocationAuthorizationStatusUnabled = YES;
                break;
            case kCLAuthorizationStatusDenied:
                isLocationAuthorizationStatusUnabled = YES;
                break;
            default:
                break;
        }
        KLog(@"[同城当前位置权限] iOS>=14 authorizationStatus :%@",@(_locationManager.authorizationStatus));
    }
    else
    {
        switch ([CLLocationManager authorizationStatus]) {
            case kCLAuthorizationStatusNotDetermined:
                isLocationAuthorizationStatusUnabled = YES;
                break;
            case kCLAuthorizationStatusDenied:
                isLocationAuthorizationStatusUnabled = YES;
                break;
            default:
                break;
        }
        KLog(@"[同城当前位置权限] iOS<14 authorizationStatus :%@",@([CLLocationManager authorizationStatus]));
    }
    
    return isLocationAuthorizationStatusUnabled;
}

- (void)refreshFinishedWithTableView:(UIScrollView *)scrollView
{
    if ([scrollView isKindOfClass:[UICollectionView class]])
    {
        KSUICollectionView *collectionScrollView = SAFE_CAST(scrollView, [KSUICollectionView class]);
        KSongRefreshScrollViewHeaderView *headerView = collectionScrollView.headerView;
        if (headerView)
        {
            [headerView endLoading:collectionScrollView shouldChangeContentInset:YES];
        }
    }
    else
    {
        KSUITableView *tableScrollView = SAFE_CAST(scrollView, [KSUITableView class]);
        KSongRefreshScrollViewHeaderView *headerView = tableScrollView.headerView;

        if (headerView)
        {
            [headerView endLoading:tableScrollView shouldChangeContentInset:YES];
        }
        
        //刷新完开始视频预览自动播放
        KDEBUG(@"[自动播]--协议刷新force play");
    }
}

- (void)loadMoreFinishedWithTableView:(UIScrollView *)scrollview
{
    if ([scrollview isKindOfClass:[UICollectionView class]])
    {
        KSUICollectionView *collectionScrollView = SAFE_CAST(scrollview, [KSUICollectionView class]);
        KSongLoadMoreScrollViewFooterView *footerView = collectionScrollView.footerView;
        if (footerView)
        {
            [footerView endLoading:scrollview shouldChangeContentInset:YES];
        }
    }
    else
    {
        KSUITableView *tableScrollView = SAFE_CAST(scrollview, [KSUITableView class]);
        KSongLoadMoreScrollViewFooterView *footerView = tableScrollView.footerView;
        if (footerView)
        {
            [footerView endLoading:scrollview shouldChangeContentInset:YES];
        }
    }
    
}

/// 拉取推荐用户 Feed 并刷新
- (void)loadRecommendUserFeedsThenRefreshWithFiltrMask:(NSInteger)filtrMask inScrollView:(UIScrollView*)scrollView
{
    // 拉推人协议
    KS_WEAK_SELF(self);
    [[KSTimelineManager sharedManager] getRecommendUserFeedListFromRemoteWithFiltrMask:filtrMask completion:^(BOOL hasMore, NSError *error) {
        CHECK_SELF_AND_RETURN();
        
        [self.loadingRequests removeObject:[NSString stringWithFormat:@"%ld_more", filtrMask]];
        
        if (!error)
        {
            // 浅关系链：折叠后部分好友动态 || 更多历史动态 || 推荐用户 Feed 数据
            
            // footer
            [self setEnableFooterView:hasMore];
        }
        else
        {
            [self onMgrError:error];
            
            // 允许再次尝试拉取推荐用户Feed
            [self setEnableFooterView:YES];
        }
        [self contentViewReloadData:scrollView]; // 防止数据源不同步
        
        [self updateEmptyView];
        [self loadMoreFinishedWithTableView:scrollView];
    }];
}

// 同城页面：上拉加载更多数据
- (void)loadFeedsNearMorebyFromRemote {
    if (![[KSNetStatusManager sharedManager] IsEnableInternet]) {
        [self tempAlert:@"网络不可用，请检查网络设置"];
        [self performSelector:@selector(refreshDidFinished) withObject:nil afterDelay:0];
        [self cancelLoadMore];
        return;
    }
    
    KS_WEAK_SELF(self);
    
    NSUInteger currentFilterMask = [KSTimelineManager sharedManager].currentFiltrMask;
    [self traceReportAdvertiseRequestWithFiltrMask:currentFilterMask isBeginRequest:YES isRefresh:NO]; // 请求 - 开始，上报
    
    // 预加载广告
    [[KSFeedAdManager sharedManager] preLoadFeedAdWithFilterMask:currentFilterMask];

    // 请求 K 歌 feed
    [[KSTimelineManager sharedManager] getFeedsNearbyLoadingMoreFromRemoteBylocation:self.locationCoordinate
                                                        locationPositionRightUnabled:[self isLocationAuthorizationStatusUnabled]
                                                                       nearbySetting:self.nearbySettingModel
                                                                      withCompletion:^(NSArray *list,
                                                                                       BOOL canRefresh,
                                                                                       BOOL hasMore,
                                                                                       NSDictionary *customInfo,
                                                                                       NSError *error) {
        KS_STRONG_SELF(self);
        if (!self) return;
        
        // 统计刷新是否成功
        [self traceReportLoadMore:self.recommendNearByLoadFirstTime error:error];
        ++self.mLoadCount;

        if (!error) {
            [self setEnableFooterView:self.currentFilterFeedsInfo.hasMore];
            [self reloadTable];
            [self traceReportAdvertiseRequestWithFiltrMask:currentFilterMask isBeginRequest:NO isRefresh:NO]; // 请求 - 结束，上报
        } else {
            [self onMgrError:error];
        }
        [self updateEmptyView];
        [self loadMoreFinishedWithTableView:self.mNearbyFeedTableView];
    }];
}

- (void)loadFeedsListFromLocal
{
    if ([self isRecCardTable:self.scrollView]) {
        //大卡片首次加载不用缓存，否则会闪一下
        [self reloadTable];
        return;
    }
    if ([self isNearByScrollView:self.scrollView]) {
        return;
    }
    KS_WEAK_SELF(self);
    NSUInteger currentFilterMask = [KSTimelineManager sharedManager].currentFiltrMask;
    [[KSTimelineManager sharedManager] getFeedsListFromLocalForFilterMask:currentFilterMask completion:^(NSArray *list, BOOL canRefresh, BOOL hasMore, NSDictionary *customInfo, NSError *error) {
        
        KS_STRONG_SELF(self);
        if (!self) return;
        
        if (!error)
        {
            [self setEnableFooterView:self.currentFilterFeedsInfo.hasMore];
            [self calLastFeedIdList:self.currentFilterFeedsInfo.feedsList];
            
            //
            if (self.currentFiltrRemoteDataMask & currentFilterMask)
            {
                self.currentFiltrRemoteDataMask ^= currentFilterMask;
            }
            
            [self reloadTable];
            self.currentFilterFeedsInfo.curPageSize = list.count;
        }
        
        if (!self.viewFirstAppear)
        {
            [self updateEmptyView];
        }
        
        if (list.count > 0) {
            /// 缓存有数据才算加载完成
            [self dataLoadFinishedUsingCache];
        }
    }];
}

- (void)updateEmptyView
{
    NSInteger currentFiltrMask = [KSTimelineManager sharedManager].currentFiltrMask;
    
    if ([[KSTimelineManager sharedManager] checkIsFollowBizWithFilterMask:currentFiltrMask])
    {
        // 浅关系链判断数据为空时要考虑是否有推荐数据
        self.emptyView.tipTitleLable.hidden = YES;
        self.emptyView.newTipButton.hidden = YES;
        NSMutableArray<KSimpleFeed *> *recFeedsList; // 浅关系链推荐数据
        
        if ([KSTimelineManager sharedManager].recFeedType == proto_feed_webapp_ENUM_REC_FEED_TYPE_ENUM_REC_FEED_TOPIC)
        {
            // 浅关系链热点推荐
            recFeedsList = [KSTimelineManager sharedManager].recHotSpotFeed.feedsList;
        }
        else
        {
            if (currentFiltrMask == [KSTimelineManager sharedManager].filtrMaskFollow)
            {
                recFeedsList = [KSTimelineManager sharedManager].recUserFeed_ForFollow.feedsList;
            }
            else if (currentFiltrMask == [KSTimelineManager sharedManager].filtrMaskUgcFeed)
            {
                recFeedsList = [KSTimelineManager sharedManager].recUserFeed_ForUgcFeed.feedsList;
            }
            else if (currentFiltrMask == [KSTimelineManager sharedManager].filtrMaskUgcFeedQQWX)
            {
                recFeedsList = [KSTimelineManager sharedManager].recUserFeed_ForUgcFeedQQWX.feedsList;
            }
            else if (currentFiltrMask == [KSTimelineManager sharedManager].filtrMaskQQWXFriends)
            {
                recFeedsList = [KSTimelineManager sharedManager].recUserFeed_ForQQWXFriends.feedsList;
            }
        }
        
        [self.emptyView.tipStringLable setText:[[WnsConfigManager sharedInstance] isRecEnabled] ? KString(@"暂时没有推荐哦") :  KString(@"暂时没有热门哦")];
        [self.emptyView.tipStringLable setFrame:CGRectMake(0, self.emptyView.tipPictureView.bottom + 20, SCREEN_WIDTH, 30)];
        [self.emptyView.tipPictureView setImage:[UIImage imageNamed:@"hotfeed_emptyguide.jpg"]];
        NSInteger emptyViewWidth = self.emptyView.origin_tipPictureViewRect.size.width;
        NSInteger emptyViewHeight = self.emptyView.origin_tipPictureViewRect.size.height;
        self.emptyView.tipPictureView.frame = CGRectMake((SCREEN_WIDTH - emptyViewWidth) / 2, 0, emptyViewWidth, emptyViewHeight);
        
        [self.emptyView.tipButton setTitle:nil forState:UIControlStateNormal];
        [self.emptyView.arrowPictureView setImage:nil];
        
        // 关注Tab没有数据
        if (self.currentFilterFeedsInfo.feedsList.count == 0 && self.uploadTaskArray.count == 0 && recFeedsList.count == 0)
        {
            [self.emptyView removeFromSuperview];
            self.emptyView = nil;
            [self emptyView];
            self.emptyView.tipButton.tag = 0;
            [self.emptyView.tipButton setAttributedTitle:nil forState:UIControlStateNormal];
            
            UITableView *contenTable = SAFE_CAST(self.scrollView, UITableView);
            CGFloat headerHeight = contenTable.tableHeaderView.height;
            self.emptyView.height = self.scrollView.height - headerHeight;
            self.emptyView.y = headerHeight;
            
            [self.emptyView.tipPictureView setImage:[UIImage imageNamed:@"hotfeed_emptyguide"]];
            [self.emptyView.tipStringLable setText:KString(@"啊喔~还没有任何动态，")];
            [self.emptyView.tipButton setTitle:[[WnsConfigManager sharedInstance] isRecEnabled] ? KString(@"去看看推荐吧") :  KString(@"去看看热门吧") forState:UIControlStateNormal];
            
            [self.emptyView.tipPictureView sizeToFit];
            [self.emptyView.tipStringLable sizeToFit];
            [self.emptyView.tipButton sizeToFit];
            [self.emptyView.arrowPictureView sizeToFit];
            
            [self.emptyView.tipPictureView setXY:CGPointMake((self.emptyView.width - self.emptyView.tipPictureView.image.size.width) / 2, (self.emptyView.height - self.emptyView.tipPictureView.image.size.height ) / 2)];
            [self.emptyView.tipStringLable setXY:CGPointMake((self.emptyView.width - self.emptyView.tipStringLable.width - self.emptyView.tipButton.width - self.emptyView.arrowPictureView.width - 6) / 2, self.emptyView.tipPictureView.bottom + 20)];
            [self.emptyView.tipButton setXY:CGPointMake(self.emptyView.tipStringLable.right, self.emptyView.tipStringLable.top - 6)];
            [self.emptyView.arrowPictureView setXY:CGPointMake(self.emptyView.tipButton.right + 6, self.emptyView.tipStringLable.top + 3)];
            if (self.scrollView != self.mNearbyUserTableView)
            {
                if (![[KSNetStatusManager sharedManager] IsEnableInternet]) {                    
                    self.emptyView = [self noNetworkEmptyViewWithDarkMode:NO];
                }
                [self.scrollView insertSubview:self.emptyView atIndex:0];
            }
        }
        else
        {
            [self.emptyView removeFromSuperview];
        }
    }
    else
    {
        // 非关注页
        self.emptyView.tipTitleLable.hidden = YES;
        self.emptyView.newTipButton.hidden = YES;
        if ((self.currentFilterFeedsInfo.feedsList.count == 0 && self.uploadTaskArray.count == 0))
        {
            // 没有数据
            [self.emptyView removeFromSuperview];
            self.emptyView.tipButton.tag = 0;
            [self.emptyView.tipButton setAttributedTitle:nil forState:UIControlStateNormal];
            
            CGFloat headerHeight = 0;
            UITableView* contenTable = SAFE_CAST(self.scrollView, UITableView);
            headerHeight = contenTable.tableHeaderView.height;
            self.emptyView.height = self.scrollView.height - headerHeight;
            self.emptyView.y = headerHeight;
        
            if ([KSTimelineManager sharedManager].currentFiltrMask  == [KSTimelineManager sharedManager].filtrMaskHot)
            {
                [self.emptyView.tipStringLable setText:KString(@"暂时没有动态哦")];
                [self.emptyView.tipStringLable setFrame:CGRectMake(0, self.emptyView.tipPictureView.bottom + 20, SCREEN_WIDTH, 30)];
                [self.emptyView.tipPictureView setImage:[UIImage imageNamed:@"hotfeed_emptyguide.jpg"]];
                NSInteger emptyViewWidth = self.emptyView.origin_tipPictureViewRect.size.width;
                NSInteger emptyViewHeight = self.emptyView.origin_tipPictureViewRect.size.height;
                self.emptyView.tipPictureView.frame = CGRectMake((SCREEN_WIDTH - emptyViewWidth) / 2, 0, emptyViewWidth, emptyViewHeight);
                
                [self.emptyView.tipButton setTitle:nil forState:UIControlStateNormal];
                [self.emptyView.arrowPictureView setImage:nil];
            }
            else if ([KSTimelineManager sharedManager].currentFiltrMask  == [KSTimelineManager sharedManager].filtrMaskRecommend)
            {
                CGFloat headerHeight = 0;
                UITableView* contenTable = SAFE_CAST(self.scrollView, UITableView);
                headerHeight = contenTable.tableHeaderView.height;
                self.emptyView.height = self.scrollView.height - headerHeight;
                self.emptyView.y = headerHeight;
                [self.emptyView.tipStringLable setText:[[WnsConfigManager sharedInstance] isRecEnabled] ? KString(@"暂时没有推荐哦") :  KString(@"暂时没有热门哦")];
                [self.emptyView.tipStringLable setFrame:CGRectMake(0, self.emptyView.tipPictureView.bottom + 20, SCREEN_WIDTH, 30)];
                [self.emptyView.tipPictureView setImage:[UIImage imageNamed:@"hotfeed_emptyguide.jpg"]];
                NSInteger emptyViewWidth = self.emptyView.origin_tipPictureViewRect.size.width;
                NSInteger emptyViewHeight = self.emptyView.origin_tipPictureViewRect.size.height;
                self.emptyView.tipPictureView.frame = CGRectMake((SCREEN_WIDTH - emptyViewWidth) / 2, 0, emptyViewWidth, emptyViewHeight);
                
                [self.emptyView.tipButton setTitle:nil forState:UIControlStateNormal];
                [self.emptyView.arrowPictureView setImage:nil];
            }
            else if ([KSTimelineManager sharedManager].currentFiltrMask  == [KSTimelineManager sharedManager].filtrMaskRecommendNearBy)
            {
                
                NSArray * arr = @[];
                
                if (self.nearbySubIndex == NearbyTabBarSubIndex_Dynamic) // 附近动态
                {
                   arr = [self getShareSimpleFeedsWithPoiInfo:YES];
                    
                } else if (self.nearbySubIndex == NearbyTabBarSubIndex_User) { // 附近的人
                    
                    arr = self.nearByUserArray;
                }
                
                //同城数据空页面
                if ([CLLocationManager authorizationStatus] == kCLAuthorizationStatusDenied)//如果没权限
                {
                                        
                    if ([arr count] > 0) {
                        [self.emptyView removeFromSuperview];
                        [self.nearByUserEmptyView removeFromSuperview];
                        return;
                    }
                    [self.emptyView removeFromSuperview];
                    self.emptyView = nil;
                    [self emptyView];
                    UIImage *emptyImage = [UIImage imageNamed:[NSString stringWithFormat:@"empty014_icon"]];
                    CGFloat headerHeight = 0;
                    self.emptyView.newTipButton.tag = 999;
                    self.emptyView.height = self.mNearbyFeedTableView.height;
                    self.emptyView.y = headerHeight;
                    [self.emptyView addSubview:self.emptyView.tipTitleLable];
                    [self.emptyView addSubview:self.emptyView.newTipButton];
                    self.emptyView.tipStringLable.numberOfLines = 2;
                    self.emptyView.tipTitleLable.hidden = NO;
                    self.emptyView.newTipButton.hidden = NO;
                    [self.emptyView bindNewtipButtonAction];
                    [self.emptyView.tipButton setTitle:nil forState:UIControlStateNormal];
                    [self.emptyView.tipPictureView setImage:emptyImage];
                    [self.emptyView.tipTitleLable setText:KString(@"无法获取地理位置")];
                    [self.emptyView.tipStringLable setText:KString(@"允许全民K歌访问地理位置，查看同城精彩作品")];
                    self.emptyView.newTipButton.title = KString(@"允许访问");
                    
                    self.emptyView.tipButton.frame = CGRectZero;
                    self.emptyView.tipPictureView.frame = CGRectMake((SCREEN_WIDTH - emptyImage.size.width)/2, 69.5, emptyImage.size.width, emptyImage.size.height);
                    self.emptyView.tipTitleLable.frame = CGRectMake(0, self.emptyView.tipPictureView.bottom + 15, SCREEN_WIDTH, 23.5);
                    self.emptyView.tipStringLable.frame = CGRectMake((SCREEN_WIDTH - 220)/2, self.emptyView.tipTitleLable.bottom + 10, 220, 45);
                    self.emptyView.newTipButton.center = CGPointMake(self.emptyView.tipPictureView.centerX, self.emptyView.tipStringLable.bottom + KSMargin(20) + 0.5 *self.emptyView.newTipButton.height);
                    
                    if (self.nearbySubIndex == NearbyTabBarSubIndex_Dynamic) // 附近动态
                    {
                        [self.mNearbyFeedTableView addSubview:self.emptyView];
                        
                    } else if (self.nearbySubIndex == NearbyTabBarSubIndex_User) { // 附近的人
                        
                        [self.mNearbyUserTableView addSubview:self.emptyView];
                    }
                   
                    [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                        reportModel.key = @"feed_nearby#unavailable_position_page#null#exposure#0";
                    }];
                }
                else  // 拥有定位权限
                {
                    if ([arr count] > 0) {
                        [self.emptyView removeFromSuperview];
                        [self.nearByUserEmptyView removeFromSuperview];
                        return;
                    }
                    [self.emptyView removeFromSuperview];
                    self.emptyView = nil;
                    [self emptyView];
                    UIImage *emptyImage = [UIImage imageNamed:[NSString stringWithFormat:@"empty014_icon"]];
                    CGFloat headerHeight = 0;
                    self.emptyView.newTipButton.tag = 999;
                    self.emptyView.height = self.mNearbyFeedTableView.height;
                    self.emptyView.y = headerHeight;
                    [self.emptyView addSubview:self.emptyView.tipTitleLable];
                    [self.emptyView addSubview:self.emptyView.newTipButton];
                    self.emptyView.tipStringLable.numberOfLines = 2;
                    self.emptyView.tipTitleLable.hidden = NO;
                    self.emptyView.newTipButton.hidden = YES;
                    [self.emptyView bindNewtipButtonAction];
                    [self.emptyView.tipButton setTitle:nil forState:UIControlStateNormal];
                    [self.emptyView.tipPictureView setImage:emptyImage];
                    [self.emptyView.tipTitleLable setText:KString(@"暂时没有同城动态哦")];
                    [self.emptyView.tipStringLable setText:[[WnsConfigManager sharedInstance] isRecEnabled] ? KString(@"去看看推荐吧") : KString(@"去看看热门吧")];
                    
                    self.emptyView.tipButton.frame = CGRectZero;
                    self.emptyView.tipPictureView.frame = CGRectMake((SCREEN_WIDTH - emptyImage.size.width)/2, 69.5, emptyImage.size.width, emptyImage.size.height);
                    self.emptyView.tipTitleLable.frame = CGRectMake(0, self.emptyView.tipPictureView.bottom + 15, SCREEN_WIDTH, 23.5);
                    self.emptyView.tipStringLable.frame = CGRectMake((SCREEN_WIDTH - 220)/2, self.emptyView.tipTitleLable.bottom + 10, 220, 45);
                    self.emptyView.newTipButton.center = CGPointMake(self.emptyView.tipPictureView.centerX, self.emptyView.tipStringLable.bottom + KSMargin(20) + 0.5 *self.emptyView.newTipButton.height);
                    
                    if (self.nearbySubIndex == NearbyTabBarSubIndex_Dynamic) // 附近动态
                    {
                        if (![[KSNetStatusManager sharedManager] IsEnableInternet]) {
                            self.emptyView = [self noNetworkEmptyViewWithDarkMode:NO];
                        }
                        [self.mNearbyFeedTableView addSubview:self.emptyView];
                    }
                }
            }
            else
            {
                [self.emptyView.tipPictureView setImage:[UIImage imageNamed:@"hotfeed_emptyguide.jpg"]];
                [self.emptyView.tipStringLable setText:KString(@"没有好友痕迹?")];
                [self.emptyView.tipButton setTitle:[[WnsConfigManager sharedInstance] isRecEnabled] ? KString(@"去看看推荐吧") : KString(@"去看看热门吧")
                                          forState:UIControlStateNormal];
                self.emptyView.tipPictureView.frame = self.emptyView.origin_tipPictureViewRect;
                self.emptyView.tipStringLable.frame = self.emptyView.origin_tipLableRect;
                self.emptyView.tipButton.frame = self.emptyView.origin_tipButtonRect;
                self.emptyView.arrowPictureView.frame = self.emptyView.origin_arrowPictureRect;
            }
            
            if (self.scrollView != self.mNearbyUserTableView)
            {
                [self.scrollView insertSubview:self.emptyView atIndex:0];
            }
        }
        else
        {
            BOOL condition = [KSTimelineManager sharedManager].currentFiltrMask == [KSTimelineManager sharedManager].filtrMaskRecommendNearBy;
            KLog(@"conditon %d", condition);
            
            if (([KSTimelineManager sharedManager].currentFiltrMask == [KSTimelineManager sharedManager].filtrMaskRecommendNearBy && [CLLocationManager authorizationStatus] == kCLAuthorizationStatusDenied))
            {
                
                BOOL isNoData = YES;
                if (self.nearbySubIndex == NearbyTabBarSubIndex_Dynamic) // 附近动态
                {
                    isNoData = self.currentFilterFeedsInfo.feedsList.count <= 0;
                    
                } else if (self.nearbySubIndex == NearbyTabBarSubIndex_User) { // 附近的人
                    
                    isNoData = self.nearByUserArray.count <= 0;
                }
                
                if (isNoData)
                {
                    [self.emptyView removeFromSuperview];
                    UIImage *emptyImage = [UIImage imageNamed:[NSString stringWithFormat:@"empty014_icon"]];
                    CGFloat headerHeight = 0;
                    self.emptyView.newTipButton.tag = 999;
                    self.emptyView.height = self.mNearbyFeedTableView.height;
                    self.emptyView.y = headerHeight;
                    [self.emptyView addSubview:self.emptyView.tipTitleLable];
                    [self.emptyView addSubview:self.emptyView.newTipButton];
                    self.emptyView.tipStringLable.numberOfLines = 2;
                    self.emptyView.tipTitleLable.hidden = NO;
                    self.emptyView.newTipButton.hidden = NO;
                    [self.emptyView bindNewtipButtonAction];
                    [self.emptyView.tipButton setTitle:nil forState:UIControlStateNormal];
                    [self.emptyView.tipPictureView setImage:emptyImage];
                    [self.emptyView.tipTitleLable setText:KString(@"无法获取地理位置")];
                    [self.emptyView.tipStringLable setText:KString(@"允许全民K歌访问地理位置，查看同城精彩作品")];
                    self.emptyView.newTipButton.title = KString(@"允许访问");
                    
                    self.emptyView.tipButton.frame = CGRectZero;
                    self.emptyView.tipPictureView.frame = CGRectMake((SCREEN_WIDTH - emptyImage.size.width)/2, 69.5, emptyImage.size.width, emptyImage.size.height);
                    self.emptyView.tipTitleLable.frame = CGRectMake(0, self.emptyView.tipPictureView.bottom + 15, SCREEN_WIDTH, 23.5);
                    self.emptyView.tipStringLable.frame = CGRectMake((SCREEN_WIDTH - 220)/2, self.emptyView.tipTitleLable.bottom + 10, 220, 45);
                    self.emptyView.newTipButton.center = CGPointMake(self.emptyView.tipPictureView.centerX, self.emptyView.tipStringLable.bottom + KSMargin(20) + 0.5 *self.emptyView.newTipButton.height);
                    
                    if (self.nearbySubIndex == NearbyTabBarSubIndex_Dynamic) // 附近动态
                    {
                        [self.mNearbyFeedTableView addSubview:self.emptyView];
                        self.mNearbyFeedTableView.scrollEnabled = NO;

                    } else if (self.nearbySubIndex == NearbyTabBarSubIndex_User) { // 附近的人
                        
                        [self.mNearbyUserTableView addSubview:self.emptyView];
                    }
                    
                    [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                        reportModel.key = @"feed_nearby#unavailable_position_page#null#exposure#0";
                    }];
                } else {
                    [self.emptyView removeFromSuperview];
                    self.mNearbyFeedTableView.scrollEnabled = YES;
                }
            } else {
                [self.emptyView removeFromSuperview];
                self.mNearbyFeedTableView.scrollEnabled = YES;
            }
        }
    }
    
    if (self.currentFilterFeedsInfo.feedsList.count > 0) {
        [self dataLoadFinished];
    }
    
        [self.nearByUserEmptyView removeFromSuperview];
        self.nearByUserEmptyView = nil;
    // 同城下的附近的人
    if ([CLLocationManager authorizationStatus] != kCLAuthorizationStatusDenied) {
        if (self.nearByUserArray.count <= 0) {
            [self.mNearbyUserTableView reloadData];
            if (![[KSNetStatusManager sharedManager] IsEnableInternet]) {
                // 新增无网络的情况
                self.nearByUserEmptyView = [self noNetworkEmptyViewWithDarkMode:NO];
            }
            [self.mNearbyUserTableView addSubview:self.nearByUserEmptyView];
        }
    }
}

- (void)loadFeedsNear {
    [self setupLocation];
}

// 拉取广告免费送礼礼物
- (void)loadFeedADFreeGift:(NSInteger)currentFilterMask {
    if (currentFilterMask == [KSTimelineManager sharedManager].filtrMaskRecommend ||
        currentFilterMask == [KSTimelineManager sharedManager].filtrMaskQQWXFriends ||
        currentFilterMask == [KSTimelineManager sharedManager].filtrMaskFollow ||
        currentFilterMask == [KSTimelineManager sharedManager].filtrMaskUgcFeed ||
        currentFilterMask == [KSTimelineManager sharedManager].filtrMaskUgcFeedQQWX) {
        KSAdFreeGiftScene *followScene = [KSAdFreeGiftScene sceneWithType:KSAdFreeGiftSceneType_FollowFeed];
        if (followScene.scenceShow) {
            [self.feedManager getFeedFreeADGiftInfoWithScene:KSAdFreeGiftSceneType_FollowFeed
                                               completionBlk:^(proto_feed_webapp_GetDataAfterExposureRsp *rsp,
                                                               KSimpleFeedGiftGuide *gift) {
                [KSTimelineManager sharedManager].adFreeGiftBtnInfo = rsp;
                [KSTimelineManager sharedManager].adFreeGift = gift;
            }];
        } else {
            [KSTimelineManager sharedManager].adFreeGiftBtnInfo = nil;
            [KSTimelineManager sharedManager].adFreeGift = nil;
        }
    }
}

#pragma mark - Drag delegate methods
- (void)setupLocation {
    KINFO(@"[feed附近定位]开始定位setupLocation");
    if ([CLLocationManager locationServicesEnabled]) {
        if ([CLLocationManager authorizationStatus] == kCLAuthorizationStatusDenied) {
            [self loadCityInfoByIP:NO];
            //涉及地理位置信息的合规，不能每天请求，甲方需求，没有弹性空间
        }
        else
        {
            if (!self.locationManager)
            {
                self.locationManager = [[CLLocationManager alloc] init];
            }
            _locationManager.delegate = self;
            if ([_locationManager respondsToSelector:@selector(requestWhenInUseAuthorization)])
            {
                [_locationManager requestWhenInUseAuthorization];
                self.locationReqTimes = SYSTEM_UPTIME;
            }
            
            _locationManager.desiredAccuracy = kCLLocationAccuracyBest;
            _locationManager.distanceFilter = 10.0f;
            [_locationManager startUpdatingLocation];
        }
       
    }
    else
    {
        self.locationCoordinate = kCLLocationCoordinate2DInvalid;
        
        if (([KSTimelineManager sharedManager].currentFiltrMask & [KSTimelineManager sharedManager].filtrMaskRecommendNearBy)) {
            [self updateEmptyView];
            [self refreshFinishedWithTableView:self.mNearbyFeedTableView];
        }
        KINFO(@"[feed附近定位]: location permission denied");
    }
    
}

#pragma mark - KSEntertainmentView
+ (void)refreshFeedLiveIfNeeded
{
    if ([KSTimelineManager sharedManager].currentFiltrMask == [KSTimelineManager sharedManager].filtrMaskLive) {
        KSTimelineRootVC *timelineVC = (KSTimelineRootVC *)[[[KSNavigationManager sharedManager] getRootTabBarController] getVCAtIndex:FEED_TAB_INDEX vcClass:[KSTimelineRootVC class]];
        [timelineVC.entertainmentView forceRefreshIfNeeded];
    }
}

#pragma mark - KSEntertainmentViewDelegate
- (void)ksEntertainmentView:(KSEntertainmentView *)entertainmentView scrollViewDidScroll:(UIScrollView *)scrollView
{
    if (self.centerSegControl.selectIndex == KSTimelineTab_Live)
    {
       [self scrollViewDidScroll:scrollView];
    }
    
}

- (void)ksEntertainmentView:(KSEntertainmentView *)entertainmentView scrollViewDidEndDragging:(UIScrollView *)scrollView willDecelerate:(BOOL)decelerate
{
    if (self.centerSegControl.selectIndex == KSTimelineTab_Live)
    {
        [self scrollViewDidEndDragging:scrollView willDecelerate:decelerate];
    }
}

- (void)ksEntertainmentView:(KSEntertainmentView *)entertainmentView scrollViewDidEndDecelerating:(UIScrollView *)scrollView
{
    if (self.centerSegControl.selectIndex == KSTimelineTab_Live)
    {
       [self scrollViewDidEndDecelerating:scrollView];
    }
}

- (void)ksEntertainmentView:(KSEntertainmentView *)entertainmentView processStartMediaPlayIgnoreExpose:(BOOL)ignore
{
    [self processStartMediaPlayIgnoreExpose:ignore otherParams:nil];
}

- (void)ksEntertainmentView:(KSEntertainmentView *)entertainmentView processStopMediaPlayIgnoreExpose:(BOOL)ignore
{
    [self processStopMediaPlayIgnoreExpose:YES when:KSTimelineStopVideoWhenDisappear];
}

- (BOOL)isAtLiveTabForEntertainmentView:(KSEntertainmentView *)entertainmentView
{
    return self.centerSegControl.selectIndex == KSTimelineTab_Live;
}

- (void)changeTabColorIfNeeded:(KSEntertainmentView *)entertainmentView
{
    [self updateTabTitleColorWithIndex:self.centerSegControl.selectIndex];
}

- (void)changeContentScorllViewEnable:(BOOL)enableScroll
{
    self.mContainerScrollView.scrollEnabled = enableScroll;
}

- (void)ksEntertainmentView:(KSEntertainmentView *)entertainmentView switchTabShowLitecard:(BOOL)isLiteCard
{
    if (isLiteCard) {
        [self.navBar setBackgroundColor:UIColor.clearColor];
    } else {
        [self.navBar setBackgroundColor:UIColor.blackColor];
    }
}

#pragma mark - CLLocationManagerDelegate

- (void)locationManager:(CLLocationManager *)manager didChangeAuthorizationStatus:(CLAuthorizationStatus)status
{
    KINFO(@"[feed附近定位]locationManager:(CLLocationManager *)manager didChangeAuthorizationStatus:(CLAuthorizationStatus)status: %d", status);
    switch (status)
    {
        case kCLAuthorizationStatusNotDetermined:
        {
            NSTimeInterval reqLastTime = DURATION_FROM(self.locationReqTimes);//位置请求耗时
            if (reqLastTime> kLocationReqOverTime && self.locationReqTimes>0)
            {
                //bugfix:76876921,避免长时间请求不到位置信息
                KINFO(@"[feed附近定位]请求定位时间超过超过%f",reqLastTime);
                self.locationReqTimes = 0;
                [self loadRecFeedNearByFeed];
                [self loadNearbyUserFromRemoteForIsMore:NO];

            }
            else
            {
//                if ([_locationManager respondsToSelector:@selector(requestWhenInUseAuthorization)])
//                {
//                    [_locationManager requestWhenInUseAuthorization];
//                    KINFO(@"[feed附近定位]locationManager kCLAuthorizationStatusNotDetermined 继续请求位置信息");
//                }
            }
            break;
        }
        case kCLAuthorizationStatusDenied:
        {
            //同城如果拒绝权限的话用ip去请求
            [KSTimelineManager sharedManager].feedsInfo_RecommendIntraCity.feedsList = nil;
            [self loadCityInfoByIP:YES];            
            break;
        }
        case kCLAuthorizationStatusAuthorizedAlways:
        case kCLAuthorizationStatusAuthorizedWhenInUse:
        {
            [self setupLocation];
            break;
        }
            
        default:
            break;
    }
}

- (void)locationManager:(CLLocationManager *)manager didUpdateLocations:(NSArray *)locations
{
    CLLocation *newLocation = locations.lastObject;
    KINFO(@"[feed附近定位]locationManager 定位成功");
    [_locationManager stopUpdatingLocation]; //只有在loadfeed的时候才开启定位
    
    _locationCoordinate.latitude  = newLocation.coordinate.latitude;
    _locationCoordinate.longitude = newLocation.coordinate.longitude;
    self.locationReqTimes = 0.0;
    
    [self loadCityInfo];
    
    // 这里加一个地理位置更新的开关，防止频繁更新附近推荐数据
    // 下面的hasFirstRequestNearByUserList和别的业务有关系，这里先不用了
    if (!self.hasLocationUpdated) {
        [self loadRecFeedNearByFeed];
        self.hasLocationUpdated = YES;
    }
    
    // 此处didUpdateLocations 经常频繁调用 需加开关控制调用频率
    if (!self.hasFirstRequestNearByUserList)
    {
        self.hasFirstRequestNearByUserList = YES;
        [self loadNearbyUserFromRemoteForIsMore:NO];
    }
}

- (void)locationManager:(CLLocationManager *)manager
       didFailWithError:(NSError *)error
{
    self.locationReqTimes = 0.0;
    [manager stopUpdatingLocation];
    KERROR(@"[feed附近定位]Error: %@",[error localizedDescription]);
    //同城下才展示
    switch ([error code]) {
        case kCLErrorDenied:
            KLog(@"[feed附近定位]location permission denied");
            break;
        case kCLErrorLocationUnknown:
            KLog(@"[feed附近定位]无法定位");
            break;
        default:
            KLog(@"[feed附近定位]出现未知错误");
            break;
    }
    // 没有成功定位的时候也发请求
    self.locationCoordinate = kCLLocationCoordinate2DInvalid;
    
    
     if (([KSTimelineManager sharedManager].currentFiltrMask & [KSTimelineManager sharedManager].filtrMaskRecommendNearBy))
     {
         [self loadCityInfoByIP:NO];//如果定位失败就用ip
     }
}

- (void)showPostionAlert
{
    UIAlertView *alertView = [[UIAlertView alloc] initWithTitle:KString(@"需要访问您的位置,请到设置里开启")
                                           message:nil
                                          delegate:self
                                 cancelButtonTitle:KString(@"取消")
                                 otherButtonTitles:KString(@"设置"),nil];
    alertView.tag = 303;
    [alertView show];
}

- (void)loadCityInfo
{
    JceProtoLbs_GPS * gps  = [[JceProtoLbs_GPS alloc] init];
    gps.jce_fLat = _locationCoordinate.latitude;
    gps.jce_fLon = _locationCoordinate.longitude;
    
    KSProtocolBase *pro = [[KSProtocolBase alloc] init];
    JceProtoLbs_GetGeoInfoReq *req = [[JceProtoLbs_GetGeoInfoReq alloc] init];
    req.jce_stGps = gps;
    KS_WEAK_SELF(self);
    [pro startWorkWithCommand:CMD_GET_GEO_INFO reqObject:req rspClass:@"JceProtoLbs_GetGeoInfoRsp" rspBlock:^(WnsProtocolBase *workProtocolObj, NSInteger workResult) {
        KS_STRONG_SELF(self);
        if (!self) return;
        
        if (PROTOCOL_SUCCESS(workResult))
        {
            self.getCityInfoFailed = NO;
            JceProtoLbs_GetGeoInfoRsp *rsp = ((JceProtoLbs_GetGeoInfoRsp*)workProtocolObj.rspObject);
            self.curCityInfo = rsp;
            if (rsp)
            {
                if ([@"Unknown" isEqualToString:rsp.jce_stGeoInfo.jce_strCity])
                {
                    self.curCityName = @"同城";
                    
                }
                else
                {
                    self.curCityName = rsp.jce_stGeoInfo.jce_strCity;
                    if ([self.curCityName hasSuffix:@"市"])
                    {
                        self.curCityName = [self.curCityName substringToIndex:self.curCityName.length - 1];
                    }
                }
            }
        }
        else
        {
            self.getCityInfoFailed = YES;
            self.curCityName = @"同城";
            NSError *error = [KSErrorHandler errorObjWithProtocolResult:workProtocolObj];
            KLog(@"[同城]GPS获取地理位置信息失败： %@",error);
        }
        if (IS_EMPTY_STR_BM(self.curCityName) || self.curCityName.length > 3)
        {
           self.curCityName = @"同城";
        }
        NSInteger index = KSTimelineTab_RecNearBy;
        [self.centerSegControl updateTitleViewText:index withString:self.curCityName];
    }];
}

//同城没有位置权限情况下用ip获取地理位置, 获取完才请求数据，这边跟之前流程不一样需要串行
- (void)loadCityInfoByIP:(BOOL)isFirstLoaded
{
    JceProtoLbs_GetLbsCityByIpReq *ipReq = [[JceProtoLbs_GetLbsCityByIpReq alloc] init];
    
    KSProtocolBase *pro = [[KSProtocolBase alloc] init];
    
    KS_WEAK_SELF(self);
    [pro startWorkWithCommand:CMD_GET_LBS_CITY reqObject:ipReq rspClass:@"JceProtoLbs_GetLbsCityByIpRsp" rspBlock:^(WnsProtocolBase *workProtocolObj, NSInteger workResult) {
        KS_STRONG_SELF(self);
        if (!self) return;
        
        if (PROTOCOL_SUCCESS(workResult))
        {
            self.getCityInfoFailed = NO;
            JceProtoLbs_GetLbsCityByIpRsp *rsp = ((JceProtoLbs_GetLbsCityByIpRsp*)workProtocolObj.rspObject);
            if (rsp)
            {
                _locationCoordinate.latitude  = rsp.jce_stCityGps.jce_fLat;
                _locationCoordinate.longitude = rsp.jce_stCityGps.jce_fLon;
                self.locationReqTimes = 0.0;
                
                if (!self.hasFirstRequestNearByUserList) {
                    self.hasFirstRequestNearByUserList = YES;
                }
                if ([@"Unknown" isEqualToString:rsp.jce_stCityGeoInfo.jce_strCity])
                {
                    self.curCityName = @"同城";
                    
                }
                else if ([[KSPrivateProtectLocationManager sharedManager] hasLocationRight])
                {
                    self.curCityName = rsp.jce_stCityGeoInfo.jce_strCity;
                    if ([self.curCityName hasSuffix:@"市"])
                    {
                        self.curCityName = [self.curCityName substringToIndex:self.curCityName.length - 1];
                    }
                }
                else {
                    self.curCityName = @"同城";
                }
            }
        }
        else
        {
            self.locationCoordinate = kCLLocationCoordinate2DInvalid;
            self.getCityInfoFailed = YES;
            self.curCityName = @"同城";
            NSError *error = [KSErrorHandler errorObjWithProtocolResult:workProtocolObj];
            KLog(@"[同城]IP获取地理位置信息失败： %@",error);
        }
        if (IS_EMPTY_STR_BM(self.curCityName) || self.curCityName.length > 3)
        {
           self.curCityName = @"同城";
        }
        NSInteger index = KSTimelineTab_RecNearBy;
        [self.centerSegControl updateTitleViewText:index withString:self.curCityName];
        [self loadRecFeedNearByFeed];
        [self loadNearbyUserFromRemoteForIsMore:NO];

        if (isFirstLoaded && !self.getCityInfoFailed)
        {
            [KSAlertManager tempAlert:WnsSwitchStringConfig(@"ShowNearByIPPositionDefaultText")];
        }
    }];
}

- (void)stopNearByLoading
{
    dispatch_async(dispatch_get_main_queue(), ^{
        [self refreshFinishedWithTableView:self.mNearbyUserTableView];
        [self loadMoreFinishedWithTableView:self.mNearbyUserTableView];
        [self refreshFinishedWithTableView:self.mNearbyFeedTableView];
        [self loadMoreFinishedWithTableView:self.mNearbyFeedTableView];
    });
}

//同城推荐feed
- (void)loadRecFeedNearByFeed {
    // 当前filter类型 & filtrMaskRecommendNearBy
    
    // 获取同城筛选配置 附近动态和附近的人共用
    self.nearbySettingModel = [self loadNearbyFilterSettingFromLocal];
    
    if (([KSTimelineManager sharedManager].currentFiltrMask & [KSTimelineManager sharedManager].filtrMaskRecommendNearBy)) {
        [self loadNearbyListFromRemote];
        [self loadNearbyBubbleInfoFromRemote];
    }
}
 
- (void)playUGCInBackground:(KSLayoutableTableCell*)cell
{
    // 打开全局播放浮窗
    [[KSGlobalPlayFloatingWindowManager sharedManager] showGlobalPlayFloatingWindow];
    //引导送礼
    [[KSGuideBubbleManager sharedManager] preConditionShowGiftGuide];
    [self showGiftGuideView:cell];
}


//拉取个人信息，触发送鲜花逻辑
- (void)loadUserInfo
{
    KS_WEAK_SELF(self);
    [[KSLoginManager sharedInstance] getUserInfo:[KSLoginManager sharedInstance].curUserInfo.userId mask:JceProfile_PROFILE_MASK_E_FLOWER | JceProfile_PROFILE_MASK_E_GROUP | JceProfile_PROFILE_MASK_E_AUTH | JceProfile_PROFILE_MASK_E_HAS_PUB_UGC usecache:YES completeBlock:^(id ksObject, NSDictionary *customInfo, NSError *error) {
        
        KS_STRONG_SELF(self);
        if (!self) return ;
        
        if (!error)
        {
            // 将鲜花保存一下
            KSUserInfo* userInfo = ksObject;
            [KSLoginManager sharedInstance].curUserInfo.bHasPubUgcs = userInfo.bHasPubUgcs;
            [[KSFlowerManager sharedManager] storeMyFlowers:[userInfo uFlowerNum]];
            
            //将是否加入家族保存下来
            if ([WnsConfigManager sharedInstance].appConfig.kSongSetting.enableGroupFeed)
            {
                self.isAuthGroup = userInfo.sAuthGroup.length > 0 ? YES : NO;
            }
            else
            {
                self.isAuthGroup = NO;
            }
        }
        else if (error.code == kProfileNeedVerify)
        {
//            NSString *url = [NSString stringWithFormat:@"%@",SAFE_STR_BM([error.userInfo objectForKey:@"errorMgr"])];
//            [self openWebviewWithVerifyUrlAndOnlyToast:url];
        }
        else
        {
            [self onMgrError:error];
        }
    }];
    
    
}

/**
  获取当前用户的avsdk角色
 */
- (void)loadAVSdkRole
{
    [[KSLiveShowManager sharedManager] loadAVSdkRoleReqWithAnchorUid:[KSLoginManager sharedInstance].curUserInfo.userId completionBlock:nil];
}


- (void)updateMutilFormsCommentPics
{
    KSPageableList *cacheList = [KSTimelineDetailManager sharedManager].mutilFormsCommentPicList;
    NSUInteger cacheTime = 0;
    if (cacheList)
    {
        cacheTime = [[cacheList.additionalListData objectForKey:kCommonConfigDataKey_MutilFormsComment_CacheTime] unsignedIntegerValue];
    }
    
    [[KSTimelineDetailManager sharedManager] getUgcCommentPicListWithCacheTime:cacheTime WithCompletion:nil];
}

/**
 新手大礼包，注册用户初次登录弹出，优先级最高
 */
- (void)timeWelcomeGift {
    
    // 需要不展示oneshot中
    if ([[KSSplashAdManager sharedManager] isInOneShotProcess]) {
        return;
    }
    
    KSProtocolBase *protocol = [[KSProtocolBase alloc] init];
    JceBadgeUpdate_GetPromptUrlReq *req = [[JceBadgeUpdate_GetPromptUrlReq alloc] init];
    req.jce_uUid = (int32_t)[KSLoginManager sharedInstance].curUserInfo.userId;
    req.jce_uFirstLogin = [KSLoginManager sharedInstance].curUserInfo.isFirstLogin;
    
//#ifdef DEBUG
//    KSWelcomeGiftWebAlertView *welcomeView = [[KSWelcomeGiftWebAlertView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT)];
//    welcomeView.loadUrl = @"http://kg.qq.com/activity/novicedialog/index.html?_wv=1";
//    [welcomeView show];
//#endif
    KS_WEAK_SELF(self);
    [protocol startWorkWithCommand:KG_GET_PROMPT_URL_CMD
                         reqObject:req
                          rspClass:NSStringFromClass([JceBadgeUpdate_GetPromptUrlRsp class])
                          rspBlock:^(WnsProtocolBase *workProtocolObj, NSInteger workResult) {
                              
                              KS_STRONG_SELF(self);
                              if (self == nil) {
                                  return;
                              }
                              
                              if (PROTOCOL_SUCCESS(workResult)) {
                                  
                                  JceBadgeUpdate_GetPromptUrlRsp *rsp = (JceBadgeUpdate_GetPromptUrlRsp *)workProtocolObj.rspObject;
                                  
                                  if (!rsp.jce_strUrl || rsp.jce_strUrl.length == 0) {
                                      
                                      return;
                                      
                                  } else {
                                      
                                      self.hasShownWelcomeGift = YES;
                                      if (rsp.jce_uPromptUrlType == JceBadgeUpdate_emPromptUrlType_PROMPT_URL_TYPE_FULL_SCREEN) {

                                          [[KSNavigationManager sharedManager] showWebView:rsp.jce_strUrl];

                                      } else if (rsp.jce_uPromptUrlType == JceBadgeUpdate_emPromptUrlType_PROMPT_URL_TYPE_NOT_FULL_SCREEN) {
                                          KSWelcomeGiftWebAlertView *welcomeView = [[KSWelcomeGiftWebAlertView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT)];
                                          welcomeView.loadUrl = rsp.jce_strUrl;
                                          [welcomeView show];
                                      }
                                  }
                              } else {
                                  KErrLog(@"修改用户kgid失败 error:%@", workProtocolObj);
                              }
                          }];

}

#pragma mark - DismissMaskViewDelegate
- (void)dismissMaskView:(NSString*)maskViewType
{
    
}

#pragma mark - EmptyViewDelegate
- (void)tapView {
    KLog(@"网络点击重试");
    [self didTriggerRefresh];
}

- (void)tipBtnDidClick:(UIButton *)Btn
{
    if (Btn.tag == 0) {
        [self ksNavigateTipViewDidSelected:self.navigateTipView.type];
    }
    else if (Btn.tag == 1 || Btn.tag == 2)
    {
        [[KSNavigationManager sharedManager] showOrderSongRoot];
        
        if (Btn.tag == 1)
        {
            //关注feeds-为空引导点唱入口点击
            [KSTraceReportHelper viewActionWithBlock:^(TraceReportView *info) {
                info.reserves = ReportSubactionL3_DongTaiPage_Click_GuanZhu_Empty_DianGeTai_Entry;
            }];
        }
        else if (Btn.tag == 2)
        {
            //好友feeds-为空引导点唱入口点击
            [KSTraceReportHelper viewActionWithBlock:^(TraceReportView *info) {
                info.reserves = ReportSubactionL3_DongTaiPage_Click_HaoYou_Empty_DianGeTai_Entry;
            }];
        }
    }
    else if (Btn.tag == 999)
    {
        [self showPostionAlert];
    }
}

//如果详情页用户屏蔽了某个用户，回调刷新
- (void)onBlockUid:(KSUidType)blockUid
{
    NSArray *feedsArray;
    if ([self getCurTab] == KSTimelineTab_Follow) {
        feedsArray = [KSTimelineManager sharedManager].feedsInfo_ForFollow.feedsList;
    }
    else if ([self getCurTab] == KSTimelineTab_QQWXFriends) {
        feedsArray = [KSTimelineManager sharedManager].feedsInfo_ForQQWXFriends.feedsList;
    }
    
    if (feedsArray) { //  有数据，需要筛选
        NSMutableArray *filtersArr = [NSMutableArray array];
        for (KSimpleFeed *feed in feedsArray) {
            if (feed.forwardFeed) { // 是转发的
                if (feed.forwardFeed.feedUserInfo.userinfo.userId != blockUid && feed.simpleUser.userinfo.userId != blockUid) {
                    [filtersArr addObject:feed];
                }
            }
            else {
                if (feed.simpleUser.userinfo.userId != blockUid) {
                    [filtersArr addObject:feed];
                }
            }
        }
        if ([self getCurTab] == KSTimelineTab_Follow) {
            [KSTimelineManager sharedManager].feedsInfo_ForFollow.feedsList = filtersArr;
        }
        else if ([self getCurTab] == KSTimelineTab_QQWXFriends) {
            [KSTimelineManager sharedManager].feedsInfo_ForQQWXFriends.feedsList = filtersArr;
        }
        [self reloadTable];
    }
}

//子类重写该key
- (NSString*)getVCExposeDurationKey
{
    NSString* reportKey = [KSTimelineRootVC getTimeLineRootReportKeyWithAction:KSTimelineRootVCViewExposeDuration simpleFeed:nil];
    return reportKey;
}

// 子类自定义上报字段
- (void)updateExposeReportModel:(KSTraceReportModel_V2 *)reportModel {
    if ([reportModel.key isEqualToString:@"feed_topic#all_module#null#duration_browse#0"])
    {
        reportModel.commonStr6 = [[KSUIABTestManager sharedManager] getAllTestIdsWithModuleId:ksUIABTest_Module_TopicFeedTab contentTestId:nil];
    }
}

- (BOOL)isViewFirstAppear
{
    return  self.viewFirstAppear;
}

#pragma mark - KSCommonScrollCellDelegate
- (CGFloat)getVisibleRateWithCell:(UITableViewCell*)tableCell
{
    if ([self.contentTableView isKindOfClass:[KSUITableView class]])
    {
        KSUITableView *ksTableView = (KSUITableView *)self.contentTableView;
        return [ksTableView calculateCellVisibleRate:ksTableView cell:tableCell];
    }
    return  0.0;
    
}


#pragma mark - Private Methods

- (void)setAudioSessionCategoryIfNeed
{
    if (self.viewState != viewStateAppeared) {
        return;
    }
    
    if (![self isRecCardFeedTab]) {
        return;
    }
    
    /// 设置AudioSession
    [KSAppDelegate setAudioSessionPlayCategory];
}

- (void)reportSiriShortCut
{
    NSUserActivity *userActivity = [[NSUserActivity alloc] initWithActivityType:@"com.tencent.KSong.playSong"];
    if (@available(iOS 9.0, *))
    {
        userActivity.eligibleForSearch = YES;
    }
#if __IPHONE_OS_VERSION_MAX_ALLOWED >= 120000
    if (@available(iOS 12.0, *))
    {
        userActivity.eligibleForPrediction = YES;
    }
#endif
    userActivity.title = @"播放歌曲";
    userActivity.userInfo = @{@"testKey" : @"testValue"};
    
    self.userActivity = userActivity;
}

- (BOOL)shouldShowShareGuideBar {
    
    //1. 没有上传完成的feed
    if (self.shareSimpleFeeds.count == 0) {
        return NO;
    }
    
    //2. 有刚上传完成的feed,且有feed.isShowShare == yes
    __block BOOL showShare = NO;
    [self.shareSimpleFeeds enumerateObjectsUsingBlock:^(id  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        
        KSimpleFeed *feed = (KSimpleFeed *)obj;
        if (feed.isShowShare == YES) {
            showShare = YES;
            *stop = YES;
        }
    }];
    
    if (!showShare) {
        return NO;
    }
    
    NSIndexPath *shareCellIndexPath = [NSIndexPath indexPathForRow:0 inSection:1];
    UITableViewCell *shareCell = [self.contentTableView cellForRowAtIndexPath:shareCellIndexPath];
    //滚出了屏幕 显示引导条
    if (!shareCell) {
        return YES;
    }
    CGRect rect = [shareCell convertRect:shareCell.bounds toView:[UIApplication sharedApplication].delegate.window];
    if (CGRectGetMinY(rect) > self.navBarHeight - 2) {//超过自带引导条
        return NO;
    } else {
        return YES;
    }
}

- (void)didClickQuitBtn{
    self.shouldShowNewUserGuide = NO;
    switch (self.ksNewUserGuideTask.type) {
        case KSNewUserGuideTaskType_hit:
            [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                reportModel.key = ReadAll_Btn_Exit_Click;
            }];
            break;
        default:
            break;
    }
    [[KSNewUserGuideAndGiftManager sharedManager] taskUserDoExit];
}

- (void)onClickCreateLiveShowButton
{
    [[KSNavigationManager sharedManager] showLiveShowEnterVC];
    
    [self traceReportForClickCreateLiveShowButton];
}



//发布带9宫格图片的假feed的过程总下拉刷新可以拉出没有带图片的feed,因为作品已生成，但是9宫格图片还在上传，这时候客户端需要作品100%上传后做去重，只保留shareFeed,删除上传过程过程中下拉的ugc feed
- (void)removeSameFeedFromFeedListCompareWithShareFeed:(NSMutableArray<KSimpleFeed*>*)feedsList
{
    for (ProductUploadTask* uploadTask in self.uploadTaskArray)
    {
        NSString *ugcId = uploadTask.publishContent.ugcId;
        if (ugcId.length > 0 && uploadTask.publishContent.localPicCount > 0)
        {
            [feedsList enumerateObjectsUsingBlock:^(KSimpleFeed* feed, NSUInteger index, BOOL *stop) {
                
                if ([feed.simpleFeedCommon.strFeedId isEqualToString:ugcId])
                {
                    *stop = YES;
                    KLog(@"%@发布过程图文uploadfeed做去重feedId=%@ songName=%@",LogFeed,feed.simpleFeedCommon.strFeedId,feed.songinfo.name);
                    [feedsList removeObject:feed];
                }
            }];
        }
    }
}

/**
 检查是否允许弹出合唱动画
 */
- (BOOL)checkIfAllowPresentGiftChorusAnimation
{
    BOOL allowAnimation = NO;
    UIViewController *topVC = [[KSNavigationManager sharedManager] getTopViewController];
    if ([topVC isKindOfClass:[KSTimelineRootVC class]] ||
        [topVC isKindOfClass:[KSUserProfileVC_V2 class]] ||
        [topVC isKindOfClass:[KSBlankDiscoverVC class]] ||
        [topVC isKindOfClass:[KSSongOrderingRootVC class]] ||
        [topVC isKindOfClass:[KSNotificationViewController class]] )
    {
        allowAnimation = YES;
    }
    return allowAnimation;
}


//推荐feed流需要置顶给到的UGCIDs
- (void)loadRecommendFeedWithUgcIds:(NSString *)ugcIds
{
    if ([self getCurTab] == KSTimelineTab_RecNearBy) {
        /// 刷新同城
        if (!IS_EMPTY_STR_BM(ugcIds)) {
            self.nearByTopUgcIdArr = [ugcIds componentsSeparatedByString:@","];
            [self refreshVC];
        }
        return;
    }
    
    NSMutableDictionary *dic = [[NSMutableDictionary alloc] init];
    KSFeedFreshInfo* freshInfo = [[KSFeedFreshInfo alloc] initWithTiming:TimelineRefreshTiming_SettopUgc];
    [dic setObject:freshInfo forKey:KSRefreshSource];
    if (!IS_EMPTY_STR_BM(ugcIds)) {
        NSArray *arr = [ugcIds componentsSeparatedByString:@","];
        [dic safeSetObject:arr forKey:KSRecommendUgcIds];
    }
    
    [UIView animateWithDuration:0.2 animations:^{
        [self.scrollView setContentOffset:CGPointZero];
    } completion:^(BOOL finished) {
        [self loadFeedsListFromRemote:dic];
    }];
}

// 推荐feed流需要更新歌房大合唱ModuleId
- (void)loadTimelinePartyWithChorusModuleId:(NSString *)moduleId
{
    [[KSTimelineManager sharedManager] loadFastPlayKtvRoom:moduleId completeBlock:^(id ksObject, NSDictionary *customInfo, NSError *error) {

        JceFriendKtv_GetFastPlayKtvRoomRsp *ktvRoomRsp = (JceFriendKtv_GetFastPlayKtvRoomRsp*)ksObject;
        if(ktvRoomRsp == nil) {
            return;
        }
        
        NSString *strJumpUrl =  ktvRoomRsp.jce_stFeedBannerItem.jce_strJumpUrl;
        if (!IS_EMPTY_STR_BM(strJumpUrl)) {
            [[KSNavigationManager sharedManager] dealWithScheme:strJumpUrl];
        }
        KLog(@"[KSTimeline][FastPlayKtv] strJumpUrl:%@",strJumpUrl);
    }];
    
}

-(void)handleDaoJuCompetitionVotes:(KSimpleFeed*) simpleFeed {
    
    KSUserInfo *userInfo = [KSLoginManager sharedInstance].curUserInfo ;
    NSUInteger activityId = simpleFeed.songinfo.activityId;
    
    KS_WEAK_SELF(self);
    [[KSTimelineManager sharedManager] getDaoJuCompetitionVotesGift:simpleFeed block:^(id ksObject, NSDictionary *customInfo, NSError *error) {
        CHECK_SELF_AND_RETURN();

        KSIapGift *iapGift = (KSIapGift*)ksObject;
        if(iapGift == nil) {
            return;
        }
        
        if (iapGift.propsNum > 0){
            KSBaseSendGiftInfo *info = [KSBaseSendGiftInfo createWithGift:iapGift giftCount:1 sendStyle:KSGiftBridgeSendStyleDirectly receiverUid:userInfo.userId statFrom:JceIapGift_CONSUME_LOCATION_E_FEED_CONSUME];
            [self.multiGiftBridgeManager sendGiftWithInfo:info atView:self.view detail:simpleFeed block:^(id  _Nullable ksObject, NSDictionary * _Nullable customInfo, NSError * _Nullable error) {
                CHECK_SELF_AND_RETURN();
                if (error) {
                    [[KSTimelineManager sharedManager] saveDaoJuCompVote:0 withActivityId:activityId];
                }
            }];;
            //更新假的数据
            simpleFeed.layoutInfo = nil;
            simpleFeed.songinfo.uCompetitionPropsVotes += 1;
            [self reloadTable];
        }
        //如果没有票数就弹框引导
        else{
            KS_WEAK_SELF(self);
            UIAlertView *alertView = [[UIAlertView alloc] initWithTitle:@"" message:@"您今天已经没有投票道具了，请先获取投票道具。" delegate:nil cancelButtonTitle:@"取消" otherButtonTitles:@"去领票",nil];
            [alertView showWithCompleteBlock:^(NSInteger buttonIndex) {
                CHECK_SELF_AND_RETURN();
                if(buttonIndex == 1){
                    [[KSNavigationManager sharedManager] showWebView:simpleFeed.songinfo.strCompetitionGetPropsUrl];
                }
            }];
            [alertView show];
        }
    }];
}

- (NSMutableArray *)getDataSourceList
{
    return self.currentFilterFeedsInfo.feedsList;
}

//背景色代码收归
-(void)setupMainVCViewBackgroundColor
{
    self.view.backgroundColor = [UIColor ks_whiteColor];
}

-(UIColor *)getSubviewBackGroundColor
{
    return [UIColor ks_normalBGColor];
}


- (void)scrollToTab:(KSTimelineTabSelectIndex)index
{
    // 这里关注页可能也会被下掉，但是不存在越界问题，先维持原来的逻辑：如果index无效，默认去关注
    NSUInteger realIndex = IsValidTimelineTabIndex(index) ? index : KSTimelineTab_Follow;
    KLog(@"[SegTab]scroll to tab=%ld",(long)realIndex);
    [self.centerSegControl setSelectIndex:realIndex];
    [self scrollTableviewTotop];
}


- (void)selectTab:(KSTimelineTabSelectIndex)index
{
    if (!IsValidTimelineTabIndex(index)) {
        // 如果非法，则不动
        return;
    }
    KLog(@"[SegTab]select tab=%ld",(long)index);
    if ([self.passUserInfo safeObjectForKey:@"filtrMaskQQWXFriends"]) {
        self.followTabCurFiltrMask = [KSTimelineManager sharedManager].filtrMaskQQWXFriends;
    }
    [self.centerSegControl setSelectIndex:index];
    // 动态筛选器
    if ((index == KSTimelineTab_Follow || index == KSTimelineTab_QQWXFriends || index == KSTimelineTab_UgcFeed || index == KSTimelineTab_UgcFeedQQWX)) {
        [self.followFilterView refreshFilterTitle];
    }
    
    if (index == KSTimelineTab_Rec) {
        NSUInteger currentFilterMask = [KSTimelineManager sharedManager].currentFiltrMask;
        // 没有在请求
        if (![self.loadingRequests containsObject:[NSString stringWithFormat:@"%lu_refresh",(unsigned long)currentFilterMask]]) {
            if ([self.passUserInfo count] > 0) {
                [self handleRecJumpSchemaWithPassUserInfo:self.passUserInfo];
                self.passUserInfo = nil;
            }
        }
    }
}

- (KSTimelineTabSelectIndex)getCurTab
{
    NSUInteger selectIndex = self.centerSegControl.selectIndex;
    return selectIndex;
}

- (void)handleRecJumpSchemaWithPassUserInfo:(NSDictionary *)ugcIdsDic {
    NSMutableDictionary* userInfo = [NSMutableDictionary new];
    //这里key不同，需要做一层转换
    NSString* recUgcId = [ugcIdsDic safeObjectForKey:PushNotification_Ugcid];
    if (recUgcId.length > 0) {
        [userInfo setObject:@[recUgcId] forKey:KSRecommendUgcIds];
    }
    NSString* trace_id = SAFE_CAST([ugcIdsDic safeObjectForKey:PushNotification_TraceID], NSString);
    [userInfo safeSetObject:trace_id forKey:KSRecommendTraceId];
    
    NSString *ablum = [ugcIdsDic safeObjectForKey:PushNotification_FeedAblum];
    if (ablum) {
        [userInfo safeSetObject:ablum forKey:KSRecommendFeedAblum];
    }
    
    [self loadFeedsListFromRemote:userInfo];
}

- (void)didABTestUpdate {
    if ([KSABTestManager.sharedManager enableSlideMission]) {
        if (!self.vMissionGestureRecognizer) {
            [self addVMissionGesture];
        }
    }
}

#pragma mark 根据mask返回对应的tableview或者collectionview
- (UIScrollView*)getTableViewWithFilterMask:(NSInteger)currentFiltrMask
{
    
    if (currentFiltrMask == [KSTimelineManager sharedManager].filtrMaskFollow ||
        currentFiltrMask == [KSTimelineManager sharedManager].filtrMaskQQWXFriends ||
        currentFiltrMask == [KSTimelineManager sharedManager].filtrMaskUgcFeed ||
        currentFiltrMask == [KSTimelineManager sharedManager].filtrMaskUgcFeedQQWX ||
        currentFiltrMask == [KSTimelineManager sharedManager].filtrMaskTopic)
    {
        return self.mFollowTableView;
    }
    else if (currentFiltrMask == [KSTimelineManager sharedManager].filtrMaskHot || currentFiltrMask == [KSTimelineManager sharedManager].filtrMaskRecommend)
    {
        if ([KSABTestManager sharedManager].recFeedStyle == KSABTestRecFeedUIType_Collection)
        {
            return self.mHotCollectionView;
        }
        else
        {
            return self.mHotTableView;
        }
        
    }
    else if (currentFiltrMask == [KSTimelineManager sharedManager].filtrMaskNearBy)
    {
        if (self.nearbySubIndex == NearbyTabBarSubIndex_Dynamic) {// 附近动态
            return self.mNearbyFeedTableView;
        }
        else if (self.nearbySubIndex == NearbyTabBarSubIndex_User) { // 附近的人
            return self.mNearbyUserTableView;
        }
    }
    else if (currentFiltrMask == [KSTimelineManager sharedManager].filtrMaskLive)
    {
        return self.entertainmentView.collectionView;
    }
    else if (currentFiltrMask == [KSTimelineManager sharedManager].filtrMaskRecommendNearBy) {
        if (self.nearbySubIndex == NearbyTabBarSubIndex_Dynamic) {// 附近动态
            return self.mNearbyFeedTableView;
        }
        else if (self.nearbySubIndex == NearbyTabBarSubIndex_User) { // 附近的人
            return self.mNearbyUserTableView;
        }
    }
    return nil;
}

- (UIStatusBarStyle)prefersKSongStatusBarStyle
{
    if (self.centerSegControl.selectIndex == KSTimelineTab_Rec)
    {
        return UIStatusBarStyleLightContent;
    }
    else
    {
        return [UIApplication ksDefaultStatusBarStyle];
    }
    return [UIApplication ksDefaultStatusBarStyle];
}

//7.0 将直播红点置为0
- (void)checkShouldResetFeedLiveNum
{
    if ([KSTimelineManager sharedManager].currentFiltrMask == [KSTimelineManager sharedManager].filtrMaskLive)
    {
        [KSBadgeUpdateManager sharedManager].feedLiveNum = 0;
        [self.centerSegControl clearUnreadBadgeWithIndex:KSTimelineTab_Live];
    }
    [[[KSNavigationManager sharedManager] getRootTabBarController] updateFeedTabRedPoint];
}


#pragma mark - Lazy Load

- (KSNotificationUserBannerEntryManager *)feedFriendManager
{
    if (!_feedFriendManager) {
        _feedFriendManager = [KSNotificationUserBannerEntryManager new];
    }
    return _feedFriendManager;
}

- (NSMutableArray *)headFriendInfos
{
    if (!_headFriendInfos) {
        _headFriendInfos = [NSMutableArray array];
    }
    return _headFriendInfos;
}

- (NSMutableSet *)ktvLiveUids
{
    if (!_ktvLiveUids) {
        _ktvLiveUids = [NSMutableSet set];
    }
    return _ktvLiveUids;
}

- (KSTimeSliceManager *)timeSliceManager
{
    if (!_timeSliceManager) {
        _timeSliceManager = [[KSTimeSliceManager alloc] init];
    }
    return _timeSliceManager;
}

- (KSEmptyView *)emptyView
{
    if (!_emptyView)
    {
        _emptyView = [[KSEmptyView alloc] initWithPictureName:@"hotfeed_emptyguide.jpg"
                                                    tipString:KString(@"没有好友痕迹?")
                                                    tipBtnStr:KString(@"去看看推荐吧")
                                               arrowImageName:nil];
        _emptyView.delegate = self;
        _emptyView.backgroundColor = UIColor.whiteColor;
        _emptyView.tipStringLable.numberOfLines = 1;
        [_emptyView.tipButton.layer setCornerRadius:0];
    }
    return _emptyView;
}

- (UIView *)nearByUserEmptyView
{
    if (!_nearByUserEmptyView)
    {
        _nearByUserEmptyView = [[UIView alloc] initWithFrame:self.mNearbyUserTableView.bounds];
        _nearByUserEmptyView.backgroundColor = UIColor.whiteColor;
        
        KSEmptyView *emptyView = [[KSEmptyView alloc] initWithPictureName:@"empty014_icon"
                                                              tipString:KString(@"没有找到附近的人")
                                                              tipBtnStr:nil
                                                        arrowImageName:nil];
        emptyView.backgroundColor = UIColor.whiteColor;
        emptyView.frame = CGRectMake(0, 0, kScreenWidthBM(), 250);
        [emptyView.tipStringLable setFrame:CGRectMake(0, emptyView.tipPictureView.bottom + 20, SCREEN_WIDTH, 30)];
        emptyView.tipStringLable.numberOfLines = 1;
        emptyView.centerY = _nearByUserEmptyView.height/2.0;
        [_nearByUserEmptyView addSubview:emptyView];
        
    }
    return _nearByUserEmptyView;
}


- (UIView *)nearbyDisableEmptyView
{
    if (!_nearbyDisableEmptyView)
    {
        _nearbyDisableEmptyView = [[UIView alloc] initWithFrame:CGRectMake(0, self.nearbyTabBarView.height, self.nearbySuperView.width, self.nearbySuperView.height - self.nearbyTabBarView.height)];
        _nearbyDisableEmptyView.backgroundColor = UIColor.whiteColor;
        
        KSEmptyView *emptyView = [[KSEmptyView alloc] initWithPictureName:@"empty014_icon"
                                                                tipString:KString(@"没有找到附近的人")
                                                                tipBtnStr:nil
                                                           arrowImageName:nil];
        emptyView.backgroundColor = UIColor.whiteColor;
        emptyView.frame = CGRectMake(0, 0, kScreenWidthBM(), 250);
        [emptyView.tipStringLable setFrame:CGRectMake(0, emptyView.tipPictureView.bottom + 20, SCREEN_WIDTH, 30)];
        emptyView.tipStringLable.numberOfLines = 1;
        emptyView.centerY = _nearbyDisableEmptyView.height/2.0;
        [_nearbyDisableEmptyView addSubview:emptyView];
    }
    return _nearbyDisableEmptyView;
}


- (KSEmptyView *)noNetworkEmptyViewWithDarkMode:(BOOL)isDarkMode
{
    EmptyTipType picType = isDarkMode ? EMPTYNONETWORK_NEW_DARK : EMPTYNONETWORK_NEW_LIGHT;
    KSEmptyView *noNetworkView =  [[KSEmptyView alloc] initWithPictureType:picType tipString:NONETWORK_EMPTYVIEW_STRING tipBtnStr:nil];
    noNetworkView.delegate = self;
    if (isDarkMode) {
        noNetworkView.backgroundColor = UIColor.ks_blackColor;
        noNetworkView.tipStringLable.textColor = UIColor.ks_whiteColor;
    } else {
        noNetworkView.backgroundColor = UIColor.ks_whiteColor;
        noNetworkView.tipStringLable.textColor = UIColor.ks_blackColor;
    }
    
    noNetworkView.tipStringLable.numberOfLines = 1;
    [noNetworkView.tipButton.layer setCornerRadius:0];
    return noNetworkView;
}

- (void)hotRelaunchDidFinishedShowAd
{
    [self setAudioSessionCategoryIfNeed];
    
    [self processStartMediaPlayIgnoreExpose:YES otherParams:@{@"autoPlayCaller":@"appEnterForground"}];
}


//每过两小时需要自动化刷新一下大卡片
- (void)checkRecfeedLastExposedTimeStampOverTwoHours {
    if ([self isRecCardTable:self.scrollView]) {
        if (self.lastRecfeedExposeTS <= 0)
        {
            self.lastRecfeedExposeTS = [[NSDate date] timeIntervalSince1970];
            KLog(@"[大卡片] 首次曝光时间 :%f",self.lastRecfeedExposeTS);
            return;
        }
        else
        {
            if (!self.recommendLoadFirstTime && [KSTimelineManager sharedManager].currentFiltrMask  == [KSTimelineManager sharedManager].filtrMaskRecommend)
            {
                BOOL shouldForceUpdate = NO;
                NSTimeInterval currentTS = [[NSDate date] timeIntervalSince1970];
                //2小时
                int delta = (int)(currentTS - self.lastRecfeedExposeTS);
                if (delta >= 7200)
                {
                    shouldForceUpdate = YES;
                }
                KLog(@"[大卡片] 上次曝光时间 :%f 本次曝光时间 :%f 间隔 %ds 需要自动刷新 :%@",self.lastRecfeedExposeTS,currentTS,delta,@(shouldForceUpdate));
                self.lastRecfeedExposeTS = currentTS;
                if (shouldForceUpdate)
                {
                    
                    [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                        reportModel.key = @"feed#reads_all_module#null#auto_refresh#0";
                    }];
                    [self forceRefresh];
                }
            }
        }
    }
}

// 大卡片预拉取列表完成
- (void)handlePrefetchFinishedEvent:(NSNotification *)notification {
    id object = notification.object;
    if ([self isRecCardFeedTab] && !self.hasHandledPretchEvent && [KSTimelineManager sharedManager].feedsInfo_Recommend.feedsList.count > 0 && !self.viewFirstAppear)
    {
        KLog(@"[大卡片预加载] handlePrefetchFinishedEvent object:%@",object);
        if (!object)
        {
            [self handleRecFeedDataPrefetchEvent];
        } else {
            NSMutableDictionary* extarInfo = [NSMutableDictionary new];
            KSFeedFreshInfo* freshInfo = [[KSFeedFreshInfo alloc] initWithTiming:TimelineRefreshTiming_PrefectFail];
            [extarInfo setObject:freshInfo forKey:KSRefreshSource];
            [self loadFeedsListFromRemote:extarInfo];
        }
    }
}

#pragma mark - KSOneShotBigCardDelegate
- (void)oneShotNotInterested
{
    [[KSTimelineManager sharedManager] removeOneShotFeed];
    [self reloadTable];
    [self processStartMediaPlayIgnoreExpose:YES otherParams:@{@"autoPlayCaller":@"reloadTable"}];
}

- (JceTimeline_GPS *)getCurrentGPSInfo {
    JceTimeline_GPS * gps  = [[JceTimeline_GPS alloc] init];
    gps.jce_fLat = _locationCoordinate.latitude;
    gps.jce_fLon = _locationCoordinate.longitude;
    return gps;
}


#pragma mark KSPIPShowPendingVCDelegate methods
- (BOOL)pipStaleWhenPendingVCAppear
{
    if (self.centerSegControl.selectIndex == KSTimelineTab_Live) {
        if ([KSABTestManager sharedManager].liveTabType == KSLiveTabTypeNewNative) {
            return YES;
        }
        return NO;
    } else {
        return YES;
    }
}

#pragma mark - 歌房大卡片进房引导

- (void)initKtvEnterRoomGuide {
    // 进房按钮强化走WNS
    NSInteger wnsGuideType = WnsSwitchIntegerConfig(kWnsKtvBigCardBtnEnhanceType);
    NSNumber *wnsLocalGuideType = [KSLocalConfigManager dailyCommonValueConfig:NSUserDefaults_ktvBigCardGuideShowType isGolbal:NO];
    
    if (!wnsLocalGuideType || ![wnsLocalGuideType isEqualToNumber:@(wnsGuideType)]) {
        [KSLocalConfigManager dailySaveLocalConfig:NSUserDefaults_ktvBigCardGuideShowType commonValue:@(wnsGuideType) isGolbal:NO];
        NSInteger wnsGuideTimes = WnsSwitchIntegerConfig(kWnsKtvBigCardBtnEnhanceTimes);
        [KSLocalConfigManager dailySaveLocalConfig:NSUserDefaults_ktvBigCardGuideShowTimes commonValue:@(wnsGuideTimes) isGolbal:NO];
    }
    
    // 自动进房走AB
    NSNumber *abGuideType = @([[KSABTestManager sharedManager] queryfeedKtvEnterGuideType]);
    NSNumber *abSLocalGuideType = [KSLocalConfigManager dailyCommonValueConfig:NSUserDefaults_feedktvBigCardAutoEnterRoomType isGolbal:NO];
    
    if (!abSLocalGuideType || ![abSLocalGuideType isEqualToNumber:abGuideType]) {
        [KSLocalConfigManager dailySaveLocalConfig:NSUserDefaults_feedktvBigCardAutoEnterRoomType commonValue:abGuideType isGolbal:NO];
        [KSLocalConfigManager dailySaveLocalConfig:NSUserDefaults_feedKtvBigCardAutoEnterRoomTimes commonValue:@([[KSABTestManager sharedManager] queryfeedKtvEnterGuideTimes]) isGolbal:NO];
    }
}

- (void)clearCurLiveFeedGameInfo
{
    if (!self.mHotTableView || [self getCurTab] != KSTimelineTab_Rec) {
        return;
    }
    KSFilterFeedsInfo *filterFeedsInfo = [self getFeedDataListByScollView:self.mHotTableView];
    NSArray <KSimpleFeed *> *feedsArray = filterFeedsInfo.feedsList;
    KSimpleFeed *simpleFeed = [KSComHelper getObjectInArray:feedsArray byIndex:self.curRecFeedRowIndex ofClassType:[KSimpleFeed class] defaultValue:nil];
    simpleFeed.liveShow.gameDispatch = nil;
}

#pragma mark - shortPlayHippyVC
// 手动记录shortPlayHippyVC的显示与消失，并调用inform通知hippy
- (void)informSquareHippyVCAppear {
    if (self.centerSegControl.selectIndex != KSTimelineTab_ShortPlay) {
        return;
    }
    
    [self.shortPlayHippyVC setVisible:YES];
}

- (void)informSquareHippyVCDisappear {
    [self.shortPlayHippyVC setVisible:NO];
}

#pragma mark - novelPageHippyVC
- (void)informNovelPageHippyVCAppear {
    if (self.centerSegControl.selectIndex != KSTimelineTab_Novel) {
        return;
    }
    
    [self.novelPageHippyVC setVisible:YES];
}

- (void)informNovelPageHippyVCDisappear{
    [self.novelPageHippyVC setVisible:NO];
}

#pragma mark - activityPageHippyVC
- (void)informActivityPageHippyVCAppear
{
    if (self.centerSegControl.selectIndex != KSTimelineTab_Activity) {
        return;
    }
    
    KSTimelineTabHippyVC *hippyVC = SAFE_CAST(self.activityTabVC, KSTimelineTabHippyVC);
    if (hippyVC) {
        [hippyVC setVisible:YES];
    }
}

- (void)informActivityPageHippyVCDisappear
{
    KSTimelineTabHippyVC *hippyVC = SAFE_CAST(self.activityTabVC, KSTimelineTabHippyVC);
    if (hippyVC) {
        [hippyVC setVisible:NO];
    }
}

#pragma mark - gameCenterPageHippyVC

- (void)informGamePageHippyVCAppear {
    [self.gameCenterPageHippyVC setVisible:YES];
}

- (void)informGamePageHippyVCDisappear{
    [self.gameCenterPageHippyVC setVisible:NO];
}

#pragma mark - 广告 -
- (KSRewardAdManager *)rewardManager
{
    if (!_rewardManager) {
        _rewardManager = [KSTimelineManager sharedManager].rewardManager;
        _rewardManager.delegate = self;
    }
    return _rewardManager;
}

- (void)onRewardAdFloatViewClosed
{
    [self onFeedRewardAdClosedWithCategory];
}

- (void)onRewardAdFloatViewClickShow
{
    [self onFeedRewardAdShowWithCategory];
}


- (void)startFeedAdGoldTimeSliceWithName:(NSString *)name
{
    [self.timeSliceManager removeTimeSliceWithName:name];
}

- (BOOL)shouldChangeToDark
{
    if (WnsLocalServerBoolConfig(@"disableRecommendDarkMode")) {
        return NO;
    }
    return [self getCurTab] == KSTimelineTab_Rec;
}

#pragma mark - Animation
- (void)showVMissionSlideGuideAnim {
    if (self.shouldNotShowVMissionGuide
        || [self getCurTab] != 0    // 这里改为必须是在物理上的第一个tab才展示任务中心动画
        || ![KSABTestManager.sharedManager enableSlideMission]
        || self != [KSNavigationManager.sharedManager getTopViewController]) {
        return;
    }
    self.shouldNotShowVMissionGuide = YES;
    if ([KSABTestManager.sharedManager enableSlideMission]) {
        NSInteger limit = [KSABTestManager.sharedManager slideMissionGuideCount];
        __block NSInteger cnt = LocalIntegerConfig(kFeedSlideMissionGuideCount);
        if (cnt < limit) {
            KS_WEAK_SELF(self);
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                CHECK_SELF_AND_RETURN();
                cnt++;
                SaveLocalIntegerConfig(kFeedSlideMissionGuideCount, cnt);
                
                KINFO(@"[FeedVMission][guide] 展示次数 %zd  上限%zd", cnt, limit);
                CGFloat scale = 938.0 / 120;
                CGFloat height = self.mHotTableView.height - 200;
                CGFloat width = height / scale; // 保证非0
                KSFeedVMissionSlideGuideView *guideView = [KSFeedVMissionSlideGuideView.alloc initWithFrame:CGRectMake(0, 0, width, height)];
                guideView.centerY = self.mHotTableView.height / 2;
                [self.mHotTableView addSubview:guideView];
                [guideView play];
            });
        }
    }
}

/// 获取侧滑能进入到任务中心的合适的tabView，用于手势的绑定
/// v9.6： 任务中心的侧滑手势应该绑定物理上的第一个子tab
- (UIView *)getValidTabViewForVMissionSlideGuide {
    
    // 第一个子tab
    KSTimelineTabSelectIndex tabIndex = 0;
    
    if (tabIndex == KSTimelineTab_Rec) {
        // 推荐
        return self.mHotTableView;
        
    } else if (tabIndex == KSTimelineTab_Follow) {
        // 关注
        return self.mFollowTableView;
        
    } else if (tabIndex == KSTimelineTab_Live) {
        // 直播
        return self.entertainmentView;
        
    } else if (tabIndex == KSTimelineTab_Novel) {
        // 小说
        return self.novelPageTableView;
        
    } else if (tabIndex == KSTimelineTab_Shop) {
        // 商城
        return self.mShopTableView;
        
    } else if (tabIndex == KSTimelineTab_ShortPlay) {
        // 短剧
        return self.shortPlayTableView;
        
    } else if (tabIndex == KSTimelineTab_RecNearBy) {
        // 附近/同城
        return self.nearbyScrollContainerView;
        
    } else if (tabIndex == KSTimelineTab_Activity) {
        // 活动
        return self.mActivityTableView;
    } else if (tabIndex == KSTimelineTab_GameCenter) {
        // 活动
        return self.mGameCenterTableView;
    } else {
        NSAssert(NO, @"[getTabViewByTabIndex] 传入了非法的tabIndex");
        return nil;
    }
}

- (void)addVMissionGesture {
    
    // 这个地方任务中心的侧滑手势应该绑定物理上的第一个子tab
    UIView *targetView = [self getValidTabViewForVMissionSlideGuide];
    if (targetView == nil) {
        return;
    }
    KSFeedVMissionGestureRecognizer *recognizer = [KSFeedVMissionGestureRecognizer.alloc initWithSuperview:targetView];
    KS_WEAK_SELF(self);
    recognizer.acquireURL = ^NSString *{
        KS_STRONG_SELF(self);
        if (self == nil) {
            return nil;
        }
        NSString *jumpUrl;
        if (!self.feedTaskEntranceRsp || self.feedTaskEntranceRsp.jce_strJumpUrl.length == 0) {
            jumpUrl = WnsUrlStringConfig(@"TaskHome");
            [self refreshTaskInfo];
        } else {
            jumpUrl = self.feedTaskEntranceRsp.jce_strJumpUrl;
        }
        return jumpUrl;
    };
    self.vMissionGestureRecognizer = recognizer;
}

#pragma mark - Memory

- (void)didReceiveMemoryWarning
{
    [super didReceiveMemoryWarning];
    [self cleanResidentPage];
}

- (void)cleanResidentPage {
    NSArray *cleanArray = WnsLocalServerArrayConfig(@"FeedEnableCleanPageList");
    KINFO(@"[Feed][Memory] 清理页面 %@", cleanArray);
    if ([cleanArray containsObject:@"playlet"]) {
        [self cleanShortPlayTabView];
    }
    if ([cleanArray containsObject:@"novel"]) {
        [self cleanNovelPageTabView];
    }
    if ([cleanArray containsObject:@"shop"]) {
        [self cleanShopPageTabView];
    }
    
    if (self.centerSegControl.selectIndex != KSTimelineTab_Follow && !WnsLocalServerBoolConfig(@"DisableFollowCellRestore")) {
        [self.feedManager restoreInvisibleTableViewCell:self.mFollowTableView];
    }
}


@end

