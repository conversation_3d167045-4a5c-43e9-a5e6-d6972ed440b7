//
//  KSSinglesDetailBottomView.m
//  QQKSong
//
//  Created by fangpeng on 2019/12/25.
//  Copyright © 2019 Tencent. All rights reserved.
//
//  详情页底部栏

#import "KSSinglesDetailBottomView.h"

#import "UIButton+VipBadge.h"
#import "KSCommonUIFactory.h"
#import "KSFreeHeartAnimationView.h"
#import "KSAudioCommentSwitcher.h"
#import "KSVoiceQuickSingingTouchView.h"

#import "KSBaseDetailVC.h"
#import "KSShareHelper.h"
#import "KSGiftHelper.h"
#import "KSABTestManager.h"
#import "KSTraceReportModel_V2.h"
#import "KSSingleDetailBottomRankView.h"
#import "KSHotCardCarouselView.h"
#import "PROTO_UGC_WEBAPP_HeatCardInfo.h"
#import "KSTimelineDetail.h"
#import "KSUserInfo.h"
#import "KSHotCradAdFreeView.h"
#import "KSGiftFreeAdView.h"
#import "PROTO_UGC_WEBAPP_MiniHeatCard.h"
// 9.11 看广告免费送礼迭代实验
#import "KSAdFreeGiftManager.h"
#import "proto_quick_gift_webapp_QueryQuickSendInfoWebRsp.h"
#import "WnsConfigManager.h"
#import "UIImageView+KSImageLoader.h"

/// 配置类
@implementation KSBottomButtonConfig

+ (instancetype)configWithOutlineIcon:(NSString *)outlineIcon title:(NSString *)title
{
    return [self configWithOutlineIcon:outlineIcon title:title tag:0];
}

+ (instancetype)configWithOutlineIcon:(NSString *)outlineIcon title:(NSString *)title tag:(NSInteger)tag
{
    KSBottomButtonConfig *config = [[KSBottomButtonConfig alloc] init];
    config.outlineIcon = outlineIcon;
    config.title = title;
    config.tag = tag;
    return config;
}

+ (instancetype)configWithVGIcon:(NSString *)vgIcon title:(NSString *)title tag:(NSInteger)tag
{
    KSBottomButtonConfig *config = [[KSBottomButtonConfig alloc] init];
    config.vgIcon = vgIcon;
    config.title = title;
    config.tag = tag;
    return config;
}

+ (instancetype)configWithImageName:(NSString *)imageName title:(NSString *)title tag:(NSInteger)tag
{
    KSBottomButtonConfig *config = [[KSBottomButtonConfig alloc] init];
    config.iconImageName = imageName;
    config.title = title;
    config.tag = tag;
    return config;
}

- (nonnull id)copyWithZone:(nullable NSZone *)zone
{
    KSBottomButtonConfig *copy = [[[self class] allocWithZone:zone] init];
    copy.iconImageName = self.iconImageName;
    copy.title = self.title;
    copy.tag = self.tag;
    return copy;
}

@end



@interface KSBottomButton ()

@property (nonatomic, strong) UIImageView *customImageView;
@property (nonatomic, strong) UIImageView *subscriptImageView;
@property (nonatomic, strong) UILabel *customLabel;
@property (nonatomic, assign) CGSize imageSize;
@property (nonatomic, assign) CGSize subscriptImageSize;
@property (nonatomic, assign) CGSize labelSize;

@end

static  BOOL isZoom()
{
    CGFloat nativeScale = [[UIScreen mainScreen] nativeScale];
    CGFloat mainScale = [[UIScreen mainScreen] scale];
    return fabs(mainScale - nativeScale) > FLT_EPSILON;
}

CGFloat kShortGiftRankViewWidth()
{
    return isZoom() ?118:_size_mid(118);
}
CGFloat const kLongGiftRankViewWidth()
{
    return isZoom() ?160:_size_mid(160);
}
CGFloat const kGiftRankViewTopMargin = 10;

@implementation KSBottomButton

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        [self setupUI];
    }
    return self;
}

- (void)setupUI
{
    
}

- (void)setTitle:(NSString *)title
{
    if (title.length > 0 && _title.length == 0)
    {
        [self addSubview:self.customLabel];
    }
    else if (_title.length > 0 && title.length == 0)
    {
        [self.customLabel removeFromSuperview];
    }
    _title = title;
    
    self.customLabel.text = title;
    CGSize size = [self.customLabel sizeThatFits:CGSizeMake(CGFLOAT_MAX, CGFLOAT_MAX)];
    self.labelSize = CGSizeMake(ceil(size.width), ceil(size.height));
    [self resetSize];
    [self relayout];
}

- (void)setImage:(UIImage *)image
{
    if (image && !_image)
    {
        [self addSubview:self.customImageView];
    }
    else if (_image && !image)
    {
        [self.customImageView removeFromSuperview];
    }
    _image = image;
    self.customImageView.image = image;
    self.imageSize = image.size;
    [self resetSize];
    [self relayout];
}

- (void)setSubscriptImage:(UIImage *)subscriptImage
{
    if (subscriptImage && !_subscriptImage)
    {
        [self addSubview:self.subscriptImageView];
    }
    else if (_subscriptImage && subscriptImage)
    {
        [self.subscriptImageView removeFromSuperview];
    }
    _subscriptImage = subscriptImage;
    self.subscriptImageView.image = subscriptImage;
    self.subscriptImageSize = subscriptImage.size;
    [self relayout];
}

- (void)setPreferSize:(CGSize)preferSize
{
    _preferSize = preferSize;
    [self resetSize];
    [self relayout];
}

- (void)resetSize
{
    CGSize preserSize;
    preserSize.width = self.labelSize.width + self.imageSize.width + ((self.title.length > 0 && self.image) ? 4 : 0);
    preserSize.height = MAX(self.labelSize.height, self.imageSize.height);
    preserSize.height = MAX(preserSize.height, self.preferSize.height);
    preserSize.width = MAX(preserSize.width, self.preferSize.width);
    
    CGRect frame = self.frame;
    frame.size = preserSize;
    self.frame = frame;
}

- (void)relayout
{
    CGSize size = self.frame.size;
    if (self.image)
    {
        self.customImageView.frame = CGRectMake(0, (size.height - self.imageSize.height) * 0.5, self.imageSize.width, self.imageSize.height);
    }
    
    if (self.title.length > 0)
    {
        CGFloat x = self.image ? (self.imageSize.width + 4) : 0;
        self.customLabel.frame = CGRectMake(x, (size.height - self.labelSize.height) * 0.5, self.labelSize.width, self.labelSize.height);
    }
    
    if (self.subscriptImage)
    {
        CGRect frame;
        frame.size = self.subscriptImageSize;
        if (self.image)
        {
            CGRect imageFrame = self.imageView.frame;
            frame.origin = CGPointMake(imageFrame.origin.x + imageFrame.size.width - frame.size.width / 2, imageFrame.origin.y - frame.size.height / 2);
        }
        else
        {
            CGRect selfFrame = self.frame;
            frame.origin = CGPointMake(selfFrame.size.width -frame.size.width / 2, -frame.size.height / 2);
        }
        self.subscriptImageView.frame = frame;
    }
}

- (UIImageView *)customImageView
{
    if (!_customImageView)
    {
        _customImageView = [[UIImageView alloc] initWithFrame:CGRectMake(0, (self.frame.size.height - 24) * 0.5, 24, 24)];
    }
    return _customImageView;
}

- (UIImageView *)subscriptImageView
{
    if (!_subscriptImageView)
    {
        _subscriptImageView = [[UIImageView alloc] init];
    }
    return _subscriptImageView;
}

- (UILabel *)customLabel
{
    if (!_customLabel)
    {
        CGSize size = self.frame.size;
        _customLabel = [[UILabel alloc] initWithFrame:CGRectMake(size.width - 24, (size.height - 24) * 0.5, 24, 24)];
        _customLabel.font = _font_big([UIFont ks_fontWithFontType:KSFontType_Middle]);
        _customLabel.textColor = [UIColor ks_primaryTextColor];
        _customLabel.textAlignment = NSTextAlignmentCenter;
    }
    return _customLabel;
}

- (UIImageView *)imageView
{
    return self.customImageView;
}

- (UILabel *)titleLabel
{
    return self.customLabel;
}

@end


CGFloat kButtonHeight()
{
    return _size_mid(50.f);
}
CGFloat kButtonWidth()
{
    return _size_mid(40.f);
}
CGFloat const kBottomViewRightPadding = 16.f; // 底部非上下结构按钮时，整体右内距
CGFloat const kBottomViewLeftPadding()
{
    return isZoom() ?26/_size_mid(26):26;//实在放不下
}// 底部整体左内距
CGFloat kBottomViewHotSpotWidth()// 底部热度卡宽度
{
    return _size_mid(100);
}
CGFloat kBottomViewHotSpotHeight()// 底部热度卡宽度
{
    return _size_mid(36);
}

/// 底部栏
@interface KSSinglesDetailBottomView () <KSFreeHeartAnimationViewDelegate, UIGestureRecognizerDelegate>

@property (nonatomic, strong) UIView *backgroundView;

// 第一个按钮 主人态 投稿
@property (nonatomic, strong) KSButton *contributeButton; /**< 投稿 */
@property (nonatomic, strong) UIButton *hotsSpotButton; /**< 上热门(新增) */
@property (nonatomic, strong) KSHotCardCarouselView *hotsSpotCarouselView; /**< 上热门轮播文案 */
@property (nonatomic, strong) KSHotCradAdFreeView *hotCardAdFreeView;
@property (nonatomic, strong) KSGiftFreeAdView *giftAdFreeView;//看广告
// 9.11 看广告免费送礼迭代实验
@property (nonatomic, strong) UIView *schemeAButton;
@property (nonatomic, strong) UIView *schemeBButton;
@property (nonatomic, strong) UIImageView *schemeBIcon;
@property (nonatomic, assign) BOOL sceneShow;
@property (nonatomic, assign) NSInteger giftBtnNum;
@property (nonatomic, strong) NSString *adGiftIconUrl;
@property (nonatomic, strong) proto_quick_gift_webapp_QueryQuickSendInfoWebRsp *giftResponse;
@property (nonatomic, strong) KSButton *editButton; /**< 编辑 */
@property (nonatomic, strong) KSSingleDetailBottomRankView *giftRankView; // 礼物榜

/// 最后展示在右下角的视图，上面几个其中1个
@property (nonatomic, weak) UIView *rightBottomView;

@property (nonatomic, strong) NSMutableDictionary *buttonDic;

@property (nonatomic, nullable) BottomButtonClickCallback bottomButtonClickCallback;
@property (nonatomic, nullable) CommentButtonClickCallback commentButtonClickCallback;
@property (nonatomic, nullable) EditButtonClickCallback editButtonClickCallback;
@property (nonatomic, nullable) ContributeButtonClickCallback contributeButtonClickCallback;
@property (nonatomic, nullable) HotsSpotButtonClickCallback hotsSpotButtonClickCallback;
@property (nonatomic, nullable) FreeGiftButtonClickCallback freeGiftButtonClickCallback;
@property (nonatomic, nullable) QuickSingingButtonClickCallback quickSingingButtonClickCallback;
@property (nonatomic, nullable) AdFreeHotViewClickCallback adFreeHotViewClickCallback;

@property (nonatomic, strong) KSFreeHeartAnimationView *freeSendGiftTagView;//免费送动画标签
@property (nonatomic, assign) BOOL needShowFreeSendGiftTagView;

@property (nonatomic, assign) BOOL enableAudioCommentRecord;
@property (nonatomic, assign) BOOL iGiftAnimating; //是否处于送礼按钮呼吸态

@end


@implementation KSSinglesDetailBottomView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self)
    {
        self.buttonDic = [NSMutableDictionary dictionary];
        self.backgroundColor = UIColor.ks_normalBGColor;
        
        _enableAudioCommentRecord = KSAudioCommentSwitcher.sharedSwitcher.enableRecord;
    }
    return self;
}

- (void)setupBGView
{
    // backgroundView 的高度会比实际需要的高度多一些，主要是为了实现阴影+圆角方便
    self.backgroundView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_SAFE_BOTTOM + 62.f)]; // 底部栏50 + safeBottm + 下圆角12
    self.backgroundView.backgroundColor = UIColor.ks_surfaceBGColor;
    self.backgroundView.layer.shadowColor = UIColor.blackColor.CGColor;
    self.backgroundView.layer.shadowOpacity = 0.05f;
    self.backgroundView.layer.shadowRadius = 5;
    [self addSubview:self.backgroundView];
}

- (void)setButtonConfigs:(NSArray<KSBottomButtonConfig *> *)buttonConfigs
{
    [self setButtonConfigs:buttonConfigs delayShowAnimation:NO];
}

- (void)setButtonConfigs:(NSArray<KSBottomButtonConfig *> *)buttonConfigs delayShowAnimation:(BOOL)delayShowAnimation
{
    if (([self needShowGiftRank] || (LocalBoolConfig(@"IsGiftDiffToHot") && (!self.enableAudioCommentRecord || !self.needShowQuickSingingBtn))) && !self.sceneShow) {
        /// 客人态礼物按钮异化成礼物榜 或 上热门
        NSMutableArray *temp = [NSMutableArray new];
        for (KSBottomButtonConfig *item in buttonConfigs) {
            if (item.tag != KSDetailPageOptType_Gift) {
                [temp addObject:item];
            }
        }
        _buttonConfigs = [temp copy];
        [self resetButtons:delayShowAnimation];
        
    } else {
        _buttonConfigs = buttonConfigs;
        [self resetButtons:delayShowAnimation];
    }
}

- (void)setCommentButtonClickCallback:(CommentButtonClickCallback)callback
{
    _commentButtonClickCallback = callback;
}

- (void)setEditButtonClickCallback:(EditButtonClickCallback)callback
{
    _editButtonClickCallback = callback;
}

- (void)setContributeButtonClickCallback:(ContributeButtonClickCallback)callback
{
    _contributeButtonClickCallback = callback;
}

- (void)setHotsSpotButtonClickCallback:(HotsSpotButtonClickCallback)callback
{
    _hotsSpotButtonClickCallback = callback;
}

- (void)setRankViewDiffHotBtnClickBlock:(RankViewDiffHotBtnClickCallback)callback
{
    _rankViewDiffHotBtnClickBlock = callback;
}

- (void)setBottomButtonClickCallback:(BottomButtonClickCallback)callback
{
    _bottomButtonClickCallback = callback;
}

- (void)setFreeGiftButtonClickCallback:(FreeGiftButtonClickCallback)callback
{
    _freeGiftButtonClickCallback = callback;
}

- (void)setQuickSingingButtonClickCallback:(QuickSingingButtonClickCallback)callback {
    _quickSingingButtonClickCallback = callback;
}

- (void)setAdFreeHotViewClickCallback:(AdFreeHotViewClickCallback)adFreeHotViewClickCallback {
    _adFreeHotViewClickCallback = adFreeHotViewClickCallback;
}

- (void)didButtonClicked:(UIButton *)button
{
    KSBottomButtonConfig *buttonConfig;
    if (button.tag < self.buttonConfigs.count) {
        buttonConfig = [self.buttonConfigs safeObjectAtIndex:button.tag];
    }
    
    if (buttonConfig.tag == KSDetailPageOptType_Comment && self.isGuest)
    {
        if (self.commentButtonClickCallback)
        {
            self.commentButtonClickCallback(self);
        }
    }
    else if (button == self.editButton)
    {
        if (self.editButtonClickCallback)
        {
            self.editButtonClickCallback(self);
        }
    }
    else if (button == self.contributeButton)
    {
        if (self.contributeButtonClickCallback)
        {
            self.contributeButtonClickCallback(self);
        }
    }
    else if (button == self.hotsSpotButton)
    {
        if (self.hotsSpotButtonClickCallback)
        {
            self.hotsSpotButtonClickCallback(self);
        }
    }
    else if (button == self.giftDiffHotBtn)
    {
        if (self.rankViewDiffHotBtnClickBlock)
        {
            self.rankViewDiffHotBtnClickBlock(self);
        }
    }
    else
    {
        if (self.bottomButtonClickCallback)
        {
            self.bottomButtonClickCallback(self, button.tag);
            
            //送礼按钮=呼吸态点击上报
            if (buttonConfig.tag == KSDetailPageOptType_Gift && self.iGiftAnimating) {
                [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                    reportModel.key = @"details_of_creations#giveflower_receive_rewards#rewardsdiff_alienation#click#0";
                }];
            }
        }
    }
}

#pragma mark - 布局按钮

- (void)setupBottomButton:(KSBaseButton *)bottomButton withConfig:(KSBottomButtonConfig *)config
{
    [bottomButton setTitle:config.title forState:UIControlStateNormal];
    
    // 为了展示“送礼(100/100)”样式的代码，暂先保留
    if (bottomButton.tag == 3 && config.title.length > 2) {
        [bottomButton.titleLabel sizeToFit];
        bottomButton.width = bottomButton.titleLabel.width;
    }
    
    if (config.vgIcon.length > 0) {
        VGImage *vgImage = [VGImage imageNamed:config.vgIcon];
        UIImage *image = [vgImage imageWithSize:CGSizeMake(_size_mid(24), _size_mid(24))];
        [bottomButton setImage:image forState:UIControlStateNormal];
    } else if (config.outlineIcon.length > 0) {
        VGOutline *outline = [VGOutline outlineNamed:config.outlineIcon];
        UIImage *image = [outline imageWithSize:CGSizeMake(_size_mid(24), _size_mid(24)) color:[UIColor colorWithWhite:42/255.0 alpha:1]];
        [bottomButton setImage:image forState:UIControlStateNormal];
    } else if (config.iconImageName) {
        //普通图片设置
        UIImage *image = [UIImage imageNamed:config.iconImageName];
        [bottomButton setImage:image forState:UIControlStateNormal];
    }
    
    if (config.unreadNum != 0) {
        [bottomButton.imageView setUnreadNumber:config.unreadNum];
    }
    
    // UIButton+ImageTitlePosition
    // setImagePosition 必须在文字、图片设置完后调用
    [bottomButton setImagePosition:KSImagePositionTop spacing:2.f];
}

- (BOOL)hasRestAdFreeHotCount {
    return KSABTestManager.sharedManager.enableMiniHeatCard &&
    self.timelineDetail.miniHeatCard.jce_unHitType > 0 &&
    !self.timelineDetail.isPreloadAdFailed;// 未开启缓存比价 || 有预加载到缓存的广告
}

- (void)removeAllSubviews
{
    NSArray *viewsToRemove = [self subviews];  // 这个是copy的
    for (UIView *view in viewsToRemove)
    {
        // Mini热度卡存在动画 不移除
        if ([view isKindOfClass:KSHotCradAdFreeView.class]
            && [self hasRestAdFreeHotCount]) {
            continue;
        }
        if ([view isKindOfClass:KSGiftFreeAdView.class]) {
            continue;
        }
        [view removeFromSuperview];
    }
}

- (void)updateFullDetail {
    [self.hotCardAdFreeView removeFromSuperview];
    self.hotCardAdFreeView = nil;
}

- (void)resetButtons
{
    [self resetButtons:NO];
}

- (void)resetButtons:(BOOL)delayShowAnimation
{
    // TODO: 刷新次数过多，需要优化 @neo

    [self removeAllSubviews];
    [self setupBGView];
    
    // 1. 布局最右侧特殊业务按钮(上热门/上热门异化/送礼上榜/编辑/投稿/快速唱 等)
    CGFloat tiledTrailing = 0; // 特殊按钮+距屏幕右侧边距
    
    BOOL needShowGiftRank = [self needShowGiftRank];
    
    if (self.isGuest) {
        UIView *behindView = nil;
        // 客人态按钮布局逻辑
        if (LocalBoolConfig(@"IsGiftDiffToHot") && (!self.enableAudioCommentRecord || !self.needShowQuickSingingBtn)) {
            /// 客人态 礼物榜或者送礼按钮 异化成上热门
            [self showDiffHotBtn:self.width - kButtonWidth() - 26.f]; // 特殊整体右内距 26.f
            
            tiledTrailing = kButtonWidth() + 26.f;
            behindView = self.giftDiffHotBtn;
        } else if (self.sceneShow) {
            [self setupExperimentButtons];
            behindView= self.schemeAButton;
        }
        else if (needShowGiftRank) {
            /// 客人态礼物按钮异化成礼物榜
            [self setupGiftRankView:delayShowAnimation];
            [self addSubview:self.giftRankView];
            
            // giftRankView Width 会变化，按照设计稿，左边的点赞这些间距应该保持不变
            tiledTrailing = kLongGiftRankViewWidth() + kBottomViewRightPadding;
            behindView = self.giftRankView;
            
        } else {
            if (self.enableAudioCommentRecord && self.needShowQuickSingingBtn) {
                // 按住唱按钮
                [self setupQuickSingingButton];
                [self addSubview:self.quickSingingButton];
                
                tiledTrailing = self.quickSingingButton.width + kBottomViewRightPadding;
            }
            behindView = self.quickSingingButton;
        }
        self.rightBottomView = behindView;
        if (self.showFreeAdType == KSSinglesDetailBottomViewFreeAdType_Show) {
            [self setupAdFreeGiftButton];
        }
        
    } else {
        // 主人态按钮布局逻辑
        if ([self hasRestAdFreeHotCount]) {
            [self setupAdFreeHotsButton:delayShowAnimation];
            tiledTrailing = 82 + kBottomViewRightPadding;
        } else if (self.needShowContributeBtn) {
            // 投稿
            [self setupContributeButtonWithStartX:0];
            [self addSubview:self.contributeButton];
            [self.contributeButton setX:self.width - self.contributeButton.width - kBottomViewRightPadding];
            
            tiledTrailing = self.contributeButton.width + kBottomViewRightPadding;
        } else if(self.needShowHotBtn) {
            
            PROTO_UGC_WEBAPP_HeatCardInfo *cardInfo = self.timelineDetail.heatCardInfo;
            // 上热门
            if (IS_EMPTY_STR_BM(cardInfo.jce_strToHotTitle)) {
                [self setupHotsSpotButtonWithStartX:0];
                [self addSubview:self.hotsSpotButton];
                [self.hotsSpotButton setX:self.width - self.hotsSpotButton.width - kBottomViewRightPadding];
            } else {
                [self setupHotsSpotCarouselViewWithStartX:self.width - kBottomViewHotSpotWidth() - kBottomViewRightPadding];
                [self.hotsSpotCarouselView updateWithCardInfo:cardInfo];
            }
            
            tiledTrailing = kBottomViewHotSpotWidth() + kBottomViewRightPadding;
            
            [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                reportModel.key = @"details_of_creations#heat_card#my#exposure#0";
                if ([self.delegate respondsToSelector:@selector(getCurrentUgcId:)]) {
                    reportModel.ugcid = [self.delegate getCurrentUgcId:self];
                    reportModel.commonStr1 = self.timelineDetail.heatCardInfo.jce_strToHotCorner.length > 0 ? self.timelineDetail.heatCardInfo.jce_strToHotCorner : @"-1";
                }
            }];
        } else if (self.needShowEditBtn) {
            // 编辑
            [self setupEditButtonWithStartX:0];
            [self addSubview:self.editButton];
            [self.editButton setX:self.width - self.editButton.width - kBottomViewRightPadding];
            
            tiledTrailing = self.editButton.width + kBottomViewRightPadding;
        }
    }
    
    // 2. 平铺布局外部配置按钮
    
    // 小按钮右间距
    CGFloat buttonRightMargin = (self.width - tiledTrailing - self.buttonConfigs.count * kButtonWidth() - kBottomViewLeftPadding()) / self.buttonConfigs.count;
    
    for (NSInteger i = 0; i < self.buttonConfigs.count; i++) {
        KSBottomButtonConfig *config = [self.buttonConfigs safeObjectAtIndex:i];
        // 送礼按钮（异化为上热门按钮）+快速唱
        if (config.tag == KSDetailPageOptType_Gift && LocalBoolConfig(@"IsGiftDiffToHot") && self.enableAudioCommentRecord && self.needShowQuickSingingBtn)
        {
            [self showDiffHotBtn:kBottomViewLeftPadding() + (kButtonWidth() + buttonRightMargin) * i];
            continue;
        }
        
        KSBaseButton *bottomButton = [[KSBaseButton alloc] init];
        [bottomButton addTarget:self action:@selector(didButtonClicked:) forControlEvents:UIControlEventTouchUpInside];
        bottomButton.tag = i;
        bottomButton.frame = CGRectMake(kBottomViewLeftPadding() + (kButtonWidth() + buttonRightMargin) * i, 5.f, kButtonWidth(), kButtonHeight());
        bottomButton.titleLabel.font = _font_big(KSFontType2MediumCaption2);
        [bottomButton setTitleColor:UIColor.ks_standardDark100Color forState:UIControlStateNormal];
        [self addSubview:bottomButton];
        
        NSString *key = [NSString stringWithFormat:@"%p", config];
        [self.buttonDic setObject:bottomButton forKey:key];
    
        [self setupBottomButton:bottomButton withConfig:config];
        
        // 点赞（无障碍化）
        if (config.tag == KSDetailPageOptType_Like) {
            if ([config.iconImageName isEqualToString:@"detail_bottom_like_red"]) {
                // 已赞
                bottomButton.accessibilityLabel = @"取消点赞";
            } else {
                // 未赞
                bottomButton.accessibilityLabel = @"点赞";
            }
        }

        if (config.animationRepeatTime > 0) {
            if (config.animationTag == 1)
            {
                KLog(@"[详情页送花领礼动画]  timeslice finished %ld", [KSGiftHelper getCurrentBreathCount]);
                [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                    reportModel.key = @"gift_board_top_banner#giveflower_receive_rewards#rewardsdiff#click#0";
                }];
                [self animationWithView:bottomButton.imageView repeatTime:config.animationRepeatTime];
                NSInteger savedCount = [KSGiftHelper getCurrentBreathCount];
                [KSGiftHelper saveGiftBtnBreathCount:savedCount+1];
            }
            else if (config.animationTag == 0)
            {
                KLog(@"[详情页分享微信动画]  timeslice finished %ld", [KSShareHelper getCurrentBreathCount]);
                [self animationWithView:bottomButton.imageView repeatTime:config.animationRepeatTime];
                NSInteger savedCount = [KSShareHelper getCurrentBreathCount];
                [KSShareHelper saveShareBtnBreathCount:savedCount+1];
            }
        }
    }
    
    // 动画中隐藏送礼按钮
    if ([self hasRestAdFreeHotCount]
        && [self.hotCardAdFreeView isAnimating]) {
        KSBottomButtonConfig *config = [self getButtonConfigWithType:KSDetailPageOptType_Gift];
        KSBaseButton *sendGiftBtn = [self getBottomButtonByConfig:config];
        sendGiftBtn.alpha = 0;
    }
    
    // 送礼小按钮上样式补充
    if (!needShowGiftRank && self.needShowFreeSendGiftTagView) {
        // 免费礼物送礼引导
        KSBottomButton *giftBtn = [self viewWithTag:self.buttonConfigs.count - 1];
        self.freeSendGiftTagView = [[KSFreeHeartAnimationView alloc] initWithFrame:giftBtn.bounds];
        self.freeSendGiftTagView.delegate = self;
        self.freeSendGiftTagView.backgroundColor = self.backgroundColor;
        self.freeSendGiftTagView.centerX = giftBtn.width / 2.0;
        [giftBtn addSubview:self.freeSendGiftTagView];
    }
}

#pragma mark - 呼吸动画

- (void)animationWithView:(UIView*)view repeatTime:(NSInteger)repeatTime
{
    if (repeatTime > 0) {
        self.iGiftAnimating = YES; //处于送礼按钮呼吸态
        [UIView animateWithDuration:0.5 animations:^{
            view.transform = CGAffineTransformMakeScale(1.2, 1.2);
        } completion:^(BOOL finished) {
            if (finished) {
                [UIView animateWithDuration:0.5 animations:^{
                    view.transform = CGAffineTransformIdentity;
                } completion:^(BOOL finished) {
                    if (finished) {
                        [self animationWithView:view repeatTime:repeatTime-1];
                    }
                }];
            }
        }];
    } else {
        self.iGiftAnimating = NO;
    }
}

#pragma mark -

- (void)showDiffHotBtn:(CGFloat)x
{
    [self setupGiftDiffHotBtnWithStartX:x];
    [self addSubview:self.giftDiffHotBtn];
    if (!self.iDiffHotAnimating)
    {
        [self animationWithView:self.giftDiffHotBtn.imageView repeatTime:WnsSwitchIntegerConfig(@"HeatCardBtnBreathCount")];
        self.iDiffHotAnimating = YES;
        [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
            reportModel.key = @"details_of_creations#heat_card#guest#exposure#0";
            if ([self.delegate respondsToSelector:@selector(getCurrentUgcId:)]) {
                reportModel.ugcid = [self.delegate getCurrentUgcId:self];
            }
            if ([self.delegate respondsToSelector:@selector(getCurrentToUid)]) {
                reportModel.touid = [self.delegate getCurrentToUid];
            }
        }];
    }
}

- (void)showSendGiftFreeTag
{
    if ([self needShowGiftRank]) {
        /// 客人态礼物按钮异化成礼物榜，不需要再调整免费送礼
        return;
    }
    
    NSString *ugcid = @"";
    if ([self.delegate respondsToSelector:@selector(getCurrentUgcId:)]) {
        ugcid = [self.delegate getCurrentUgcId:self];
    }
    KS_WEAK_SELF(self);
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        CHECK_SELF_AND_RETURN();
        if (!self.freeSendGiftTagView && [self.delegate respondsToSelector:@selector(getCurrentUgcId:)]) {
            NSString *curUgcid = [self.delegate getCurrentUgcId:self];
            if ([curUgcid isEqualToString:ugcid]) {
                KSBottomButton *giftBtn = [self viewWithTag:self.buttonConfigs.count - 1];
                self.freeSendGiftTagView = [[KSFreeHeartAnimationView alloc] initWithFrame:giftBtn.bounds];
                self.freeSendGiftTagView.delegate = self;
                self.freeSendGiftTagView.backgroundColor = self.backgroundColor;
                self.freeSendGiftTagView.centerX = giftBtn.width / 2.0;
                [giftBtn addSubview:self.freeSendGiftTagView];
                [self.freeSendGiftTagView popAnimationView];
                self.needShowFreeSendGiftTagView = YES;
            }
        }
    });
}

- (void)clearSendGiftFreeTag
{
    if (self.freeSendGiftTagView) {
        [self.freeSendGiftTagView removeFromSuperview];
        self.freeSendGiftTagView = nil;
        self.needShowFreeSendGiftTagView = NO;
    }
    [self clearFreeAdGift];
}
- (void)clearFreeAdGift;
{
    if (self.giftAdFreeView) {
        [self.giftAdFreeView removeFromSuperview];
        self.giftAdFreeView = nil;
    }
    [self cleanupExperimentButtons];
    self.showFreeAdType = KSSinglesDetailBottomViewFreeAdType_None;
}

#pragma mark - ContributeButton

/// 投稿按钮
- (void)setupContributeButtonWithStartX:(CGFloat)startX
{
    self.contributeButton = [KSButton buttonWithButtonType:KSButtonTypeGrayBgBlackTitleNoBorder buttonSize:KSButtonSizeMiddle];
    self.contributeButton.fontScaleMode = KSSizeMode_Mid;
    [self.contributeButton.titleLabel setFont:_font_big([UIFont ks_fontWithFontType:KSFontType_LargeBold])];
    [self.contributeButton.titleLabel setTextColor:UIColor.ks_primaryTextColor];
    [self.contributeButton setXY:CGPointMake(startX, 13.f)];
    self.contributeButton.fixedWidth = 80.f;
    self.contributeButton.title = KString(@"投稿");
    self.contributeButton.tag = self.singleButtonTag;
    
    NSString *submitImageName = @"detail_bottom_submit";
    if (self.isSubmitDone)
    {
        // 已投稿
        submitImageName = @"detail_bottom_submit_done";
    }
    
    [self.contributeButton setImage:[UIImage imageNamed:submitImageName]];
    
    // VIP 角标
    UIImageView *vipImageView = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"label_red_VIP"]];
    [self.contributeButton addSubview:vipImageView];
    [vipImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.contributeButton);
        make.right.equalTo(self.contributeButton);
        make.width.equalTo(@(_size_mid(18)));
        make.height.equalTo(@(_size_mid(18)));
    }];
    
    [self.contributeButton addTarget:self action:@selector(didButtonClicked:) forControlEvents:UIControlEventTouchUpInside];
}

// 上热门按钮
- (void)setupHotsSpotButtonWithStartX:(CGFloat)startX
{
    [self.hotsSpotCarouselView removeFromSuperview];
    self.hotsSpotCarouselView = nil;
    
    // 视觉还原要求，只能自己实现了
    CGFloat btnH = _size_mid(36.f);
    CGFloat iconH = _size_mid(24.f);
    CGFloat labelH = _size_mid(20.f);
    self.hotsSpotButton = [[UIButton alloc] initWithFrame:CGRectMake(startX, 13.f, 100.f, btnH)];
    [KSCommonUIFactory applyRoundCornerForView:self.hotsSpotButton radius:btnH/2.f];
    self.hotsSpotButton.backgroundColor = [UIColor ks_colorWithRGBHex:0xF2F2F6];
    
    UIImageView *iconImageView = [[UIImageView alloc] initWithFrame:CGRectMake(13.f, (btnH - iconH)/2.f, iconH, iconH)];
    iconImageView.contentMode = UIViewContentModeScaleAspectFit;
    iconImageView.image = [UIImage imageNamed:@"detail_bottom_giftdiff_hotbtn"];
    [self.hotsSpotButton addSubview:iconImageView];
    
    UILabel *titleLabel = [[UILabel alloc] initWithFrame:CGRectMake(iconImageView.right, (btnH - labelH)/2.f, 45.f, labelH)];
    titleLabel.font = _font_big(KSFontType1MediumBody1);
    titleLabel.textColor = UIColor.ks_standardDark100Color;
    titleLabel.text = @"上热门";
    [self.hotsSpotButton addSubview:titleLabel];
    
    [self.hotsSpotButton setXY:CGPointMake(startX, 12.f)];
    self.hotsSpotButton.tag = self.singleButtonTag;
    [self.hotsSpotButton addTarget:self action:@selector(didButtonClicked:) forControlEvents:UIControlEventTouchUpInside];
}
- (void)setupHotsSpotCarouselViewWithStartX:(CGFloat)startX {
    [self.hotsSpotButton removeFromSuperview];
    self.hotsSpotButton = nil;
    
    self.hotsSpotCarouselView = [KSHotCardCarouselView.alloc initWithFrame:CGRectMake(startX, 13, kBottomViewHotSpotWidth(), kBottomViewHotSpotHeight())];
    UITapGestureRecognizer *tap = [UITapGestureRecognizer.alloc initWithTarget:self action:@selector(didTapHotSpotCarouse)];
    self.hotsSpotCarouselView.userInteractionEnabled = YES;
    [self.hotsSpotCarouselView addGestureRecognizer:tap];
    [self addSubview:self.hotsSpotCarouselView];
}

// 上热门按钮
- (void)setupAdFreeHotsButton:(BOOL)delayShowAnimation
{
    if (self.hotCardAdFreeView) {
        [self bringSubviewToFront:self.hotCardAdFreeView];
        return;
    }
    self.hotCardAdFreeView = [[KSHotCradAdFreeView alloc] initWithDelayAnimation:delayShowAnimation];
    KS_WEAK_SELF(self);
    self.hotCardAdFreeView.startAnimation = ^{
        CHECK_SELF_AND_RETURN();
        if ([self hasRestAdFreeHotCount]) {
            KSBottomButtonConfig *config = [self getButtonConfigWithType:KSDetailPageOptType_Gift];
            KSBaseButton *sendGiftBtn = [self getBottomButtonByConfig:config];
            sendGiftBtn.alpha = 0;
            
            if(isZoom() && _size_mid(1) > 1)
            {
                //大字&放大模式下这里会重叠
                config = [self getButtonConfigWithType:KSDetailPageOptType_Share];
                sendGiftBtn = [self getBottomButtonByConfig:config];
                sendGiftBtn.alpha = 0;
            }
            
        }
    };
    self.hotCardAdFreeView.endAnimation = ^{
        CHECK_SELF_AND_RETURN();
        if ([self hasRestAdFreeHotCount]) {
            KSBottomButtonConfig *config = [self getButtonConfigWithType:KSDetailPageOptType_Gift];
            KSBaseButton *sendGiftBtn = [self getBottomButtonByConfig:config];
            sendGiftBtn.alpha = 1;
            
            if(isZoom() && _size_mid(1) > 1)
            {
                config = [self getButtonConfigWithType:KSDetailPageOptType_Share];
                sendGiftBtn = [self getBottomButtonByConfig:config];
                sendGiftBtn.alpha = 1;
            }
        }
    };
    UITapGestureRecognizer *tap = [UITapGestureRecognizer.alloc initWithTarget:self action:@selector(didTapHotCardAdFreeView)];
    self.hotCardAdFreeView.userInteractionEnabled = YES;
    [self.hotCardAdFreeView addGestureRecognizer:tap];
    [self addSubview:self.hotCardAdFreeView];
    self.hotCardAdFreeView.x = self.width - self.hotCardAdFreeView.width - kBottomViewRightPadding;
    self.hotCardAdFreeView.y = 13;
    
    [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
        reportModel.key = @"details_of_creations#free_heat_btn#null#exposure#0";
        reportModel.commonStr1 = KSABTestManager.sharedManager.miniHeatCardExposureNum;
        reportModel.ugcid = self.timelineDetail.ugcId;
        reportModel.touid = KSLoginManager.sharedInstance.curUserInfo.userId;
        reportModel.mid = self.timelineDetail.ksongMid;
    }];
}

- (void)didTapHotCardAdFreeView {
    !self.adFreeHotViewClickCallback ?: self.adFreeHotViewClickCallback(self, [self.hotCardAdFreeView isAnimating]);
}

- (void)didTapHotSpotCarouse {
    !self.hotsSpotButtonClickCallback ?: self.hotsSpotButtonClickCallback(self);
}

#pragma mark - EditButton

/// 编辑按钮
- (void)setupEditButtonWithStartX:(CGFloat)startX
{
    self.editButton = [KSButton buttonWithButtonType:KSButtonTypeGrayBgBlackTitleNoBorder buttonSize:KSButtonSizeMiddle];
    self.editButton.fontScaleMode = KSSizeMode_Mid;
    [self.editButton.titleLabel setFont:_font_big([UIFont ks_fontWithFontType:KSFontType_LargeBold])];
    [self.editButton.titleLabel setTextColor:UIColor.ks_primaryTextColor];
    [self.editButton setXY:CGPointMake(startX, 13.f)];
    self.editButton.fixedWidth = 80.f;
    self.editButton.title = KString(@"编辑");
    [self.editButton addTarget:self action:@selector(didButtonClicked:) forControlEvents:UIControlEventTouchUpInside];
    self.editButton.tag = self.singleButtonTag;
}

- (BOOL)hasFreeSendGiftTagView
{
    if (self.freeSendGiftTagView) {
        return YES;
    } else {
        return NO;
    }
}

#pragma mark - quicksingsingButton

- (void)setupQuickSingingButton {
    // 按钮宽90
    KSVoiceQuickSingingTouchView *bottomButton = [[KSVoiceQuickSingingTouchView alloc] initWithImage:IMAGENAMED(@"bar_single_quicksingsing")];
    bottomButton.frame = CGRectMake(self.width-_size_mid(90)-kBottomViewRightPadding, 5, _size_mid(90), _size_mid(36));
    self.quickSingingButton = bottomButton;
    KS_WEAK_SELF(self);
    self.quickSingingButton.touchCallBack = ^(KSVoiceQuickSingingTouchView * _Nonnull touchView, QuickSingingPressState state) {
        CHECK_SELF_AND_RETURN();
        if (self.quickSingingButtonClickCallback) {
            self.quickSingingButtonClickCallback(self, state);
        }
    };
    
    UITapGestureRecognizer *quickTap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(didTapBottomViewQuickSingButton:)];
    [self.quickSingingButton addGestureRecognizer:quickTap];
}

#pragma mark - 礼物榜

- (BOOL)needShowGiftRank
{
    /// 客人态礼物按钮异化成礼物榜
    return self.isGuest && ![self.timelineDetail.userInfo isLogicAuthStar];
}

- (void)delayChangeRankViewColor:(BOOL)delayShowAnimation
{
    [self.giftRankView updateNormalColorSuit];
    KS_WEAK_SELF(self);
    float time = 5.0 + (delayShowAnimation ? 2 : 0);
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(time * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        // 5s 之后自动变色
        CHECK_SELF_AND_RETURN();
        [self.giftRankView updateEnhanceColorSuit];
    });
}
- (void)setupGiftRankView:(BOOL)delayShowAnimation
{
    if (self.giftRankView) {
        [self delayChangeRankViewColor:delayShowAnimation];
        return;
    }
    CGFloat width = kShortGiftRankViewWidth();
    CGRect frame = CGRectMake(SCREEN_WIDTH - kBottomViewRightPadding - width, kGiftRankViewTopMargin, width, _size_mid(36));
    KSSingleDetailBottomRankView *rankView = [[KSSingleDetailBottomRankView alloc] initWithFrame:frame];
    KS_WEAK_SELF(self);
    rankView.clickTextBlock = ^{
        CHECK_SELF_AND_RETURN();
        if (self.rankViewClickTextBlock) {
            self.rankViewClickTextBlock(self);
        }
    };
    rankView.clickAvatarBlock = ^{
        CHECK_SELF_AND_RETURN();
        if (self.rankViewClickAvatarBlock) {
            self.rankViewClickAvatarBlock(self);
        }
    };
    self.giftRankView = rankView;
    [self delayChangeRankViewColor:delayShowAnimation];
}

#pragma mark - Update

/// 根据按钮枚举类型查找对应按钮的config
/// @param configType configType
- (KSBottomButtonConfig *)getButtonConfigWithType:(KSDetailPageOptType)configType
{
    KSBottomButtonConfig *targetConfig;
    for (NSInteger i = 0; i < self.buttonConfigs.count; ++i) {
        KSBottomButtonConfig *config = [self.buttonConfigs safeObjectAtIndex:i];
        if (config.tag == configType) {
            targetConfig = config;
            break;
        }
    }
    
    return targetConfig;
}

/// 根据按钮config获取对应按钮
- (KSBaseButton *)getBottomButtonByConfig:(KSBottomButtonConfig *)config
{
    NSString *key = [NSString stringWithFormat:@"%p", config];
    KSBaseButton *bottomButton = [self.buttonDic objectForKey:key];
    return bottomButton;
}

// 更新评论数
- (void)updateCommentCount:(NSInteger)commentCount
{
    NSString *commentTitle = @"评论";
    if (commentCount > 0) {
        commentTitle = [KSFormatHelper formatNewNumber:commentCount];
    }
    
    KSBottomButtonConfig *commentBtnConfig = [self getButtonConfigWithType:KSDetailPageOptType_Comment];
    
    KSBaseButton *commentBtn = [self getBottomButtonByConfig:commentBtnConfig];
    
    // 更新
    commentBtnConfig.title = commentTitle;
    [self setupBottomButton:commentBtn withConfig:commentBtnConfig];
}

- (void)updateGiftRanks:(NSArray<KSGiftRank *> *)giftRanks
{
    if (self.giftRankView) {
                
        [self.giftRankView updateGiftRanks:giftRanks];
        
        if (giftRanks.count > 0) {
            // 样式 #1: 长一点的
            CGFloat width = kLongGiftRankViewWidth();
            CGRect frame = CGRectMake(SCREEN_WIDTH - kBottomViewRightPadding - width, kGiftRankViewTopMargin, width, _size_mid(36));
            self.giftRankView.frame = frame;
        } else {
            // 样式 #2: 短一点的
            CGFloat width = kShortGiftRankViewWidth();
            CGRect frame = CGRectMake(SCREEN_WIDTH - kBottomViewRightPadding - width, kGiftRankViewTopMargin, width, _size_mid(36));
            self.giftRankView.frame = frame;
        }
        if (self.rankViewExposureBlock) {
            self.rankViewExposureBlock(self);
        }
    }
}
#pragma mark - 客人态免费看视频送K币礼物
//客人态免费看视频送K币礼物按钮
//如果有看广告免费送礼物，这个临时展示一下后，再退回之前的按钮
- (void)setupAdFreeGiftButton {
    if (self.giftAdFreeView && self.giftAdFreeView.superview) {
        [self bringSubviewToFront:self.giftAdFreeView];
        return;
    }
    
    if (self.sceneShow && self.giftResponse) {
        self.giftAdFreeView = [KSGiftFreeAdView viewWithBehindView:self.rightBottomView giftResponse:self.giftResponse];
    } else {
        self.giftAdFreeView = [KSGiftFreeAdView viewWithBehindView:self.rightBottomView];
    }
    KS_WEAK_SELF(self);
    self.giftAdFreeView.startAnimation = ^{
        CHECK_SELF_AND_RETURN();
    };
    self.giftAdFreeView.endAnimation = ^{
        CHECK_SELF_AND_RETURN();
        [self.giftAdFreeView removeFromSuperview];
        self.giftAdFreeView = nil;
        self.showFreeAdType = KSSinglesDetailBottomViewFreeAdType_AleradyShow;
        if (self.sceneShow && (self.giftBtnNum == 1 || self.giftBtnNum == 2)) {
            self.giftRankView.alpha = 0;
            [self.giftRankView removeFromSuperview];
            self.giftRankView = nil;
            [self setNeedsLayout];
            [self setupExperimentButtons];
        }
    };
    UITapGestureRecognizer *tap = [UITapGestureRecognizer.alloc initWithTarget:self action:@selector(didTapGiftAdFreeView)];
    self.giftAdFreeView.userInteractionEnabled = YES;
    [self.giftAdFreeView addGestureRecognizer:tap];
    [self addSubview:self.giftAdFreeView];

}

/// 看视频免费送礼
- (void)showFreeAdGift;
{
    
    self.showFreeAdType = KSSinglesDetailBottomViewFreeAdType_Show;
    
    [self setupAdFreeGiftButton];
}

- (void)didTapGiftAdFreeView
{
    [self.giftAdFreeView removeFromSuperview];
    self.giftAdFreeView = nil;
    self.showFreeAdType = KSSinglesDetailBottomViewFreeAdType_AleradyShow;
    !self.giftFreeAdBlock ?: self.giftFreeAdBlock();
}

#pragma mark - Experiment Support
- (void)initExperimentWithRes:(proto_quick_gift_webapp_QueryQuickSendInfoWebRsp *)giftResponse {
    self.giftResponse = giftResponse;
    [self setupGiftIconUrl];
    // AB实验配置
    KSAdFreeGiftScene *detailScene = [KSAdFreeGiftScene sceneWithType:KSAdFreeGiftSceneType_TimelineDetail];
    self.giftBtnNum = [detailScene getGiftBtnNum];
    self.sceneShow = detailScene.scenceShow;
    
    //test
    self.giftBtnNum = 1;
    self.sceneShow = true;
}

- (void)setupGiftIconUrl {
    if (self.giftResponse && self.giftResponse.jce_stInfo.jce_stAdvertGift && ![self.giftResponse.jce_stInfo.jce_stAdvertGift.jcev2_p_2_o_strLogo isEmpty]) {
        self.adGiftIconUrl = [[WnsConfigManager sharedInstance].appConfig.urlConfig getIapGiftPicUrlByStrLogo:self.giftResponse.jce_stInfo.jce_stAdvertGift.jcev2_p_2_o_strLogo];
    } else {
        self.adGiftIconUrl = nil;
    }
}

- (void)setupExperimentButtons {
    if (self.giftBtnNum == 1) {
        [self setupSchemeAButton];
    } else if (self.giftBtnNum == 2) {
        [self setupSchemeBButtons];
    }
}

- (void)setupSchemeAButton {
    if (self.schemeAButton) {
        [self.schemeAButton removeFromSuperview];
    }
    
    self.schemeAButton = [[UIView alloc] init];
    self.schemeAButton.backgroundColor = [UIColor ks_colorWithHexString:@"#F2F2F6"];
    self.schemeAButton.layer.cornerRadius = 18;
    
    // 初始frame（原来的大小）
    CGRect originalFrame = CGRectMake(SCREEN_WIDTH - 105 - kBottomViewRightPadding, kGiftRankViewTopMargin, 105, 36);
    // 目标frame（和rankView一样长）
    CGRect expandedFrame = CGRectMake(SCREEN_WIDTH - kLongGiftRankViewWidth() - kBottomViewRightPadding, kGiftRankViewTopMargin, kLongGiftRankViewWidth(), 36);
    
    self.schemeAButton.frame = originalFrame;
    
    UIImageView *iconView = [[UIImageView alloc] init];
    iconView.contentMode = UIViewContentModeScaleAspectFit;
    iconView.frame = CGRectMake(15, 10, 20, 20);
    if (self.adGiftIconUrl) {
        [iconView ks_setImageWithUrl:self.adGiftIconUrl];
    } else {
        iconView.image = [UIImage imageNamed:@"ad_free_gift_icon"];
    }
    [KSAdFreeGiftManager addShakeAnimationToGiftIcon:iconView];
    [self.schemeAButton addSubview:iconView];
    
    UILabel *titleLabel = [[UILabel alloc] init];
    titleLabel.text = @"免费送";
    titleLabel.font = _font_big(KSFontType1MediumBody1);
    titleLabel.textColor = [UIColor ks_standardDark100Color];
    titleLabel.frame = CGRectMake(39, 8, 55, 20);
    [self.schemeAButton addSubview:titleLabel];
    
    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(didTapSchemeAButton)];
    self.schemeAButton.userInteractionEnabled = YES;
    [self.schemeAButton addGestureRecognizer:tap];
    
    [self addSubview:self.schemeAButton];
    
    // A方案动画：先扩展到rankView大小，再恢复原状
    [UIView animateWithDuration:0.3 animations:^{
        self.schemeAButton.frame = expandedFrame;
    } completion:^(BOOL finished) {
        [UIView animateWithDuration:0.3 delay:0.2 options:UIViewAnimationOptionCurveEaseInOut animations:^{
            self.schemeAButton.frame = originalFrame;
        } completion:nil];
    }];
    
    // A方案隐藏送礼按钮
    KSBottomButtonConfig *config = [self getButtonConfigWithType:KSDetailPageOptType_Gift];
    KSBaseButton *sendGiftBtn = [self getBottomButtonByConfig:config];
    sendGiftBtn.alpha = 0;
}

- (void)setupSchemeBButtons {
    if (self.schemeBButton) {
        [self.schemeBButton removeFromSuperview];
    }
    
    self.schemeBButton = [[UIView alloc] init];
    self.schemeBButton.backgroundColor = [UIColor ks_colorWithHexString:@"#F2F2F6"];
    self.schemeBButton.layer.cornerRadius = 18;
    
    // 初始frame（原来的大小）
    CGRect originalFrame = CGRectMake(SCREEN_WIDTH - 105 - kBottomViewRightPadding, kGiftRankViewTopMargin, 105, 36);
    // 目标frame（和rankView一样长）
    CGRect expandedFrame = CGRectMake(SCREEN_WIDTH - kLongGiftRankViewWidth() - kBottomViewRightPadding, kGiftRankViewTopMargin, kLongGiftRankViewWidth(), 36);
    
    self.schemeBButton.frame = originalFrame;
    
    self.schemeBIcon = [[UIImageView alloc] init];
    self.schemeBIcon.contentMode = UIViewContentModeScaleAspectFit;
    if (self.adGiftIconUrl) {
        [self.schemeBIcon ks_setImageWithUrl:self.adGiftIconUrl];
    } else {
        self.schemeBIcon.image = [UIImage imageNamed:@"ad_free_gift_icon"];
    }
    
    UIImageView *iconView = [[UIImageView alloc] init];
    iconView.contentMode = UIViewContentModeScaleAspectFit;
    iconView.frame = CGRectMake(15, 10, 20, 20);
    if (self.adGiftIconUrl) {
        [iconView ks_setImageWithUrl:self.adGiftIconUrl];
    } else {
        iconView.image = [UIImage imageNamed:@"ad_free_gift_icon"];
    }
    [KSAdFreeGiftManager addShakeAnimationToGiftIcon:iconView];
    [self.schemeBButton addSubview:iconView];
    
    UILabel *titleLabel = [[UILabel alloc] init];
    titleLabel.text = @"免费送";
    titleLabel.font = _font_big(KSFontType1MediumBody1);
    titleLabel.textColor = [UIColor ks_standardDark100Color];
    titleLabel.frame = CGRectMake(39, 8, 55, 20);
    [self.schemeBButton addSubview:titleLabel];
    
    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(didTapSchemeBButton)];
    self.schemeBButton.userInteractionEnabled = YES;
    [self.schemeBButton addGestureRecognizer:tap];
    
    [self addSubview:self.schemeBButton];
    
    // B方案动画：先扩展到rankView大小，再恢复原状
    [UIView animateWithDuration:0.3 animations:^{
        self.schemeBButton.frame = expandedFrame;
    } completion:^(BOOL finished) {
        [UIView animateWithDuration:0.3 delay:0.2 options:UIViewAnimationOptionCurveEaseInOut animations:^{
            self.schemeBButton.frame = originalFrame;
        } completion:nil];
    }];
    
    [KSAdFreeGiftManager addShakeAnimationToGiftIcon:self.schemeBIcon];
}

- (void)didTapSchemeAButton {
    !self.giftFreeAdBlock ?: self.giftFreeAdBlock();
    [self cleanupExperimentButtons];
}

- (void)didTapSchemeBButton {
    !self.giftFreeAdBlock ?: self.giftFreeAdBlock();
    [self cleanupExperimentButtons];
}

- (void)cleanupExperimentButtons {
    if (self.schemeAButton) {
        [self.schemeAButton removeFromSuperview];
        self.schemeAButton = nil;
    }
    if (self.schemeBButton) {
        [self.schemeBButton removeFromSuperview];
        self.schemeBButton = nil;
    }
}
#pragma mark - 礼物榜异化上热门

- (void)setupGiftDiffHotBtnWithStartX:(CGFloat)startX
{
    if (self.giftDiffHotBtn) {
        return;
    }

    KSBaseButton *giftDiffHotBtn = [[KSBaseButton alloc] init];
    [giftDiffHotBtn addTarget:self action:@selector(didButtonClicked:) forControlEvents:UIControlEventTouchUpInside];
    giftDiffHotBtn.frame = CGRectMake(startX, 5.f, kButtonWidth(), kButtonHeight());
    giftDiffHotBtn.titleLabel.font = _font_big(KSFontType2MediumCaption2);
    [giftDiffHotBtn setTitleColorAllState:UIColor.ks_standardDark100Color];
    [giftDiffHotBtn setTitle:@"上热门" forState:UIControlStateNormal];
    [giftDiffHotBtn setImage:[UIImage imageNamed:@"detail_bottom_giftdiff_hotbtn"] forState:UIControlStateNormal];
    giftDiffHotBtn.tag = KSDetailPageOptType_GiftDiffHot;
    [giftDiffHotBtn setImagePosition:KSImagePositionTop spacing:1.f];
    self.giftDiffHotBtn = giftDiffHotBtn;
}

#pragma mark KSFreeHeartAnimationViewDelegate

- (void)freeTagSendGiftDidClick:(KSFreeHeartAnimationView *)animationView {
    if (self.freeGiftButtonClickCallback) {
        self.freeGiftButtonClickCallback(self);
    }
}

#pragma mark - UITapGestureRecognizer
- (void)didTapBottomViewQuickSingButton:(UITapGestureRecognizer *)tapGuesture {
    NSUserDefaults *userDefault = [NSUserDefaults standardUserDefaults];
    BOOL hasShow = [userDefault boolForKey:@"KSHasShowQuickSingLongPressToast"];
    if (!hasShow) {
        [KSToast showToast:@"需要长按哦" duration:1];
        [userDefault setBool:YES forKey:@"KSHasShowQuickSingLongPressToast"];
        [userDefault synchronize];
    }
}


@end
