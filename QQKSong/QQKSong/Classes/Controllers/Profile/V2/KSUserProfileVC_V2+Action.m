//
//  KSUserProfileVC_V2+Action.m
//  QQKSong
//
//  Created by sun<PERSON><PERSON><PERSON> on 2017/2/23.
//  Copyright © 2017年 Tencent. All rights reserved.
//

#import "KSWnsConfigDef.h"
#import "KSNotificationDefines.h"
#import "KSUserProfileVC_V2+Action.h"
#import "KSUserProfileVC_V2+Avatar.h"
#import "KSUserProfileVC_V2+Property.h"
#import "KSUserProfileVC_V2+Share.h"
#import "KSUserProfileVC_V2+Follow.h"
#import "KSUserProfileVC_V2+Data.h"
#import "KSUserProfileVC_V2+BgPhoto.h"
#import "KSUserProfileVC_V2+TraceReport.h"
#import "KSUserProfileVC_V2+Album.h"
#import "KSUserProfileVC_V2+GiftAchievement.h"
#import "KSUserProfileVC_V2+VipDirectPay.h"
#import "KSSettingVC.h"
#import "KSReportVC.h"
#import "KSNetAlbumVC_V4.h"
#import "KSImageViewer.h"
#import "KSFansVC.h"
#import "KSAccountSettingVC.h"
#import "KSRootTabBarController+Badge.h"
#import "KSPlayingHistoryVC.h"
#import "KSMyFavorVC.h"
#import "KSAppDelegate.h"
#import "KSBuyVipViewHelper.h"
#import "KSAppDelegateProtocol.h"
#import "KSWebViewController.h"
#import "KSStarProfileFansRankVC.h"
#import "KSTraceReportHelper.h"
#import "KSUgcPlayManager.h"
#import "KSVipManager.h"
#import "KSActionSheetOld+Item.h"
#import "KSAlertTipView.h"
#import "KSPrivacyFootprintUserListViewController.h"
#import "JceKSVip_AddInvisibleListRsp.h"
#import "JceKSVip_emGetInvisibleList.h"
#import "KSPersonalGiftVC.h"
#import "KSTraceReprotHelper_V2+Feed.h"
#import "KSTraceReprotHelper_V2+UgcDetail.h"
#import "KSongInfoManager.h"
#import "JceProfile_VerifyResult.h"
#import "KSPhoneCardManager.h"
#import "KSUserProfileVC_V2+Play.h"
#import "KSUserProfileVC_V2+HolidayReceiceCard.h"
#import "KSABTestManager.h"
#import "KSLocalDataManager.h"
#import "KSTimelineBaseVC+Input.h"
#import "KSTimelineBaseVC+Statistic.h"
#import "KSMultiAccountViewController.h"
#import "KSMultiAccountManager.h"
#import "KSChatRoomNavigationManager.h"
#import "KSUserProfileChorusListVC.h"
#import "KSUserProfileVC_V2+Upload.h"
#import "KSViewSongListFolderVC.h"
#import "KSUserAccompanyInfo.h"
#import "KSUgcRecycleBinVC.h"
#import <KSBasicUI/KSGridActionSheet.h>
#import "KSMultiAccountAddView.h"
#import "KSRelationManager.h"
#import "KSTeachSingComTableCell.h"
#import "KSSyncToMultiAccountManager.h"
#import "KSRecordFrompage.h"
#import "KSTeenModeManager.h"
#import "KSTeachSingCourseManager.h"
#import "KSTeachSingCourseManager.h"
#import "KSTeachSingCourseManager.h"
#import "KSTeachCourseListViewController.h"
#import "JceDcreport_emReportType.h"
#import "JceKG_AbtestRspItem.h"
#import "JceKG_HcSongInfo.h"
#import "JcePayAlbum_GroupChatInfoItem.h"
#import "JceProfile_AuthKey.h"
#import "JceProfile_AvatarKtvInfo.h"
#import "KSAvatarDownloadManager.h"
#import "KSDetailPlaybackModel.h"
#import "KSDrawItemAction.h"
#import "KSFileDownloadManager.h"
#import "KSGlobalPlayItem.h"
#import "KSLiveShowRoom.h"
#import "KSLoginManager.h"
#import "KSMailSessionDetailViewController.h"
#import "KSPathReportManager.h"
#import "KSRecommendModel.h"
#import "KSRecordStrategyReport.h"
#import "KSRecordingData.h"
#import "KSRecordingStrategies.h"
#import "KSReportManager.h"
#import "KSTeachSingComTableCellModel.h"
#import "KSTimelineForward.h"
#import "KSTraceReportEnumV2.h"
#import "KSTraceReportManager+BuyKB.h"
#import "KSTraceReportModel_V2.h"
#import "KSUIABTestConstants.h"
#import "KSUIABTestManager.h"
#import "KSUserProfileVC_V2+UI.h"
#import "KSUserVip.h"
#import "KSimpleFeed.h"
#import "KSABTestManager+Common.h"
#import "KSong+Common.h"
#import "KSReachability.h"
#import "KSCellUser.h"
#import "KSBadgeUpdateManager.h"
#import "KSUserProfileRemarkDialogView.h"
#import "KSLoginManager+ThirdPartyAuthDialog.h"
#import "KSTaskCenterManager.h"
#import "KSHippyViewController.h"
#import "KSAuthBitmapHelper.h"
#import "FileUpload.h"
#import "JceTimeline_Detail_HitedSingGuide.h"
#import "KSLocalPlayViewController.h"
#import "KSShareActionReportMgr.h"
#import "KSVoiceStatusUtility.h"
#import "JceTimeline_VoiceInfo.h"
#import "JceProfile_RoomBasicInfo.h"
#import "JceProfile_KtvInfo.h"
#import "proto_live_room_launch_LiveRoomLaunchQueryProfileLiveEntranceRsp.h"
#import "proto_webapp_heat_card_GetHotIndicatorRsp.h"
#import "KSRecCellModel.h"
#import "KSEffectSoundManager.h"
#import "KSCRMManager.h"
#import "KSNavigationManager.h"
#import "KSPhotoManager.h"
#import "KSSettingOldManVC.h"
#import "KSGlobalPlayFloatingWindowManager.h"
#import "KSBackgroundCompositeManager.h"
#import "KSProductUploadManagerBridge.h"
#import "KSCommonModel.h"
#import "KSTimelineSortRankInfo.h"
#import "KSActionSheetFilterMusicTypeItem.h"
#import "NS_UGC_SEARCH_emUgcSearchType.h"
#import "KSMusicTypeFilterSheetModel.h"
#import "KSMusicTypeFilterSheetSubView.h"
#import "KSMusicTypeFilterSheetParentView.h"
#import "KSVipManager+VipIcon.h"
#import "KSUpFansRightManager.h"
#import <KSFoundation/KSDateFormatterHelper.h>
#import "proto_main_page_webapp_MainPageIconStyle.h"
#import "proto_main_page_webapp_MainPageIconID.h"
#import "NSDate+ComUtils.h"
#import "proto_profile_DataplateInfo.h"
#import "proto_vip_dataplate_webapp_DataplateItem.h"
#import "KSUserInfo+Follow.h"
#import "KSCloseFriendManager.h"
#import "KSAccountWXChannelBindTipVC.h"
#import "proto_account_microservice_WebappFinderGetCodeRsp.h"
#import "proto_main_page_webapp_emJumpActionType.h"
#import "proto_main_page_webapp_JumpAction.h"
#import "KSUserProfileMusicToolBarCell_v2.h"
#import "proto_main_page_webapp_AnchorCenterEntrance.h"
#import "KSLiveShowHelper.h"
#import "proto_vip_dataplate_webapp_MarketDataplateItem.h"
#import "proto_widget_webapp_WidgetGetRsp.h"
#import "proto_widget_webapp_WidgetItem.h"
#import <KSJceFiles/proto_ugc_ranking_UgcRankProfileItem.h>
#import <KSJceFiles/proto_ugc_friend_rank_comm_DrainageUnionItem.h>
#import <KSPresentation/UIViewController+KSPresentation.h>
#import <KSPresentation/KSPresentTask.h>
#import "KSUserProfileVC_V2+closeFriend.h"
#import "KSPersonalWidgetManager.h"
#import "KSPersuadePushManager.h"
#import "NSString+KSXPI.h"
#import "JceKG_BuildItem.h"
#import "JceKG_UserItem.h"
#import "proto_kg_badge_NewReachHeader.h"
#import "KSShopProductURLInfo.h"
#import "NSString+URLEncoding.h"
#import "KSPlayletConstants.h"
#import "KSShopProductURLInfo.h"
#import "NSString+URLEncoding.h"
#import "KSUserWorksOnListViewController.h"
#import "proto_kg_badge_NewReachHeader.h"
#import "KSLiveShowECommerceService.h"
#import "proto_main_page_tmem_MonitorUrl.h"
#import "KSPlayletRouter.h"
#import "KSLiveShowHelper+Common.h"
#import "proto_ai_svc_GetModelEntranceRsp.h"
#import "proto_ai_svc_ModelInfo.h"
#import "proto_ai_svc_Activity.h"
#import "proto_ai_svc_SongInfo.h"
#import "KSModelEntranceInfo.h"
#import "proto_main_page_webapp_EcommerceEntrance.h"
#import "proto_relation_res_RelationResItem.h"
#import "KSLiveShowManager.h"
#import "JceKG_proto_aggregation_rank_webapp_QueryUserHonorRsp.h"
#import "TMEKTVPayManager.h"
#import "JceKG_proto_ai_svc_emGuideType.h"
#import "KSAdFreeGiftScene.h"
typedef NS_ENUM(NSInteger, DrawerTipStyle) {
    DrawerTipStyle_None = 0,
    DrawerTipStyle_RedPoint = 1,  // 抽屉红点
    DrawerTipStyle_Different = 2, // 抽屉异化
};


#ifdef INTERNALBUILD
#import "KSDebugPullLogInfoView.h"
#endif

#define HeatPushDaysKey @"ksHostFansHeatPushDaysKey"
#define HeatPushTimesKey @"ksHostFansHeatPushTimesKey"
// VIP banner直接支付动作参数key
#define VipDirectPayActivityCodeKey @"activity_code"


@class KSimpleFeedManager;

typedef NS_ENUM(NSUInteger, KSActionSheetOldButtonType) {
    KSActionSheetOldButton_Privacy     = 1,
    KSActionSheetOldButton_Black       = 2
};

typedef NS_ENUM(NSUInteger, KSActionSheetOperationType) {
    KSActionSheetOperation_Add      = 1,
    KSActionSheetOperation_Remove   = 2
};

@implementation KSUserProfileVC_V2 (Action)

#pragma mark - NarBar Action
- (void)moreBtnClickAction:(UIButton *)sender
{
    if (self.isGuest)
    {
        [self showProfileSidePageWithIsGuest:self.isGuest];
        
        [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
            reportModel.key = @"homepage_guest#top_line#more#click#0";
            reportModel.commonInt7 = self.userID;
        }];
    }
    else
    {
        self.hideSingerGuide = YES;
        [self.singerGuideTip removeFromSuperview];
        [self.navBar.rightNavBarBtn setUnreadNumber:0];
        [self.moreButton setImage:[self getNavMoreButtonImage] forState:UIControlStateNormal];

        /// 新版弹出侧边栏
        [self showProfileSidePageWithIsGuest:self.isGuest];
        

        if (self.moreBtnIRedDot == 0) {
            [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                reportModel.key = @"homepage_me#top_line#more#click#0";
                reportModel.commonInt2 = 0;
            }];

        } else {

            DrawerTipStyle style = [self getDrawStyle];
            [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                reportModel.key = @"homepage_me#top_line#more#click#0";
                reportModel.commonInt2 = style; // 这里都是抽屉按钮无异化点击进来 // 或者红点 这里上报有改动一下
            }];
        }

    }
    // 消除红点数据
    self.moreBtnIRedDot = 0;

}

- (void)backBtnDidClick
{
    BOOL showPersuadeAlert = [KSPersuadePushManager showPersuadeAlertIfNeed:KSPersuadePushScene_Personal];
    if (showPersuadeAlert) {
        return;
    }
    [super backBtnDidClick];
}

- (DrawerTipStyle)getDrawStyle {
    for (proto_main_page_webapp_DrawerIconStyle* drawIconStyle in self.drawerIconStyleList.jce_vctItems) {
        if (!IS_EMPTY_STR_BM(drawIconStyle.jce_strIconURL)) {
            return DrawerTipStyle_Different;

        } else if (drawIconStyle.jce_iRedDot != 0) {
            return DrawerTipStyle_RedPoint;
        }
    }
    return DrawerTipStyle_None;
}



- (void)moreDrawerBtnClickAction
{
    if (self.isGuest)
    {
        [self showProfileSidePageWithIsGuest:self.isGuest];
        
        [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
            reportModel.key = @"homepage_guest#top_line#more#click#0";
            reportModel.commonInt7 = self.userID;
        }];
    }
    else
    {
        self.hideSingerGuide = YES;
        [self.singerGuideTip removeFromSuperview];
        [self.navBar.rightNavBarBtn setUnreadNumber:0];

        /// 新版弹出侧边栏
        [self showProfileSidePageWithIsGuest:self.isGuest];
        
        [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
            reportModel.key = @"homepage_me#top_line#more#click#0";
            reportModel.commonInt2 = self.drawStyleText ? 2 : (self.moreBtnIRedDot = 0 ? 0 : 1); //todo 取值可能有问题
        }];
    }
}

- (void)settingBtnClickAction:(id)sender {
    
    self.needLoadHolidayBless = YES;
    [KSLocalConfigManager saveLocalConfig:KSUserHasClickedSettingBtnKey boolValue:YES isGolbal:NO];
    KSSettingVC *settingVC = [[KSSettingVC alloc] init];
    [self.navigationController pushViewController:settingVC animated:YES];
    
    // 隐藏设置红点
    [self hideSettingButtonRedPoint];
    [[KSEffectSoundManager sharedInstance] recordUserSuperSoundGuideClickEvents:KSSuperSoundGuideRedPointType_ProfileSetting];
}

- (void)shareBtnClickAction:(id)sender {
    [self showShareVC];
    [self traceClickReport:ReportSubactionL3_UserProfile_Click_Share];
    if (!self.isGuest) {
        [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
            reportModel.key = @"homepage_me#top_line#share#click#0";
        }];
    }
    [[KSShareActionReportMgr sharedManager] reportShareButtonIsClick:YES shareContentType:KSReportShareContent_UserProfile];
}

- (void)scanBtnClickAction:(id)sender
{
    [[KSNavigationManager sharedManager] showQRCodeReaderVCWithType:0];
    [self traceClickReport:ReportSubactionL3_UserProfile_Click_Scan];

    if (!self.isGuest)
    {
        [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
            reportModel.key = @"homepage_me#top_line#scan#click#0";
        }];
    }
}

- (void)switchBtnClickAction:(id)sender {
    [self handleSwitchAccountClick];

    [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
        reportModel.key= @"homepage_me#personal_information#change_account#click#0";
        reportModel.commonInt1 = [KSMultiAccountManager sharedManager].totalBadgeNum > 0 ? 3 : 1;
        reportModel.commonInt2 = [self showSwitchOrAddPannel] ? 1 : 2;
    }];
}

- (void)handleSwitchAccountClick {
    if ([self showSwitchOrAddPannel]) {
        KSMultiAccountViewController *vc = [KSMultiAccountViewController showMultiAccountView];
        [[KSMultiAccountManager sharedManager] refreshMultiAccountListIfAllow:^(id ksObject, NSDictionary *customInfo, NSError *error) {
            [vc reload];
        }];
    } else {
        // 添加
        BOOL enableTeenModeButton = WnsSwitchBoolConfig(@"EnableTeenModeAccountButton");
        KSMultiAccountAddView *addView = [[KSMultiAccountAddView alloc] initWithFrame:self.view.bounds enableTeenModeButton:enableTeenModeButton];
        [addView showInWindow:1];
    }
}

- (BOOL)showSwitchOrAddPannel {
    BOOL showSwitchPannel = NO;
    NSArray *arr = [[KSMultiAccountManager sharedManager] getMultiAccountListWithHiddenAccount];
    if (arr.count > 1) {
        // 明确超过了1个账号， 就要展示多账号切换面板
        showSwitchPannel = YES;
    } else if ([KSMultiAccountManager sharedManager].wnsInfoLoadStatus != KSPageListLoadStatus_Success) {
        // 小号列表还没拉到，也要展示多账号切换面板；因为担心已经存在小号了。
        showSwitchPannel = YES;
    }

    return showSwitchPannel;
}

/// 点击隐身访问
- (void)onVisibleAccessClickWithType:(KSActionSheetOperationType)operateType
{
    if (operateType == KSActionSheetOperation_Add) {

        [[KSVipTraceReportManager sharedManager] vipPrivacySettingClickReport:VipPid_Privacy_Footprint_Click_Entrance_p3 int1:0 toUid:0];
        //需要vip才能进入列表
        if (!GetFlexBOOL([[KSLoginManager sharedInstance].curUserInfo.userVipInfo isKSVip])) {
            // 体验类VIP 及 非会员 都不能使用此特权
            id<KSBuyVipViewProtocol> buyVipView = [KSBuyVipViewHelper initWithType:KSBuyVipTypeDefault
                                                              titleString:KString(@"隐身访问为VIP特权")
                                                        descriptionString:KString(@"不留足迹，随心访问与播放，可对任意用户进行设置，可设高达50人。")];
            buyVipView.subVipBuyType = KSBuyVipSubType_HideFootprint;
            [buyVipView showInView:self.view];
            return;
        }

        KS_WEAK_SELF(self);
        [[KSVipManager sharedManager] addInvisiableUserList:@[@(self.userProfileInfo.userInfo.userId)] completionBlock:^(id ksObject, NSDictionary *customInfo, NSError *error) {

            KS_STRONG_SELF(self);
            if (!self) return;
            if (!ksObject) {
                KErrLog(@"%@", error);
                [self onMgrError:error];
            } else if ([ksObject isKindOfClass:[JceKSVip_AddInvisibleListRsp class]]) {

                JceKSVip_AddInvisibleListRsp *rsp = (JceKSVip_AddInvisibleListRsp *)ksObject;

                if (rsp.jce_uAuthStatus == JceKSVip_emGetInvisibleList_ENUM_VIP_ADD_NOT_ALLOW) {
                    KSBasicDialog *dialog = [[KSBasicDialog alloc] init];
                    dialog.caption = KString(@"超出人数限制");
                    [dialog addContentText:rsp.jce_strNoticeMsg];
                    KS_WEAK_SELF(self);
                    [dialog addControlButton:KString(@"取消")
                                    subtitle:nil
                                 buttonStyle:KSBasicDialogButtonStyle_Normal
                                    callback:^(KSDialog * _Nonnull dialog) {
                                            CHECK_SELF_AND_RETURN();
                                            [dialog dismiss];
                                            [[KSVipTraceReportManager sharedManager] vipPrivacySettingClickReport:VipPid_Privacy_Footprint_Click_Entrance_p6 int1:0 toUid:0];
                    }];
                    [dialog addControlButton:KString(@"查看设置")
                                    subtitle:nil
                                 buttonStyle:KSBasicDialogButtonStyle_Emphasis
                                    callback:^(KSDialog * _Nonnull dialog) {
                                            CHECK_SELF_AND_RETURN();
                                            [dialog dismiss];
                                            [[KSVipTraceReportManager sharedManager] vipPrivacySettingClickReport:VipPid_Privacy_Footprint_Click_Entrance_p5 int1:0 toUid:0];
                        // 跳转隐身访问页
                        KSPrivacyFootprintUserListViewController *footprintVC = [[KSPrivacyFootprintUserListViewController alloc] init];
                        [self.navigationController pushViewController:footprintVC animated:YES];
                    }];

                    [dialog showInView:self.view];
                    [[KSVipTraceReportManager sharedManager] vipPrivacySettingExposureReport:@[@(VipPid_Privacy_Footprint_Exposure_Entrance_p5),
                                                                                               @(VipPid_Privacy_Footprint_Exposure_Entrance_p6)]
                                                                                        int1:0
                                                                                       toUid:0];
                } else if (rsp.jce_uAuthStatus == JceKSVip_emGetInvisibleList_ENUM_VIP) {
                    // 更新隐藏访问列表
                    self.userProfileInfo.userInfo.isInInvisibleList = YES;
                    [self fetchInvisiableUserList];

                    if (LocalBoolConfig(@"kUserHasSetInvisiableUserFirstSuccess")) {
                        [self onMgrFinishToast:KString(@"设置成功")];
                    }
                    else {
                        KSBasicDialog *dialog = [[KSBasicDialog alloc] init];
                        __block BOOL checkResult = YES;
                        dialog.caption = @"隐身访问设置成功";
                        [dialog addContentText:@"您过去对TA的访问痕迹将被同时删除，对方无法在最近观众列表看到您对其主页和作品的访问记录。" withAlignment:NSTextAlignmentLeft];
                        [dialog addContentCheckBox:@"下次不再提醒" checked:YES callback:^(KSDialog * _Nonnull dialog, BOOL checked) {
                            checkResult = checked;
                        }];
                        [dialog addControlButton:@"我知道了" subtitle:nil buttonStyle:KSBasicDialogButtonStyle_Bold callback:^(KSDialog * _Nonnull dialog) {
                            SaveLocalBoolConfig(@"kUserHasSetInvisiableUserFirstSuccess", checkResult);
                            [dialog dismiss];
                        }];
                        [dialog setBlankSpaceClickCallback:^(KSDialog * _Nonnull dialog) {
                            [dialog dismiss];
                        }];
                        [dialog show];
                        //VIP首次隐私访问弹窗曝光
                        [[KSVipTraceReportManager sharedManager] vipExposeWithExtraBlock:^(TraceVipReportCommon *reportCommon) {
                            reportCommon.posid = VipPid_Privacy_Footprint_Click_Process_p10;
                            reportCommon.rightid = VipRightId_PrivacyRight;
                        }];
                    }

                    [[KSVipTraceReportManager sharedManager] vipWriteReport:VipWriteID_PrivacyRight
                                                              subactiontype:VipWriteID_PrivacyRight_InvisiableList
                                                                   reserves:VipWriteID_PrivacyRight_InvisiableList_Add
                                                                    userVip:[KSLoginManager sharedInstance].curUserInfo.userVipInfo
                                                                    songMid:0
                                                                      ugcId:@""
                                                                    fileBit:0
                                                                      toUid:self.userProfileInfo.userInfo.userId];
                }
                else {
                    [self onMgrFinishToast:KString(@"设置失败")];
                }
            }
        }];

    } else if (operateType == KSActionSheetOperation_Remove) {

        [[KSVipTraceReportManager sharedManager] vipPrivacySettingClickReport:VipPid_Privacy_Footprint_Click_Entrance_p4 int1:0 toUid:0];
        KS_WEAK_SELF(self);
        [[KSVipManager sharedManager] delInvisiableUserList:@[@(self.userProfileInfo.userInfo.userId)] completionBlock:^(id ksObject, NSDictionary *customInfo, NSError *error) {
            KS_STRONG_SELF(self);
            if (!self) return;
            if (!ksObject) {
                KErrLog(@"%@", error);
                [self onMgrError:error];
                return ;
            }
            [self onMgrFinishToast:KString(@"设置成功")];
            // 更新隐藏访问列表
            self.userProfileInfo.userInfo.isInInvisibleList = NO;
            [self fetchInvisiableUserList];
            [[KSVipTraceReportManager sharedManager] vipWriteReport:VipWriteID_PrivacyRight
                                                      subactiontype:VipWriteID_PrivacyRight_InvisiableList
                                                           reserves:VipWriteID_PrivacyRight_InvisiableList_Del
                                                            userVip:[KSLoginManager sharedInstance].curUserInfo.userVipInfo
                                                            songMid:0
                                                              ugcId:@""
                                                            fileBit:0
                                                              toUid:self.userProfileInfo.userInfo.userId];
        }];

    } else {
        NSAssert(1, @"个人主页操作隐藏足迹操作位失效！");
    }
}

/// 点击备注名
- (void)onRemarknameClickAction
{
    KS_WEAK_SELF(self);
    KSBasicDialog *dialog = [[KSBasicDialog alloc] init];
    dialog.caption = @"设置备注名";

    KSUserProfileRemarkDialogView *remarkContent = [[KSUserProfileRemarkDialogView alloc] initWithFrame:CGRectMake(0, 0, 315, 71.5)];
    [remarkContent updatePlaceholderMsg:@"无备注名"];

    NSString *curRemark = [self.userProfileInfo.userInfo remarkNameWithType:NS_PROFILE_RemarksKey_E_REMARKS_KG_MARK];
    if (!IS_EMPTY_STR_BM(curRemark)) {
        [remarkContent updateInputText:curRemark];
    }

    NSString *thirdNick = [self.userProfileInfo.userInfo remarkNameWithType:NS_PROFILE_RemarksKey_E_REMARKS_THIRD_NICK];
    NSString *relationStr = nil;
    if (!IS_EMPTY_STR_BM(thirdNick)) {
        if ((self.userProfileInfo.userInfo.relationFlag & JceProfile_VerifyResult_VERIFYRESULT_QQ_FRIEND)) {
            relationStr = [NSString stringWithFormat:@"QQ好友 : %@", thirdNick];
        } else if (self.userProfileInfo.userInfo.relationFlag & JceProfile_VerifyResult_VERIFYRESULT_WEIXIN_FRIEND) {
            relationStr = [NSString stringWithFormat:@"微信好友 : %@", thirdNick];
        }
    }
    [remarkContent updateTipsMsg:relationStr textColor:UIColor.ks_secondaryTextColor];

    remarkContent.textDidChangeBlk = ^(NSString * _Nonnull inputText, KSUserProfileRemarkDialogView * _Nonnull contentView) {
        CHECK_SELF_AND_RETURN();
        NSInteger RemarkNameMaxLength = WnsSwitchIntegerConfig(@"RemarkNameMaxLength");
        if (RemarkNameMaxLength <= 0) {
            RemarkNameMaxLength = 12;
        }
        if (inputText.length > RemarkNameMaxLength) {
            [contentView updateTipsMsg:@"超过字数限制" textColor:UIColor.ks_brandColor];
        } else {
            [contentView updateTipsMsg:relationStr textColor:UIColor.ks_secondaryTextColor];
        }
    };

    [dialog addContentView:remarkContent];
    [dialog setBlankSpaceClickCallback:^(KSDialog * _Nonnull dialog) {
        [dialog dismiss];
    }];
    [dialog addControlButton:@"取消" subtitle:nil buttonStyle:KSBasicDialogButtonStyle_Normal callback:^(KSDialog * _Nonnull dialog) {
        [dialog dismiss];
    }];

    [dialog addControlButton:@"确认" subtitle:nil buttonStyle:KSBasicDialogButtonStyle_Bold callback:^(KSDialog * _Nonnull dialog) {
        CHECK_SELF_AND_RETURN();
        NSString *nickName = [self.remarkContent curInputText];
        NSInteger RemarkNameMaxLength = WnsSwitchIntegerConfig(@"RemarkNameMaxLength");
        if (RemarkNameMaxLength <= 0) {
            RemarkNameMaxLength = 18;
        }
        if (nickName.length > RemarkNameMaxLength) {
            [self.remarkContent updateTipsMsg:@"超过字数限制" textColor:UIColor.ks_brandColor];
            return;
        }

        [self.remarkContent hideKeyboard];
        [self setRemarkWithUid:self.userProfileInfo.userInfo.userId nickName:self.remarkContent.curInputText completion:^(NSString *nickName, NSError *error) {
            CHECK_SELF_AND_RETURN();
            if (!error) {
                // 设置成功
                [dialog dismiss];
                [KSErrorHandler onMgrFinishToast:@"设置备注成功" duration:1.0];
                [self didTriggerRefresh];

                [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                    CHECK_SELF_AND_RETURN();
                    reportModel.key = @"remark_name#successfully_modified#null#write_edit_notes#0";
                    reportModel.touid = self.userProfileInfo.userInfo.userId;
                }];

            } else {
                if (error.code == 20002 || error.code == 20003) {
                    // 20002 敏感字
                    // 20003 字数问题
                    // 特殊处理，更新tipsMsg
                    [self.remarkContent updateTipsMsg:([error.userInfo objectForKey:@"errorMgr"]) textColor:UIColor.ks_brandColor];
                } else {
                    // 一般错误直接toast提示
                    [KSErrorHandler onMgrError:error];
                }
            }
        }];
    }];

    [dialog show];
    self.remarkContent = remarkContent;
    [self.remarkContent showKeyboard];


    [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
        CHECK_SELF_AND_RETURN();
        reportModel.key = @"personal_information_more#remark_name#null#click#0";
        reportModel.touid = self.userProfileInfo.userInfo.userId;
    }];

    [[NSUserDefaults standardUserDefaults] setBool:YES forKey:kGuideMaskKeyUserProfileRemarkNew];
}

/// 密友入口点击
- (void)onCloseFriendiconClickAction
{
    [KSCloseFriendManager.sharedManager navigateToCloseFriendPageWithUid:self.userID];
}

#pragma mark - Bottom Action
- (void)followControlClickActionWithRecommendCell:(KSRecommdUserCell *)cell {
    if (self.isRcmFollowRequesting)
    {
        return;
    }
    KSUserInfo *user = cell.recommendUser.recommendUserInfo;
    //点击事件上报
    NSString *key = GuestProfilePage_RecommendPeople_FollowBtnRead;
    [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
        reportModel.key = key;
        reportModel.touid = user.userId; //touid 上报所推用户的uid
        //int1 上报用户类型：0普通人，1明星已入驻，2明星未入驻
        if (!self.userProfileInfo.userInfo.isStar) {
            reportModel.commonInt1 = KSRecommendTraceReprotTypeNormal;
        } else {
            if (self.userProfileInfo.userInfo.isStarSettledIn) {
                reportModel.commonInt1 = KSRecommendTraceReprotTypeStarSettledIn;
            } else {
                reportModel.commonInt1 = KSRecommendTraceReprotTypeStarUnSettledIn;
            }
        }
        reportModel.commonInt7 = self.userProfileInfo.userInfo.userId; //int2上报所访问主页的uid
        reportModel.item_type = cell.recommendUser.itemType;
        reportModel.trace_id = cell.recommendUser.traceId;
        reportModel.algorithm_type = cell.recommendUser.algorithmType;
        reportModel.algoritym_id = cell.recommendUser.algoritymPara;
    }];

    if (!(user.relationFlag & JceRelation_VerifyResult_VERIFYRESULT_FOLLOW)) //未关注则发送关注请求
    {
        self.isRcmFollowRequesting = YES;
        KS_WEAK_SELF(self);
        [self addFollowWithRecommendUser:cell.recommendUser complation:^(NSError *error) {
            if (error == nil) {
                KS_STRONG_SELF(self);
                KSUserProfileRecommendCell *collectionCell = [self.contentTableView cellForRowAtIndexPath:[NSIndexPath indexPathForRow:0 inSection:self.recommendSection]];
                //要先保存好indexPath
                NSIndexPath *indexPath = [collectionCell.collectionView indexPathForCell:cell];
                [collectionCell.collectionView reloadData];
                self.isRcmFollowRequesting = NO;
                [self performSelector:@selector(afterFollowRemoveCell:) withObject:indexPath afterDelay:0.5];
            }
        }];
    }
    else //已关注弹窗确认是否取关
    {
        [self alert:KString(@"不再关注此人?") title:nil btns:@[KString(@"不再关注"), KString(@"取消")] tag:KSUserProfileAlertType_cancelRecommendFollow];
    }
}

//点击关注-->已关注再消失
- (void)afterFollowRemoveCell:(NSIndexPath *)indexPath {
    if (!indexPath) {
        return;
    }
   // 需要延迟执行的代码
    KSUserProfileRecommendCell *cell = [self.contentTableView cellForRowAtIndexPath:[NSIndexPath indexPathForRow:0 inSection:self.recommendSection]];
    [cell.item.dataList safeRemoveObjectAtIndex:indexPath.row];
    if (indexPath) {
        [cell.collectionView deleteItemsAtIndexPaths:@[indexPath]];
    }
}

- (void)followControlClickAction:(id)sender
{
    BOOL isFansAndFollow = [KSUserInfo isFollowingUser:self.userProfileInfo.userInfo] &&
                    [KSUserInfo isBeenFollowed:self.userProfileInfo.userInfo];
    if (isFansAndFollow && self.userProfileInfo.userInfo.closeFriendInfo.jce_bIntroduce) {
        /// 互关用户+后台开关, 弹出邀请密友+取消关注actionSheet
        [self showInviteCloseFriendActionSheet];
        return;
    }

    if (self.isFollowRequesting) {
        return;
    }
    if (!(self.userProfileInfo.userInfo.relationFlag & JceRelation_VerifyResult_VERIFYRESULT_FOLLOW)) {
        self.isFollowRequesting = YES;
        [self addFollow];
        [self traceReportFollowUnFollowAction:1];
        
        // 添加动画
        KSAdFreeGiftScene *personalScene = [KSAdFreeGiftScene sceneWithType:KSAdFreeGiftSceneType_Personal];
        BOOL sceneShow = personalScene.scenceShow;
        NSInteger giftBtnNum = [personalScene getGiftBtnNum];
        if (sceneShow && giftBtnNum == 2) {
            
        }
    } else {
        [self alert:KString(@"不再关注此人?") title:nil btns:@[KString(@"不再关注"), KString(@"取消")] tag:KSUserProfileAlertType_cancelFollow];
        [self traceReportFollowUnFollowAction:2];
    }

    [self traceClickReport:ReportSubactionL3_UserProfile_Click_FollowBtn];
}

- (void)showInviteCloseFriendActionSheet
{
    KSActionSheet *actionSheet = [[KSActionSheet alloc] init];
    NSMutableArray *items = [NSMutableArray array];

    NSInteger inviteTag = 1;
    KSActionSheetItem *inviteItem = [[KSActionSheetItem alloc] init];
    inviteItem.title = KString(@"成为密友");
    inviteItem.tag = inviteTag;
    [items safeAddObject:inviteItem];

    NSInteger cancelFollowTag = 2;
    KSActionSheetItem *cancelFollowItem = [[KSActionSheetItem alloc] init];
    cancelFollowItem.title = KString(@"取消关注");
    cancelFollowItem.tag = cancelFollowTag;
    [items safeAddObject:cancelFollowItem];

    actionSheet.items = [items copy];
    KS_WEAK_SELF(self);
    [actionSheet setItemClickCallback:^(KSActionSheet * _Nonnull actionSheet, KSActionSheetItem * _Nonnull item) {
        CHECK_SELF_AND_RETURN();
        [actionSheet close];
        if (item.tag == inviteTag) {
            /// 邀请密友
            [KSCloseFriendManager.sharedManager inviteCloseFriendWithTargetUid:self.userProfileInfo.userInfo.userId
                                                                          from:KSCloseFriendInviteFromProfile];
            [self traceReportFollowUnFollowAction:3];

        } else if (item.tag == cancelFollowTag) {
            [self alert:KString(@"不再关注此人?") title:nil btns:@[KString(@"不再关注"), KString(@"取消")] tag:KSUserProfileAlertType_cancelFollow];
            [self traceReportFollowUnFollowAction:2];
        }
    }];

    [actionSheet setCancelClickCallback:^(KSActionSheet * _Nonnull actionSheet) {
        [actionSheet close];
    }];

    actionSheet.cancelString = @"取消";
    [actionSheet show];
}


- (void)privateLetterControlClickAction:(id)sender
{
    // 进入私信详情是否自动发送来自同城消息
    BOOL isSendNearByMsg = NO;
    if (self.extroInfo && [self.extroInfo objectForKey:@"isSendNearByMsg"]) {
        isSendNearByMsg = [self.extroInfo[@"isSendNearByMsg"] boolValue];
    }

    [[KSNavigationManager sharedManager] showMailSessionDetail:self.userID driftBottle:self.driftBottle isSendNearByMsg:isSendNearByMsg];
    
    [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
        reportModel.key = @"homepage_guest#personal_information#direct_message#click#0";
        reportModel.commonInt7 = self.userID;
        reportModel.open_relationtype = YES;
        reportModel.touid = self.userID;
        reportModel.from_page = self.from_page;
        reportModel.commonStr9 = [[KSUpFansRightManager sharedManager] getTraceValueInfoWithKey:NSLLToString(self.userID)];
        if (reportModel.commonStr9.length == 0) {
            reportModel.commonStr9 = self.fromPageTraceId;
        }
    }];
}


- (void)vipTipBtnDidClick
{
    NSString *pid = NSIToString(VipPid_CommonTip_p1);
    if(!GetFlexBOOL([[KSLoginManager sharedInstance].curUserInfo.userVipInfo isKSVip]))
    {
        pid = NSIToString(VipPid_CommonTip_p2);
    }

    NSString *aid = [KSPayManager generateAid:pid];

    [[KSVipTraceReportManager sharedManager] vipClickReport:0
                                                        aid:aid
                                                    rightid:NSIToString(VipRightId_NONE)
                                                      posid:pid
                                                    userVip:[KSLoginManager sharedInstance].curUserInfo.userVipInfo songMid:@""
                                                      ugcId:@""
                                                       int1:self.personalPageBottomItem.jce_uId];


    id<KSBuyVipViewProtocol> buyVipView = [KSBuyVipViewHelper initWithMainType:KSBuyVipTypeRecharge subType:KSBuyVipSubTypeDefault];
    UIViewController *topVC = [[[[KSNavigationManager sharedManager] getMainPageNavController] viewControllers] lastObject];
    [buyVipView showInView:topVC.view];
}

- (void)showVipPortalView
{
    NSString *aid = [KSPayManager generateAid:NSIToString(VipPid_ProfileIcon)];
    NSString *url = [[WnsConfigManager sharedInstance].appConfig.urlConfig getVipPageUrl:aid pf:@"" actSource:[[KSVipTraceReportManager sharedManager] getActSourceID:0] topSource:[[KSVipTraceReportManager sharedManager] getTopSource:0]];
    if (self.personalPageBottomItem)
    {
        url = [url stringByReplacingOccurrencesOfString:@"$advertId" withString:[NSString stringWithFormat:@"%d",self.personalPageBottomItem.jce_uId]];
    }

    //数据上报
    NSString *pid = NSIToString(VipPid_CommonTip_p1);
    if(!GetFlexBOOL([[KSLoginManager sharedInstance].curUserInfo.userVipInfo isKSVip]))
    {
        pid = NSIToString(VipPid_CommonTip_p2);
    }
    [[KSVipTraceReportManager sharedManager] vipClickReport:0
                                                        aid:[KSPayManager generateAid:pid]
                                                    rightid:NSIToString(VipRightId_NONE)
                                                      posid:pid
                                                    userVip:[KSLoginManager sharedInstance].curUserInfo.userVipInfo songMid:@""
                                                      ugcId:@""
                                                       int1:self.personalPageBottomItem.jce_uId];

    [[KSNavigationManager sharedManager] showWebView:url];
}

#pragma mark - 上热门push频控

// 频控，出现N次的判断
- (BOOL)hasShowMultipleTimes:(NSInteger)times forKey:(NSString *)uniqueKey
{
    if (IS_EMPTY_STR_BM(uniqueKey)) {
        return YES;
    }
    BOOL hasShow = NO;
    NSString *realStoreKey = [NSString stringWithFormat:@"%@",uniqueKey];
    NSString *realStoreValue = LocalStringConfig(realStoreKey);
    NSInteger hasShowTimes = 0;
    if (realStoreValue.length > 0) {
        hasShowTimes = [realStoreValue integerValue];
    }
    if (hasShowTimes >= times) {
        hasShow = YES;
    }
    return hasShow;
}

// 频控，出现N次的判断
- (void)saveMultipleTimesForKey:(NSString *)uniqueKey
{
    if (IS_EMPTY_STR_BM(uniqueKey)) {
        return;
    }

    NSString *realStoreKey = [NSString stringWithFormat:@"%@",uniqueKey];
    NSString *realStoreValue = LocalStringConfig(realStoreKey);
    NSInteger hasShowTimes = 0;
    if (realStoreValue.length > 0) {
        hasShowTimes = [realStoreValue integerValue];
    }

    SaveCommonLocalConfig(realStoreKey, NSIToString(hasShowTimes + 1));
}

#pragma mark - PopManager
/// pop完成时调用
- (void)doCommonWithType:(KSProfilePopType)type {
    switch (type) {
        case KSProfilePopTypeNone:
        case KSProfilePopTypeCard:
            [self refreshHeaderCloseFriendWidget];
            break;
        case KSProfilePopTypeCloseFriend:
        default:
            break;
    }
    // 跳转个人主页时打开半屏hippy
    if (self.extroInfo && !IS_EMPTY_STR_BM([self.extroInfo ks_stringValueForKey:@"openSchemeUrl"]) && self.skipShowHippy) {
        self.skipShowHippy = NO;
        NSString *url = [self.extroInfo ks_stringValueForKey:@"openSchemeUrl"];
        BOOL halfHippy = YES;
        NSRange range = [url rangeOfString:@"_hwv="];
        if (range.location != NSNotFound) {
            NSRange resultRange;
            resultRange.location = range.location + range.length;
            resultRange.length = 1;
            NSString *result = [url KSXPI_safeSubstringWithRange:resultRange];
            if (IS_EMPTY_STR_BM(result) || ![result isEqualToString:@"1"]) {
                halfHippy = NO;
            }
        } else {
            halfHippy = NO;
        }
        url = [url URLEncodedString];
        if (halfHippy) {
            url = [NSString stringWithFormat:@"qmkege://kege.com?action=hippyview&url=%@", url];
        } else {
            url = [NSString stringWithFormat:@"qmkege://kege.com?action=webview&url=%@", url];
        }
        NSInteger delaySeconds = [self.extroInfo integerValueForKey:@"delaySeconds"];
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(delaySeconds * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [KSNavigationManager.sharedManager dealWithScheme:url];
        });
    }
}


#pragma mark - KSUserProfileHeaderViewDelegate

- (void)userProfileHeaderViewClickByType:(KSUserProfileHeaderClickType)type
{

    KSUserInfo *userInfo = self.userProfileInfo.userInfo;
    switch (type)
    {
        case KSUserProfileHeaderClickType_MedalNamePlate:
        {
            // 通知已经跳转到铭牌购买
            [self jumpToMedalShop];
        }
            break;
        case KSUserProfileHeaderClickType_BgPhoto:
        {
            [self showBgPhotoOptionMenu];
            break;
        }
        case KSUserProfileHeaderClickType_Avatar:
        {
            [self didClickOnUserHeadView];

            break;
        }
        case KSUserProfileHeaderClickType_Family:
        {
            [self didTapFamilyLabel];
        }
            break;
        case KSUserProfileHeaderClickType_Nickname:
        {
            break;
        }
        case KSUserProfileHeaderClickType_VIP:
        {
            // VIP Icon 点击
            VipPid posid = self.isGuest ? VipPid_ProfileGuestIcon : VipPid_ProfileIcon;
            NSString *fromPage = self.isGuest ? @"homepage_guest#personal_information#vipicon" : @"homepage_me#personal_information#vipicon";

            BOOL shouldJumpTpVipCenter = KSABTestManager.sharedManager.shouldVIPIconClickJumpToVIPCenter;
            NSString *url = @"";
            if (shouldJumpTpVipCenter) {
                KSUserVip *userVip = [KSUserVip userVipStatus:self.userProfileInfo.userInfo.sMapAuth];
                NSDictionary *customParams = (userVip.superVipStatus == 1)? @{ @"$vip": @"svip" } : nil;
                url = [KSVipManager getVIPCenterUrlWithPosID:posid customParams:customParams];
            }
            [KSVipManager vipIconComponentClickToUser:self.userProfileInfo.userInfo handlePresentVip:YES posid:posid vipUrl:url];
            [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                reportModel.key = @"vip_icon#icon#null#click#0";
                reportModel.from_page = fromPage;
            }];
            break;
        }
        case KSUserProfileHeaderClickType_Fans:
        {
            if(!self.isGuest)
            {
                //主人态进入粉丝的时候要abtest,客人态不用
                // 因为有可能走WNS配置，和安卓对齐，从替换参数改为拼接参数字符串的形式
                NSString *strUrl = WnsUrlStringConfig(@"MyFanPageUrl");
                strUrl = [strUrl urlStringByAppendingParameter:[NSString stringWithFormat:@"act_source=%@", SAFE_STR_BM([[KSVipTraceReportManager sharedManager] getActSourceID:0])]];
                strUrl = [strUrl urlStringByAppendingParameter:[NSString stringWithFormat:@"num=%ld", userInfo.fansCount]];
                [[KSNavigationManager sharedManager] showWebView:strUrl];
                // 上热门push
                if (![[NSUserDefaults standardUserDefaults] objectForKey:HeatPushDaysKey])
                {
                    NSDate *now = [NSDate date];
                    [[NSUserDefaults standardUserDefaults] setValue:now forKey:HeatPushDaysKey];
                    [[NSUserDefaults standardUserDefaults] synchronize];
                }
                // 2天内粉丝页+最近听众页最多push N次
                NSInteger shownDays = [NSDate diffBetweenDate1:[[NSUserDefaults standardUserDefaults] objectForKey:HeatPushDaysKey] date2:[NSDate date]];
                NSInteger pushTimes = [WnsSwitchStringConfig(@"HeatCardPushLimit") integerValue];
                if (shownDays <= 2 && ![self hasShowMultipleTimes:pushTimes forKey:HeatPushTimesKey]) {
                    KS_WEAK_SELF(self);
                    [self getHotIndicatorInfo:^(proto_webapp_heat_card_GetHotIndicatorRsp *rsp, NSError *err) {
                        CHECK_SELF_AND_RETURN();
                        if (!err) {
                            if (rsp.jce_hotFlag) {
                                [self saveMultipleTimesForKey:HeatPushTimesKey];
                            }
                        }
                    }];
                }
            }
            else
            {
                // 因为有可能走WNS配置，和安卓对齐，从替换参数改为拼接参数字符串的形式
                NSString *strUrl = WnsUrlStringConfig(@"OtherFanPageUrl");
                strUrl = [strUrl urlStringByAppendingParameter:[NSString stringWithFormat:@"uid=%lld", userInfo.userId]];
                strUrl = [strUrl urlStringByAppendingParameter:[NSString stringWithFormat:@"num=%ld", userInfo.fansCount]];
                [[KSNavigationManager sharedManager] showWebView:strUrl];
            }

            [self updateRedBadge];
            [[[KSNavigationManager sharedManager] getRootTabBarController] updateMsgTabRedPoint];
            
            if (!self.isGuest)
            {
                NSInteger commonInt1 = [KSBadgeUpdateManager sharedManager].newFollowRedPoint > 0;

                [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                    reportModel.key = @"homepage_me#personal_information#followers#click#0";
                    reportModel.commonInt1 = commonInt1;
                }];
            }
            else
            {
                [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                    reportModel.key = @"homepage_guest#personal_information#followers#click#0";
                    reportModel.commonInt7 = self.userID;
                    reportModel.open_relationtype = YES;
                    reportModel.touid = self.userID;
                    reportModel.from_page = self.from_page;
                }];
            }
            break;
        }
        case KSUserProfileHeaderClickType_Follows:
        {
            if (!self.isGuest)
            {
                NSString *strUrl = WnsUrlStringConfig(@"MyFollowPageUrl");
                [[KSNavigationManager sharedManager] showWebView:strUrl];

                [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                    reportModel.key = @"homepage_me#personal_information#following#click#0";
                }];
            }
            else
            {
                NSString *strUrl = WnsUrlStringConfig(@"OtherFollowPageUrl");
                strUrl = [strUrl stringByReplacingOccurrencesOfString:@"$uid" withString:[NSString stringWithFormat:@"%lld",userInfo.userId]];
                [[KSNavigationManager sharedManager] showWebView:strUrl];

                [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                    reportModel.key = @"homepage_guest#personal_information#following#click#0";
                    reportModel.commonInt7 = self.userID;
                    reportModel.open_relationtype = YES;
                    reportModel.touid = self.userID;
                    reportModel.from_page = self.from_page;
                }];
            }

            

            break;
        }
        case KSUserProfileHeaderClickType_Friends:
        {
            [[KSNavigationManager sharedManager] showMyFriendPage];

            

            if (!self.isGuest)
            {
                [[KSBadgeUpdateManager sharedManager] clearFindFriendProfileRedNum];
                NSInteger commonInt1 = ([KSBadgeUpdateManager sharedManager].newFriendRedPointNum +
                                        [KSBadgeUpdateManager sharedManager].newContactsRedPointNum +
                                        [KSBadgeUpdateManager sharedManager].newFriendBindRedPointNum +
                                        [KSBadgeUpdateManager sharedManager].newContactsBindRedPointNum +
                                        [KSBadgeUpdateManager sharedManager].mayYouKnowNewNum +
                                        [KSBadgeUpdateManager sharedManager].nearNewNum +
                                        [KSBadgeUpdateManager sharedManager].topVNewNum +
                                        ([KSLoginManager sharedInstance].canPullSocialRelation ? 0 : 1)) > 0;

                [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                    reportModel.key = @"homepage_me#personal_information#friends#click#0";
                    reportModel.commonInt1 = commonInt1;
                }];
            }

            break;
        }
        case KSUserProfileHeaderClickType_QMusic:
        {
            // 入驻歌手跳转
            [self jumpToOriginMusicianPage:userInfo.singerJumpUrl];
            break;
        }
        case KSUserProfileHeaderClickType_Uid:
        {
#ifdef INTERNALBUILD
            [self onClickDebugUserID:userInfo.userId];
#endif
            break;
        }
        case KSUserProfileHeaderClickType_Switch:
        {
            if (KSBackgroundCompositeManager.defaultManager.backgroundCompositeTasks.count > 0)
            {
                KSBasicDialog *dialog = [[KSBasicDialog alloc] init];
                dialog.caption = KString(@"后台合成进行中，无法切换账号，请稍后");
                [dialog addControlButton:@"确定" subtitle:nil buttonStyle:KSBasicDialogButtonStyle_Normal callback:^(KSDialog * _Nonnull dialog) {
                    [dialog dismiss];
                }];
                [dialog show];
            }
            else
            {
                bool stop = NO;
                NSArray<ProductUploadTask *> *tasks = [[KSProductUploadManagerBridge sharedManager] tasksForDisplayScene:KSUploadTaskDisplaySceneAll];
                for (ProductUploadTask *task in tasks)
                {
                    if (task.publishContent.state != kUploadState_Failed &&
                        task.publishContent.state != kUploadState_SyncFailed)
                    {
                        stop = YES;
                    }
                }

                if (stop)
                {
                    [self tempAlert:@"正在发布作品，无法切换账号" delay:3];
                }
                else
                {
                    UIView *contentView = self.view;
                    if (self.tabBarController) {
                        contentView = self.tabBarController.view;
                    }
                    KS_WEAK_SELF(self);
                    [[KSTeenModeManager sharedInstance] showTeenModeAlertWithConfigKey:nil contentView:contentView closeFrom:4 alertDidAppear:^{

                    } cancel:^{

                    } completion:^{
                        CHECK_SELF_AND_RETURN();
                        if ([self showSwitchOrAddPannel]) {
                            KSMultiAccountViewController *vc = [KSMultiAccountViewController showMultiAccountView];
                            [[KSMultiAccountManager sharedManager] refreshMultiAccountListIfAllow:^(id ksObject, NSDictionary *customInfo, NSError *error) {
                                [vc reload];
                            }];
                        } else {
                            // 添加
                            BOOL enableTeenModeButton = WnsSwitchBoolConfig(@"EnableTeenModeAccountButton");
                            KSMultiAccountAddView *addView = [[KSMultiAccountAddView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT) enableTeenModeButton:enableTeenModeButton];
                            [addView showInWindow:1];
                            [self.headerView updateSwitchBadgeByPoint:0];
                        }
                        [[KSMultiAccountManager sharedManager] setReportSwitchSource:accountSwitchSource_me];
                    }];
                }
            }


            [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                reportModel.key= @"homepage_me#personal_information#change_account#click#0";
                reportModel.commonInt1 = [KSMultiAccountManager sharedManager].totalBadgeNum > 0 ? 3 : 1;
                reportModel.commonInt2 = [self showSwitchOrAddPannel] ? 1 : 2;
            }];

            break;
        }

        case KSUserProfileHeaderClickType_Wealth:
        {
            if (self.isGuest) {
                [[KSNavigationManager sharedManager] jumpToWealthGlobalRank:userInfo.userId timestamp:userInfo.avatarTimestamp wealthLevel:userInfo.sAuthWealthLevel wealthCount:userInfo.sAuthWealthValue];
            } else {
                [[KSNavigationManager sharedManager] jumpToWealthGlobalRank];
            }
            [self traceClickReport:ReportSubactionL3_UserProfile_Click_Wealth];
            [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                if (self.isGuest) {
                    reportModel.key = @"homepage_guest#personal_information#wealth_label#click#0";
                } else {
                    reportModel.key = @"homepage_me#personal_information#wealth_label#click#0";
                }
                reportModel.touid = userInfo.userId;
                reportModel.commonInt1 = userInfo.sAuthWealthLevel;
            }];

            break;
        }
        case KSUserProfileHeaderClickType_Level: {
            NSString *uid = [NSString stringWithFormat:@"%lld", self.userID];
            NSString *url = [[WnsConfigManager sharedInstance].appConfig.urlConfig getLevelUrl:uid];

            [[KSNavigationManager sharedManager] showWebView:url];
            [self traceClickReport:ReportSubactionL3_UserProfile_Click_Level];
            break;
        }
        case KSUserProfileHeaderClickType_AnchorLevel: {
            [[KSNavigationManager sharedManager] showAnchorLevel:self.userProfileInfo.userInfo.userId extraFrom:@"profile" reportClickFrom:1];
            break;
        }
        case KSUserProfileHeaderClickType_Green: {
            NSString *url = [[WnsConfigManager sharedInstance].appConfig.urlConfig getVipUrl];
            [[KSNavigationManager sharedManager] showWebView:url];
            [self traceClickReport:ReportSubactionL3_UserProfile_Click_Green];
            break;
        }
        case KSUserProfileHeaderClickType_Invite_Family: {
            // 邀请加入家族
            [self inviteFamily:@"homepage_guest#explicit_family_invite_entry#null"];
            break;
        }
        case KSUserProfileHeaderClickType_AuthInfo: {
            [self didTapAuthInfo];
            break;
        }


        case KSUserProfileHeaderClickType_PhotoNum: {
            if (!self.isGuest) {
                [self showPhotosVC];
            } else {
                /// 客态点击头像旁边小尾巴，直接浏览头像+相册
                [self previewAvatarAndPhotoAblum];
            }
            break;
        }
        case KSUserProfileHeaderClickType_VipNamePlate: {
            NSString *jumpUrl = IS_EMPTY_STR_BM(userInfo.dataplateInfo.jce_dataplate.jce_strJumpUrl) ? KSUserProfileHeaderView_VipNamePlateBtn_JumpUrl : userInfo.dataplateInfo.jce_dataplate.jce_strJumpUrl;
            if (jumpUrl) {
                [[KSNavigationManager sharedManager] dealWithScheme:jumpUrl];
            }
        }
            break;
        case KSUserProfileHeaderClickType_VipMarketDataplate: {
            NSString *jumpUrl = IS_EMPTY_STR_BM(userInfo.dataplateInfo.jce_marketDataplate.jce_strSkipUrl) ? KSUserProfileHeaderView_VipNamePlateBtn_JumpUrl : userInfo.dataplateInfo.jce_marketDataplate.jce_strSkipUrl;
            if (jumpUrl) {
                [[KSNavigationManager sharedManager] dealWithScheme:jumpUrl];
            }
        }
            break;
        case KSUserProfileHeaderClickType_SurpriseGift: {
            [self openSupriseGiftHippyPage];
        }
            break;
        case KSUserProfileHeaderClickType_CloseFriend: {
            NSString *jumpUrl = self.widgetRsp.jce_stWidget.jce_strJumpURL;
            if (jumpUrl) {
                [[KSNavigationManager sharedManager] dealWithScheme:jumpUrl];
                [self traceReportIntimateFriendWidgetClick:self.widgetRsp uid:self.userID];
            }
            break;
        }
        case KSUserProfileHeaderClickType_Sign: {
            // 客人态不跳
            if (self.isGuest) {
                return;
            }

            //直接跳转到设置签名
            NSString *url = WnsStringConfig(kWnsConfig_Url, @"EditUserInfoUrl");
            [[KSNavigationManager sharedManager] showWebView:url];

            [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                reportModel.key = @"homepage_me#personal_information#signature#click#0";
            }];
        }
            break;
        case KSUserProfileHeaderClickType_AnchorLevelNew:
        {
            NSString *tureUrl = WnsUrlStringConfig(@"LiveAnchorLevelPageUrl");
            tureUrl = [tureUrl stringByAppendingString:@"%26from%3D0"];
            tureUrl = [tureUrl stringByReplacingOccurrencesOfString:@"%24anchorId" withString:[NSString stringWithFormat:@"%lld", self.userProfileInfo.userInfo.userId]];
            [[KSNavigationManager sharedManager] dealWithScheme:tureUrl];
            KLog(@"[dd] test ur:%@",tureUrl);
        }
            break;
        default:
            break;
    }
}

- (void)userProfileHeaderViewExposureByType:(KSUserProfileHeaderExposureType)type {
    switch (type) {
        case KSUserProfileHeaderExposureType_CloseFriend:
            [self traceReportIntimateFriendWidgetExpose:self.widgetRsp uid:self.userID];
            break;
        default:
            break;
    }
}

- (void)userProfileHeaderViewUpdateHeight:(BOOL)needRelod {
    self.contentTableView.tableHeaderView = self.headerView;
    if (needRelod) {
        [self.contentTableView reloadData];
    }
}

// 个人主页改版二期 合并相同代码
- (void)didTapAuthInfo {
    if (!IS_EMPTY_STR_BM(self.userProfileInfo.userInfo.displayAuthUrl)) {
        NSString *targetUrl = [self.userProfileInfo.userInfo.displayAuthUrl urlStringByAppendingParameter:[NSString stringWithFormat:@"authFrom=%d", self.isGuest ? 3 : 2]];
        [[KSNavigationManager sharedManager] dealWithScheme:targetUrl];
    }
    NSString *reportKey = @"homepage_me#vtitle#null#click#0";
    if (self.isGuest) {
        reportKey = @"homepage_guest#vtitle#null#click#0";
    }
    [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
        reportModel.key = reportKey;
        reportModel.touid = self.userProfileInfo.userInfo.userId;
        reportModel.commonStr1 = [NSString stringWithFormat:@"%lld", [KSAuthBitmapHelper newAuthBitmapFromMapAuth:self.userProfileInfo.userInfo.sMapAuth]];
    }];
}

- (void)didTapFamilyLabel {
    if (IS_EMPTY_STR_BM(self.userProfileInfo.userInfo.sAuthGroupName) &&
        self.userProfileInfo.userInfo.showInviteEntry) {
        if (self.userProfileInfo.didInvited) {
            return;
        }
        // 客态去除“邀请加入家族”
        [self inviteFamily:@"homepage_guest#explicit_family_invite_entry#null"];

        [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
            reportModel.key = @"homepage_guest#explicit_family_invite_entry#null#click#0";
            reportModel.commonStr1 = [KSLoginManager sharedInstance].curUserInfo.sAuthGroup;
            reportModel.commonInt1 = [KSLoginManager sharedInstance].curUserInfo.sAuthGroupRole;
            reportModel.touid = self.userProfileInfo.userInfo.userId;
        }];

        [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
            reportModel.key = @"homepage_me#personal_information#family_information#click#0";
        }];
    } else {
        [self showFamilyVC];
    }
}

#pragma mark - KSStarProfileHeaderViewDelegate

- (void)starProfileHeaderViewClickByType:(KSStarProfileHeaderClickType)type {
    KSUserInfo *userInfo = self.userProfileInfo.userInfo;
    switch (type) {
        case KSStarProfileHeaderClickType_MedalNamePlate:
        {
            [self jumpToMedalShop];
        }
            break;
        case KSStarProfileHeaderClickType_Avatar:{
            [self didClickOnUserHeadView];
            break;
        }
        case KSStarProfileHeaderClickType_BgPhoto: {
            [self showBgPhotoOptionMenu];
            break;
        }
        case KSStarProfileHeaderClickType_QMusic: {
            // 入驻歌手跳转
            [self jumpToOriginMusicianPage:userInfo.singerJumpUrl];
            break;
        }
        case KSStarProfileHeaderClickType_Uid: {
#ifdef INTERNALBUILD
            [self onClickDebugUserID:userInfo.userId];
#endif
            break;
        }
        case KSStarProfileHeaderClickType_Fans:
        {
            // 粉丝点击
            if(!self.isGuest)
            {
                //主人态进入粉丝的时候要abtest,客人态不用
                NSString *strUrl = WnsUrlStringConfig(@"MyFanPageUrl");
                strUrl = [strUrl urlStringByAppendingParameter:[NSString stringWithFormat:@"act_source=%@", SAFE_STR_BM([[KSVipTraceReportManager sharedManager] getActSourceID:0])]];
                strUrl = [strUrl urlStringByAppendingParameter:[NSString stringWithFormat:@"num=%ld", userInfo.fansCount]];
                [[KSNavigationManager sharedManager] showWebView:strUrl];
            }
            else
            {
                NSString *strUrl = WnsUrlStringConfig(@"OtherFanPageUrl");
                strUrl = [strUrl urlStringByAppendingParameter:[NSString stringWithFormat:@"uid=%lld", userInfo.userId]];
                strUrl = [strUrl urlStringByAppendingParameter:[NSString stringWithFormat:@"num=%ld", userInfo.fansCount]];
                [[KSNavigationManager sharedManager] showWebView:strUrl];
            }

            [[[KSNavigationManager sharedManager] getRootTabBarController] updateMsgTabRedPoint];
            
            if (!self.isGuest)
            {
                NSInteger commonInt1 = [KSBadgeUpdateManager sharedManager].newFollowRedPoint > 0;

                [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                    reportModel.key = @"homepage_me#personal_information#followers#click#0";
                    reportModel.commonInt1 = commonInt1;
                }];
            }
            else
            {
                [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                    reportModel.key = @"homepage_guest#personal_information#followers#click#0";
                    reportModel.commonInt7 = self.userID;
                    reportModel.open_relationtype = YES;
                    reportModel.touid = self.userID;
                    reportModel.from_page = self.from_page;
                }];
            }

            if (!self.isGuest)
            {
                // 上热门push
                if (![[NSUserDefaults standardUserDefaults] objectForKey:HeatPushDaysKey])
                {
                    NSDate *now = [NSDate date];
                    [[NSUserDefaults standardUserDefaults] setValue:now forKey:HeatPushDaysKey];
                    [[NSUserDefaults standardUserDefaults] synchronize];
                }
                // 2天内粉丝页+最近听众页最多push N次
                NSInteger shownDays = [NSDate diffBetweenDate1:[[NSUserDefaults standardUserDefaults] objectForKey:HeatPushDaysKey] date2:[NSDate date]];
                NSInteger pushTimes = [WnsSwitchStringConfig(@"HeatCardPushLimit") integerValue];
                if (shownDays <= 2 && ![self hasShowMultipleTimes:pushTimes forKey:HeatPushTimesKey]) {
                    KS_WEAK_SELF(self);
                    [self getHotIndicatorInfo:^(proto_webapp_heat_card_GetHotIndicatorRsp *rsp, NSError *err) {
                        CHECK_SELF_AND_RETURN();
                        if (!err) {
                            if (rsp.jce_hotFlag) {
                                [self saveMultipleTimesForKey:HeatPushTimesKey];
                            }
                        }
                    }];
                }
            }

            break;
        }
        case KSStarProfileHeaderClickType_Follow:
        {
            // 关注点击
            if (!self.isGuest)
            {
                NSString *strUrl = WnsUrlStringConfig(@"MyFollowPageUrl");
                [[KSNavigationManager sharedManager] showWebView:strUrl];

                [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                    reportModel.key = @"homepage_me#personal_information#following#click#0";
                }];
            }
            else
            {
                NSString *strUrl = WnsUrlStringConfig(@"OtherFollowPageUrl");
                strUrl = [strUrl stringByReplacingOccurrencesOfString:@"$uid" withString:[NSString stringWithFormat:@"%lld",userInfo.userId]];
                [[KSNavigationManager sharedManager] showWebView:strUrl];

                [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                    reportModel.key = @"homepage_guest#personal_information#following#click#0";
                    reportModel.commonInt7 = self.userID;
                }];
            }

            
            break;
        }
        case KSStarProfileHeaderClickType_Friends:
        {
            [[KSNavigationManager sharedManager] showMyFriendPage];

            

            if (!self.isGuest)
            {
                [[KSBadgeUpdateManager sharedManager] clearFindFriendProfileRedNum];
                NSInteger commonInt1 = ([KSBadgeUpdateManager sharedManager].newFriendRedPointNum +
                                        [KSBadgeUpdateManager sharedManager].newContactsRedPointNum +
                                        [KSBadgeUpdateManager sharedManager].newFriendBindRedPointNum +
                                        [KSBadgeUpdateManager sharedManager].newContactsBindRedPointNum +
                                        [KSBadgeUpdateManager sharedManager].mayYouKnowNewNum +
                                        [KSBadgeUpdateManager sharedManager].nearNewNum +
                                        [KSBadgeUpdateManager sharedManager].topVNewNum +
                                        ([KSLoginManager sharedInstance].canPullSocialRelation ? 0 : 1)) > 0;

                [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                    reportModel.key = @"homepage_me#personal_information#friends#click#0";
                    reportModel.commonInt1 = commonInt1;
                }];
            }

            break;
        }
        case KSStarProfileHeaderClickType_VIP:
        {
            // VIP Icon 点击
            VipPid posid = self.isGuest ? VipPid_ProfileGuestIcon : VipPid_ProfileIcon;
            [KSVipManager vipIconComponentClickToUser:self.userProfileInfo.userInfo handlePresentVip:YES posid:posid vipUrl:self.myCommercializebarModel.VipEntrance.jumpUrl];
            break;
        }
        case KSStarProfileHeaderClickType_AuthInfo: {
            [self didTapAuthInfo];
            break;
        }
        case KSStarProfileHeaderClickType_UserPhoto : {
            if (!self.isGuest) {
                [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                    reportModel.key = @"homepage_me#personal_information#avatar_gallery#click#0";
                    // reportModel
                }];
            } else {
                [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                    reportModel.key = @"homepage_guest#personal_information#avatar_gallery#click#0";
                    reportModel.from_page = self.from_page;
                    reportModel.touid = self.userID;
                    reportModel.open_relationtype = YES;
                }];
            }
            /// 跳转相册
            [self showPhotosVC];

            break;
        }
        case KSStarProfileHeaderClickType_Switch : {
            if (KSBackgroundCompositeManager.defaultManager.backgroundCompositeTasks.count > 0)
            {
                KSBasicDialog *dialog = [[KSBasicDialog alloc] init];
                dialog.caption = KString(@"后台合成进行中，无法切换账号，请稍后");
                [dialog addControlButton:@"确定" subtitle:nil buttonStyle:KSBasicDialogButtonStyle_Normal callback:^(KSDialog * _Nonnull dialog) {
                    [dialog dismiss];
                }];
                [dialog show];
            }
            else
            {
                [self switchBtnClickAction:nil];
            }
            break;
        }
        case KSStarProfileHeaderClickType_VipNamePlate: {
            NSString *jumpUrl = IS_EMPTY_STR_BM(userInfo.dataplateInfo.jce_dataplate.jce_strJumpUrl) ? KSUserProfileHeaderView_VipNamePlateBtn_JumpUrl : userInfo.dataplateInfo.jce_dataplate.jce_strJumpUrl;
            if (jumpUrl) {
                [[KSNavigationManager sharedManager] dealWithScheme:jumpUrl];
            }
        }
            break;
        case KSStarProfileHeaderClickType_VipMarketDataplate: {
            NSString *jumpUrl = IS_EMPTY_STR_BM(userInfo.dataplateInfo.jce_marketDataplate.jce_strSkipUrl) ? KSUserProfileHeaderView_VipNamePlateBtn_JumpUrl : userInfo.dataplateInfo.jce_marketDataplate.jce_strSkipUrl;
            if (jumpUrl) {
                [[KSNavigationManager sharedManager] dealWithScheme:jumpUrl];
            }
        }
            break;
        case KSStarProfileHeaderClickType_SurpriseGift: {
            [self openSupriseGiftHippyPage];
            break;

        }
            break;
        case KSStarProfileHeaderClickType_CloseFriend: {
            NSString *jumpUrl = self.widgetRsp.jce_stWidget.jce_strJumpURL;
            jumpUrl = [jumpUrl appendUrlParameter:[NSString stringWithFormat:@"&pageUid=%lld", self.userID]];
            jumpUrl = [KSNavigationManager.sharedManager handleHalfHippyScheme:jumpUrl];
            if (jumpUrl) {
                [[KSNavigationManager sharedManager] dealWithScheme:jumpUrl];
                [self traceReportIntimateFriendWidgetClick:self.widgetRsp uid:self.userID];
            }
            break;
        }
        case KSStarProfileHeaderClickType_Family:
            [self didTapFamilyLabel];
            break;
        default:
            break;
    }
}

- (void)starProfileHeaderViewExposureByType:(KSUserProfileHeaderExposureType)type {
    switch (type) {
        case KSStarProfileHeaderExposureType_CloseFriend:
            [self traceReportIntimateFriendWidgetExpose:self.widgetRsp uid:self.userID];
            break;
        default:
            break;
    }
}

- (void)starProfileHeaderViewUpdateHeight:(BOOL)needRelod {
    self.contentTableView.tableHeaderView = self.starHeaderView;
    if (needRelod) {
        [self.contentTableView reloadData];
    }
}

- (void)showFamilyVC
{
    NSString *from = @"";
    if (self.isGuest) {
        from = [@"homepage_guest#personal_information#null" URLEncodedString];
    }
    else {
        from = [@"homepage_me#all_module#null" URLEncodedString];;
    }

    [[KSNavigationManager sharedManager] showWebView:[self getFamilySchemaUrl]];
    [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
        reportModel.key = @"homepage_guest#personal_information#family#click#0";
        reportModel.touid = self.userProfileInfo.userInfo.userId;
        reportModel.commonStr1 = self.userProfileInfo.userInfo.sAuthGroup;
    }];
}

- (NSString *)getFamilySchemaUrl
{
    NSString *strUrl = ksFamilyPageNewEntrySchema_Profile;
    strUrl = [strUrl urlStringByAppendingParameter:[NSString stringWithFormat:@"familyId=%@", self.userProfileInfo.userInfo.sAuthGroup]];
    strUrl = [strUrl urlStringByAppendingParameter:[NSString stringWithFormat:@"from=family_main"]];
    return strUrl;
}

// 邀请加入家族
- (void)inviteFamily:(NSString *)fromPage {
    KS_WEAK_SELF(self);
    [self inviteUserToMyFamily:self.userProfileInfo.userInfo.userId fromPage:fromPage completion:^(NSError *error) {
        CHECK_SELF_AND_RETURN();
        if (error) {
            [self onMgrError:error];
        } else {
            // 设置已邀请标记
            self.userProfileInfo.didInvited = YES;
            [self.contentTableView reloadData];
            [self.headerView updateByUserInfo:self.userProfileInfo];
            [KSToast showToast:@"已邀请加入你的家族"];
        }
    }];
}

- (void)showKKShowDress:(NSString *)frompage {
    // 我的KK秀装扮
    KSWebViewController *webViewController = [[KSWebViewController alloc] init];
    webViewController.webOpenFrom = KSWebOpenFrom_Unknown;
    NSString *url = WnsUrlStringConfig(@"KKShowWebviewUrlProfile");
    url = [KSComHelper appendQueryString:[NSString stringWithFormat:@"hostId=%lld&frompage=%@", self.userID, frompage] toUrl:url];
    webViewController.url = url;
    webViewController.delegate = self;
    [self.navigationController pushViewController:webViewController animated:YES];
}

- (void)showPhotosVC
{
    KSNetAlbumVC_V4 *nextVC2 = [[KSNetAlbumVC_V4 alloc] init];
    nextVC2.userId = self.userID;
    nextVC2.userInfo = self.userProfileInfo.userInfo;
    nextVC2.fromPage = self.from_page;
    [self.navigationController pushViewController:nextVC2 animated:YES];
}

/**
 点击头像
 */
- (void)didClickOnUserHeadView
{
    if (self.isGuest)
    {
        [self showHeadImageViewer];
        [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
            reportModel.key = @"homepage_guest#personal_information#avatar#click#0";
            reportModel.commonInt7 = self.userID;
            reportModel.roomid = self.userProfileInfo.userInfo.liveRoom.strRoomId;
            reportModel.showid = self.userProfileInfo.userInfo.liveRoom.strShowId;
            reportModel.touid = self.userProfileInfo.userInfo.userId;
            reportModel.open_relationtype = YES;
            reportModel.from_page = self.from_page;
            if (self.userProfileInfo.userInfo.liveRoom.strRoomId.length > 0)
            {
                reportModel.commonInt4 = 2;
            }
            else if (self.userProfileInfo.userInfo.avatarKtvInfo.jce_strRoomId.length > 0)
            {
                reportModel.commonInt4 = 3;
                reportModel.roomid = self.userProfileInfo.userInfo.avatarKtvInfo.jce_strRoomId;
            }
            else
            {
                reportModel.commonInt4 = 1;
            }
        }];
    }
    else
    {
        /// 新版包含查看相册
        NSMutableArray* actionSheetItems = [NSMutableArray array];
        if (WnsLocalServerIntegerConfig(@"DisplayAIAvatarEntranceSwitch")) {
            KSActionSheetOldItem *aiAvatar = KSActionSheetOldItem.new;
            aiAvatar.title = KString(@"魔法头像");
            aiAvatar.icon = [UIImage imageNamed:@"ai_avatar_entrance_icon"];
            [actionSheetItems addObject:aiAvatar];
            [self traceReportAIAvatarEntranceExpose];
        }

        KSActionSheetOldItem *setPendantItem = [[KSActionSheetOldItem alloc] init];
        setPendantItem.title = KString(@"设置头像挂件");
        setPendantItem.icon = [UIImage imageNamed:@"download_vip"];

        KSActionSheetOldItem *avatar = [[KSActionSheetOldItem alloc] init];
        avatar.title = KString(@"我的KK秀装扮");

        KSActionSheetOldItem *photoSet = [[KSActionSheetOldItem alloc] init];
        photoSet.title = KString(@"查看K歌相册");

        KSActionSheetOldItem *imageViewerItem = [[KSActionSheetOldItem alloc] init];
        imageViewerItem.title = KString(@"查看大图");

        KSActionSheetOldItem *userProfileEditItem = [[KSActionSheetOldItem alloc] init];
        userProfileEditItem.title = KString(@"编辑个人资料");

        [actionSheetItems addObject:setPendantItem];

        [actionSheetItems addObject:avatar];
        [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
            reportModel.key = @"homepage_me#avatar#dress_my_KK#exposure#0";
        }];

        [actionSheetItems addObject:photoSet];
        [actionSheetItems addObject:imageViewerItem];
        [actionSheetItems addObject:userProfileEditItem];

        KSActionSheetOld* actionsheet = [[KSActionSheetOld alloc] initWithTitle:nil delegate:self cancelButtonTitle:KString(@"取消") destructiveButtonTitle:nil otherButtonItems:actionSheetItems tag:KSACTIONSHEETSTYLE_USERPROFILE_HEAD_VIEW];
        [actionsheet showInView:self.view];

        //主人态个人主页_点击头像后的操作项_设置头像挂件曝光
        [[KSVipTraceReportManager sharedManager] vipExpoidReport:NSIToString(VipRightId_DressUp)
                                                           posid:NSIToString(VipPid_AvatarPendant_Profile_Master_HeadTap_SetUp_Pendant)
                                                         songMid:nil
                                                           ugcId:nil];
    }

}


/**
 查看用户头像大图
 */
- (void)showHeadImageViewer
{
    // 普通合明星的header都有查看大图 //确定下这里是否有问题
    KSPendantAvatarView *imageView = nil;
    if (self.starHeaderView && self.getUserInfo.isStarSettledIn)
    {
        imageView = self.starHeaderView.pendantAvatarView;
    }
    else if (self.headerView)
    {
        imageView = self.headerView.pendantAvatarView;
    }
    else{
        return;
    }

    if (imageView.avatarImage == nil) {
        return;
    }

    // 获取到头像在屏幕上的位置
    CGRect rect = imageView.frame;
    rect.origin.y -= self.contentTableView.contentOffset.y;

    KSImageViewer *viewer = [[KSImageViewer alloc] initWithFrame:CGRectMake(0, 0, self.view.frame.size.width, self.view.frame.size.height)];
    viewer.userInfo = self.userProfileInfo.userInfo;
    viewer.userInteractionEnabled = YES;
    viewer.isGuest = self.isGuest;

    if (self.isGuest) {
        [self previewAvatarAndPhotoAblum];

    } else {
        //主人态需要放到window上面，遮住下面的tab
        viewer.frame = CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT);
        [viewer showInView:COMP_SERVICE_FOR(KSAppDelegateProtocol).window withImage:imageView.avatarImage inRect:rect];
    }

 
}

- (void)openSupriseGiftHippyPage {
    KSUserInfo *userInfo = self.userProfileInfo.userInfo;

    NSString *jumpUrl =  @"https://kg.qq.com?hippy=blindBox2023&r=sendFullScreen&mid=4847&toUid=";
    jumpUrl = [jumpUrl stringByAppendingString:NSIToString(userInfo.userId)];
    // 惊喜礼物需求的跳转 全屏hippy
    NSString *result = [NSString stringWithFormat:@"qmkege://kege.com?action=webview&url=%@",[jumpUrl URLEncodedString]];

    [[KSNavigationManager sharedManager] dealWithScheme:result];
}

// 点击头像挂件
- (void)didClickPersonalPendant {
    //主人态个人主页_点击头像后的操作项_设置头像挂件点击
    [[KSVipTraceReportManager sharedManager] vipClickReport:NSIToString(VipRightId_DressUp)
                                                      posid:NSIToString(VipPid_AvatarPendant_Profile_Master_HeadTap_SetUp_Pendant)
                                                    songMid:nil
                                                      ugcId:nil
                                                       int1:0];

    //设置头像挂件
    NSString *pendantSettingUrl = WnsUrlStringConfig(@"AvatarPendantSettingUrl");
    pendantSettingUrl = [pendantSettingUrl stringByReplacingOccurrencesOfString:@"$topSource" withString:[[KSVipTraceReportManager sharedManager] getTopSource:0]];
    pendantSettingUrl = [pendantSettingUrl stringByReplacingOccurrencesOfString:@"$actSource" withString:[[KSVipTraceReportManager sharedManager] getActSourceID:0]];
    [[KSNavigationManager sharedManager] showWebView:pendantSettingUrl];
}

// 点击魔法头像
- (void)didClickAIAvatar {
    [self traceReportAIAvatarEntranceClick];
    NSString *url = WnsLocalServerStringConfig(@"AIAvatarEntranceURL");
    if (!IS_EMPTY_STR_BM(url)) {
        [KSNavigationManager.sharedManager dealWithScheme:url];
    }
}

// 点击编辑个人资料
- (void)didClickEditPersonalInfo {
    NSString *url = WnsStringConfig(kWnsConfig_Url, @"EditUserInfoUrl");
    if (!IS_EMPTY_STR_BM(url)) {
        [[KSNavigationManager sharedManager] showWebView:url];
    }
}

#pragma mark - KSActionSheetOldDelegate

- (void)ksActionSheet:(KSActionSheetOld*)actionSheet didClickOnButtonIndex:(NSInteger)buttonIndex withTag:(KSACTIONSHEETSTYLE)tag withBtn:(UIButton*)btn {

    switch (tag)
    {
        case KSACTIONSHEETSTYLE_USERPROFILE_BGPHOTO_OPTION:
        {
            [self clickPhotoOptionMenu:buttonIndex withBtn:btn];
        }
            break;
        case KSACTIONSHEETSTYLE_USERPROFILE_HEAD_VIEW:
        {
            [self clickHeaderViewMaster_v2:buttonIndex];
        }
            break;
        default:
            KErrLog(@"invalid tag:%lu", (unsigned long)tag);
            break;
    }
}

- (void)tappedCancelDidTap
{

}

// 个人主页用户头像点击 -- 主人态
- (void)clickHeaderViewMaster:(NSInteger)buttonIndex
{
    switch (buttonIndex) {
        case 0:
        {
            //主人态个人主页_点击头像后的操作项_设置头像挂件点击
            [[KSVipTraceReportManager sharedManager] vipClickReport:NSIToString(VipRightId_DressUp)
                                                              posid:NSIToString(VipPid_AvatarPendant_Profile_Master_HeadTap_SetUp_Pendant)
                                                            songMid:nil
                                                              ugcId:nil
                                                               int1:0];

            //设置头像挂件
            NSString *pendantSettingUrl = WnsUrlStringConfig(@"AvatarPendantSettingUrl");

            pendantSettingUrl = [pendantSettingUrl stringByReplacingOccurrencesOfString:@"$topSource" withString:[[KSVipTraceReportManager sharedManager] getTopSource:0]];
            pendantSettingUrl = [pendantSettingUrl stringByReplacingOccurrencesOfString:@"$actSource" withString:[[KSVipTraceReportManager sharedManager] getActSourceID:0]];

            [[KSNavigationManager sharedManager] showWebView:pendantSettingUrl];
        }
            break;
        case 1:
        {
            //查看大图
            [self showHeadImageViewer];
        }
            break;
        case 2:
        {
            //编辑个人资料
            NSString *url = WnsStringConfig(kWnsConfig_Url, @"EditUserInfoUrl");
            [[KSNavigationManager sharedManager] showWebView:url];
        }
            break;

        default:
            break;
    }
}

// 新版个人主页用户头像点击 -- 主人态
- (void)clickHeaderViewMaster_v2:(NSInteger)buttonIndex
{
    if (WnsLocalServerIntegerConfig(@"DisplayAIAvatarEntranceSwitch")) {
        switch (buttonIndex) {
            case 0: {
                // 点击魔法头像
                [self didClickAIAvatar];
                self.needLoadUserInfo = YES;
            }
                break;
            case 1: {
                // 点击头像挂件
                [self didClickPersonalPendant];
            }
                break;
            case 2: {
                // KKShow 装扮
                [self showKKShowDress:@"homepage_me.avatar.dress_my_KK"];
                [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                    reportModel.key = @"homepage_me#avatar#dress_my_KK#click#0";
                }];
            }
                break;
            case 3: {
                // 查看相册
                [self showPhotosVC];
                self.needLoadUserInfo = YES;
            }
                break;
            case 4: {
                // 查看大图
                [self showHeadImageViewer];
                break;
            }
            case 5: {
                //编辑个人资料
                [self didClickEditPersonalInfo];
                self.needLoadUserInfo = YES;
            }
                break;
            default:
                break;
        }
    } else {
        switch (buttonIndex) {
            case 0: {
                // 点击头像挂件
                [self didClickPersonalPendant];
            }
                break;
            case 1: {
                // KKShow 装扮
                [self showKKShowDress:@"homepage_me.avatar.dress_my_KK"];
                [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                    reportModel.key = @"homepage_me#avatar#dress_my_KK#click#0";
                }];
            }
                break;
            case 2: {
                //查看相册
                [self showPhotosVC];
            }
                break;
            case 3: {
                //查看大图
                [self showHeadImageViewer];
                break;
            }
            case 4: {
                //编辑个人资料
                [self didClickEditPersonalInfo];
            }
                break;
            default:
                break;
        }
    }
}

#pragma mark -- Global Play Aciton
- (void)playAllMusicList
{
    [self playAllMusicListWithPathReportStartFromIndex:0 palyInBackground:YES];

    if (!self.isGuest)
    {
        [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
            reportModel.key = @"homepage_me#music#play_all_button#click#0";
        }];
    }
    else
    {
        [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
            reportModel.key = @"homepage_guest#music#play_all_button#click#0";
            reportModel.commonInt7 = self.userID;
            reportModel.from_page = self.from_page;
            reportModel.touid = self.userID;
            reportModel.open_relationtype = YES;
        }];
    }
}

- (void)onSortButtonClick:(id)sender
{
    if (!self.isGuest)
    {
        [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
            reportModel.key = @"homepage_me#music#rank#click#0";
        }];
    }
    else
    {
        [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
            reportModel.key = @"homepage_guest#music#rank#click#0";
            reportModel.commonInt7 = self.userID;
        }];
    }

    if (self.musicAdapter.curSorter == JceTimeline_Detail_emSorter_ENUM_SORTER_TIME) {
        self.musicAdapter.curSorter = JceTimeline_Detail_emSorter_ENUM_SORTER_PLAY;
    }
    else{
        self.musicAdapter.curSorter = JceTimeline_Detail_emSorter_ENUM_SORTER_TIME;
    }

    self.musicWorksList = nil;
    [self loadUserMusicWorkList:NO];
    [self.contentTableView reloadData];
}

- (void)onSortButtonClick_V2:(id)sender
{
    if (!self.isGuest) {
        [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
            reportModel.key = @"homepage_me#music#rank#click#0";
        }];
    } else {
        [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
            reportModel.key = @"homepage_guest#music#rank#click#0";
            reportModel.commonInt7 = self.userID;
        }];
    }
    [self showSortActionView];
}

- (void)onFilterButtonClick:(id)sender
{
    if (!self.isGuest)
    {
        [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
            reportModel.key = @"homepage_me#music#filter#click#0";
        }];
    }
    else
    {
        [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
            reportModel.key = @"homepage_guest#music#filter#click#0";
            reportModel.commonInt7 = self.userID;
        }];
    }

    [self showUgcFilterView];
}


#pragma mark - KSTimelineFilterView
- (void)showSortActionView
{
    KSActionSheet *actionSheet = [[KSActionSheet alloc] init];
    actionSheet.textAlignmentLeft = YES;
    NSMutableArray *items = [NSMutableArray array];

    KSActionSheetItem *item = [[KSActionSheetItem alloc] init];
    item.title = KString(@"按时间排序");
    item.tag = JceTimeline_Detail_emSorter_ENUM_SORTER_TIME;
    item.selected = self.musicAdapter.curSorter == JceTimeline_Detail_emSorter_ENUM_SORTER_TIME;
    [items safeAddObject:item];

    item = [[KSActionSheetItem alloc] init];
    item.title = KString(@"按播放排序");
    item.tag = JceTimeline_Detail_emSorter_ENUM_SORTER_PLAY;
    item.selected = self.musicAdapter.curSorter == JceTimeline_Detail_emSorter_ENUM_SORTER_PLAY;
    [items safeAddObject:item];

    actionSheet.items = [items copy];
    KS_WEAK_SELF(self);
    [actionSheet setItemClickCallback:^(KSActionSheet * _Nonnull actionSheet, KSActionSheetItem * _Nonnull item) {
        CHECK_SELF_AND_RETURN();
        if (self.musicAdapter.curSorter != item.tag) {
            self.musicAdapter.curSorter = item.tag;
            self.musicWorksList = nil;
            [self loadUserMusicWorkList:NO];
            [self.contentTableView reloadData];
        }
        [actionSheet close];
    }];
    [actionSheet setCancelClickCallback:^(KSActionSheet * _Nonnull actionSheet) {
        [actionSheet close];
    }];
    actionSheet.cancelString = @"取消";
    [actionSheet show];
}

// 展示个人主页筛选器
- (void)showUgcFilterView
{
    // 构建父视图
    KSMusicTypeFilterSheetParentView *musicTypeFilterSheetParentView = [[KSMusicTypeFilterSheetParentView alloc] init];
    musicTypeFilterSheetParentView.title = @"全部筛选";
    musicTypeFilterSheetParentView.titleSelectMode = YES;
    musicTypeFilterSheetParentView.isGuest = self.isGuest;
    if (self.filterTags.count) {
        musicTypeFilterSheetParentView.filterTags = self.filterTags;
    }
    KS_WEAK_SELF(self);
    [musicTypeFilterSheetParentView setFinishClickCallback:^(KSMusicTypeFilterSheetParentView * _Nonnull musicTypeFilterParentView, NSMutableArray * _Nonnull tags) {
        CHECK_SELF_AND_RETURN();
        self.musicAdapter.curFilter = JceTimeline_Detail_emFilter_ENUM_FILTER_ALL_WITH_HC;
        NSInteger lFilterMask = 0;
        NSInteger lFilterMaskNot = 0;

        NSMutableString *reportStr = [NSMutableString new];
        self.filterTags = nil;
        if (tags.count) {
            self.musicAdapter.curFilter = JceTimeline_Detail_emFilter_ENUM_FILTER_MASK;
            self.filterTags = [tags copy];
            for (NSNumber *tag in tags) {
                if ([tag isEqual:@(KSMusicTypeTag_Public)]) {
                    lFilterMask |= NS_UGC_SEARCH_emUgcSearchType_ENUM_UGCSEARCH_TYPE_GUEST;
                    [reportStr appendString:@"公开"];
                }
                else if ([tag isEqual:@(KSMusicTypeTag_Private)]) {
                    lFilterMaskNot |= NS_UGC_SEARCH_emUgcSearchType_ENUM_UGCSEARCH_TYPE_GUEST;
                    [reportStr appendString:@"私密"];
                }

                if ([tag isEqual:@(KSMusicTypeTag_Video)]) {
                    lFilterMask |= NS_UGC_SEARCH_emUgcSearchType_ENUM_UGCSEARCH_TYPE_MV;
                    [reportStr appendString:@"视频"];
                }
                else if ([tag isEqual:@(KSMusicTypeTag_Audio)]) {
                    lFilterMaskNot |= NS_UGC_SEARCH_emUgcSearchType_ENUM_UGCSEARCH_TYPE_MV;
                    [reportStr appendString:@"音频"];
                }
                else if ([tag isEqual:@(KSMusicTypeTag_AiImage)]) {
                    lFilterMask |= NS_UGC_SEARCH_emUgcSearchType_ENUM_UGCSEARCH_TYPE_AI_RICH_TEXT;
                    [reportStr appendString:@"图文"];
                }

                if ([tag isEqual:@(KSMusicTypeTag_Chorus)]) {
                    lFilterMask |= NS_UGC_SEARCH_emUgcSearchType_ENUM_UGCSEARCH_TYPE_CHORUS;
                    [reportStr appendString:@"合唱成品"];

                }

                if ([tag isEqual:@(KSMusicTypeTag_SingleSing)]) {
                    lFilterMask |= NS_UGC_SEARCH_emUgcSearchType_ENUM_UGCSEARCH_TYPE_SINGLE;
                    [reportStr appendString:@"独唱单曲"];
                }

                if ([tag isEqual:@(KSMusicTypeTag_Recommended)]) {
                    lFilterMask |= NS_UGC_SEARCH_emUgcSearchType_ENUM_UGCSEARCH_TYPE_RECOMMENDED;
                    [reportStr appendString:@"推荐"];
                }
            }
        }

        NSString *reportKey = (self.isGuest ? @"homepage_guest#music#checkbox#write_checkbox#0" : @"homepage_me#music#checkbox#write_checkbox#0");

        [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {

             reportModel.key = reportKey;
            if (IS_EMPTY_STR_BM(reportStr)) {
                reportModel.commonStr1 = @"unknown";
            } else {
                reportModel.commonStr1 = reportStr;
            }
         }];

        self.musicAdapter.lFilterMask = lFilterMask;
        self.musicAdapter.lFilterMaskNot = lFilterMaskNot;
        [self loadUserMusicWorkList:NO];
        self.musicWorksList = nil;
        [self.contentTableView reloadData];
        [musicTypeFilterParentView close];
    }];
    [musicTypeFilterSheetParentView show];
}

- (void)reportClickedActionSheet:(JceTimeline_Detail_emFilter)filter
{
//    int1=具体筛选项 1全部作品 2单曲 3合唱 4mv 5推荐
//    int7=个人主页所有者uid
    int num1 = 0;
    switch (filter) {
        case JceTimeline_Detail_emFilter_ENUM_FILTER_ALL:
            num1 = 1;
            break;
        case JceTimeline_Detail_emFilter_ENUM_FILTER_AUD:
            num1 = 2;
            break;
        case JceTimeline_Detail_emFilter_ENUM_FILTER_HC:
            num1 = 3;
            break;
        case JceTimeline_Detail_emFilter_ENUM_FILTER_FINAL_MV:
            num1 = 4;
            break;
        case JceTimeline_Detail_emFilter_ENUM_FILTER_RECOMMENDED:
            num1 = 5;
            break;
        case JceTimeline_Detail_emFilter_ENUM_FILTER_SHORT_MV:
            num1 = 6;
            break;
        case JceTimeline_Detail_emFilter_ENUM_FILTER_AUD_WITH_HC:
            num1 = 7;
            break;
        case JceTimeline_Detail_emFilter_ENUM_FILTER_ALL_WITH_HC:
            num1 = 8;
            break;
        case JceTimeline_Detail_emFilter_ENUM_FILTER_MV_WITH_HC:
            num1 = 9;
            break;
        case JceTimeline_Detail_emFilter_ENUM_FILTER_HC_WITH_HC:
            num1 = 10;
            break;
        default:
            break;
    }

    NSString *reportKey = @"homepage_me#music#filters#click#0";

    NSInteger hostInteger = [self getHostState];
    if (hostInteger == 2)
    {
        reportKey = @"homepage_guest#music#filters#click#0";
    }
    [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel)
     {
        reportModel.key = reportKey;
        reportModel.commonInt1 = num1;
        reportModel.commonInt7 = self.userID;
    }];
}

- (NSInteger)getHostState
{
    NSInteger state = 1;
    // 1- 主人态 2- 客人态
    if (self.userID == [[KSLoginManager sharedInstance] curUserInfo].userId)
    {
        state = 1;
    }
    else
    {
        state = 2;
    }
    return state;
}

- (void)playVideoItem:(KSTimelineDetail *)item {
    if (!item) {
        KLog(@"空的视频文件");
        return;
    }

    KSGlobalPlayItem* playItem = [self getGlobalPlayItemFromTimelineDetail:item];
    NSString *fromPage = [self isGuest] ? @"homepage_guest#the_video#null" : @"homepage_me#the_video#null";
    playItem.fromPage = fromPage;
    [[KSUgcPlayManager sharedManager] playUgcWithItem:playItem showPlayVC:YES];

    if (playItem && playItem.ugcMask & JceTimeline_Detail_KGE_UGC_MASK_BIT_KGE_UGC_MASK_SHORT_VIDEO) {
        //短视频写->播放->来自个人主页
        [KSTraceReportHelper writeActionWithBlock:^(TraceReportWrite *info) {
            info.reserves = ReportSubactionL3_GeRenZhuYe_Write_ShortVideo_Play;
            info.commInt1 = [playItem.userInfo isAnchor]?1:2;
        }];
    }
}

- (void)playMusicItemWithTimelineDetail:(KSTimelineDetail *)content {
    if (!content) {
        [self tempAlert:KString(@"添加失败")];
        return;
    }

    KSGlobalPlayItem* playItem = [self getGlobalPlayItemFromTimelineDetail:content];
    NSString *fromPage = [self isGuest] ? @"homepage_guest#music#null" : @"homepage_me#music#null";
    playItem.fromPage = fromPage;

    // 音频保留原来跳详情页播放的体验
    playItem.playfrom = KSPlayingFromType_Global;
    
    if ([content isShortPlay]) {
        /// 短剧
        [KSPlayletRouter navigateToPlayletVCWithDetail:content playNext:NO fromPage:kPlayletFromPage_Profile];
    }
    else if ([content isAiImageFeed]) {
        /// AI图文
        [[KSNavigationManager sharedManager] jumpToAiImageDetailWithUgcId:content.ugcId];
    }
    else {
        /// 普通ugc
        [[KSUgcPlayManager sharedManager] playUgcWithItem:playItem showPlayVC:YES];
    }
    
}

- (void)playAllMusicListWithPathReportStartFromIndex:(NSUInteger)startInedx palyInBackground:(BOOL)inBackground
{
    KSJumpToOtherSceneFrom sceneFrom = [[KSNavigationManager sharedManager] canJumpFromCurrentToOtherScene];
    if (sceneFrom != KSJumpOtherSceneFrom_NO) {
        [[KSAlertManager sharedManager] showAlertView:[[KSNavigationManager sharedManager] getJumpAlertViewText:sceneFrom]
                                                title:nil
                                                 btns:@[KString(@"取消"),KString(@"确认")]
                                        completeBlock:^(NSInteger buttonIndex, NSString *buttonTitle) {
            if (buttonIndex == 1) {
                [[KSPathReportManager sharedManager] lazyAddNodeAfter:^{
                    [[KSNavigationManager sharedManager] clearLiveShowVCWhenLogout];
                    [[KSNavigationManager sharedManager] removeLiveVCInStack];
                    [self playAllMusicListStartFromIndex:startInedx palyInBackground:inBackground];
                } completeBlock:nil];
            }
        }];
    } else {
        [self playAllMusicListStartFromIndex:startInedx palyInBackground:inBackground];
    }
}

- (void)playAllMusicListStartFromIndex:(NSUInteger )startInedx palyInBackground:(BOOL)inBackground
{
    NSMutableArray *playList = [NSMutableArray arrayWithCapacity:10];

    BOOL haveArchivedUgc = NO;
    BOOL hasAiImageFeed = NO;
    // 点击个人主页全部播放
    for (int i = 0; i < self.musicWorksList.datalist.count; i++) {
        KSTimelineDetail *content = [self.musicWorksList.datalist safeObjectAtIndex:i];
        if (content.isAiImageFeed) {
            // Ai图文过滤
            hasAiImageFeed = YES;
            continue;
        }
        
        if (!content.isArchived) {
            // 需要过滤下已被归档的作品
            KSGlobalPlayItem *playItem = [self getGlobalPlayItemFromTimelineDetail:content];
            NSString *fromPage = [self isGuest] ? @"homepage_guest#music#null" : @"homepage_me#music#null";
            playItem.fromPage = fromPage;
            [playList safeAddObject:playItem];
        } else {
            haveArchivedUgc = YES;
        }
    }

    if ([playList count] > 0) {
        [[KSUgcPlayManager sharedManager] playUgcList:playList startIndex:startInedx sourceType:KSPlayListSourceType_ProfileVC listTag:nil showPlayVC:!inBackground];
        if (inBackground)
        {
            [self tempAlert:KString(@"已经添加至播放列表")];

            KSTraceReportManager *traceMgr = [KSTraceReportManager sharedManager];
            TraceReportView *viewInfo = [[TraceReportView alloc] init];
            viewInfo.reportType = JceDcreport_emReportType_REPORT_TYPE_GlobalPlayList;
            viewInfo.subaction = ReportSubactionGlobalPlayListPlayAllClick;
            viewInfo.commInt1 = 1; // 1- 个人主页 2- 专辑 3- 金曲榜 4- 播放历史 5- 发现页歌单 6- 我的下载
            // 1- 主人态 2- 客人态
            viewInfo.commInt2 = [self getHostState];

            [traceMgr viewActionReport:viewInfo];
        }
        else
        {
            KSGlobalPlayItem *playItem = [KSComHelper getObjectInArray:playList byIndex:startInedx ofClassType:[KSGlobalPlayItem class] defaultValue:nil];
            if (playItem && playItem.ugcMask & JceTimeline_Detail_KGE_UGC_MASK_BIT_KGE_UGC_MASK_SHORT_VIDEO)
            {
                //短视频写->播放->来自个人主页
                [KSTraceReportHelper writeActionWithBlock:^(TraceReportWrite *info) {
                    info.reserves = ReportSubactionL3_GeRenZhuYe_Write_ShortVideo_Play;
                    info.commInt1 = [playItem.userInfo isAnchor]?1:2;
                }];
            }
        }
    }
    else
    {
        if (hasAiImageFeed) {
            [self tempAlert:@"图文作品无法加入播放列表"];
        }
        else {
            [self tempAlert:KString(@"该分类下暂无作品")];
        }
    }

    if (haveArchivedUgc) {//若第一页有被归档作品，点击播放按钮时弹出toast“有n首作品已被深度存储，读取后可播放。”
        [KSToast showToast:[NSString stringWithFormat:@"有%ld首作品已被深度存储，读取后可播放。",(long)self.musicAdapter.arhivedUgcs]];

    }
}

- (void)didClickedTopChorusButton
{
     KLog(@"[KSUserProfile] didClickedTopChorusButton self.topChorusInfo = %@ availiable = %@ chorusType = %zd",self.topChorusInfo,@([self.topChorusInfo isTopChorusInfoAvaliable]),[self.topChorusInfo currentUserProfileTopChorusType]);
    if (!self.topChorusInfo || ![self.topChorusInfo isTopChorusInfoAvaliable])
    {
        return;
    }

    if ([self.topChorusInfo currentUserProfileTopChorusType] == KSUserProfileTopChorus_JoinNormalChorus)
    {
        KSRecordingJoinNormalUgcChorusStrategy *strategy = [KSRecordingJoinNormalUgcChorusStrategy new];
        strategy.recordingData.ugc.ugcId = self.topChorusInfo.hcSongInfo.jce_strUgcId;
        KSong *song = [KSong new];
        song.songMid = self.topChorusInfo.hcSongInfo.jce_strMid;
        song.name = self.topChorusInfo.hcSongInfo.jce_strSongName;
        strategy.recordingData.song = song;

        strategy.recordStrategyReport.from_page = @"homepage_guest#duet_tip#null";

        [[KSNavigationManager sharedManager] singSongWithStrategy:strategy];

    }
    else if([self.topChorusInfo currentUserProfileTopChorusType] == KSUserProfileTopChorus_HalfUgc)
    {
        KSong *song = [[KSong alloc] init];
        song.songMid = self.topChorusInfo.hcSongInfo.jce_strMid;
        song.name = self.topChorusInfo.hcSongInfo.jce_strSongName;
        KSRecordingNormalStrategy *strategy = [[KSRecordingNormalStrategy alloc] init];
        strategy.recordingData.song = song;
        strategy.recordStrategyReport.from_page = @"homepage_guest#duet_tip#null";
        [[KSNavigationManager sharedManager] singSongWithStrategy:strategy];
    }
    else if([self.topChorusInfo currentUserProfileTopChorusType] == KSUserProfileTopChorus_InitChorus)
    {
        KSong *song = [[KSong alloc] init];
        song.songMid = self.topChorusInfo.hcSongInfo.jce_strMid;
        song.name = self.topChorusInfo.hcSongInfo.jce_strSongName;
        KSRecordingNormalStrategy *strategy = [[KSRecordingNormalStrategy alloc] init];
        strategy.inviteChorusUser = self.userProfileInfo.userInfo;
        strategy.recordingData.song = song;
        strategy.recordStrategyReport.from_page = @"homepage_guest#duet_tip#null";
        [[KSNavigationManager sharedManager] singSongWithStrategy:strategy toNewRecordType:SponsorChorus_Recording];
    }
}

- (void)jumpToOriginMusicianPage:(NSString *)url
{
    int commonInt = 1;
    if ([[UIApplication sharedApplication] canOpenURL:[NSURL URLWithString:url]]) {
            if (@available(iOS 10.0, *))
            {
                [[UIApplication sharedApplication] openURL:[NSURL URLWithString:url] options:@{} completionHandler:^(BOOL success) {

                }];
            }
            else
            {
                [[UIApplication sharedApplication] openURL:[NSURL URLWithString:url]];
            }

            if([url containsString:kKuWoAppScheme]){
                commonInt = 4;
            }
            else{
                commonInt = 1;
            }
        }
        else{
            if([url containsString:kKuWoAppScheme]){
                [[UIApplication sharedApplication] openURL:[NSURL URLWithString:kKuWoAppstoreURL] options:@{} completionHandler:^(BOOL success) {

                }];
                commonInt = 5;
            }
            else{
                [[UIApplication sharedApplication] openURL:[NSURL URLWithString:kQQMusicAppstoreURL] options:@{} completionHandler:^(BOOL success) {

                }];
                commonInt = 2;
            }
        }
        //听原唱引导上报、
        TraceReportView *reportView = [[TraceReportView alloc] init];
        reportView.reportType = JceDcreport_emReportType_REPORT_TYPE_FEEDS;
        reportView.subaction = ReportSubactionAccompanyDetailHeaderViewClick;
        reportView.reserves = ReportSubactionL3AccompanyDetailListenOriginMusicClicked;
        reportView.commInt3 = commonInt;
        reportView.commInt4 = 6;
        [[KSTraceReportManager sharedManager] viewActionReport:reportView];

        [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
            reportModel.key = @"homepage_guest#personal_information#certified_singer#click#0";
            reportModel.commonInt7 = self.userProfileInfo.userInfo.userId;
            reportModel.commonInt3 = commonInt;
        }];

}

#pragma mark - KSUserProfileFeedAdapterDelegate

// 直接转发到老的feed代理上
- (void)feedAdapter:(KSUserProfileFeedAdapter *)adapter KSLayoutableTableCell:(KSLayoutableTableCell*)cell didClickWithAction:(KSDrawItemAction*)action {
    [self KSLayoutableTableCell:cell didClickWithAction:action];
}

- (void)feedAdapter:(KSUserProfileFeedAdapter *)adapter clickByActionType:(KSUserProfileFeedTabActionType)actionType {
    switch (actionType) {
        case KSUserProfileFeedTabActionType_Reload_Error: {
            [self loadUserFeeds:NO];
            [self.contentTableView reloadData];
            break;
        }
        case KSUserProfileFeedTabActionType_Empty_Link: {
            [[KSNavigationManager sharedManager] showOrderSongRoot];
            //动态-为空引导点唱入口点击
            [KSTraceReportHelper viewActionWithBlock:^(TraceReportView *info) {
                info.reserves = ReportSubactionL3_UserProfile_Click_DongTai_Empty_DianGeTai_Entry;
            }];
            break;
        }
        case KSUserProfileFeedTabActionType_VoiceStatus_detail: {
            NSString *url = adapter.voiceStatusInfo.jce_strJumpUrl;
            KINFO(@"跳语音动态页 %@", url);
            [KSNavigationManager.sharedManager dealWithScheme:url];
            break;
        }
        case KSUserProfileFeedTabActionType_VoiceStatus_action: {
            NSString *url = adapter.voiceStatusInfo.jce_strPublishUrl;
            KINFO(@"跳语音动态页-录制 %@", url);
            [KSNavigationManager.sharedManager dealWithScheme:url];
            break;
        }
        default:
            break;
    }
}

- (void)feedAdapter:(KSUserProfileFeedAdapter *)adapter customExposeReportWithFeed:(KSimpleFeed *)feed cell:(KSBaseTableViewCell *)cell {
    [self timelineExposeTraceReport:feed cell:cell];
}

- (void)onUgcFeedExposeWithSimpleFeed:(KSimpleFeed *)simpleFeed cell:(KSLayoutableTableCell *)cell {
    [self onFeedExposeWithSimpleFeed:simpleFeed cell:cell];
}

// 个人生日动态
- (void)onPersonalBirthdayDynamicExposureWithFeed:(KSimpleFeed*)simpleFeed cell:(KSBaseTableViewCell *)cell
{
    if (self.isGuest)
    {
        [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
            reportModel.key = @"homepage_guest#feed_personal_birthday_news#null#exposure#0";
        } simpleFeed:simpleFeed];
    }
    [[KSTraceReportManager sharedManager] buyKBReportClickOrExpose:NO extraBlock:^(TraceBuyKBReportCommon *reportCommon) {
        reportCommon.posid = BuyKBPid_Feeds_TabFocus_PersonalBirthdayDynamic_GliftButtonClick;
        reportCommon.commonInt1 = cell.indexPath.row;
    }];
}

#pragma mark - KSUserProfileFeedAdapterDelegate 向feedManager 提供tableview
- (KSUITableView*)feedAdapter:(KSUserProfileFeedAdapter *)adapter fetchInterTableViewProviderForManager:(KSimpleFeedManager*)feedManager
{
    return self.contentTableView;
}

#pragma mark - KSUserProfileMusicAdapterDelegate

- (void)musicAdapter:(KSUserProfileMusicAdapter *)adapter clickByActionType:(KSUserProfileMusicTabActionType)actionType userInfo:(nullable NSDictionary *)userInfo {
    switch (actionType) {
        case KSUserProfileMusicTabActionType_Filter: {
            [self onFilterButtonClick:nil];
            break;
        }
        case KSUserProfileMusicTabActionType_PlayAll: {
            [self playAllMusicList];
            break;
        }
        case KSUserProfileMusicTabActionType_UgcFrozenTip: {

            CGRect rect = [self.musicAdapter.storeLimitTipImageView convertRect:self.musicAdapter.storeLimitTipImageView.bounds toView:self.contentTableView];

            if (!self.ugcFrozeTipView) {
                self.ugcFrozeTipView = [[KSUserProfileUgcFrozeTipView alloc] init];
            }

            [self.ugcFrozeTipView removeFromSuperview];
            NSString *title = [NSString stringWithFormat:@"深度存储作品%ld个", self.musicAdapter.arhivedUgcs];
            [self.ugcFrozeTipView updateSubViewWithRect:rect title:title];
            [self.contentTableView addSubview:self.ugcFrozeTipView];

            [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                reportModel.key = @"homepage_me#music#archive_creation_bubble#exposure#0";
            }];

            [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                reportModel.key = @"homepage_me#music#archive_creation_bubble#click#0";
            }];

            break;
        }
        case KSUserProfileMusicTabActionType_Sort: {
            [self onSortButtonClick:nil];
            break;
        }
        case KSUserProfileMusicTabActionType_Sort_V2: {
            [self onSortButtonClick_V2:nil];
            break;
        }
        case KSUserProfileMusicTabActionType_Search: {
            if (!self.isGuest) {
                [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                    reportModel.key = @"homepage_me#music#search_button#click#0";
                }];
            } else {
                [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                    reportModel.key = @"homepage_guest#music#search_button#click#0";
                    reportModel.commonInt7 = self.userID;
                }];
            }

            [[KSNavigationManager sharedManager] showSearchWorksView:nil uid:(JceUInt32)self.userID];
            break;
        }
        case KSUserProfileMusicTabActionType_SongList: {

            KSViewSongListFolderVC *vc = [[KSViewSongListFolderVC alloc] initWithUid:self.userProfileInfo.userInfo.userId];
            [[KSNavigationManager sharedManager] pushVC:vc animated:YES];

            if (!self.isGuest)
            {
                [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel)
                 {
                    reportModel.key = @"homepage_me#music#song_list_view_all#click#0";
                }];
            }
            else
            {
                [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel)
                 {
                    reportModel.key = @"homepage_guest#music#song_list_view_all#click#0";
                    reportModel.commonInt7 = self.userID;
                }];
            }
            break;
        }
        case KSUserProfileMusicTabActionType_MoreChorus: {
            KSUserProfileChorusListVC *chorusVC = [[KSUserProfileChorusListVC alloc] initWithUid:self.userID];
            [[KSNavigationManager sharedManager] pushVC:chorusVC animated:YES];
            break;
        }
        case KSUserProfileMusicTabActionType_ChorusGuest: {
            [self didClickedTopChorusButton];
            break;
        }
        case KSUserProfileMusicTabActionType_ChorusGift:{
            NSString *addHcGiftHippyUrl = WnsUrlStringConfig(@"AddHcGiftByTicketUrl");
            [[KSNavigationManager sharedManager] showWebView:addHcGiftHippyUrl];
            break;
        }
        case KSUserProfileMusicTabActionType_CloseChorusGift:{
            adapter.hideHcGiftCell = YES;
            [self.contentTableView reloadData];
            break;
        }
        case KSUserProfileMusicTabActionType_Reload_Error:{
            [self loadUserMusicWorkList:NO];
            [self.contentTableView reloadData];
            break;
        }
        case KSUserProfileMusicTabActionType_Empty_Link:{
            [[KSNavigationManager sharedManager] showOrderSongRoot];
            //作品-为空引导点唱入口点击
            [KSTraceReportHelper viewActionWithBlock:^(TraceReportView *info) {
                info.reserves = ReportSubactionL3_UserProfile_Click_ZuoPin_Empty_DianGeTai_Entry;
            }];
            break;
        }
        case KSUserProfileMusicTabActionType_RecycleBin: {

            // 主人态，且检查登录号才展示
            if (!self.userProfileInfo.isGuest && self.userProfileInfo.userInfo.userId == [KSLoginManager sharedInstance].curUserInfo.userId) {
                KSUgcRecycleBinVC *recycleBinVC = [[KSUgcRecycleBinVC alloc] init];
                [[KSNavigationManager sharedManager] pushVC:recycleBinVC animated:YES];
            }

            break;
        }
        case KSUserProfileMusicTabActionType_BindWXChannel:
        {
            [self bindToWXChannelFromNative:YES];
            break;
        }
        case KSUserProfileMusicTabActionType_Archive:
        {
            self.musicAdapter.hideArchiveCell = YES;
            [self.contentTableView reloadData];
            break;
        }
        case KSUserProfileMusicTabActionType_UgcStoreLimitClose:
        {
            adapter.hideUgcStoreLimitCell = YES;
            [self.contentTableView reloadData];
            break;
        }
        case KSUserProfileMusicTabActionType_UgcStoreLimitDetail:
        {
            self.userWorksManagerPanelVC.view.hidden = NO;
            UIWindow *window = COMP_SERVICE_FOR(KSAppDelegateProtocol).window;
            self.userWorksManagerPanelVC.type = KSUserWorksManagePanelType_StoreLimitDetail;
            [self.userWorksManagerPanelVC showInViewIfNeeded:window];
            break;
        }
        case KSUserProfileMusicTabActionType_UgcManage:
        {
            [self.userWorksManagerPanelVC dismiss];
            [[KSNavigationManager sharedManager] dealWithScheme:kProfileUgcManageOnlineUrl];
            break;
        }
        case KSUserProfileMusicTabActionType_UgcRankingClick: {
            proto_ugc_ranking_UgcRankProfileItem *item = [userInfo objectForKey:KSUserProfileMusicTabActionKey_UgcRankProfileItem];
            item = SAFE_CAST(item, proto_ugc_ranking_UgcRankProfileItem);
            if (item) {
                [self handleUgcRankingItemClickActionWith:item];
            }
            break;
        }
        case KSUserProfileMusicTabActionType_UgcRankingClose: {
            [self.contentTableView reloadData];
            break;
        }
        case KSUserProfileMusicTabActionType_ActivityInfoClick:
        {
            proto_relation_res_RelationResItem *item = SAFE_CAST([userInfo objectForKey:KSUserProfileMusicTabActionKey_ActivityInfoItem], proto_relation_res_RelationResItem);
            if (!IS_EMPTY_STR_BM(item.jce_strJumpUrl)) {
                [KSNavigationManager.sharedManager dealWithScheme:item.jce_strJumpUrl];
            }
            break;
        }
        case KSUserProfileMusicTabActionType_ActivityInfoClose:
        {
            [self.contentTableView reloadData];
            break;
        }
        default:
            break;
    }
}

- (void)handleUgcRankingItemClickActionWith:(proto_ugc_ranking_UgcRankProfileItem *)rankProfileItem {
    KSUidType userId = self.userID;
    BOOL hasMore = rankProfileItem.jce_bHasMore;
    if (hasMore && rankProfileItem.jce_vecExtraData.count > 0) {
        KSUserWorksOnListViewController *vc = [[KSUserWorksOnListViewController alloc] initWithUserId:userId songWorks:rankProfileItem.jce_vecExtraData];

        CGFloat screenHeight = self.view.frame.size.height ?: [UIScreen mainScreen].bounds.size.height;
        CGFloat height = 544;
        CGFloat top = 307;
        height = MAX((screenHeight - top), height);
        CGSize size = CGSizeMake(self.view.frame.size.width, height);

        //UIViewController *topVC = [[KSNavigationManager sharedManager] getTopVCToShowSubView];

        [self presentViewController:vc configuration:^(KSPresentTask * _Nonnull task, KSPresentConfiguration * _Nonnull configuration) {
            task.identifier = @"ugcRanking";
            task.preemptible = YES;

            configuration.size = size;
            configuration.onTapBackground = ^(KSPresentContainerViewController * _Nonnull container) {
                [container dismiss];
            };
        }];
    } else {
        proto_ugc_friend_rank_comm_DrainageUnionItem *detail = rankProfileItem.jce_stDrainageDetail;
        if (!IS_EMPTY_STR_BM(detail.jce_strJumpUrl)) {
            [[KSNavigationManager sharedManager] dealWithScheme:detail.jce_strJumpUrl];
        }
    }
}

// 直接转发到老的feed代理上
- (void)musicAdapter:(KSUserProfileMusicAdapter *)adapter KSLayoutableTableCell:(KSLayoutableTableCell*)cell didClickWithAction:(KSDrawItemAction*)action {
    [self KSLayoutableTableCell:cell didClickWithAction:action];
}

- (void)musicAdapter:(KSUserProfileMusicAdapter *)adapter didClickMusicHeatCardBtn:(KSTimelineDetail *)timelineDetail
{

    // 阻断弹窗
    if (!self.isGuest && timelineDetail.isArchived) {
        [self handleTimeLineDetailArchived:timelineDetail];
        return;
    }

    if (timelineDetail && self.musicWorksList.datalist && self.musicWorksList.datalist.count > 0)
    {
        NSUInteger objectIndex = [self.musicWorksList.datalist indexOfObject:timelineDetail];
        if (objectIndex < [self.musicWorksList.datalist count])
        {
            [self playMusicItemWithTimelineDetail:timelineDetail];
            // 拉起热度卡购买面板
            if ([[KSNetStatusManager sharedManager] IsEnableInternet] && !self.isGuest && !IS_EMPTY_STR_BM(timelineDetail.strBuyUrl)) {
                [[KSNavigationManager sharedManager] dealWithScheme:timelineDetail.strBuyUrl];
                [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                    reportModel.key = @"homepage_me#music#heat_card#click#0";
                    reportModel.ugcid = timelineDetail.ugcId;
                    reportModel.commonInt1 = objectIndex + 1;
                }];
            }
        }
    }
}

- (void)handleTimeLineDetailArchived:(KSTimelineDetail *)timelineDetail {
    if (timelineDetail.isRecoving || timelineDetail.isArchived) {
        self.userWorksManagerPanelVC.type = timelineDetail.isRecoving ? KSUserWorksManagePanelType_Recoving : KSUserWorksManagePanelType_Frozen;        
        self.userWorksManagerPanelVC.frozenUgcId = timelineDetail.ugcId;
        self.userWorksManagerPanelVC.view.hidden = NO;
        UIWindow *window = COMP_SERVICE_FOR(KSAppDelegateProtocol).window;
        [self.userWorksManagerPanelVC showInViewIfNeeded:window];
    }
}

- (void)musicAdapter:(KSUserProfileMusicAdapter *)adapter didClickMusicItem:(id)itemModel
{
    if ([itemModel isKindOfClass:KSTimelineDetail.class])
    {
        // 点击单个作品播放
        KSTimelineDetail *timelineDetail = SAFE_CAST(itemModel, KSTimelineDetail.class);

        if (timelineDetail && self.musicWorksList.datalist && self.musicWorksList.datalist.count > 0)
        {
            NSUInteger objectIndex = [self.musicWorksList.datalist indexOfObject:timelineDetail];
            if (objectIndex < [self.musicWorksList.datalist count]) {
                // 阻断弹窗
                if (timelineDetail.isRecoving || (!self.isGuest && timelineDetail.isArchived)) {
                    self.userWorksManagerPanelVC.type = timelineDetail.isRecoving ? KSUserWorksManagePanelType_Recoving : KSUserWorksManagePanelType_Frozen;
                    self.userWorksManagerPanelVC.frozenUgcId = timelineDetail.ugcId;
                    UIWindow *window = COMP_SERVICE_FOR(KSAppDelegateProtocol).window;                    
                    [self.userWorksManagerPanelVC showInViewIfNeeded:window];
                } else {
                    [self playMusicItemWithTimelineDetail:timelineDetail];
                }

                if (!self.isGuest) {
                    [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                        reportModel.key = @"homepage_me#music#creation_drawing#click#0";
                        [KSTraceReprotHelper_V2 autoFillReportModel:reportModel detail:timelineDetail];
                        reportModel.commonInt3 = objectIndex + 1;
                        reportModel.commonInt2 = self.musicAdapter.curSorter == JceTimeline_Detail_emSorter_ENUM_SORTER_PLAY ? 2 : 1;
                        reportModel.commonInt4 = self.musicAdapter.curFilter == JceTimeline_Detail_emFilter_ENUM_FILTER_ALL ? 1 : 2;
                        reportModel.commonInt5 = IS_EMPTY_STR_BM(timelineDetail.sortRankInfo.rankLabelText) ? 2 : 1;
                        reportModel.commonInt6 = timelineDetail.isArchived ? 1 : 0; // int6=是否归档 0=否 1=是  取不到值报-1
                        reportModel.commonStr1 = timelineDetail.sortRankInfo.sortRankType;
                        reportModel.ugcid = timelineDetail.ugcId;
                        reportModel.touid = self.userID;
                        reportModel.ugcmask1 = timelineDetail.jceUgcMask;
                        reportModel.ugcmask2 = timelineDetail.ugc_mask_ext;
                    } detail:timelineDetail];
                } else {
                    [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                        reportModel.key = @"homepage_guest#music#creation_drawing#click#0";
                        [KSTraceReprotHelper_V2 autoFillReportModel:reportModel detail:timelineDetail];
                        reportModel.commonInt3 = objectIndex + 1;
                        reportModel.commonInt7 = self.userID;
                        reportModel.commonInt2 = self.musicAdapter.curSorter == JceTimeline_Detail_emSorter_ENUM_SORTER_PLAY ? 2 : 1;
                        reportModel.commonInt4 = self.musicAdapter.curFilter == JceTimeline_Detail_emFilter_ENUM_FILTER_ALL ? 1 : 2;
                        reportModel.commonInt5 = IS_EMPTY_STR_BM(timelineDetail.sortRankInfo.rankLabelText) ? 2 : 1;
                        reportModel.commonStr1 = timelineDetail.sortRankInfo.sortRankType;
                        reportModel.from_page = self.from_page;
                        reportModel.touid = self.userID;
                        reportModel.open_relationtype = YES;
                    } detail:timelineDetail];
                }
            }
        }
    }
}

- (void)musicAdapter:(KSUserProfileMusicAdapter *)adapter didTriggerAction:(KSUserProfileMusicTabActionType)actionType withMusicItem:(KSTimelineDetail *)timelineDetail
{
    //原有逻辑已下线
}

// 私密上传的事件转发
- (void)musicAdapter:(KSUserProfileMusicAdapter *)adapter deleteUploadTask:(ProductUploadTask *)uploadTask {
    [self deleteUploadTask:uploadTask];
}

- (void)musicAdapter:(KSUserProfileMusicAdapter *)adapter reloadUploadTask:(ProductUploadTask *)uploadTask {
    [self reloadUploadTask:uploadTask];
}

- (void)musicAdapter:(KSUserProfileMusicAdapter *)adapter publishUploadTask:(ProductUploadTask *)uploadTask {
    [self publishUploadTask:uploadTask];
}

/// 点击关闭微信视频号绑定引导banner
- (void)musicAdapter:(KSUserProfileMusicAdapter *)adapter didClickWXChannelBannerCloseBtn:(NSDate *)date {

    [[NSUserDefaults standardUserDefaults] setObject:[NSDate date] forKey:NSUserDefaults_Profile_WXChannelBanner_LastDate];
    self.showWXChannelBindEntry = NO;

    [self reloadTable];
}

- (void)musicAdapter:(KSUserProfileMusicAdapter *)adapter showHotCard:(UIView *)view
{
    
    view.centerX = kScreenWidthBM()/2;
    view.top = kScreenHeightBM()-KSTabBarHeight;
    [self.view addSubview:view];
    [UIView animateWithDuration:0.15 animations:^{
        view.bottom = kScreenHeightBM()-KSTabBarHeight-16;
    }];
    KS_WEAK_SELF(view);
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(8 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        KS_STRONG_SELF(view);
        [UIView animateWithDuration:0.15 animations:^{
            view.top = kScreenHeightBM()-KSTabBarHeight;
        } completion:^(BOOL finished) {
            [view removeFromSuperview];
        }];
    });
}

- (void)musicAdapter:(KSUserProfileMusicAdapter *)adapter clickHotCard:(UIView *)view timelineDetail:(KSTimelineDetail *)timelineDetail
{
    [view removeFromSuperview];
    KLog(@"clickHotCard: %@",timelineDetail);
    [self playMusicItemWithTimelineDetail:timelineDetail];
}

#pragma mark - KSUserProfileAccompanyAdapterDelegate

- (void)accompanyAdapter:(KSUserProfileAccompanyAdapter *)adapter didClickAccompanyItem:(KSUserAccompanyInfo *)info {
    [self showUploadAccompanyDetail:info];
}

- (void)accompanyAdapter:(KSUserProfileAccompanyAdapter *)adapter didClickSingBtn:(KSUserAccompanyInfo *)info {
    if (info)
    {
        if ([KSong isCappellaSong:info.songMid] == YES)
        {
            [[KSNavigationManager sharedManager] showCappllaVCWithToRecordFromPage:0 contestId:0 writeRecordFromPage:@"unknow_page#null#null" strategyBlock:nil];
        }
        else
        {
            KSRecordingNormalStrategy *normalStrategy = [[KSRecordingNormalStrategy alloc] init];
            KSong* song = [info songConvertFromUserAccompany];
            normalStrategy.recordingData.song = song;
            if (self.isGuest)
            {
                normalStrategy.recordStrategyReport.from_page = [[self setupRecordFrompageForSingBtn] buildStrFrompage];
            }
            else
            {
                normalStrategy.recordStrategyReport.from_page = @"me#comp_and_duet#sing_button";
            }

            NSString *int1 = self.isGuest ? @"2":@"1";
            normalStrategy.recordStrategyReport.otherReportParam = [NSString stringWithFormat:@"commonInt1=%@&touid=%@",int1,[NSString stringWithFormat:@"%lld",self.userProfileInfo.userInfo.userId]];
            [[KSNavigationManager sharedManager] singSongWithStrategy:normalStrategy];
        }

        [self traceClickReport:ReportSubactionL3_UserProfile_Click_UploadAccompanimentKSong];
    }
}

- (void)accompanyAdapter:(KSUserProfileAccompanyAdapter *)adapter didClickDeleteBtn:(KSUserAccompanyInfo *)info {
    if (info) {
        KSBasicDialog *dialog = [[KSBasicDialog alloc] init];
        dialog.caption = @"确认删除该伴奏吗?";

        KS_WEAK_SELF(self);
        [dialog addControlButton:@"取消" subtitle:nil buttonStyle:KSBasicDialogButtonStyle_Normal callback:^(KSDialog * _Nonnull dialog) {
            [dialog dismiss];
        }];
        [dialog addControlButton:@"删除" subtitle:nil buttonStyle:KSBasicDialogButtonStyle_Emphasis callback:^(KSDialog * _Nonnull dialog) {
            [dialog dismiss];
            CHECK_SELF_AND_RETURN();
            [self deleteUserAccompany:info];
        }];
        [dialog show];
    }
}

// 通用的简单事件
- (void)accompanyAdapter:(KSUserProfileAccompanyAdapter *)adapter clickByActionType:(KSUserProfileAccompanyTabActionType)actionType {
    switch (actionType) {
        case KSUserProfileAccompanyTabActionType_Reload_Error: {
            [self loadUserUploadCompList:NO];
            [self.contentTableView reloadData];
            break;
        }
        default:
            break;
    }
}

#pragma mark - KSUserProfileStarAccompanyAdapterDelegate

- (void)starAccompanyAdapter:(KSUserProfileStarAccompanyAdapter *)adapter didClickUploadAccompanyItem:(KSUserAccompanyInfo *)info {
    [self showUploadAccompanyDetail:info];
}

- (void)starAccompanyAdapter:(KSUserProfileStarAccompanyAdapter *)adapter didClickUploadAccompanySingBtn:(KSUserAccompanyInfo *)info {
    if (info)
    {
        if ([KSong isCappellaSong:info.songMid] == YES)
        {
            [[KSNavigationManager sharedManager] showCappllaVCWithToRecordFromPage:0 contestId:0 writeRecordFromPage:@"unknow_page#null#null" strategyBlock:nil];
        }
        else
        {
            KSRecordingNormalStrategy *normalStrategy = [[KSRecordingNormalStrategy alloc] init];
            KSong* song = [info songConvertFromUserAccompany];
            normalStrategy.recordingData.song = song;
            normalStrategy.recordStrategyReport.from_page = @"me#comp_and_duet#sing_button";
            NSString *int1 = self.isGuest ? @"2":@"1";
            normalStrategy.recordStrategyReport.otherReportParam = [NSString stringWithFormat:@"commonInt1=%@&touid=%@",int1,[NSString stringWithFormat:@"%lld",self.userProfileInfo.userInfo.userId]];
            [[KSNavigationManager sharedManager] singSongWithStrategy:normalStrategy];
        }

        [self traceClickReport:ReportSubactionL3_UserProfile_Click_UploadAccompanimentKSong];
    }
}

- (void)starAccompanyAdapter:(KSUserProfileStarAccompanyAdapter *)adapter clickByActionType:(KSUserProfileStarAccompanyTabActionType)actionType {

    switch (actionType) {
        case KSUserProfileStarAccompanyTabActionType_MoreUpload: {
            [self showUploadAccompanyVC:nil];
            break;
        }
        case KSUserProfileStarAccompanyTabActionType_Reload_Error: {
            [self loadStarSongCompList:NO];
            [self.contentTableView reloadData];
            break;
        }
        default:
            break;
    }
}

- (void)starAccompanyAdapter:(KSUserProfileStarAccompanyAdapter *)adapter didClickStarAccompanyItem:(KSong *)info {
    [self traceClickReport:ReportSubactionL3_UserProfile_Click_StarCompCell];
    // 伴奏详情
    if (info) {
        if (info.bAreaCopyright == NO) {
            [self alert:nil title:KString(@"由于版权限制，您所在的地区暂时无法使用。") btns:[NSArray arrayWithObjects:KString(@"我知道了"),nil] tag:0];
        } else {
            [[KSNavigationManager sharedManager] showSongDetail:info];
        }
    }
}

- (void)starAccompanyAdapter:(KSUserProfileStarAccompanyAdapter *)adapter didClickStarAccompanySingBtn:(KSong *)info {
    if (info) {
        [self traceClickReport:ReportSubactionL3_UserProfile_Click_StarCompKSong];
        KSRecordingNormalStrategy *normalStrategy = [[KSRecordingNormalStrategy alloc] init];
        normalStrategy.recordingData.song = info;
        normalStrategy.recordStrategyReport.from_page = @"me#comp#sing_button";
        NSString * commonStr1 = @"1";
        if (self.isGuest)
        {
            commonStr1 = @"2";
        }
        normalStrategy.recordStrategyReport.otherReportParam = [NSString stringWithFormat:@"touid=%lld&commonStr1=%@",self.userProfileInfo.userInfo.userId,commonStr1];
        
        
        
        NetworkStatus status = [[KSReachability reachabilityForInternetConnection]  currentReachabilityStatus];
        
        NSMutableDictionary *param = [NSMutableDictionary dictionary];
        [param safeSetObject:normalStrategy.recordStrategyReport.from_page forKey:@"from_page"];
        [param safeSetObject:normalStrategy.recordStrategyReport.otherReportParam forKey:@"otherReportParam"];

        BOOL hasLocalAccompany = [[KSongInfoManager sharedManager] hasAccompanimentCachedBySongMid:info.songMid];

        //跟设置里面弹框逻辑绑定
        SettingConfig *KSongSetting = [WnsConfigManager sharedInstance].appConfig.kSongSetting;

        BOOL shouldShowAlert = ((SettingConfigNet_Every == KSongSetting.SettingNetWorkTip) || (SettingConfigNet_Exchange == KSongSetting.SettingNetWorkTip && !KSongSetting.hasAlert));

        if (status == NotReachable && !hasLocalAccompany)
        {
            KSActionSheetOld *actionSheet = [[KSActionSheetOld alloc] initWithWeakNetWorkActionSheet:info delegate:self param:[param copy] tag:KSACTIONSHEETSTYLE_KSONG_NOTNETWORK];
            [actionSheet showInView:self.view];

            return;
        }
        else if (status == ReachableViaWWAN && ![[KSPhoneCardManager sharedManager] canShowFreeDataIcon] && !hasLocalAccompany)
        {
            if (shouldShowAlert) {
                KSActionSheetOld *actionSheet = [[KSActionSheetOld alloc] initWithWeakNetWorkActionSheet:info delegate:self param:[param copy] tag:KSACTIONSHEETSTYLE_KSONG_NOTWIFI];
                [actionSheet showInView:self.view];
                return;
            }
            else if(SettingConfigNet_Exchange == KSongSetting.SettingNetWorkTip && KSongSetting.hasAlert){
                [KSAlertManager tempAlert:KString(@"当前为非wifi环境，请注意流量消耗")];
            }
        }
        [[KSNavigationManager sharedManager] singSongWithStrategy:normalStrategy];
    }
}

#pragma mark - KSUserProfileVideoAdapterDelegate

- (void)videoAdapter:(KSUserProfileVideoAdapter *)adapter didClickVideoItem:(KSTimelineDetail *)timelineDetail {
    [self playVideoItem:timelineDetail];
}

- (void)videoAdapter:(KSUserProfileVideoAdapter *)adapter clickByActionType:(KSUserProfileVideoTabActionType)actionType {
    switch (actionType) {
        case KSUserProfileVideoTabActionType_Reload_Error: {
            [self loadUserVideoWorkList:NO];
            [self.contentTableView reloadData];
            break;
        }
        case KSUserProfileVideoTabActionType_Empty_Link:{
            [[KSNavigationManager sharedManager] showOrderSongRoot];
            //作品-为空引导点唱入口点击
            [KSTraceReportHelper viewActionWithBlock:^(TraceReportView *info) {
                info.reserves = ReportSubactionL3_UserProfile_Click_ZuoPin_Empty_DianGeTai_Entry;
            }];
            break;
        }
        default:
            break;
    }
}

// 私密上传的事件转发
- (void)videoAdapter:(KSUserProfileVideoAdapter *)adapter deleteUploadTask:(ProductUploadTask *)uploadTask {
    [self deleteUploadTask:uploadTask];
}

- (void)videoAdapter:(KSUserProfileVideoAdapter *)adapter reloadUploadTask:(ProductUploadTask *)uploadTask {
    [self reloadUploadTask:uploadTask];
}

- (void)videoAdapter:(KSUserProfileVideoAdapter *)adapter publishUploadTask:(ProductUploadTask *)uploadTask {
    [self publishUploadTask:uploadTask];
}

- (void)videoAdapter:(KSUserProfileVideoAdapter *)adapter reloadSyncTask:(KSPublishContent *)publishContent {
    NSString *ugcId = publishContent.ugcId;
    NSString *vid = [publishContent.ExtraInfoDic safeObjectForKey:@"vid"];
    NSArray *list = [publishContent.ExtraInfoDic safeObjectForKey:@"retryList" ClassType:NSArray.class];
    [self reSyncUgcID:ugcId andVid:vid toMultiAccount:list isSilently:publishContent.isSilently];
    [self.contentTableView reloadData];
}

- (void)reSyncUgcID:(NSString *)ugcId andVid:(NSString *)vid toMultiAccount:(NSArray *)accountList isSilently:(BOOL)isSilently
{

    if (accountList && accountList.count == 0) {
        KLog(@"没有需要同步的Account");
        return;
    }

    [[KSSyncToMultiAccountManager sharedManager] sendUGCSyncOtherAccountInfo:accountList
                                                                       strVid:vid
                                                                     strUgcId:ugcId
                                                                  isSilently:isSilently
                                                                completeBlock:^(id ksObject, NSDictionary *customInfo, NSError *error) {
        if (error)
        {
            [KSErrorHandler onMgrError:error forceErrorMsg:KString(@"系统错误，同步失败")];
        }
        else
        {
            NSDictionary *resultDict = SAFE_CAST(ksObject, NSDictionary.class);
            if (resultDict)
            {
                NSNumber *failedCount = [resultDict safeObjectForKey:@"failed" ClassType:NSNumber.class];
                NSNumber *totalCount = [resultDict safeObjectForKey:@"total" ClassType:NSNumber.class];

                if (failedCount.integerValue > 0)
                {
                    // 同步失败
                    if ([failedCount isEqualToNumber:totalCount])
                    {
                        if (failedCount.integerValue == 1)
                        {
                            // 一个失败
                            [KSErrorHandler onMgrFinishToast:KString(@"同步失败")];
                        }
                        else
                        {
                            // 全部失败
                            [KSErrorHandler onMgrFinishToast:KString(@"全部同步失败")];
                        }
                    }
                    else
                    {
                        // 部分失败
                        [KSErrorHandler onMgrFinishToast:[NSString stringWithFormat:@"%@个账号同步失败", failedCount]];
                    }
                }
                else
                {
                    // 同步成功
                    if (totalCount.integerValue == 1)
                    {
                        [KSErrorHandler onMgrFinishToast:KString(@"同步成功")];
                    }
                    else
                    {
                        [KSErrorHandler onMgrFinishToast:KString(@"全部同步成功")];
                    }
                }
            }
        }
    }];

    [self.contentTableView reloadData];
}


#pragma mark - KSUserProfileUserInfoAdapterDelegate

- (void)userInfoAdapter:(KSUserProfileUserInfoAdapter *)adapter clickByActionType:(KSUserProfileInfoActionType)actionType {

    KSUserInfo *userInfo = self.userProfileInfo.userInfo;

    switch (actionType) {
        case KSUserProfileInfoActionType_IntimateRelationship: {
            // 个人主页点击跳转至hippy的密友页面
            [self jumpToHippyKtvIntimateView];
            // 埋点
            [self traceReportIntimateFriendClick:nil userId:self.userID int1:2];
            break;
        }
        case KSUserProfileInfoActionType_PhotoAlbum: {
            [self showPhotosVC];
            break;
        }
        case KSUserProfileInfoActionType_Family: {
            NSString *isguest  = @"1";
            NSString *from = nil;
            if (userInfo.userId == [KSLoginManager sharedInstance].curUserInfo.userId) {
                isguest = @"0";
                from = [@"homepage_me#all_module#null" URLEncodedString];
            }
            else {
                from = [@"homepage_guest#album_and_profile#null" URLEncodedString];
            }

            [[KSNavigationManager sharedManager] showWebView:[self getFamilySchemaUrl]];
            [self traceClickReport:ReportSubactionL3_UserProfile_Click_Family];

            if (self.isGuest) {
                [[KSVipTraceReportManager sharedManager] vipClickReport:0 aid:@"" rightid:NSIToString(VipRightId_Family) posid:NSIToString(VipPid_Family_ProfileGuest_p7)  userVip:[KSLoginManager sharedInstance].curUserInfo.userVipInfo songMid:@"" ugcId:@"" int1:userInfo.sAuthGroup.integerValue];

                [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                    reportModel.key = @"homepage_guest#album_and_profile#family_information#click#0";
                    reportModel.commonInt1 = self.userProfileInfo.userInfo.sAuthGroupRole > 0? self.userProfileInfo.userInfo.sAuthGroupRole : -1;
                    reportModel.commonInt7 = self.userID;
                    reportModel.commonStr1 = IS_EMPTY_STR_BM(userInfo.sAuthGroup) ? @"0" : userInfo.sAuthGroup;
                }];
            } else {
                [[KSVipTraceReportManager sharedManager] vipClickReport:0 aid:@"" rightid:NSIToString(VipRightId_Family) posid: NSIToString(VipPid_Family_Profile_p6) userVip:[KSLoginManager sharedInstance].curUserInfo.userVipInfo songMid:@"" ugcId:@"" int1:userInfo.sAuthGroup.integerValue];

                [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                    reportModel.key = @"homepage_me#album_and_profile#family_information#click#0";
                    reportModel.commonInt1 = self.groupInfo.jce_role > 0? self.groupInfo.jce_role : -1;
                    reportModel.commonStr1 = IS_EMPTY_STR_BM(userInfo.sAuthGroup) ? @"0" : userInfo.sAuthGroup;
                }];
            }
            break;
        }
        case KSUserProfileInfoActionType_ChatRoom_Manage: {
            [[KSChatRoomNavigationManager sharedManager] jumpToProfileChatRoomVCWithUserID:self.userID fromPageStr:@"profile"];

            {// 主人态下上报点击管理群聊，客人态上报点击查看更多群聊
                NSString *reportKey = @"homepage_me#album_and_profile#group_manage#click#0";
                if(self.isGuest){
                    reportKey = @"homepage_guest#album_and_profile#group_more#click#0";
                }

                [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                    reportModel.key = reportKey;
                    reportModel.commonInt7 = self.userProfileInfo.userInfo.userId;
                }];
            }

            break;
        }
        case KSUserProfileInfoActionType_ChatRoom_Create: {
            [[KSChatRoomNavigationManager sharedManager] jumpToChatRoomGroupInfoEnterVCWithFromPageStr:@"homepage_me#all_module#null" popToRootVC:NO];

            {// 主人态下上报点击创建群聊
                [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                    reportModel.key = @"homepage_me#album_and_profile#group_create_group_entry#click#0";
                }];
            }

            break;
        }
        case KSUserProfileInfoActionType_Level: {
            NSString *uid = [NSString stringWithFormat:@"%lld", self.userID];
            NSString *url = [[WnsConfigManager sharedInstance].appConfig.urlConfig getLevelUrl:uid];

            [[KSNavigationManager sharedManager] showWebView:url];
            [self traceClickReport:ReportSubactionL3_UserProfile_Click_Level];
            break;
        }
        case KSUserProfileInfoActionType_Wealth: {
            if (self.isGuest) {
                [[KSNavigationManager sharedManager] jumpToWealthGlobalRank:userInfo.userId timestamp:userInfo.avatarTimestamp wealthLevel:userInfo.sAuthWealthLevel wealthCount:userInfo.sAuthWealthValue];
            } else {
                [[KSNavigationManager sharedManager] jumpToWealthGlobalRank];
            }
            [self traceClickReport:ReportSubactionL3_UserProfile_Click_Wealth];
            [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                if (self.isGuest) {
                    reportModel.key = @"homepage_guest#personal_information#wealth_level_icon#click#0";
                } else {
                    reportModel.key = @"homepage_me#personal_information#wealth_level_icon#click#0";
                }
                reportModel.touid = userInfo.userId;
                reportModel.commonInt1 = userInfo.sAuthWealthLevel;
            }];
            break;
        }
        case KSUserProfileInfoActionType_AnchorLevel: {
            [[KSNavigationManager sharedManager] showAnchorLevel:self.userProfileInfo.userInfo.userId extraFrom:@"profile" reportClickFrom:1];
            break;
        }
        case KSUserProfileInfoActionType_Vip: {
            // VIP Icon 点击
            VipPid posid = self.isGuest ? VipPid_ProfilePhotoAlbumGuestIcon : VipPid_ProfilePhotoAlbumIcon;
            NSString *fromPage = self.isGuest ? @"homepage_guest#album_and_profile#grade#vipicon" : @"homepage_me#album_and_profile#grade#vipicon";
            BOOL shouldJumpTpVipCenter = KSABTestManager.sharedManager.shouldVIPIconClickJumpToVIPCenter;
            NSString *url = @"";
            if (shouldJumpTpVipCenter) {
                NSDictionary *customParams = (self.userProfileInfo.userInfo.userVipInfo.superVipStatus == 1)? @{ @"$vip": @"svip" } : nil;
                url = [KSVipManager getVIPCenterUrlWithPosID:posid customParams:customParams];
            }
            [KSVipManager vipIconComponentClickToUser:self.userProfileInfo.userInfo handlePresentVip:NO posid:posid vipUrl:url];
            [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                reportModel.key = @"vip_icon#icon#null#click#0";
                reportModel.from_page = fromPage;
            }];
            break;
        }
        case KSUserProfileInfoActionType_GiftAchieve:
        {
            // 跳转收礼成就主页
            [self jumpToGiftAchievementPage:NO];
            break;
        }
        case KSUserProfileInfoActionType_Intro: {
            if (userInfo.isStar == YES)
            {
                NSString *jumpUrl = [[WnsConfigManager sharedInstance].appConfig.urlConfig getStarAuthMsgUrlByShareUid:self.userProfileInfo.userInfo.shareUid];
                if (jumpUrl.length == 0) {
                    return;
                }
                [[KSNavigationManager sharedManager] showWebView:jumpUrl];
                [self traceClickReport:ReportSubactionL3_UserProfile_Click_StarWiki];
            }
            break;
        }
        case KSUserProfileInfoActionType_Sign: {
            //签名
            if(!self.isGuest) {
                //主人态不再展开，直接跳转到设置签名
                NSString *url = WnsStringConfig(kWnsConfig_Url, @"EditUserInfoUrl");
                [[KSNavigationManager sharedManager] showWebView:url];
                [self traceClickReport:ReportSubactionL3_UserProfile_Click_SignSetting];

                [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                    reportModel.key = @"homepage_me#album_and_profile#signature#click#0";
                }];
            }
            break;
        }
        case KSUserProfileInfoActionType_Fans: {
            // 上报
            [self reportExposeOrClickPersonalGiftCell:YES];

            if (self.isGuest)
            {
                [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                    reportModel.key = @"homepage_guest#personal_information#fans_list#click#0";
                    reportModel.commonInt7 = self.userID;
                }];
            }
            [self showPersonalGiftVC];
            break;
        }
        case KSUserProfileInfoActionType_GoodsWindows: {
            if (self.isGuest) {
                NSString *urlStr = WnsUrlStringConfig(@"PersonalGoodsShowcaseUrl");
                urlStr = [urlStr stringByReplacingOccurrencesOfString:@"$anchor_uid" withString:[NSString stringWithFormat:@"%lld", self.userID]];
                urlStr = [urlStr stringByReplacingOccurrencesOfString:@"$type" withString:[self isGuest] ? @"guest" : @"host"];
                [[KSNavigationManager sharedManager] showWebView:urlStr from:KSWebOpenFrom_Unknown];

                [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                    reportModel.key = @"homepage_guest#personal_information#personal_window#click#0";
                    reportModel.commonInt7 = self.userProfileInfo.userInfo.userId;
                }];
            } else {
                NSString *urlStr = WnsUrlStringConfig(@"PersonalGoodsManagerUrl");
                [[KSNavigationManager sharedManager] showWebView:urlStr from:KSWebOpenFrom_Unknown];

                [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                    reportModel.key = @"homepage_me#personal_information#personal_window#click#0";
                }];
            }
            break;
        }
        case KSUserProfileInfoActionType_RemindPhoto: {
            [self remindPhotoWithUid:self.userID completion:nil];
            break;
        }
        case KSUserProfileInfoActionType_AchievementMedal:
        {
            NSString *url = @"https://kg.qq.com/achievements/index.html?hippy=achievements";
            if (self.isGuest) {
                url = [url stringByAppendingFormat:@"&uid=%@",NSLLToString(self.userID)];
            }

            [[KSNavigationManager sharedManager] dealWithScheme:url];
            break;
        }
        case KSUserProfileInfoActionType_MyHonorRank:
        {
            [[KSNavigationManager sharedManager] dealWithScheme:self.userInfoAdapter.userHonorRsp.jce_strJumpUrl];
            
            [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                reportModel.key = self.isGuest ? @"homepage_guest#list_honor#null#click#0" : @"homepage_me#list_honor#null#click#0";
                reportModel.commonInt1 = self.userInfoAdapter.userHonorRsp.jce_vecItem.count > 0;
                reportModel.commonInt2 = 0;
                reportModel.commonStr1 = self.userInfoAdapter.userHonorRsp.jce_strTitle;
                reportModel.commonStr2 = @"-1";
                
                if (self.userProfileInfo.isGuest) {
                    reportModel.touid = self.userProfileInfo.userInfo.userId;
                }
            }];
            break;
        }
        default:
            break;
    }
}

- (void)userInfoAdapter:(KSUserProfileUserInfoAdapter *)adapter clickPhotoAlbumWithUrl:(NSString *)url
{
    [self previewPhotoAlbumWithUrl:url];
}

// 礼物墙标签上报
- (void)userInfoAdapter:(KSUserProfileUserInfoAdapter *)adapter giftAchieveUserInfoTagViewTraceReportWithClick:(BOOL)isClick
{
    [self traceReportGiftAchieveUserInfoTagView:isClick];
}

// 点击单个密友
- (void)userInfoAdapter:(KSUserProfileUserInfoAdapter *)adapter clickIntimateFriendWithItem:(JceKG_BuildItem *)item {
    // 个人主页点击跳转至hippy的密友页面
    NSString *url = @"https://kg.qq.com?hippy=ktvIntimateLink";
    if (self.isGuest) {
        url = [url stringByAppendingFormat:@"&ownerId=%lld", self.userID];
        url = [url stringByAppendingFormat:@"&source=4"];
    } else {
        url = [url stringByAppendingFormat:@"&source=5"];
    }
    // 存在 item = nil 的情况
    if (self.userID == item.jce_stUser.jce_uUID) {
        url = [url stringByAppendingFormat:@"&r=relation&relationUid=%lld_%u", self.userID, item.jce_stUserAnother.jce_uUID];
    } else if (self.userID == item.jce_stUserAnother.jce_uUID) {
        url = [url stringByAppendingFormat:@"&r=relation&relationUid=%lld_%u", self.userID, item.jce_stUser.jce_uUID];
    }

    [KSNavigationManager.sharedManager dealWithScheme:url];

    // 埋点 item不存在，说明点的是加号
    if (item) {
        [self traceReportIntimateFriendClick:item userId:self.userID int1:3];
    } else {
        [self traceReportIntimateFriendClick:item userId:self.userID int1:1];
    }
}

// 单个密友曝光
- (void)userInfoAdapter:(KSUserProfileUserInfoAdapter *)adapter intimateFriendExpose:(JceKG_BuildItem *)item {
    [self traceReportIntimateFriendExpose:item userId:self.userID];
}

-(void)clickUserProfileCommercialiBarByType:(KSUserProfileToolType)toolType
{
    switch (toolType) {
        case KSUserProfileToolType_AccountForKCoin:
            // 第三方登录态过期阻断 (我的-k币账户)
            if ([self checkIfThirdPartyAuthExpiredWithType:KSThirdAuthBlockType_Profile from:KSThirdAuthBlockFrom_KBAccount] == NO) {
                [self clickKcoinButton];
            }
            break;
        case KSUserProfileToolType_MyVip:
            if ([self checkIfThirdPartyAuthExpiredWithType:KSThirdAuthBlockType_Default from:KSThirdAuthBlockFrom_VIPCenter] == NO) {
                [self clickVipButton];
            }
            // 第三方登录态过期阻断 (我的-vip中心)
            break;
        case KSUserProfileToolType_MyTask:
            [self clickMyTaskButton];
            break;
        case KSUserProfileToolType_KolEntry:
            [self clickKolEntryButton];
            break;
        default:
            break;
    }
}

- (void)clickKolEntryButton {
    NSString *url = self.myCommercializebarModel.KolEntrance.jumpUrl;
    if (!IS_EMPTY_STR_BM(url)) {
        [[KSNavigationManager sharedManager] dealWithScheme:url];

        KLog(@"[个人主页] 星计划跳转:strJumpUrl:%@", url);
    }

    proto_main_page_webapp_DrawerIconStyle *drawStyle = [self getDrawIconStyleForEmIconId:proto_main_page_webapp_DrawerIconID_DrawerIconKOL];

    NSInteger commontInt2 = 0;
    if (drawStyle.jce_strIconURL.length > 0) {
        commontInt2 = 2;
    } else {
        commontInt2 = (drawStyle.jce_iRedDot == 0 ? 0 : 1);
    }

    [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
        reportModel.key = @"homepage_me#category_for_option#star_programs#click#0";
        reportModel.commonInt1 = commontInt2;
    }];
}

- (void) clickMyTaskButton
{
    if (!self.isGuest)
    {
        [self taskBtnClickReport];
        self.didTapTaskCenter = YES;
    }

    NSString *url = self.myCommercializebarModel.TaskEntrance.jumpUrl;
    if (url.length > 0)
    {
        [[KSNavigationManager sharedManager] showWebView:url];
        KLog(@"[个人主页] 任务中心跳转:strJumpUrl:%@", url);
    }
    else
    {
        [[KSNavigationManager sharedManager] showWebView:[WnsConfigManager sharedInstance].appConfig.urlConfig.taskPageUrl];
        KLog(@"[个人主页] 任务中心跳转:strJumpUrl:%@", [WnsConfigManager sharedInstance].appConfig.urlConfig.taskPageUrl);
    }

    // 点击存储活动id
    [KSUserProfileCommercializebarModel saveTaskBtnClickedActId:self.myCommercializebarModel.TaskEntrance.purchaseActId];
    //发送状态改变通知其他两个任务中心入口外显需更新
    [[NSNotificationCenter defaultCenter] postNotificationName:KSNotification_MissionEntranceChange object:nil];

}

- (void) clickVipButton
{

    if (self.myCommercializebarModel.VipEntrance && !self.myCommercializebarModel.VipEntrance.clicked) {
        self.myCommercializebarModel.VipEntrance.clicked = YES;
        [self.myCommercializebarModel.class saveClickedActId:self.myCommercializebarModel.VipEntrance.purchaseActId forKey:kProfileCardLightAnimSubKeyVip];
        [self.commercializeCell updateByTools:self.myCommercializebarModel];
    }

    //上报
    NSString *aid = [KSPayManager generateAid:NSIToString(VipPid_ProfileEnter_p1)];
    NSInteger rightid = VipRightId_NONE;

    [[KSVipTraceReportManager sharedManager] vipClickReport:0
                                                        aid:aid
                                                    rightid:NSIToString(rightid)
                                                      posid:NSIToString(VipPid_ProfileEnter_p1)
                                                    userVip:[KSLoginManager sharedInstance].curUserInfo.userVipInfo songMid:@""
                                                      ugcId:@""
                                                       int1:self.personalPageItem.jce_uId];

    NSString *url = self.myCommercializebarModel.VipEntrance.jumpUrl;
    if(url){
        [[KSNavigationManager sharedManager] jumpToVipCenter:url];
    }
    else{

        NSString *url = [[WnsConfigManager sharedInstance].appConfig.urlConfig getVipPageUrl:aid pf:@"" actSource:[[KSVipTraceReportManager sharedManager] getActSourceID:0] topSource:[[KSVipTraceReportManager sharedManager] getTopSource:0]];
        if (self.personalPageItem)
        {
            url = [url stringByReplacingOccurrencesOfString:@"$advertId" withString:[NSString stringWithFormat:@"%d",self.personalPageItem.jce_uId]];
        }
        [[KSNavigationManager sharedManager] showWebView:url];
    }
}

- (void) clickKcoinButton
{
    if (self.myCommercializebarModel.AccountEntrance && !self.myCommercializebarModel.AccountEntrance.clicked) {
        self.myCommercializebarModel.AccountEntrance.clicked = YES;
        [self.myCommercializebarModel.class saveClickedActId:self.myCommercializebarModel.AccountEntrance.purchaseActId forKey:kProfileCardLightAnimSubKeyAccount];
        [self.commercializeCell updateByTools:self.myCommercializebarModel];
    }

    // K币账户入口点击
    KSBuyKBReportItem *kbReportItem = [KSBuyKBReportItem new];
    kbReportItem.posid = BuyKBPid_Profile_KB_Page_Entrance;
    kbReportItem.toUid = self.userProfileInfo.userInfo.userId;
    kbReportItem.family = self.userProfileInfo.userInfo.sAuthGroup;
    KSUserProfileToolButton *toolButton = [self.toolBarCell getButtonWithToolType:KSUserProfileToolType_AccountForKCoin];
    if (toolButton) {
        kbReportItem.commonInt1 = toolButton.hasVipMarketingBubble ? 1:0;
        kbReportItem.commonInt2 = toolButton.hasVipMarketingIcon ? 1:0;
    }
    [[KSTraceReportManager sharedManager] buyKBClickPosidWithItem:kbReportItem];
    

    NSString *url = self.myCommercializebarModel.AccountEntrance.jumpUrl;
    if(url){
        [[KSNavigationManager sharedManager] jumpToStarDiamondAccount:url];
    }
    else{
        [[KSNavigationManager sharedManager] jumpToStarDiamondAccount:STARDIAMOND_AID_MINE withJumpTab:nil];
    }
    [[KSPayManager sharedInstance] preloadIAPProductIDsWhen:KSPayPreloadProdIDWhenShowKBAccountPage];
    if ([[TMEKTVPayManager sharedInstance] enableTMEPayRegister]) {
        [[TMEKTVPayManager sharedInstance] preloadIAPProductIDsWhen:TMEKTVPayPreloadProdIDWhenShowKBAccountPage];
    }
}

- (void)clickUserProfileCommercialialInfo {
    if (self.myCommercializebarModel) {
        // VIP直接支付逻辑
        proto_main_page_webapp_JumpAction *jumpAction = self.myCommercializebarModel.bannerJumpAction;
        BOOL iDirectPayType = jumpAction.jce_uActionType == proto_main_page_webapp_emJumpActionType_EM_JUMP_ACTION_TYPE_VIP_DIRECT_PAY;
        NSString *activityCode = SAFE_STR_BM([jumpAction.jce_mapActionParam safeObjectForKey:VipDirectPayActivityCodeKey]);

        // 判断动作参数&类型
        if (iDirectPayType && !IS_EMPTY_STR_BM(activityCode)) {
            [self userProfileVipBannerDirectPaywithActivityCode:activityCode];
        } else {
            NSString *scheme = [[KSLiveShowManager sharedManager] handleLiveSchemeIfNeededWithString:self.myCommercializebarModel.bannerJumpUrl fromPageNew:@"live#0#homepage#banner#null"];
            if (IS_EMPTY_STR_BM(scheme)) {
                scheme = self.myCommercializebarModel.bannerJumpUrl;
            }
            [[KSNavigationManager sharedManager] dealWithScheme:scheme]; // 正常跳转兜底
        }
    }
    // VIP banner点击营收上报
    NSString *pageAction = [KSComHelper getValueForKey:@"page_action" inUrl:self.myCommercializebarModel.bannerJumpUrl];
    if ([pageAction isEqualToString:@"openvip"]) {
        [[KSVipTraceReportManager sharedManager] vipReportClickOrExpose:YES extraBlock:^(TraceVipReportCommon *reportCommon) {
            reportCommon.rightid = VipRightId_ProfileVipBanner;
            reportCommon.posid = VipPid_UserProfile_Banner_Direct_Pay;
        }];
    }

    [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
        reportModel.key = @"homepage_me#Banner#null#click#0";
        reportModel.commonStr1 = [NSString stringWithFormat:@"%d",self.myCommercializebarModel.bannerId];
        reportModel.commonInt7 = self.myCommercializebarModel.businessType;
    }];
    // 运营位第三方监控上报
    [self reportMonitorClickOrExpose:NO];
}

-(void)isShouldShowCommercialBannerCell:(BOOL)isShow
{
    dispatch_async(dispatch_get_main_queue(), ^{
        self.isShouldShowCommercializeBanner = isShow;
        [self.contentTableView reloadData];
    });
}

#pragma mark - KSUserProfileStarFansAdapterDelegate

- (void)starFansAdapter:(KSUserProfileStarFansAdapter *)adapter clickByActionType:(KSUserProfileStarFansActionType)actionType {
    
    KSUserInfo *userInfo = self.userProfileInfo.userInfo;
    
    switch (actionType) {
        case KSUserProfileStarFansActionType_IntimateFans: {
            // 上报
            [self reportExposeOrClickPersonalGiftCell:YES];

            if (self.isGuest) {
                [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                    reportModel.key = @"homepage_guest#personal_information#fans_list#click#0";
                    reportModel.commonInt7 = self.userID;
                }];
            }

            [self showPersonalGiftVC];
            break;
        }
        case KSUserProfileStarFansActionType_Family: {
            [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                reportModel.key = !self.isGuest? @"homepage_me#fan_club#family_cell#click#0" : @"homepage_guest#fan_club#family_cell#click#0";
                reportModel.commonInt1 = self.userProfileInfo.userInfo.sAuthGroupRole > 0? self.userProfileInfo.userInfo.sAuthGroupRole : -1;
                reportModel.commonStr1 = IS_EMPTY_STR(userInfo.sAuthGroup) ? @"0" : userInfo.sAuthGroup;
            }];
        }
    }
}

#pragma mark - KSUserProfileToolBarCellDelegate

- (void)reportClickProfileToolType:(KSUserProfileToolType)toolType {

    if (self.isGuest) return;

    NSInteger emIconID = -1;
    NSString *reportKey = @"";

    if (toolType == KSUserProfileToolType_MyVip) { // VIP中心
        emIconID = proto_main_page_webapp_MainPageIconID_MainPageIconVIP;
        reportKey = @"homepage_me#category_for_option#VIP#click#0";

    } else if (toolType == KSUserProfileToolType_AccountForKCoin) { // K币账户
        emIconID = proto_main_page_webapp_MainPageIconID_MainPageIconAccount;
        reportKey = @"homepage_me#category_for_option#K_coin_account#click#0";

    }

    if (toolType == KSUserProfileToolType_AccountForKCoin ||
        toolType == KSUserProfileToolType_MyVip) {

        // K币账户和VIP中心点击后需要增加本地频度次数
        NSInteger showTimes = LocalIntegerConfig(kLocalMainPageIconStyleListShowTimes);
        if (self.mainPageIconStyleList && showTimes < self.mainPageIconStyleList.jce_iFreqLimit) {
            showTimes += 1;
            SaveLocalIntegerConfig(kLocalMainPageIconStyleListShowTimes, showTimes);
            [self reportClickWithMainPageIconId:emIconID reportKey:reportKey];

        } else {

            [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                reportModel.key = reportKey;
                reportModel.commonInt2 = 0;
            }];
        }
    }
}

// 上报点击运营态是红点或
- (void)reportClickWithMainPageIconId:(NSInteger)emIconID reportKey:(NSString *)reportKey
{
    if (emIconID < 0 || IS_EMPTY_STR_BM(reportKey)) return;
        
    for (proto_main_page_webapp_MainPageIconStyle* mainPageIconStyle in self.mainPageIconStyleList.jce_vctItems) {

        if (mainPageIconStyle.jce_emIconID == emIconID) {
            if (mainPageIconStyle.jce_strIconURL.length) { // I级异化上报

                [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                    reportModel.key = reportKey;
                    reportModel.commonInt2 = 2;
                }];
            }
            else // II级红点
            {
                // jce_iRedDot > 0 显示数字  = 0 不显示 =-1 只显示红点
                NSInteger commonInt2 = (mainPageIconStyle.jce_iRedDot == 0 ? 0 : 1);
                [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                    reportModel.key = reportKey;
                    reportModel.commonInt2 = commonInt2;
                }];
            }
            // 点击后去除I级异化和II级红点显示
            mainPageIconStyle.jce_strIconURL = nil;
            mainPageIconStyle.jce_iRedDot = 0;
        }
    }
}



- (void)clickUserProfileToolBarByType:(KSUserProfileToolType)toolType
{
    // 点击后刷新未读红点状态
    KSUserProfileToolModel *tool = [self getUserProfileToolForType:toolType];
    if (tool) {
        tool.shouldShowRedDot = NO;
        tool.unreadRedPointCount = 0;
        tool.strDesc = nil;
    }
    [self.toolBarCell updateByTools:self.tools];

    [self reportClickProfileToolType:toolType];

    switch (toolType)
    {
        case KSUserProfileToolType_MyAccompany:
        {
            // 已点伴奏
            [[KSNavigationManager sharedManager] showOrderedAndPracticeListVC:2 recordFrompage:nil];
            

            if (!self.isGuest)
            {
                [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                    reportModel.key = @"homepage_me#category_for_option#comp#click#0";

                    reportModel.commonInt1 = [KSBadgeUpdateManager sharedManager].orderedAccRedPoint > 0 ? 1 : 0;
                }];
            }

            // 消红点
            [self clearRecordSongListRedDot];

            break;
        }
        case KSUserProfileToolType_LocalRecord:
        {
            // 草稿箱
            [[KSNavigationManager sharedManager] showLocalVCWithAnimate];

            

            [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                reportModel.commonInt1 = [KSBadgeUpdateManager sharedManager].showProfileLocalWorksRedDot ? 1 : 0;
                reportModel.key = @"homepage_me#category_for_option#recordings#click#0";
                // int2=区分点击时是否带数字角标 0不带,1带,取不到值报 -1
                reportModel.commonInt2 = [[KSBadgeUpdateManager sharedManager] cloudUgcDraftBoxNum] ? 1 : 0;
            }];

            //点击草稿箱，消除红点
            [[KSBadgeUpdateManager sharedManager] setCloudUgcDraftBoxNum:0];

            [self clearLocalDraftsRedDot];

            break;
        }
        case KSUserProfileToolType_PlayHistory:
        {
            KSPlayingHistoryVC *playinghistoryVC = [[KSPlayingHistoryVC alloc] initWithUserId:self.userID];
            [self.navigationController pushViewController:playinghistoryVC animated:YES];
            [self traceClickReport:ReportSubactionL3_UserProfile_Click_PlayHistory];

            break;
        }
        case KSUserProfileToolType_OfflineUGC:
        {
            [self traceReportOfflineUgcClick];
            [self clearOfflineUgcRedDot];
            NSString *url = WnsUrlStringConfig(kWnsConfig_Url_offlineUGCPageUrl);
            if (url)
            {
                [[KSNavigationManager sharedManager] dealWithScheme:url];
            }

            break;
        }
        case KSUserProfileToolType_MyFavorite:
        {
            KSMyFavorVC *myFavorVC = [[KSMyFavorVC alloc] init];
            [self.navigationController pushViewController:myFavorVC animated:YES];
            [self traceClickReport:ReportSubactionL3_UserProfile_Click_MyFavorite];

            if (!self.isGuest)
            {
                [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                    reportModel.key = @"homepage_me#category_for_option#my_favorites#click#0";
                }];
            }

            break;
        }
        case KSUserProfileToolType_MyDownload:
        {
            [[KSNavigationManager sharedManager] showDownloadListVC:nil];
            [self traceClickReport:ReportSubactionL3_UserProfile_Click_MyDownload];

            if (!self.isGuest)
            {
                [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                    reportModel.key = @"homepage_me#more#download#click#0";
                }];
            }

            break;
        }
        case KSUserProfileToolType_MyMegagame:
        {
            NSString * jumpUrl = [WnsConfigManager sharedInstance].appConfig.urlConfig.myMegagameUrl;

            if (jumpUrl.length > 0)
            {
                [[KSNavigationManager sharedManager] showWebView:jumpUrl];
            }
            [self traceClickReport:ReportSubactionL3_UserProfile_Click_MyMegagame];

            break;
        }
        case KSUserProfileToolType_MyContribute:
        {

            NSString* contributeH5Page = [[WnsConfigManager sharedInstance].appConfig.contributeConfig getContributeH5PageUrl:nil];
            [[KSNavigationManager sharedManager] dealWithScheme:contributeH5Page from:KSWebOpenFrom_UserProfileVC];

            [self traceClickReport:ReportSubactionL3_UserProfile_Click_MyContribute];
            [self traceReportForContribute];
            break;
        }
        case KSUserProfileToolType_Remark:
        {
            NSString *url = WnsUrlStringConfig(@"StudentDianpingUrl");
            if (([self.userProfileInfo.userInfo.sMapAuth[@(JceProfile_AuthKey_AUTH_BIMTAP_FLAG_1)] longLongValue] & KSUserInfoAuthType_RemarkTeacher) > 0) {
                url = WnsUrlStringConfig(@"TeacherDianpingUrl");
            }
            url = [url stringByReplacingOccurrencesOfString:@"$topSource" withString:[[KSTraceReportManager sharedManager] getBuyKBTopSource]];
            [[KSNavigationManager sharedManager] showWebView:url];

            break;
        }
        case KSUserProfileToolType_MyOrders:
        {
            NSString *myOdersUrl = [WnsConfigManager sharedInstance].appConfig.urlConfig.myBuyList;
            [[KSNavigationManager sharedManager] showWebView:myOdersUrl];
            //TODO上报
            // [self traceClickReport:ReportSubactionL3_UserProfile_Click_FiveStar];

            break;
        }
        case KSUserProfileToolType_SingShop:
        {
            // 第三方登录态过期阻断  (我的-商城点击按钮)
            if ([self checkIfThirdPartyAuthExpiredWithType:KSThirdAuthBlockType_Default from:KSThirdAuthBlockFrom_Mall] == NO) {
                NSString *schema = WnsUrlStringConfig(@"SingShop");
                [[KSNavigationManager sharedManager] dealWithScheme:schema];

                if (!self.isGuest)
                {
                    [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                        reportModel.key = @"homepage_me#category_for_option#store#click#0";
                    }];
                }
            }

            break;
        }
        case SUserProfileToolType_LearnCollege:
        {
            [KSTeachSingCourseManager openAnchorCourseManagePage:kTeachCollegeTab_Course];
            break;
        }
        case KSUserProfileToolType_Verify: {

            proto_main_page_webapp_DrawerIconStyle *drawStyle = [self getDrawIconStyleForEmIconId:proto_main_page_webapp_DrawerIconID_DrawerIconCertificate];

            NSInteger commontInt2 = 0;
            if (drawStyle.jce_strIconURL.length > 0) {
                commontInt2 = 2;
            } else {
                commontInt2 = (drawStyle.jce_iRedDot == 0 ? 0 : 1);
            }

            [[KSNavigationManager sharedManager] dealWithScheme:@"https://kg.qq.com?hippy=userAuth&authFrom=2"];
            [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                reportModel.key = @"homepage_me#category_for_option#usercertificate#click#0";
                reportModel.touid = self.userProfileInfo.userInfo.userId;
                reportModel.commonStr1 = [NSString stringWithFormat:@"%lld", [KSAuthBitmapHelper newAuthBitmapFromMapAuth:self.userProfileInfo.userInfo.sMapAuth]];
                reportModel.commonInt2 = commontInt2;

            }];
            break;
        }
        case KSUserProfileToolType_AccountForKCoin: {
            if ([self checkIfThirdPartyAuthExpiredWithType:KSThirdAuthBlockType_Profile from:KSThirdAuthBlockFrom_KBAccount] == NO) {
                [self clickKcoinButton];
            }
            break;
        }
        case KSUserProfileToolType_MyVip: {
            if ([self checkIfThirdPartyAuthExpiredWithType:KSThirdAuthBlockType_Default from:KSThirdAuthBlockFrom_VIPCenter] == NO) {
                [self clickVipButton];
            }
            break;
        }
        case KSUserProfileToolType_MyTask: {
            [self clickMyTaskButton];
            break;
        }
        case KSUserProfileToolType_KolEntry:{
            [self clickKolEntryButton];
            break;
        }
        case KSUserProfileToolType_SingerCenter:{
            [[KSNavigationManager sharedManager] dealWithScheme:self.singerEntrance.jce_strJumpUrl];
            break;
        }
        default:
            break;
    }
}

- (void)userProfileToolbarCell:(KSUserProfileToolbarCell *)cell buttonExposureWithType:(KSUserProfileToolType)type
{
    switch (type)
    {
        case KSUserProfileToolType_Verify:{
            KSUserProfileToolButton *VerifyButton = [cell getButtonWithToolType:KSUserProfileToolType_Verify];
            [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                reportModel.key = @"homepage_me#category_for_option#usercertificate#exposure#0";
                reportModel.touid = [KSLoginManager sharedInstance].curUserInfo.userId;
                reportModel.commonStr1 = [NSString stringWithFormat:@"%lld", [KSAuthBitmapHelper newAuthBitmapFromMapAuth:[KSLoginManager sharedInstance].curUserInfo.sMapAuth]];
                reportModel.commonInt2 = VerifyButton.getToolModel.strIconUrl.length > 0 ? 2: (VerifyButton.getToolModel.shouldShowRedDot ? 1 : 0 );
            }];
            break;
        }
        default:
            break;
    }
}
#pragma mark - KSUserProfileVIPBannerCellDelegate
/// 新版个人主页，VIP banner被点击
- (void) clickVIPBanner {
    NSString *aid = [KSPayManager generateAid:NSIToString(VipPid_VIPBanner_Click)];
    NSString *url = self.myCommercializebarModel.VipEntrance.jumpUrl;
    if(url){
        [[KSNavigationManager sharedManager] jumpToVipCenter:url];
    } else {
        NSString *url = [[WnsConfigManager sharedInstance].appConfig.urlConfig getVipPageUrl:aid pf:@"" actSource:[[KSVipTraceReportManager sharedManager] getActSourceID:0] topSource:[[KSVipTraceReportManager sharedManager] getTopSource:0]];
        if (self.personalPageItem) {
            url = [url stringByReplacingOccurrencesOfString:@"$advertId" withString:[NSString stringWithFormat:@"%d",self.personalPageItem.jce_uId]];
        }
        [[KSNavigationManager sharedManager] showWebView:url];
    }
}

/// 点击了VIP banner的活动按钮
- (void) clickVIPBannerActivityBtnWithUrl:(NSString *)url {
    if (url) {
        [[KSNavigationManager sharedManager] showWebView:url];
    } else {
        KLog(@"[VIP Banner]运营位跳转地址为空");
    }
}
#pragma mark - KSEmptyCellDelegate

- (void)emptyViewClickAction
{
    KSUserProfileTabIndex index = [self curSelectTab];

    if ((index == KSUserProfileTabIndex_StarFans) && (self.loadStarFansRankStatus == KSUserProfileLoadDataStatus_Fail))
    {
        [self loadStarProfileFansRankList];
    }

    [self.contentTableView reloadData];
}

- (void)linkClickAction
{
    [[KSNavigationManager sharedManager] showOrderSongRoot];
}

- (void) KSLayoutableTableCell:(KSLayoutableTableCell*)cell didClickWithAction:(KSDrawItemAction*)action{
    [super KSLayoutableTableCell:cell didClickWithAction:action];
    if(action.type == DRAWITEM_ACTION_TYPE_AVATAR)
    {
        if(![cell.busiData isKindOfClass:[KSimpleFeed class]])
        {
            return;
        }
        KSimpleFeed *feed = (KSimpleFeed *)cell.busiData;
        if (feed.ugcRemark)
        {
            [self traceReportForRemarkAvatarClick:feed];
        }
    }
    else if (action.type == DRAWITEM_ACTION_TYPE_BUTTON_REPLY)
    {
        //点了评论/回复，包含关注热门附近tab
        if ([[KSAlertManager sharedManager] noNetworkAlert])
        {
            return;
        }
        KSimpleFeed *feed = (KSimpleFeed *)action.busiData;

        if (feed.isMusicMoodFeed && feed.isRefUGCUnavailable)
        {
            [self tempAlert:@"作品无法查看，暂不支持此操作"];
            return;
        }

        self.commentSimpleFeed = feed;
        self.ugcCommentItem = nil;
        self.commentType = KSTimelineReply_UgcComment;
        //对转发feed的评论会同时发给ugc作者和当前的转发人(中间链路上的转发人不会接受评论)，或者只发给ugc作者（类似详情页的回复）
        KSUidType uid = [KSLoginManager sharedInstance].curUserInfo.userId;
        if (feed.forwardFeed && feed.forwardFeed.feedUserInfo.userinfo.userId != uid)
        {
            self.commentType = KSTimelineReply_ForwardComment;
        }

        [self showTimelineCommentsView:feed];

        [self traceReportCommentClickedWithAction:action simpleFeed:feed];

    }
    else if (action.type == DRAWITEM_ACTION_TYPE_REMARK_UGC)
    {
        if(![cell.busiData isKindOfClass:[KSimpleFeed class]]){
            return;
        }
        KSimpleFeed *feed = (KSimpleFeed *)cell.busiData;
        [self traceReportForRemarkUgcClick:feed];
    }
    else if (action.type == DRAWITEM_ACTION_TYPE_BUTTON_PLAY)
    {
        //点击播放
        KSimpleFeed *simpleFeed = (KSimpleFeed *)action.busiData;
        [[KSUIABTestManager sharedManager] fireUIABTestModule:ksUIABTest_Module_GlobalPlay strategyBlock:^(JceKG_AbtestRspItem *item) {

            self.abTestPlayListMode = [item.jce_mapParams[ksUIABTest_Module_GlobalPlay_Pattern] integerValue];
        }];

        if (self.abTestPlayListMode == 0)
        {
            [self playCurrentFeed:simpleFeed isShowPlayVC:NO];
        }
        else
        {
            [self playAllListStartFromFeed:simpleFeed feedList:self.feedsInfo.feedsList isShowPlayVC:NO];
        }
    } else if(action.type == DRAWITEM_ACTION_TYPE_AVATAR_GOTO_LIVE_OR_KTV) {
        // 点击feed直播态头像
        KSCellUser *cellUser = (KSCellUser*)action.busiData;

        NSString *liveShowRoomId = cellUser.strRoomId;
        if (!IS_EMPTY_STR_BM(liveShowRoomId)) {
            // 跳转直播间
            [[KSNavigationManager sharedManager] watchLiveShow:liveShowRoomId forcePop:YES];
        }
    } else if (action.type == DRAWITEM_ACTION_TYPE_HC_TEXT) {
        // 点击“与xxx合唱”文案
        KSimpleFeed *simpleFeed = SAFE_CAST(action.busiData, KSimpleFeed);
        NSString *strUgcId = simpleFeed.songinfo.strHalfUgcId;
        KSGlobalPlayItem *playItem = [[KSGlobalPlayItem alloc] initWithUgcId:strUgcId ugcMask:simpleFeed.songinfo.ugcMask songName:simpleFeed.songinfo.name nickName:simpleFeed.HcUserInfo.nickName scoreRank:0];
        playItem.sourcePage = KSPlaySourcePage_UnknowSourcePageType;
        [[KSUgcPlayManager sharedManager] playUgcWithItem:playItem];
    }
}

- (void)deleteFeedForCell:(KSLayoutableTableCell*)cell action:(KSDrawItemAction*)action
{
    if (!cell || !cell.indexPath)
    {
        KLog(@"%@ 删除feed index nil",LogFeed);
        return;
    }
    if (!action || !action.busiData)
    {
        KLog(@"%@ 删除feed action nil",LogFeed);
        return;
    }

    KSimpleFeed *feed = action.busiData;
    NSString *ugcId = feed.simpleFeedCommon.strFeedId;
    //转发feed只删除feed，转发的源作品不删除
    if (feed.remarkFeedId.length > 0)
    {
        ugcId = feed.remarkFeedId;
    }
    else if (feed.forwardFeed.strForwardId.length>0)
    {
        ugcId = feed.forwardFeed.strForwardId;
    }

    JceBadgeUpdate_GPS *gps = [[JceBadgeUpdate_GPS alloc] init];
    gps.jce_fLat = feed.lbs.fLat;
    gps.jce_fLon = feed.lbs.fLon;
    gps.jce_eType = (JceInt32)feed.lbs.eType;
    gps.jce_iAlt = (JceInt32)feed.lbs.iAlt;

    KS_WEAK_SELF(self);
    [[KSTimelineManager sharedManager] deleteFeedWithUgcId:ugcId feedType:feed.simpleFeedCommon.uTypeid gps:nil comletion:^(id ksObject, NSDictionary *customInfo, NSError *error) {
        CHECK_SELF_AND_RETURN();

        if (error)
        {
            KLog(@"TimelineRoot 删除feed失败 feedId=%@ error=%@",feed.simpleFeedCommon.strFeedId,error);
            [KSErrorHandler onMgrError:error forceErrorMsg:@"删除失败"];
        }
        else
        {
            NSMutableArray *dataList = [self getDataSourceList];
            [dataList removeObject:feed];

            [self reloadTable];

            //通知timelineroot和KSProfileForwardVC删除对应的feed
            NSString *strFeedId = feed.simpleFeedCommon.strFeedId;
            if (feed.remarkFeedId.length > 0)
            {
                strFeedId = feed.remarkFeedId;
            }
            else if (feed.forwardFeed.strForwardId.length>0)
            {
                strFeedId = feed.forwardFeed.strForwardId;
            }
            [[NSNotificationCenter defaultCenter] postNotificationName:KSNotification_DeleteSelfFeed object:strFeedId];
            [self traceReportDeleteFeedSuccessWithAction:action simpleFeed:feed];
        }

    }];

}

//点击删除feed成功
- (void)traceReportDeleteFeedSuccessWithAction:(KSDrawItemAction*)action  simpleFeed:(KSimpleFeed*)feed
{
    KSTraceReportManager *traceMgr = [KSTraceReportManager sharedManager];
    TraceReportView *viewInfo = [[TraceReportView alloc] init];
    viewInfo.reportType = JceDcreport_emReportType_REPORT_TYPE_PERSONAL;
    viewInfo.subaction = ReprotSubactionUserProfileWrite;
    viewInfo.reserves = ReportWriteL3_UserProfile_DeleteFeedSuccess;
    viewInfo.prd_id = feed.simpleFeedCommon.strFeedId;
    [self modifyViewInfo:viewInfo action:action];
    // 去掉上报
//    [traceMgr viewActionReport:viewInfo];

}

- (void)didBadgeGiftListClickActionWithitemType:(KSUserProfileItemType)itemType isReddot:(BOOL)isReddot
{
    if (itemType == KSUserProfileItemType_Badge) {
        self.isNeedUpdateBadgeInfo = YES;
        NSString *enableMedalShare = WnsSwitchStringConfig(@"EnableMedalShare");
        NSString *urlGuestStr = [NSString stringWithFormat:@"http://kg.qq.com?hippy=badge&shareUid=%@&enableShare=%@&from=1",self.userProfileInfo.userInfo.shareUid,enableMedalShare];
        __block NSString *urlOwnerStr = [NSString stringWithFormat:@"http://kg.qq.com?hippy=badge&enableShare=%@&from=1&reddot=%d",enableMedalShare,isReddot];
        if (self.badgeEntryInfo.jce_uNewReach && self.badgeEntryInfo.jce_vctNewReach.count > 0) {
            urlOwnerStr = [urlOwnerStr stringByAppendingString:@"&vctBadgeId="];
            NSArray<proto_kg_badge_NewReachHeader *> *reachArray = self.badgeEntryInfo.jce_vctNewReach;
            [reachArray enumerateObjectsUsingBlock:^(proto_kg_badge_NewReachHeader * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
                obj = SAFE_CAST(obj, proto_kg_badge_NewReachHeader);
                urlOwnerStr = [urlOwnerStr stringByAppendingString:[NSString stringWithFormat:@"%@%@", idx? @"-" : @"", obj.jce_strBadgeId]];
            }];
        }
        NSString *urlStr = self.isGuest ? urlGuestStr : urlOwnerStr;
        [[KSNavigationManager sharedManager] dealWithScheme:urlStr];
        [self jumpToMedalShop];
    } else if (itemType == KSUserProfileItemType_GiftList) {
        self.isNeedUpdateGiftListInfo = YES;
        NSString *achieveJumpUrl = self.achievementEntryInfo.jce_strJumpUrl;
        [[KSNavigationManager sharedManager] dealWithScheme:achieveJumpUrl];
    } else if (itemType == KSUserProfileItemType_AIVoice) {
        // 没有命中ABT走老的跳转音色库
        if (self.modelEntranceInfo.modelEntranceRsp.jce_abtHitType == 0) {
            // 个人资产页
            NSString *fromPage = [@"homepage_me#ai_song_entrance#null" URLEncodedString];
            NSString *scheme = [NSString stringWithFormat:@"https://kg.qq.com/index.html?hippy=aiSellSong&r=train&from_page=%@",fromPage];
            [[KSNavigationManager sharedManager] dealWithScheme:scheme];
        }
        else {
            int guideType = self.modelEntranceInfo.modelEntranceRsp.jce_guideType;
            KLog(@"[AISingGuide] jump hippy, guideType: %d", guideType);
            switch (guideType) {
                
                case JceKG_proto_ai_svc_emGuideType_emGuideTypeHasActivity:
                {
                    JceKG_proto_ai_svc_ActivityCmem *info = self.modelEntranceInfo.modelEntranceRsp.jce_activityCmem;
                    if (info.jce_activity_link.length) {
                        NSString *fromPage = [@"homepage_me#ai_song_entrance#null" URLEncodedString];
                        NSMutableString *realUrl = [[NSMutableString alloc] initWithFormat:@"%@&from_page=%@", info.jce_activity_link, fromPage];
                        [[KSNavigationManager sharedManager] dealWithScheme:realUrl];
                    } else {
                        KErrLog(@"[AISingGuide] activity jump url is nil");
                    }
                }
                    break;
                default:
                {
                    NSString *jumpUrl = self.modelEntranceInfo.modelEntranceRsp.jce_strJumpUrl;
                    if (jumpUrl.length) {
                        NSString *fromPage = [@"homepage_me#ai_song_entrance#null" URLEncodedString];
                        NSMutableString *realUrl = [[NSMutableString alloc] initWithFormat:@"%@&from_page=%@", jumpUrl, fromPage];
                        [[KSNavigationManager sharedManager] dealWithScheme:realUrl];
                    } else {
                        KErrLog(@"[AISingGuide] jump url is nil");
                    }
                }
                    break;
            }
        }
    }
}

#pragma mark -- 上传伴奏tabView
- (void)showUploadAccompanyVC:(id)sender
{
    [self traceClickReport:ReportSubactionL3_UserProfile_Click_UploadAccompany];
    [[KSNavigationManager sharedManager] showUserAccompanyListVCWithUserID:self.userID];

    [self traceClickReport:ReportSubactionL3_UserProfile_Click_UploadAccompanimentShowAll];
}

- (void)showUploadAccompanyDetail:(KSUserAccompanyInfo*)info
{
    if (info)
    {
        KSong *song = [KSong new];
        song.songMid = info.songMid;
        [[KSNavigationManager sharedManager] showSongDetail:song fromRecite:NO recordFrompage:[self setupRecordFrompageForAcc]];

        [self traceClickReport:ReportSubactionL3_UserProfile_Click_UploadAccompanimentDetail];
    }
}

#pragma mark KSUserProfileHeaderFolderCellDelegate
// 更多 跳转
- (void)didClickMoreWithHeaderFolderCell:(KSUserProfileHeaderFolderCell *)cell
{
    if ([self curSelectTab] == KSUserProfileTabIndex_StarFans)
    {
        KSStarProfileFansRankVC *rankVC = nil;
        if (cell.indexPath.row == self.fansFanchangRankCellIndexRange.location)
        {
            [self traceClickReport:ReportSubactionL3_UserProfile_Click_FansMoreFanchang];
            rankVC = [[KSStarProfileFansRankVC alloc] initWithRankType:KSStarProfileRankType_FanChang uid:self.userID singerMid:self.userProfileInfo.userInfo.strSingerMid];
        }
        else if (cell.indexPath.row == self.fansGiftRankCellIndexRange.location)
        {
            [self traceClickReport:ReportSubactionL3_UserProfile_Click_FansMoreGift];
            rankVC = [[KSStarProfileFansRankVC alloc] initWithRankType:KSStarProfileRankType_Gift uid:self.userID singerMid:self.userProfileInfo.userInfo.strSingerMid];
        }
        else if (cell.indexPath.row == self.fansActiveRankCellIndexRange.location)
        {
            [self traceClickReport:ReportSubactionL3_UserProfile_Click_FansMoreActive];
            rankVC = [[KSStarProfileFansRankVC alloc] initWithRankType:KSStarProfileRankType_Active uid:self.userID singerMid:self.userProfileInfo.userInfo.strSingerMid];
        }

        if (rankVC)
        {
            [[KSNavigationManager sharedManager] pushVC:rankVC animated:YES];
        }
    }
}

#pragma mark -- KSVideoChannelDelegate
-(void)videoListDidUpdate:(NSArray<KSDetailPlaybackModel *> *)dataList lastUgcId:(NSString *)lastUgcId
{
    for (int i = 0; i < self.musicWorksList.datalist.count; i++)
    {
        KSTimelineDetail *item = [KSComHelper getObjectInArray:self.musicWorksList.datalist byIndex:i ofClassType:[KSTimelineDetail class] defaultValue:nil];
        
        for (KSDetailPlaybackModel *model in dataList)
        {
            // 送礼送花评论分享关注等写操作数据的更新
            if ([item.ugcId isEqualToString:model.detail.ugcId])
            {
                item.numOfKB = model.detail.numOfKB;
                item.numOfFlowers = model.detail.numOfFlowers;;
                item.numOfComment = model.detail.numOfComment;
                item.isFollowed = model.detail.isFollowed;
                item.numOfForward = model.detail.numOfForward;
                break;
            }
        }
    }

    // 因为作品拆分成了音乐和视频，所以视频列表也跑一圈
    for (int i = 0; i < self.videoWorksList.datalist.count; i++)
    {
        KSTimelineDetail *item = [KSComHelper getObjectInArray:self.videoWorksList.datalist byIndex:i ofClassType:[KSTimelineDetail class] defaultValue:nil];
        
        for (KSDetailPlaybackModel *model in dataList)
        {
            // 送礼送花评论分享关注等写操作数据的更新
            if ([item.ugcId isEqualToString:model.detail.ugcId])
            {
                item.numOfKB = model.detail.numOfKB;
                item.numOfFlowers = model.detail.numOfFlowers;;
                item.numOfComment = model.detail.numOfComment;
                item.isFollowed = model.detail.isFollowed;
                item.numOfForward = model.detail.numOfForward;
                break;
            }
        }
    }
}

#pragma mark NSNotificationCenter Event

-(void)ksDownloadSongProgressChange:(NSNotification *)notification
{

    NSMutableDictionary *object = notification.object;
    NSString *songMid = [object safeObjectForKey:@"songMid"];
    BOOL isFinished = [(NSNumber *)[object safeObjectForKey:@"isFinished"] boolValue];

    if (isFinished)
    {
        NSString *fromPage = SAFE_STR_BM([object safeObjectForKey:@"fromPage"]);
        if ([fromPage isEqualToString:@"star_page#all_module#null"])
        {
            [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                reportModel.key = @"all_page#all_module#null#write_download_comp#0";
                reportModel.mid = songMid;
                reportModel.commonInt1 = 1;
                reportModel.commonInt2 = [[KSFileDownloadManager defaultManager] isAutomaticDownLoadSong:songMid]?1:2;
                reportModel.from_page = @"star_page#all_module#null";
            }];
        }

        BOOL hasDownLoaded = [[KSFileDownloadManager defaultManager] hasDownLoadFile:songMid];
        if (hasDownLoaded)
        {
            if ([self curSelectTab] == KSUserProfileTabIndex_StarAccompany)
            {
                [self.contentTableView reloadData];
            }
        }
    }
}

#pragma mark KSActionSheetOldDelegate
- (void)didClickOnWeakNetWorkButtonIndex:(NSInteger)buttonIndex songInfo:(KSong *)song param:(NSDictionary *)paramDict withTag:(KSACTIONSHEETSTYLE)tag
{

    if (buttonIndex == 0 || buttonIndex == 1)
    {
        return;
    }
    else if(buttonIndex == 2)
    {
        KSWebViewController *webViewVC = [[KSWebViewController alloc] init];

        if ([[WnsConfigManager sharedInstance] isPGSH]) {
            webViewVC.url = WnsUrlStringConfig(@"KingCardNotOpenedUrlInPGSH");
        }
        else {
            webViewVC.url = WnsUrlStringConfig(@"KingCardNotOpenedUrl");
        }

        KLog(@"[弱网络下载] 开通免流量url = %@",webViewVC.url);

        dispatch_async(dispatch_get_main_queue(), ^{
            [[[KSNavigationManager sharedManager] getMainPageNavController] pushViewController:webViewVC animated:YES];
        });

        [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
            reportModel.key = @"wifi_unavailable_window#launch_free_traffic_service#null#click#0";
        }];
    }
    else if (buttonIndex == 3)
    {
        if(tag == KSACTIONSHEETSTYLE_KSONG_NOTWIFI)
        {
            KSRecordingNormalStrategy *normalStrategy = [[KSRecordingNormalStrategy alloc] init];
            normalStrategy.recordingData.song = song;

            normalStrategy.recordStrategyReport.from_page = (NSString *)[paramDict safeObjectForKey:@"from_page"];

            normalStrategy.recordStrategyReport.otherReportParam = (NSString *)[paramDict safeObjectForKey:@"otherReportParam"];

            [[KSNavigationManager sharedManager] singSongWithStrategy:normalStrategy needShowNetAlertView:NO];
        }
        else if (tag == KSACTIONSHEETSTYLE_DOWNLOADSONG_NOTWIFI)
        {
            [[KSFileDownloadManager defaultManager] startDownLoadSong:song delegate:self fromPage:@"star_page#all_module#null" completionBlock:nil];
        }

    }
    else if (buttonIndex == 4)
    {
        BOOL isSingLater = NO;
        if (tag == KSACTIONSHEETSTYLE_KSONG_NOTWIFI || tag == KSACTIONSHEETSTYLE_KSONG_NOTNETWORK)
        {
            isSingLater = YES;
        }
        [[KSFileDownloadManager defaultManager] startDownLoadLater:song singLater:isSingLater];
    }

}

#pragma mark KSUserProfileOrgTutorsAdapterDelegate
- (void)orgAdapterReloadTableview
{
    [self updateTableViewByIndex:KSUserProfileTabIndex_AgencyTutors];
}

-(void)orgAdapter:(KSUserProfileOrgTutorsAdapter *)adapter actionType:(KSUserProfileOrgTutorsTabActionType)actionType withKSUserInfo:(KSUserInfo *)userInfo
{
    if (actionType == KSUserProfileOrgTutorsTabActionType_ActionBtn) {
        NSString* reportKey = self.isGuest ? @"homepage_guest#tutor_tab#following#click#0" : @"homepage_me#tutor_tab#following#click#0";
        [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
            reportModel.key = reportKey;
            reportModel.commonInt7 = self.userID;
            reportModel.touid = userInfo.userId;
        }];

        KSTraceReportModel_V2 *reportModel = [[KSTraceReportModel_V2 alloc] init];
        reportModel.key = self.isGuest ? @"homepage_guest#tutor_tab#following#write_follow#0" : @"homepage_me#tutor_tab#following#write_follow#0";
        reportModel.commonInt7 = self.userID;
        [[KSRelationManager sharedManager] followUser:userInfo sceneId:KSFollowScene_PersonalPage needTips:YES reportSucc:reportModel];
    } else if (actionType == KSUserProfileOrgTutorsTabActionType_CellClick) {
         NSString* reportKey = self.isGuest ? @"homepage_guest#tutor_tab#tutor_item#click#0" : @"homepage_me#tutor_tab#tutor_item#click#0";
        [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
            reportModel.key = reportKey;
            reportModel.commonInt7 = self.userID;
            reportModel.touid = userInfo.userId;
        }];
        [[KSNavigationManager sharedManager] showProfileView:userInfo.userId fromPage:0 withRectItme:nil extraInfo:nil];
    }
}

- (void)orgAdapterjudgeExpose3WithCell:(UITableViewCell *)cell
{
    if ([cell isKindOfClass:[KSTeachSingComTableCell class]]) {
        KSTeachSingComTableCell *teacherCell = SAFE_CAST(cell, KSTeachSingComTableCell);
        [teacherCell judgeExpose3WithMinExpose:0.5 minVisibleRate:0 extraExposeCondition:nil cellModel:teacherCell.configModel exposeFinishBlock:^{
            NSString *reportKey = self.isGuest ? @"homepage_guest#tutor_tab#tutor_item#exposure#0" : @"homepage_me#tutor_tab#tutor_item#exposure#0";
            [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                reportModel.key = reportKey;
                reportModel.commonInt7 = self.userID;
                reportModel.touid = teacherCell.configModel.avatarInfo.userId;
            }];
        }];
    }
}

#pragma mark KSUserProfileTutorGroupAdapterDelegate
- (void)tutorAdapterReloadTableview
{
    [self updateTableViewByIndex:KSUserProfileTabIndex_TutorsLearnGoup];
}

- (void)tutorGroupAdater:(KSUserProfileTutorGroupAdapter *)adpter clickWithActionType:(KSUserProfileTutorGroupTabActionType)action withModel:(JcePayAlbum_GroupChatInfoItem*)model
{
    if (self.isGuest) {
        NSString *reportKey;
        if (action == KSUserProfileTutorGroupTabActionType_ActionBtn) {
            reportKey = @"homepage_guest#learning_cycle_tab#join#click#0";
        } else if (action == KSUserProfileTutorGroupTabActionType_CellClick){
            reportKey = @"homepage_guest#learning_cycle_tab#group_cell#click#0";
        }

        [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
            reportModel.key = reportKey;
            reportModel.commonInt7 = self.userID;
            reportModel.commonStr8 = NSIToString(model.jce_uGroupId);
        }];
    }
}

- (void)tutorGroupAdater:(KSUserProfileTutorGroupAdapter *)adpter courseItemClick:(proto_teaching_course_CourseInfo *)model
{
    [KSTeachSingCourseManager openCourseDetailPage:SAFE_STR_BM(model.jce_strCourseId)];
}

- (void)tutorGroupAdater:(KSUserProfileTutorGroupAdapter *)adpter fakeHeaderCellClick:(KSLearchGroupSetionModel *)sectionModel
{
    if (![self isGuest]) {
        // 主人态
        [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
            reportModel.key = @"homepage_me#learning_cycle_tab#lesson_manage#click#0";
            reportModel.commonInt7 = self.userID;
        }];
        [KSTeachSingCourseManager openAnchorCourseManagePage:kTeachCollegeTab_Manager];
    } else {
        KSTeachCourseListViewController *vc = [[KSTeachCourseListViewController alloc] init];
        vc.anchorId = self.userID;
        [[KSNavigationManager sharedManager] pushVC:vc animated:YES];
    }
    [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
        reportModel.key = @"homepage_me#learning_cycle_tab#lesson_manage#click#0";
        reportModel.commonInt7 = self.userID;
    }];
}

#pragma mark - KSUserProfileAuthItemCellDelegate

- (void)userProfileAuthItemCell:(KSUserProfileAuthItemCell *)cell clickType:(KSUserProfileAuthItemType)type
{
    switch (type) {
        case KSUserProfileAuthItemTypeAuth: {
            [self didTapAuthInfo];
            break;
        }
        case KSUserProfileAuthItemTypeQMusic: {
            [self jumpToOriginMusicianPage:self.userProfileInfo.userInfo.singerJumpUrl];
            break;
        }
        case KSUserProfileAuthItemTypeFamily: {
            [self didTapFamilyLabel];
            break;
        }
        default:
            break;
    }
}

#pragma mark - KSUserProfileLiveAndKTVCellDelegate

- (void)didUserProfileLiveAndKTVCell:(KSUserProfileLiveAndKTVCell *)cell clickLiveRoom:(KSLiveShowRoom *)liveRoom
{
    [self didTapLiveRoom:liveRoom];
}

- (void)didUserProfileLiveAndKTVCell:(KSUserProfileLiveAndKTVCell *)cell clickSimpleKTV:(JceProfile_RoomBasicInfo *)simpleKTVInfo
{
    int64_t int4 = [self getItemIndexCell:cell ktvInfo:simpleKTVInfo] + 1;
    [self didTapSimpleKTV:simpleKTVInfo int4:int4];
}

- (void)didUserProfileLiveAndKTVCell:(KSUserProfileLiveAndKTVCell *)cell clickFriendKTV:(JceProfile_RoomBasicInfo *)friendKTVInfo
{
    int64_t int4 = [self getItemIndexCell:cell ktvInfo:friendKTVInfo] + 1;
    [self didTapFriendKTV:friendKTVInfo int4:int4];
}

- (void)didUserProfileLiveAndKTVCell:(KSUserProfileLiveAndKTVCell *)cell clickSocialKTV:(JceProfile_RoomBasicInfo *)socialKTVInfo
{
    int64_t int4 = [self getItemIndexCell:cell ktvInfo:socialKTVInfo] + 1;
    [self didTapSocialKTV:socialKTVInfo int4:int4];
}

- (void)didUserProfileLiveAndKTVCell:(KSUserProfileLiveAndKTVCell *)cell clickSingOrderEntrance:(JceProfile_SingOrderEntrance *)singOrderEntrance
{
    [self didTapSingOrder:singOrderEntrance];
}

- (void)didUserProfileLiveAndKTVCell:(KSUserProfileLiveAndKTVCell *)cell clickUserAuthGiftActivityEntrance:(KSUserAuthGiftActivityInfo *)giftActivityInfo;
{
    [self didTapGiftActivity:giftActivityInfo];
}


- (NSUInteger)getItemIndexCell:(KSUserProfileLiveAndKTVCell *)cell ktvInfo:(JceProfile_RoomBasicInfo *)ktvInfo
{
    NSArray *roomInfoDataArray = [cell getRoomInfoDataArray];
    NSUInteger cellIndex = [roomInfoDataArray indexOfObject:ktvInfo];
    cellIndex = MIN(cellIndex, roomInfoDataArray.count - 1);
    cellIndex = MAX(cellIndex, 0);
    return cellIndex;
}

- (NSUInteger)getItemIndexCellV2:(KSUserProfileLiveAndKTVCellV2 *)cell ktvInfo:(JceProfile_RoomBasicInfo *)ktvInfo
{
    NSArray *roomInfoDataArray = [cell getRoomInfoDataArray];
    NSUInteger cellIndex = [roomInfoDataArray indexOfObject:ktvInfo];
    cellIndex = MIN(cellIndex, roomInfoDataArray.count - 1);
    cellIndex = MAX(cellIndex, 0);
    return cellIndex;
}


#pragma mark - KSUserProfileLiveAndKTVCellDelegateV2

- (void)didUserProfileLiveAndKTVCellV2:(KSUserProfileLiveAndKTVCellV2 *)cell clickLiveRoom:(KSLiveShowRoom *)liveRoom {
    [self didTapLiveRoom:liveRoom];
}

- (void)didUserProfileLiveAndKTVCellV2:(KSUserProfileLiveAndKTVCellV2 *)cell clickSimpleKTV:(JceProfile_RoomBasicInfo *)simpleKTVInfo {
    int64_t int4 = [self getItemIndexCellV2:cell ktvInfo:simpleKTVInfo] + 1;
    [self didTapSimpleKTV:simpleKTVInfo int4:int4];
}

- (void)didUserProfileLiveAndKTVCellV2:(KSUserProfileLiveAndKTVCellV2 *)cell clickFriendKTV:(JceProfile_RoomBasicInfo *)friendKTVInfo {
    int64_t int4 = [self getItemIndexCellV2:cell ktvInfo:friendKTVInfo] + 1;
    [self didTapFriendKTV:friendKTVInfo int4:int4];
}

- (void)didUserProfileLiveAndKTVCellV2:(KSUserProfileLiveAndKTVCellV2 *)cell clickSocialKTV:(JceProfile_RoomBasicInfo *)socialKTVInfo {
    int64_t int4 = [self getItemIndexCellV2:cell ktvInfo:socialKTVInfo] + 1;
    [self didTapSocialKTV:socialKTVInfo int4:int4];
}

- (void)didUserProfileLiveAndKTVCellV2:(KSUserProfileLiveAndKTVCellV2 *)cell clickSingOrderEntrance:(JceProfile_SingOrderEntrance *)singOrderEntrance {
    [self didTapSingOrder:singOrderEntrance];
}

- (void)didUserProfileLiveAndKTVCellV2:(KSUserProfileLiveAndKTVCellV2 *)cell clickUserAuthGiftActivityEntrance:(KSUserAuthGiftActivityInfo *)giftActivityInfo {
    [self didTapGiftActivity:giftActivityInfo];
}

- (void)didTapLiveRoom:(KSLiveShowRoom *)liveRoom {
    if (self.isGuest) {
        if (liveRoom.strRoomId.length > 0) {
            [[KSNavigationManager sharedManager] watchLiveShow:liveRoom.strRoomId roomInfo:nil forcePop:YES isFromContribution:NO infoDict:@{PushNotification_liveShowFromPageNew:@"live#0#homepage_guest#live#live_information_item"}];
        }
    } else {
        [[KSNavigationManager sharedManager] showLiveShowEnterVC];
    }

    KS_WEAK_SELF(self);
    [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
        CHECK_SELF_AND_RETURN();
        reportModel.key = @"homepage_guest#live#live_information_item#click#0";
        reportModel.commonInt5 = self.userProfileInfo.userInfo.isStar ? 2 : 1;
        reportModel.commonInt7 = self.userProfileInfo.userInfo.userId;
        reportModel.touid = self.userProfileInfo.userInfo.userId;
    }];
}

- (void)didTapSimpleKTV:(JceProfile_RoomBasicInfo *)simpleKTVInfo int4:(int64_t)int4 {
    if (!self.isGuest && simpleKTVInfo.jce_iType == 4) {
        // 进入歌房创建页
        [[KSNavigationManager sharedManager] showKtvEnterVC:363001004 multiFromPage:nil];

        [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
            reportModel.key = @"homepage_me#my_KTV#create#click#0";
            reportModel.commonStr6 = [[KSUIABTestManager sharedManager] getAllTestIdsWithModuleId:ksUIABTest_Module_Profile_KTV_ENTRY contentTestId:nil];
        }];
        return;
    }

    NSString *route = self.isGuest ?
    @"homepage_guest#online_KTV#online_KTV_information_item" :
    @"homepage_me#online_KTV#online_KTV_information_item";
    NSString *fromRoute = [NSString stringWithFormat:@"%@#%lld", route, self.userProfileInfo.userInfo.userId];
    NSString *jumpUrl = simpleKTVInfo.jce_strJumpUrl;
    if (!IS_EMPTY_STR_BM(jumpUrl) && !IS_EMPTY_STR_BM(fromRoute)) {
        jumpUrl = [KSComHelper replaceUrl:jumpUrl queryName:@"ktvFromRoute" withValue:fromRoute];
    }

    [[KSNavigationManager sharedManager] dealWithScheme:jumpUrl];

    NSString* reportKey = self.isGuest ?
    @"homepage_guest#online_KTV#online_KTV_information_item#click#0" :
    @"homepage_me#online_KTV#online_KTV_information_item#click#0";

    NSInteger commonInt7 = self.userProfileInfo.userInfo.userId;
    NSInteger commonInt2 = [self commonUserTypeInt];
    NSString *roomid = simpleKTVInfo.jce_strRoomId;

    [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
        reportModel.key = reportKey;
        reportModel.commonInt1 = 1;
        reportModel.commonInt3 = 2; // 1.歌房开播 2. 歌房 3、歌房创建入口 双端统一为2
        reportModel.commonInt7 = commonInt7;
        reportModel.commonInt2 = commonInt2; // int2= 上报用户类型 0普通人 1明星已入驻 3明星未入驻
        reportModel.touid = self.userProfileInfo.userInfo.userId; // 进房来源需要从链路取 touid 字段
        reportModel.commonInt4 = int4;
        reportModel.roomid = roomid;
    }];

    [[KSTraceReportManager sharedManager] viewReportWithType:JceDcreport_emReportType_REPORT_TYPE_PERSONAL
                                                   subaction:203023
                                                    reserves:0
                                                    commInt1:[self reportCommint1:self.userProfileInfo.userInfo]
                                                    commInt2:0
                                                       touid:self.userProfileInfo.userInfo.userId
                                                      prd_id:self.userProfileInfo.userInfo.ktvRoomID];
}

- (void)didTapFriendKTV:(JceProfile_RoomBasicInfo *)friendKTVInfo int4:(int64_t)int4 {

    NSString* reportKey = self.isGuest ?
    @"homepage_guest#online_KTV#online_KTV_information_item#click#0" :
    @"homepage_me#online_KTV#online_KTV_information_item#click#0";

    NSInteger commonInt7 = self.userProfileInfo.userInfo.userId;
    NSInteger commonInt2 = [self commonUserTypeInt];
    NSString *roomid = friendKTVInfo.jce_strRoomId;

    [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
        reportModel.key = reportKey;
        reportModel.commonInt1 = 2;
        reportModel.commonInt3 = 2; // 1.歌房开播 2. 歌房 3、歌房创建入口 双端统一为2
        reportModel.commonInt4 = int4;
        reportModel.commonInt7 = commonInt7;
        reportModel.commonInt2 = commonInt2; // int2= 上报用户类型 0普通人 1明星已入驻 3明星未入驻
        reportModel.touid = self.userProfileInfo.userInfo.userId; // 进房来源需要从链路取 touid 字段
        reportModel.roomid = roomid;
    }];

    [[KSTraceReportManager sharedManager] viewReportWithType:JceDcreport_emReportType_REPORT_TYPE_PERSONAL
                                                   subaction:203023
                                                    reserves:0
                                                    commInt1:[self reportCommint1:self.userProfileInfo.userInfo]
                                                    commInt2:0
                                                       touid:self.userProfileInfo.userInfo.userId
                                                      prd_id:self.userProfileInfo.userInfo.ktvRoomID];
}

- (void)didTapSocialKTV:(JceProfile_RoomBasicInfo *)socialKTVInfo int4:(int64_t)int4 {
    NSString *fromRoute = [NSString stringWithFormat:@"%@#%lld", self.isGuest ? @"homepage_guest#online_KTV#online_KTV_information_item" : @"homepage_me#online_KTV#online_KTV_information_item", self.userProfileInfo.userInfo.userId];

    // 欢聚歌房
    [[KSNavigationManager sharedManager] showSocialKtvWithRoomId:socialKTVInfo.jce_strRoomId
                                                  invitationCode:@""
                                                        forcePop:YES
                                                         ktvFrom:@"me#online_KTV#online_KTV_information_item"
                                                            info:@{PushNotification_KtvFromRoute : fromRoute}];

    NSString* reportKey = self.isGuest ?
    @"homepage_guest#online_KTV#online_KTV_information_item#click#0" :
    @"homepage_me#online_KTV#online_KTV_information_item#click#0";

    NSInteger commonInt7 = self.userProfileInfo.userInfo.userId;
    NSInteger commonInt2 = [self commonUserTypeInt];
    NSInteger commonInt4 = int4;
    NSString *roomid = socialKTVInfo.jce_strRoomId;

    if (self.userProfileInfo.userInfo.liveRoom) {
        commonInt4 += 1;
    }

    [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
        reportModel.key = reportKey;
        reportModel.commonInt1 = 3;
        reportModel.commonInt3 = 2; // 1.歌房开播 2. 歌房 3、歌房创建入口 双端统一为2
        reportModel.commonInt4 = commonInt4;
        reportModel.commonInt7 = commonInt7;
        reportModel.commonInt2 = commonInt2; // int2= 上报用户类型 0普通人 1明星已入驻 3明星未入驻
        reportModel.touid = self.userProfileInfo.userInfo.userId; // 进房来源需要从链路取 touid 字段
        reportModel.roomid = roomid;
    }];

    [[KSTraceReportManager sharedManager] viewReportWithType:JceDcreport_emReportType_REPORT_TYPE_PERSONAL
                                                   subaction:203023
                                                    reserves:0
                                                    commInt1:[self reportCommint1:self.userProfileInfo.userInfo]
                                                    commInt2:0
                                                       touid:self.userProfileInfo.userInfo.userId
                                                      prd_id:self.userProfileInfo.userInfo.ktvRoomID];
}

- (void)didTapSingOrder:(JceProfile_SingOrderEntrance *)singOrderEntrance {
    // int1新增21客人态个人主页
    KSUidType singerUid = self.userProfileInfo.userInfo.userId;
    NSUInteger timestamp = self.userProfileInfo.userInfo.avatarTimestamp;
    [[KSNavigationManager alloc] showKtvSingOrderAlertViewWithSingerUid:singerUid
                                                              timestamp:timestamp
                                                              andTaskId:@""
                                                                   from:21
                                                        reportExtraInfo:nil
                                                             completion:nil];
}

- (void)didTapGiftActivity:(KSUserAuthGiftActivityInfo *)giftActivityInfo; {
    // 个人主页入口跳转到春节礼物活动
    [[KSNavigationManager sharedManager] dealWithScheme:giftActivityInfo.jumpUrl];

    NSString *key = self.isGuest ?
    @"homepage_guest#personal_information#gift_collection_entrance#click#0" :
    @"homepage_me#personal_information#gift_collection_entrance#click#0";
    [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
        reportModel.key = key;
        reportModel.commonStr1 = giftActivityInfo.status;
    }];
}

#pragma mark KSFeedReportDelegate
- (showReportSceneType)getRewardSendGiftSource
{
    return RewardsPersongPageFeedQuickGift;
}

- (NSUInteger)getVipPosid
{
    return self.isGuest ? VipPid_ProfileNewFeedsGuestIcon : VipPid_ProfileNewFeedsIcon;
}

- (NSString *)getVipCenterFromPageForReport {
    return self.isGuest ? @"homepage_guest#feed_tab#vipicon" : @"homepage_me#feed_tab#vipicon";
}

- (NSUInteger)getDissimilateGiftRankKBPosid
{
    return BuyKBPid_Profile_Feeds_Tab_GiftRank_Guide;
}

- (void)onKTVFeedExposeWithSimpleFeed:(KSimpleFeed *)simpleFeed cell:(KSLayoutableTableCell *)cell
{
    [self userProfle_onKTVFeedReportWith:NO simpleFeed:simpleFeed cell:cell];
}

- (void)onKTVFeedClickCoverWithAction:(KSDrawItemAction *)action simpleFeed:(KSimpleFeed *)simpleFeed cell:(KSLayoutableTableCell *)cell {
    [self userProfle_onKTVFeedReportWith:YES simpleFeed:simpleFeed cell:cell];
}

#pragma mark - Private

- (void)showPersonalGiftVC {
    KSPersonalGiftVC* personalGiftVC = [[KSPersonalGiftVC alloc] init];
    personalGiftVC.visitorUserInfo = [KSLoginManager sharedInstance].curUserInfo;
    KSUserInfo *masterUserInfo = [[KSUserInfo alloc] init];
    masterUserInfo.userId = self.userProfileInfo.userInfo.userId;
    masterUserInfo.avatarUrl = self.userProfileInfo.userInfo.avatarUrl;
    [personalGiftVC setDefaultRankIndex:self.personalGiftVCDefaultTabIndex];
    personalGiftVC.masterUserInfo = masterUserInfo;
    [[[KSNavigationManager sharedManager] getMainPageNavController] pushViewController:personalGiftVC animated:YES];
}

- (BOOL)checkIfThirdPartyAuthExpiredWithType:(KSThirdAuthBlockType)type from:(KSThirdAuthBlockFrom)from
{
    if([[KSLoginManager sharedInstance] isCurUserThirdPartyLoginExpired]){
        [[KSLoginManager sharedInstance] showBlockAuthActionSheetWithType:type from:from];
        return YES;
    }
    return NO;
}

#pragma mark - 抽屉引导
- (void)showMoreBtnStyle:(proto_main_page_webapp_DrawerIconID)iconID
{
    if (iconID == proto_main_page_webapp_DrawerIconID_DrawerIconKOL) {
        [self.moreButton setImage:[UIImage imageNamed:@"moreButton_starplan"] forState:UIControlStateNormal]; // 星计划
    }
    if (iconID == proto_main_page_webapp_DrawerIconID_DrawerIconCertificate) {
        [self.moreButton setImage:[UIImage imageNamed:@"moreButton_apply_auth"] forState:UIControlStateNormal]; // K歌认证
    }
    if (iconID == proto_main_page_webapp_DrawerIconID_DrawerIconSongGod) {
        [self.moreButton setImage:[UIImage imageNamed:@"moreButton_singer"] forState:UIControlStateNormal]; // 歌神中心
    }
    if (iconID == proto_main_page_webapp_DrawerIconID_DrawerIconMall) {
        [self.moreButton setImage:[UIImage imageNamed:@"moreButton_shop"] forState:UIControlStateNormal]; // 商城
    }
    if (iconID == proto_main_page_webapp_DrawerIconID_DrawerIconTeaching) {
        [self.moreButton setImage:[UIImage imageNamed:@"moreButton_college"] forState:UIControlStateNormal]; // 教唱学院
    }
    if (iconID == proto_main_page_webapp_DrawerIconID_DrawerIconCompetition) {
        [self.moreButton setImage:[UIImage imageNamed:@"moreButton_mega_game"] forState:UIControlStateNormal]; // 大赛
    }
}
// 复用星计划引导
- (void)showDrawerIconStyle:(NSString*)strDrawText
{
    NSInteger showTimes = LocalIntegerConfig(kLocalDrawerBuddleShowTimes);
    showTimes += 1;
    SaveLocalIntegerConfig(kLocalDrawerBuddleShowTimes, showTimes);

    if (self.drawStyleBubbleView) {
        return;
    }
    if (strDrawText) {
        self.drawStyleText = strDrawText;
    }
    CGSize bubbleSize = [self.drawStyleText kSongSizeWithFont:KSFontOfSize(14)];
    CGFloat bubbleSizeW = bubbleSize.width + 14.0 + 24.0 + 14.0;
    CGRect frame = CGRectMake(self.navBar.rightNavBarBtn.right - 10 - bubbleSizeW,
                              self.navBar.rightNavBarBtn.bottom + 6,
                              bubbleSizeW,
                              33);
    self.drawStyleBubbleView = [[KSUserProfileStarPlanBubbleView alloc] initWithFrame:frame];
    self.drawStyleBubbleView.titleLabel.text = self.drawStyleText;
    UITapGestureRecognizer *tapgest = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(moreDrawerBtnClickAction)];
    [self.drawStyleBubbleView addGestureRecognizer:tapgest];
    KS_WEAK_SELF(self);
    self.drawStyleBubbleView.closeAction = ^{
        CHECK_SELF_AND_RETURN();
        [self hideDrawStyleGuideIfNeed];
    };
    [self.view addSubview:self.drawStyleBubbleView];
    [self.drawStyleBubbleView ks_setAnchorPoint:CGPointMake(1, 0)];
    self.drawStyleBubbleView.transform = CGAffineTransformMakeScale(0.1, 0.1);
    [UIView animateWithDuration:0.5 animations:^{
        self.drawStyleBubbleView.transform = CGAffineTransformIdentity;
    }];
}
#ifdef INTERNALBUILD
- (void)onClickDebugUserID:(KSUidType)userID
{
    KSActionSheet *actionSheet = [[KSActionSheet alloc] init];

    KSActionSheetItem *copyItem = [[KSActionSheetItem alloc] init];
    copyItem.title = @"复制 uid";
    copyItem.tag = 1;

    KSActionSheetItem *logItem = [[KSActionSheetItem alloc] init];
    logItem.title = @"拉取日志";
    logItem.tag = 2;

    actionSheet.items = @[copyItem, logItem];

    KS_WEAK_SELF(self);
    [actionSheet setItemClickCallback:^(KSActionSheet * _Nonnull actionSheet, KSActionSheetItem *item) {
        CHECK_SELF_AND_RETURN();
        switch (item.tag) {
            case 1:
            {
                UIPasteboard *pboard = [UIPasteboard generalPasteboard];
                pboard.string = [NSString stringWithFormat:@"%lld", userID];
                [KSAlertManager tempAlert:[NSString stringWithFormat:@"uid  %@ 已经复制", pboard.string]];
            }
                break;
            case 2:
            {
                KSDebugPullLogInfoView *infoView = [[KSDebugPullLogInfoView alloc] initWithContentHeight:500 + SCREEN_SAFE_BOTTOM atWhere:KSDebugPullLogViewAtUserProfile];
                [infoView fillUID:userID];
                [infoView showInView:(self.isGuest ? self.view : self.tabBarController.view)];
            }
                break;

            default:
                break;
        }
        [actionSheet close];
    }];

    [actionSheet setCancelClickCallback:^(KSActionSheet * _Nonnull actionSheet) {
        [actionSheet close];
    }];
    actionSheet.cancelString = @"关闭";

    [actionSheet show];
}
#endif

#pragma mark - 侧边栏

- (void)hideDrawStyleGuideIfNeed
{
    if (self.drawStyleBubbleView.superview) {
        [UIView animateWithDuration:0.3 animations:^{
            self.drawStyleBubbleView.alpha = 0;

        } completion:^(BOOL finished) {
            [self.drawStyleBubbleView removeFromSuperview];
            self.drawStyleBubbleView = nil;
        }];
    }
}

#pragma mark - 侧边栏
- (void)showProfileSidePageWithIsGuest:(BOOL)isGuest
{
    if (!isGuest) {

        NSInteger showTimes = LocalIntegerConfig(kLocalDrawerIconStyleListShowTimes);

        // 是否超过频控次数
        BOOL isFrequentOver = (showTimes >= self.drawerIconStyleList.jce_iFreqLimit);
        if (isFrequentOver) { // 超过频控次数
            [self.moreButton setUnreadNumber:0];

        } else {
            showTimes += 1;
            SaveLocalIntegerConfig(kLocalDrawerIconStyleListShowTimes, showTimes);
        }
        [self.moreButton setImage:[self getNavMoreButtonImage] forState:UIControlStateNormal];
    }
    self.sidePageVC.isFirstGuideAnim = NO;
    [self.sidePageVC setSingerEntrance:self.singerEntrance];
    [self.sidePageVC setDrawerIconStyleList:self.drawerIconStyleList];
    [self.sidePageVC setUserProfile:self.userProfileInfo];    
    [self.sidePageVC showWithCompletion:nil];

    // 抽屉异化消失
    [self hideDrawStyleGuideIfNeed];
}

#pragma mark - KSUserProfileSidePage Actions
- (void)jumpToStartLiveShowForCRM:(BOOL)isCRM
{
    [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
        reportModel.key = @"homepage_me#launch_live#create#click#0";
        reportModel.commonInt1 = self.feedAdapter.liveRoomEntranceRsp.jce_uConfId;
        reportModel.commonInt2 = isCRM ? 1 : 2;
    }];
    NSString *jumpUrl = self.feedAdapter.liveRoomEntranceRsp.jce_stLaunchEntranceStyle.jce_strJumpUrl;
    if (!IS_EMPTY_STR_BM(jumpUrl)) {
        [[KSNavigationManager sharedManager] dealWithScheme:jumpUrl];
    } else {
        KLog(@"user side page click start live with nil scheme");
        [[KSNavigationManager sharedManager] showLiveShowEnterVC];
    }
}

- (void)showLiveShowDropViewAt:(UIView *)targetView pageVC:(KSUserProfileSidePageVC *)pageVC
{
    CGPoint point = [self.view convertPoint:CGPointMake(targetView.centerX, targetView.bottom) fromView:targetView.superview];

    NSMutableArray *items = [NSMutableArray array];
    {
        KSDropDownViewItem *item = [[KSDropDownViewItem alloc] init];
        item.iconOutlineName = @"Outline/36x36/f/ic_36f_live";
        item.title = @"立即开播";
        KS_WEAK_SELF(self);
        item.clickAction = ^(KSDropDownView * _Nonnull dropDownView, KSDropDownViewItem * _Nonnull item) {
            CHECK_SELF_AND_RETURN();
            KS_WEAK_SELF(self);
            [dropDownView dismiss];
            [pageVC dismissCompletion:^{
                CHECK_SELF_AND_RETURN();
                [self jumpToStartLiveShowForCRM:YES];
            }];
        };
        [items addObject:item];
    }
    {
        KSDropDownViewItem *item = [[KSDropDownViewItem alloc] init];
        item.iconOutlineName = @"Outline/dropdown_crm";
        item.title = @"直播数据";
        KS_WEAK_SELF(self);
        item.clickAction = ^(KSDropDownView * _Nonnull dropDownView, KSDropDownViewItem * _Nonnull item) {
            CHECK_SELF_AND_RETURN();
            KS_WEAK_SELF(self);
            [dropDownView dismiss];
            [pageVC dismissCompletion:^{
                CHECK_SELF_AND_RETURN();
                [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                    reportModel.key = @"homepage_me#crm#null#click#0";
                }];
                [[KSNavigationManager sharedManager] dealWithScheme:[KSCRMManager getCRMWebUrl]];
            }];
        };
        [items addObject:item];
    }
    KSDropDownView *ddv = [[KSDropDownView alloc] init];
    ddv.hostPoint = point;
    ddv.items = items;
    [ddv show];

    [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
        reportModel.key = @"homepage_me#lauch_live#crm_entrance#click#0";
    }];

    [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
        reportModel.key = @"homepage_me#crm#null#exposure#0";
    }];
}

#pragma mark - KSUserProfileSidePageDelegate
/// 侧边栏点击
- (void)ksUserProfileSidePageVC:(KSUserProfileSidePageVC *)pageVC
didUserProfileSidePageItemClick:(KSUserProfileSideItemInfo *)itemInfo
                     targetView:(UIView *)targetView
{
    switch (itemInfo.type) {
        /// 头部section
        case KSUserProfileSideItemType_Scan: {
            [self scanBtnClickAction:nil];
            break;
        }
        case KSUserProfileSideItemType_StartLive: {
            if ([KSCRMManager sharedInstance].canUseCRM) {
                [self showLiveShowDropViewAt:targetView pageVC:pageVC];
            }
            else {
                [self jumpToStartLiveShowForCRM:NO];
            }
            break;
        }
        case KSUserProfileSideItemType_Share: {
            [self shareBtnClickAction:nil];
            break;
        }
        case KSUserProfileSideItemType_StealthAccess: {
            KSActionSheetOperationType operateType = KSActionSheetOperation_Add;
            if ([self isInInvisiableUserList:self.userProfileInfo.userInfo.userId]) {
                operateType = KSActionSheetOperation_Remove;
            }
            [self onVisibleAccessClickWithType:operateType];
            break;
        }
        case KSUserProfileSideItemType_RemarkName: {
            [self onRemarknameClickAction];
            break;
        }

        /// 头部以下
        case KSUserProfileSideItemType_MyFavorite: {
            KSMyFavorVC *myFavorVC = [[KSMyFavorVC alloc] init];
            [self.navigationController pushViewController:myFavorVC animated:YES];
            [self traceClickReport:ReportSubactionL3_UserProfile_Click_MyFavorite];
            if (!self.isGuest) {
                [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                    reportModel.key = @"homepage_me#category_for_option#my_favorites#click#0";
                }];
            }
            break;
        }
        case KSUserProfileSideItemType_SongList: {
            KSViewSongListFolderVC *vc = [[KSViewSongListFolderVC alloc] initWithUid:self.userProfileInfo.userInfo.userId];
            [[KSNavigationManager sharedManager] pushVC:vc animated:YES];
            if (!self.isGuest) {
                [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                    reportModel.key = @"homepage_me#music#song_list_view_all#click#0";
                }];
            } else {
                [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                    reportModel.key = @"homepage_guest#music#song_list_view_all#click#0";
                    reportModel.commonInt7 = self.userID;
                }];
            }
            break;
        }
        case KSUserProfileSideItemType_MusicAlbum: {
            [[KSNavigationManager sharedManager] showPayAlbumListVCWith:self.userProfileInfo.userInfo.userId];
            if (!self.isGuest) {
                [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                    reportModel.key = @"homepage_me#category_for_option#album#click#0";
                }];
            }
            break;
        }
        case KSUserProfileSideItemType_PlayHistory: {
            KSPlayingHistoryVC *playinghistoryVC = [[KSPlayingHistoryVC alloc] initWithUserId:self.userID];
            [self.navigationController pushViewController:playinghistoryVC animated:YES];
            [self traceClickReport:ReportSubactionL3_UserProfile_Click_PlayHistory];
            if (!self.isGuest) {
                [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                    reportModel.key = @"homepage_me#category_for_option#history#click#0";
                }];
            }
            break;
        }
        case KSUserProfileSideItemType_Download: {
            [[KSNavigationManager sharedManager] showDownloadListVC:nil];
            [self traceClickReport:ReportSubactionL3_UserProfile_Click_MyDownload];
            if (!self.isGuest) {
                [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                    reportModel.key = @"homepage_me#more#download#click#0";
                }];
            }
            break;
        }
        case KSUserProfileSideItemType_VIPHeatCard: {
            NSString *vipHeatCardUrl = WnsStringConfig(kWnsConfig_VIPConfig, kWnsConfig_VipConfig_VIPHeatCardRightsPageUrl);
            [[KSNavigationManager sharedManager] dealWithScheme:vipHeatCardUrl from:KSWebOpenFrom_UserProfileVC];
            [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                reportModel.key = @"homepage_me#category_for_option#works_heating_right#click#0";
            }];
            break;
        }
        case KSUserProfileSideItemType_MyContribute: {
            NSString* contributeH5Page = [[WnsConfigManager sharedInstance].appConfig.contributeConfig getContributeH5PageUrl:nil];
            [[KSNavigationManager sharedManager] dealWithScheme:contributeH5Page from:KSWebOpenFrom_UserProfileVC];
            [self traceClickReport:ReportSubactionL3_UserProfile_Click_MyContribute];
            [self traceReportForContribute];
            if (!self.isGuest) {
                [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                    reportModel.key = @"homepage_me#category_for_option#submit#click#0";
                }];
            }
            break;
        }
        case KSUserProfileSideItemType_OfflineUGC: {
            [self traceReportOfflineUgcClick];
            [self clearOfflineUgcRedDot];
            NSString *url = WnsUrlStringConfig(kWnsConfig_Url_offlineUGCPageUrl);
            if (url) {
                [[KSNavigationManager sharedManager] dealWithScheme:url];
            }
            if (!self.isGuest) {
                [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                    reportModel.key = @"homepage_me#category_for_option#offline_creation#click#0";
                }];
            }
            break;
        }
        case KSUserProfileSideItemType_RecentDel: {
            if (!self.isGuest) {
                KSUgcRecycleBinVC *recycleBinVC = [[KSUgcRecycleBinVC alloc] init];
                [[KSNavigationManager sharedManager] pushVC:recycleBinVC animated:YES];
                [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                    reportModel.key = @"homepage_me#category_for_option#delete_recently#click#0";
                }];
            }
            break;
        }
        case KSUserProfileSideItemType_SingerEntrance: {
            if (self.isShowSingerGuideRedPoint) {
                NSString *curDateStr = [KSDateFormatterHelper formatDate:[NSDate date] withType:KSDateFormatFullWithDot];
                SaveCommonLocalConfig(kSingerGuideDay, SAFE_STR_BM(curDateStr));
                self.hideSingerGuide = YES;
                self.isShowSingerGuideRedPoint = NO;
            }
            if (self.singerGuideTip) {
                [self removeSingerGuide];
            }
            [[KSNavigationManager sharedManager] dealWithScheme:self.singerEntrance.jce_strJumpUrl];
            break;
        }
        case KSUserProfileSideItemType_SingShop: {
            if ([self checkIfThirdPartyAuthExpiredWithType:KSThirdAuthBlockType_Default from:KSThirdAuthBlockFrom_Mall] == NO) {
                NSString *schema = WnsUrlStringConfig(@"SingShop");
                [[KSNavigationManager sharedManager] dealWithScheme:schema];
                if (!self.isGuest) {

                    proto_main_page_webapp_DrawerIconStyle *drawStyle = [self getDrawIconStyleForEmIconId:proto_main_page_webapp_DrawerIconID_DrawerIconMall];

                    NSInteger commontInt2 = 0;
                    if (drawStyle.jce_strIconURL.length > 0) {
                        commontInt2 = 2;
                    } else {
                        commontInt2 = (drawStyle.jce_iRedDot == 0 ? 0 : 1);
                    }

                    [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                        reportModel.key = @"homepage_me#category_for_option#store#click#0";
                        reportModel.commonInt2 = commontInt2;
                    }];

                    [self cancelDrawIconStyle:drawStyle];
                }
            }
            break;
        }
        case KSUserProfileSideItemType_MyOrders: {
            NSString *myOdersUrl = [WnsConfigManager sharedInstance].appConfig.urlConfig.myBuyList;
            [[KSNavigationManager sharedManager] showWebView:myOdersUrl];
            if (!self.isGuest) {
                [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                    reportModel.key = @"homepage_me#category_for_option#purchase#click#0";
                }];
            }
            break;
        }
        case KSUserProfileSideItemType_LearnCollege: {
            [KSTeachSingCourseManager openAnchorCourseManagePage:kTeachCollegeTab_Course];
            if (!self.isGuest) {

                proto_main_page_webapp_DrawerIconStyle *drawStyle = [self getDrawIconStyleForEmIconId:proto_main_page_webapp_DrawerIconID_DrawerIconTeaching];

                NSInteger commontInt2 = 0;
                if (drawStyle.jce_strIconURL.length > 0) {
                    commontInt2 = 2;
                } else {
                    commontInt2 = (drawStyle.jce_iRedDot == 0 ? 0 : 1);
                }


                [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                    reportModel.key = @"homepage_me#category_for_option#study_circle#click#0";
                    // 取值 是否做异化
                    reportModel.commonInt2 = commontInt2;
                }];

                [self cancelDrawIconStyle:drawStyle];
            }
            break;
        }
        case KSUserProfileSideItemType_MyMegaGame: {
            NSString * jumpUrl = [WnsConfigManager sharedInstance].appConfig.urlConfig.myMegagameUrl;
            if (jumpUrl.length > 0) {
                [[KSNavigationManager sharedManager] showWebView:jumpUrl];
            }
            [self traceClickReport:ReportSubactionL3_UserProfile_Click_MyMegagame];
            if (!self.isGuest) {

                proto_main_page_webapp_DrawerIconStyle *drawStyle = [self getDrawIconStyleForEmIconId:proto_main_page_webapp_DrawerIconID_DrawerIconCompetition];

                NSInteger commontInt2 = 0;
                if (drawStyle.jce_strIconURL.length > 0) {
                    commontInt2 = 2;
                } else {
                    commontInt2 = (drawStyle.jce_iRedDot == 0 ? 0 : 1);
                }


                [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                    reportModel.key = @"homepage_me#category_for_option#contest#click#0";
                    // 取值 是否做异化
                    reportModel.commonInt2 = commontInt2;
                }];

                [self cancelDrawIconStyle:drawStyle];
            }
            break;
        }
        case KSUserProfileSideItemType_KSongAuth: {
            [[KSNavigationManager sharedManager] dealWithScheme:@"https://kg.qq.com?hippy=userAuth&authFrom=2"];

            proto_main_page_webapp_DrawerIconStyle *drawStyle = [self getDrawIconStyleForEmIconId:proto_main_page_webapp_DrawerIconID_DrawerIconCertificate];

            NSInteger commontInt2 = 0;
            if (drawStyle.jce_strIconURL.length > 0) {
                commontInt2 = 2;
            } else {
                commontInt2 = (drawStyle.jce_iRedDot == 0 ? 0 : 1);
            }

            [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                reportModel.key = @"homepage_me#category_for_option#usercertificate#click#0";
                reportModel.touid = self.userProfileInfo.userInfo.userId;
                reportModel.commonStr1 = [NSString stringWithFormat:@"%lld",
                                          [KSAuthBitmapHelper newAuthBitmapFromMapAuth:self.userProfileInfo.userInfo.sMapAuth]];
                // 取值 是否做异化
                reportModel.commonInt2 = commontInt2;
            }];
            [self cancelDrawIconStyle:drawStyle];

            break;
        }
        case KSUserProfileSideItemType_PersonCard: {
            [self showPersonalCardVC];
            break;
        }
        case KSUserProfileSideItemType_StarPlan: {
            NSString *url = self.myCommercializebarModel.KolEntrance.jumpUrl;
            if (!IS_EMPTY_STR_BM(url)) {
                [[KSNavigationManager sharedManager] dealWithScheme:url];
            }

            proto_main_page_webapp_DrawerIconStyle *drawStyle = [self getDrawIconStyleForEmIconId:proto_main_page_webapp_DrawerIconID_DrawerIconKOL];

            NSInteger commontInt2 = 0;
            if (drawStyle.jce_strIconURL.length > 0) {
                commontInt2 = 2;
            } else {
                commontInt2 = (drawStyle.jce_iRedDot == 0 ? 0 : 1);
            }
            [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                reportModel.key = @"homepage_me#category_for_option#star_programs#click#0";
                reportModel.commonInt1 = 1;
                reportModel.commonInt2 = commontInt2;
            }];
            [self cancelDrawIconStyle:drawStyle];
            break;
        }
        case KSUserProfileSideItemType_CloseFriend: {
            [self showCloseFriendActionSheet:NO];
            [self traceReportCloseFriendSettingClick];
            break;
        }

        /// 客态独有
        case KSUserProfileSideItemType_StarAuth: {
            NSString *url = [NSString stringWithFormat:@"https://kg.qq.com/starCertification?hippy=starCertification&singerMid=%@&singerName=%@",
                             self.userProfileInfo.userInfo.strSingerMid,
                             [self.userProfileInfo.userInfo.nickName URLEncodedString]];
            [[KSNavigationManager sharedManager] showWebView:url];
            break;
        }
        case KSUserProfileSideItemType_RemoveFans: {
            [self alert:KString(@"确定删除该粉丝吗？")
                  title:nil
                   btns:@[KString(@"确定"),KString(@"取消")]
                    tag:KSUserProfileAlertType_removeFans];
            break;
        }
        case KSUserProfileSideItemType_Report: {
            KSLoginManager *loginManger = [KSLoginManager sharedInstance];
            NSURL *url = [[KSReportManager sharedManager] generatePersonReportURL:self.userID
                                                                   withImpeachuin:loginManger.curUserInfo.userId];
            KSReportVC *report = [[KSReportVC alloc] initWithURL:url title:KString(@"举报")];
            [self.navigationController pushViewController:report animated:YES];
            break;
        }
        case KSUserProfileSideItemType_AddBlackList: {
            KSActionSheetOperationType operateType = KSActionSheetOperation_Add;
            if (self.userProfileInfo.userInfo.isBlack) {
                operateType = KSActionSheetOperation_Remove;
            }

            if (operateType == KSActionSheetOperation_Add) {
                [self traceClickReport:ReportSubactionL3_UserProfile_Click_AddBlacklist];
                NSString *desc = WnsSwitchStringConfig(kWnsConfig_SwitchConfig_AddBlackPreDesc);
                NSString *nick = IS_EMPTY_STR_BM(self.userProfileInfo.userInfo.nickName) ? @"对方" : self.userProfileInfo.userInfo.nickName;
                desc = [desc stringByReplacingOccurrencesOfString:@"${NickName}" withString:nick];
                [self ksAlert:desc title:KString(@"确定加入黑名单") btns:@[KString(@"确定"),KString(@"取消")] tag:KSUserProfileAlertType_addBlack];

            } else if (operateType == KSActionSheetOperation_Remove) {
                [self traceClickReport:ReportSubactionL3_UserProfile_Click_DeleteBlacklist];
                [self ksAlert:KString(@"确定移除黑名单") title:nil btns:@[KString(@"确定"),KString(@"取消")] tag:KSUserProfileAlertType_delBlack];
            }

            break;
        }
        case KSUserProfileSideItemType_VoiceLab: {

            NSString *link = WnsUrlStringConfig(@"VoiceComposeEntryURL");
            [[KSNavigationManager sharedManager] showWebView:link];

            [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                reportModel.key = @"homepage_me#category_for_option#sound_lab#click#0";
            }];

            //fix 95519305
            [[KSUgcPlayManager sharedManager] pauseGlobalPlay];

            break;
        }
        case KSUserProfileSideItemType_HeatCardPage:{
            // 点击后不展示红点（加热作品tab）
            SaveLocalBoolConfig(kLocalHeatCardiRedDotShowTimes, YES);
            // 先走默认wns
            NSString *heatCardJumpUrl = WnsSwitchStringConfig(@"heatCardPageJumpUrl");
            [[KSNavigationManager sharedManager] dealWithScheme:heatCardJumpUrl];

            // 加热作品item click上报
            [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                reportModel.key = @"homepage_me#category_for_option#heat_card#click#0";
                reportModel.commonInt1 = itemInfo.needShowRedpoint ? 1 : 0;
            }];
        }
            break;
        case KSUserProfileSideItemType_KKShowDress:
        {
            // 我的KK秀装扮
            [self showKKShowDress:@"homepage_me.category_for_option.dress_my_KK"];

            [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                reportModel.key = @"homepage_me#category_for_option#dress_my_KK#click#0";
            }];
        }
            break;
        case KSUserProfileSideItemType_ShortPlayMine:
        {
            // 我的短剧
            NSString *hippyUrl = [kPlayletHistoryPageUrl stringByAppendingString:@"&from_page=homepage_me"];
            [[KSNavigationManager sharedManager] dealWithScheme:hippyUrl];
//            // 点击后不展示红点（加热作品tab）
//            SaveLocalBoolConfig(kLocalHeatCardiRedDotShowTimes, YES);
//            // 先走默认wns
//            NSString *heatCardJumpUrl = WnsSwitchStringConfig(@"heatCardPageJumpUrl");
//            [[KSNavigationManager sharedManager] dealWithScheme:heatCardJumpUrl];
//
            // 上报
            [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                reportModel.key = @"homepage_me#category_for_option#shortplay#click#0";
            }];
        }
            break;
        case KSUserProfileSideItemType_ShortPlayUpload:
        {
            // 短剧上传(审查功能，目的是证明短剧不是K歌官方上传的)
            [[KSNavigationManager sharedManager] dealWithScheme:@"https://kg.qq.com/playletH5/index.html?r=upload"];
        }
            break;
        case KSUserProfileSideItemType_AnchorCenter:
        {
            NSString *anchorCenterUrl = self.userProfileInfo.anchorEntrance.jce_strJumpUrl;
            if(!IS_EMPTY_STR_BM(anchorCenterUrl)){
                [[KSNavigationManager sharedManager] dealWithScheme:anchorCenterUrl];
            }
            [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                reportModel.key = @"homepage_me#category_for_option#anchor_center#click#0";
                reportModel.commonInt3 = [KSLiveShowHelper isAnchorAuthorizedPGCWithUs:[KSLoginManager sharedInstance].curUserInfo] ? 1 : 2;
            }];
            break;
        }
        case KSUserProfileSideItemType_LiveShopConfig:
        {
            NSString *profileLiveEcommerceUrl = self.userProfileInfo.ecommerceEntrance.jce_strJumpUrl;
            if(!IS_EMPTY_STR(profileLiveEcommerceUrl)){
                [[KSNavigationManager sharedManager] dealWithScheme:profileLiveEcommerceUrl];
            }
        }
            break;
        default:
            break;
    }
}

// 侧边栏抽屉隐藏
- (void)userProfileSidePageDidDismiss:(KSUserProfileSidePageVC *)pageVC
{

}

- (BOOL)ksUserProfileSidePageVC:(KSUserProfileSidePageVC *)pageVC
         shouldDismissWhenClick:(KSUserProfileSideItemInfo *)itemInfo
                     targetView:(UIView *)targetView
{
    return [KSCRMManager sharedInstance].canUseCRM && itemInfo.type == KSUserProfileSideItemType_StartLive ? NO : YES;
}

- (void)ksUserProfileSidePageVC:(KSUserProfileSidePageVC *)pageVC
didUserProfileSidePageItemExpose:(KSUserProfileSideItemInfo *)itemInfo
                     targetView:(UIView *)targetView
{
    switch (itemInfo.type) {
        case KSUserProfileSideItemType_StartLive: {
            [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                reportModel.key = @"homepage_me#launch_live#create#exposure#0";
                reportModel.commonInt1 = self.feedAdapter.liveRoomEntranceRsp.jce_uConfId;
                reportModel.commonInt2 = [KSCRMManager sharedInstance].canUseCRM ? 1 : 2;
            }];
        }
            break;

        case KSUserProfileSideItemType_ShortPlayMine: {
            [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                reportModel.key = @"homepage_me#category_for_option#shortplay#exposure#0";
                reportModel.commonInt1 = itemInfo.needShowRedpoint ? 1 : 0;
            }];
        }
            break;

        default:
            break;
    }
}

#pragma mark - WXChannel

- (void)bindToWXChannelFromNative:(BOOL)fromNative {
    // 同步微信视频号
    WXChannelBindTipViewType type = fromNative ? WXChannelBindTipViewType_Profile : WXChannelBindTipViewType_Poplayer;
    self.wxChannelBindVC = [[KSAccountWXChannelBindTipVC alloc] initWithType:type];

    KS_WEAK_SELF(self);
    self.wxChannelBindVC.didJumpToWXChannel = ^{
        CHECK_SELF_AND_RETURN()
        self.needRefreshWXChannelBindState = YES;
    };

    UIView *superview = [[KSNavigationManager sharedManager] getRootTabBarController].view;
    [superview addSubview:self.wxChannelBindVC.view];
}

- (void)JumpToAccountSettingVC
{
    if (self.VCCurrentState != KSVCStateForegroundAndTopVC) {
        return;
    }

    KSAccountSettingVC *asVC = [[KSAccountSettingVC alloc] init];
    asVC.reportWXChannelBindResult = YES;
    [self.navigationController pushViewController:asVC animated:YES];
}

// 个人主页点击跳转至hippy的密友页面
- (void)jumpToHippyKtvIntimateView {
    NSString *url = @"https://kg.qq.com?hippy=ktvIntimateLink";
    if (self.isGuest) {
        url = [url stringByAppendingFormat:@"&ownerId=%lld", self.userID];
        url = [url stringByAppendingFormat:@"&source=4"];
    } else {
        url = [url stringByAppendingFormat:@"&source=5"];
    }
    [KSNavigationManager.sharedManager dealWithScheme:url];
}

// 跳到勋章铭牌购买
- (void)jumpToMedalShop {
    self.needLoadUserInfo = YES;
}

- (void)showCloseFriendActionSheet:(BOOL)sendMsg {
    KS_WEAK_SELF(self);
    [KSPersonalWidgetManager requestPersonalGetWidgetSwitchWithUid:self.userID
                                                          complete:^(BOOL success, BOOL on) {
        CHECK_SELF_AND_RETURN();
        if (success) {
            KSActionSheet *actionSheet = KSActionSheet.new;

            actionSheet.title = @"主页挂件设置";
            actionSheet.titleSelectMode = YES;
            actionSheet.cancelString = @"关闭";

            KSActionSheetItem *item1 = KSActionSheetItem.new;
            item1.title = @"展示密友挂件";
            item1.summary = @"允许密友赠送挂件并展示在我的个人主页";
            item1.tag = 1;
            item1.switchBtnStatus = on ? KActionSheetSwitchBtnStatus_Open : KActionSheetSwitchBtnStatus_Close;

            actionSheet.items = @[item1];

            [actionSheet setSwitchChangeCallback:^(KSActionSheet * _Nonnull actionSheet, KSActionSheetItem * _Nonnull item) {
                CHECK_SELF_AND_RETURN();
                [actionSheet close];
                if (item.switchBtnStatus == KActionSheetSwitchBtnStatus_Close) {
                    if (self.widgetRsp && self.widgetRsp.jce_stWidget.jce_uWidgetID > 0) {
                        KSBasicDialog *dialog = KSBasicDialog.new;
                        dialog.caption = @"关闭密友挂角";
                        [dialog addContentText:@"当前有密友挂角，关闭会影响他人体验且会私信告知对方状态变更"];
                        [dialog addControlButton:@"取消" subtitle:nil buttonStyle:KSBasicDialogButtonStyle_Normal callback:^(KSDialog * _Nonnull dialog) {
                            [dialog dismiss];
                        }];
                        KS_WEAK_SELF(self);
                        [dialog addControlButton:@"确定关闭" subtitle:nil buttonStyle:KSBasicDialogButtonStyle_Emphasis callback:^(KSDialog * _Nonnull dialog) {
                            CHECK_SELF_AND_RETURN();
                            [dialog dismiss];
                            [self changeCloseFriendWidget:NO sendMsg:sendMsg];
                        }];
                        [dialog show];
                    } else {
                        [self changeCloseFriendWidget:NO sendMsg:sendMsg];
                    }
                } else {
                    [self changeCloseFriendWidget:YES sendMsg:sendMsg];
                }
            }];

            [actionSheet setCancelClickCallback:^(KSActionSheet * _Nonnull actionSheet) {
                [actionSheet close];
            }];

            actionSheet.textAlignmentLeft = YES;

            [actionSheet show];
        }
    }];


}

#pragma mark - KSUserProfileShopAdapterDelegate
- (void)starShopAdapter:(KSUserProfileShopAdapter *)adapter
      clickByActionType:(KSUserProfileShopTabActionType)actionType
                  param:(NSDictionary *)paramDic {
    if (actionType == KSUserProfileShopTabActionType_Detail) {
        /// 打开商品详情页
        NSNumber *useWXprogram = [paramDic safeObjectForKey:@"useWXprogram" ClassType:NSNumber.class];
        KSShopProductURLInfo *detailUrlInfo = [paramDic safeObjectForKey:@"detailUrlInfo" ClassType:KSShopProductURLInfo.class];
        if (useWXprogram.boolValue) {
            /// 使用微信小程序打开
            NSString *jumpUrl = [detailUrlInfo.jumpURL URLEncodedString];
            NSString *scheme = [NSString stringWithFormat:@"qmkege://kege.com?action=openWXMiniProgram&appid=%@&path=%@",detailUrlInfo.wxMiniprogramAppid,jumpUrl];
            [[KSNavigationManager sharedManager] dealWithScheme:scheme];
            [KSLiveShowECommerceService wxMiniProgramReport:detailUrlInfo.clickURL];
        } else {
            [[KSNavigationManager sharedManager] dealWithScheme:detailUrlInfo.jumpURL];
        }
    } else if (actionType == KSUserProfileShopTabActionType_Order) {
        NSString *url = WnsUrlStringConfig(@"liveShopOrderListUrl");
        if (!url) {
            KLog(@"816新电商：订单页url不存在！");
            return;
        }
        url = [url stringByAppendingFormat:@"?business=mlive&anchor=%@",self.userProfileInfo.userInfo.shareUid];
        [[KSNavigationManager sharedManager] dealWithScheme:url];
    }
}

#pragma mark - Family
- (void)jumpToFamily {
    if (IS_EMPTY_STR(self.userProfileInfo.userInfo.sAuthGroup)) {
        NSString *url = WnsStringConfig(kWnsConfig_Url, kWnsConfig_Family_Homepage_Url);
        //取不到留个保底
        if (IS_EMPTY_STR(url)) {
            url = @"http://kg.qq.com/family/index.html?sharefrom=10002&ts=1520847169#/hot";
        }
        [[KSNavigationManager sharedManager] dealWithScheme:url];
    } else {
        [[KSNavigationManager sharedManager] showWebView:[self getFamilySchemaUrl]];
    }
}

#pragma mark - EmptyViewDelegate
- (void)tapView {
    KLog(@"个人主页无网络点击重试");
    [self didTriggerRefresh];
}
@end

