//
//  KSAdFreeGiftManager.m
//  QQKSong
//
//  Created by t<PERSON> liang on 2025/2/19.
//  Copyright © 2025 Tencent. All rights reserved.
//

#import "KSAdFreeGiftManager.h"
#import "KSAdFreeGiftAdNative.h"
#import "KSAdFreeGiftAdHippy.h"
#import "KSPropsManager.h"
#import "JceKG_RoomLotteryGift.h"
#import "KSFlowerManager.h"
#import "KSGiftBridgeHelper.h"

@interface KSAdFreeGiftManager()

/// 场景
@property (nonatomic, readwrite) KSAdFreeGiftScene *scene;
/// 广告流程：hippy、native
@property (nonatomic, strong) id<KSAdFreeGiftAdProtocol> adHandle;

@end


@implementation KSAdFreeGiftManager

+ (instancetype)managerWithScene:(KSAdFreeGiftScene *)scene {
    KSAdFreeGiftManager *m = KSAdFreeGiftManager.new;
    m.scene = scene;
    if (scene.isHippyAd) {
        m.adHandle = KSAdFreeGiftAdHippy.new;
    } else {
        m.adHandle = KSAdFreeGiftAdNative.new;
    }
    [m setup];
    return m;
}

- (void)setup {
    self.adHandle.scene = self.scene;
    [self.adHandle setup];
    KS_WEAK_SELF(self);
    self.adHandle.needSendGift = ^(NSArray<new_task_Reward *> *rewards) {
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            CHECK_SELF_AND_RETURN();
            [self sendGifts:rewards];
        });
    };
}

- (void)sendGifts:(NSArray<new_task_Reward *> *)tmpRewards {
    NSMutableDictionary <NSNumber *, new_task_Reward* >*tempDict = [NSMutableDictionary dictionary];
    NSMutableArray <new_task_Reward *>*resultArray = [NSMutableArray array];

    for (new_task_Reward *obj in tmpRewards) {
        if (!tempDict[@(obj.jce_uId)]) {
            [resultArray safeInsertObject:obj atIndex:0];
            tempDict[@(obj.jce_uId)] = obj;
        } else {
            //多次鲜花的，合并一下
            new_task_Reward *r_reward = tempDict[@(obj.jce_uId)];
            r_reward.jce_uNum += obj.jce_uNum;
        }
    }
    // 最终结果
    NSArray<new_task_Reward *> *rewards= [resultArray copy];
    
    KLog(@"KSAdFreeGift send tmp:%@ final:%@",tmpRewards,rewards);
    
    //循环送出，就鲜花和最后一个礼物
    for (new_task_Reward *reward in rewards) {
        //鲜花
        if (reward.jce_uId == GIFT_FLOWER_ID) {
            KS_WEAK_SELF(self);
            [[KSFlowerManager sharedManager] loadFlowerFromRemoteWithFroceStore:YES completion:^(id ksObject, NSDictionary *customInfo, NSError *error) {
                KS_STRONG_SELF(self);
                NSInteger myFlowers = [[KSFlowerManager sharedManager] getMyFlowers];
                if ( myFlowers < reward.jce_uNum) {
                    KLog(@"KSAdFreeGift sendFlowers count less:%u %ld",reward.jce_uNum,myFlowers);
                    return;
                }
                KSIapGift *flowerGift = [KSGiftBridgeHelper createFlowerGift];
                !self.scene.sendBlock ?: self.scene.sendBlock(flowerGift, reward.jce_uNum);
            } reportModel:nil];
            continue;
        }
        
        //K币礼物
        KS_WEAK_SELF(self);
        [[KSPropsManager sharedManager] loadUserBackpackWithExtraInfo:nil
                                                            needCache:NO
                                                        completeBlock:^(NSError *error,
                                                                        NSArray<JceKG_RoomLotteryGift *> *jcePrizes,
                                                                        NSArray<JcePackage_PropsItemCore *> *jceProps,
                                                                        NSArray<JcePackage_ExternalPropsItemCore *> *jceExternalProps,
                                                                        NSArray<diamond_comm_DiamondGift *> *jceDiamonds, BOOL isCache) {
            CHECK_SELF_AND_RETURN();
            if (!error) {
                JceKG_RoomLotteryGift *gift = nil;
                for (JceKG_RoomLotteryGift *tmpGift in jcePrizes) {
                    if (tmpGift.jce_uGiftId == reward.jce_uId) {
                        //背包刷新后找到了
                        gift = tmpGift;
                        break;
                    }
                }
                if (!gift) {
                    [KSToast showToastAndClean:@"背包礼物获取失败"];
                    KLog(@"KSAdFreeGift not find backpack");
                    return;
                }
                KSIapGift *iapGift = [[KSIapGift alloc] initWithLotteryGift:gift];
                !self.scene.sendBlock ?: self.scene.sendBlock(iapGift, reward.jce_uNum);
            } else {
                [KSToast showToastAndClean:@"背包礼物获取失败"];
                KLog(@"KSAdFreeGift 背包礼物获取失败");
            }
        }];
        
    }
}

/// 跳去看广告
- (void)jumpToShowAd;
{
    [self.adHandle jumpToShowAd];
}

+ (void)addShakeAnimationToGiftIcon:(UIView *)view {
    CAKeyframeAnimation *shakeAnimation = [CAKeyframeAnimation animationWithKeyPath:@"transform.rotation.z"];

    shakeAnimation.values = @[@0, @(-M_PI/9), @(M_PI/9), @(-M_PI/9), @(M_PI/9), @0]; // M_PI/9 = 20
    shakeAnimation.keyTimes = @[@0, @0.2, @0.4, @0.6, @0.8, @1.0];
    shakeAnimation.duration = 1.0;
    shakeAnimation.repeatCount = 3;
    shakeAnimation.autoreverses = NO;
    shakeAnimation.timingFunction = [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionEaseInEaseOut];
    
    [view.layer addAnimation:shakeAnimation forKey:@"ad_free_gift_shake"];
}

@end
