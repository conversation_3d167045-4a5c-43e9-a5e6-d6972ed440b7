//
//  KSAdFreeGiftAdNative.m
//  QQKSong
//
//  Created by tian liang on 2025/2/19.
//  Copyright © 2025 Tencent. All rights reserved.
//

#import "KSAdFreeGiftAdNative.h"
#import "KSRewardAdManager.h"
#import "KSRewardAdManager+TMESDK.h"
#import "KSRewardAdManager+Property.h"
#import "JceKG_multi_ad_quick_gift_webapp_GetMainPageReq.h"
#import "JceKG_multi_ad_quick_gift_webapp_GetMainPageRsp.h"

#import "JceKG_multi_ad_quick_gift_webapp_AdTask.h"
#import "new_task_Reward.h"
#import "JceKG_multi_ad_quick_gift_webapp_CheckAdTokenReq.h"
#import "JceKG_multi_ad_quick_gift_webapp_CheckAdTokenRsp.h"

#import "JceKG_multi_ad_quick_gift_webapp_CompleteFinalAdTaskReq.h"
#import "JceKG_multi_ad_quick_gift_webapp_CompleteFinalAdTaskRsp.h"
#import "JceKG_multi_ad_quick_gift_webapp_FinalAdTask.h"

typedef NS_ENUM(NSUInteger, KSAdFreeGiftAdNativeRewardADType) {
    KSAdFreeGiftAdNativeRewardADType_None,     // 默认值
    KSAdFreeGiftAdNativeRewardADType_Normal,   // 普通激励
    KSAdFreeGiftAdNativeRewardADType_Step,     // 阶梯激励
};

@interface KSAdFreeGiftAdNative()<KSRewardAdManagerDelegate>
// 激励广告管理
@property (nonatomic, strong) KSRewardAdManager *rewardAdManager;

/// 广告实例
@property (nonatomic, weak) TMEAdRewardedVideoAd *rewardAd;

/// 奖励信息
@property (nonatomic, strong) JceKG_multi_ad_quick_gift_webapp_GetMainPageRsp *giftTaskRsp;

/// 看广告成功后sdk返回类型
@property (nonatomic, assign) KSAdFreeGiftAdNativeRewardADType rewardADType;

/// 获取次数
@property (nonatomic, assign) NSInteger rewardSumTime;

/// 广告是否已经关闭
@property (nonatomic, assign) BOOL isAdClose;
@end

static NSString *kKSAdFreeGiftAdNativePosId = @"40201402";

@implementation KSAdFreeGiftAdNative
@synthesize scene = _scene;
@synthesize needSendGift = _needSendGift;
- (void)setup
{
    self.rewardAdManager = [KSRewardAdManager new];
    self.rewardAdManager.delegate = self;
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(rewardAdDidClosed:)
                                                 name:kNotiRewardAd_DidCloseAd_UserInfo
                                               object:nil];
}

/// 1、跳去看广告
- (void)jumpToShowAd;
{
    [self preloadRewardADsInfoExtParams:nil handler:nil];
}

/// 2、预加载，后续处理在广告的回调didKSRewardedVideoAdDidLoad
- (void)preloadRewardADsInfoExtParams:(NSDictionary *)params
                               handler:(KSAdFreeGiftAdHandle)handler
{
    NSMutableDictionary *extParams = @{ @"ad_count": @(self.scene.ad_num)}.mutableCopy;//固定4个
    if (params.count > 0) {
        [extParams addEntriesFromDictionary:params];
    }
    
    [self.rewardAdManager TME_preloadRewardAdPosiId:kKSAdFreeGiftAdNativePosId
                                        immediately:YES
                                           callback:^(id result) {
        NSDictionary *resDict = SAFE_CAST(result, NSDictionary);
        NSNumber *preloadNum = SAFE_CAST([resDict objectForKey:@"preLoadRewardSuccess"], NSNumber);
        KLog(@"KSAdFreeGift preload status: %@", preloadNum);
        BOOL preloadOk = preloadNum.boolValue;
        NSError *error = nil;
        if (!preloadOk) {
            error = [NSError errorWithDomain:NSStringFromClass(self.class)
                                        code:-1
                                    userInfo:@{ @"errMsg": @"TME加载广告失败" } ];
            [KSToast showToastAndClean:@"TME加载广告失败"];
        }
        !handler ?: handler(preloadOk, error);
    } needSaveRewardWithPodid:YES
                    extParams:extParams];
}


/// 4、查询后台奖励信息
- (void)loadGiftTaskCount
{
    //广告返回次数
    NSInteger adCount = self.rewardAd.subAdCount;
    KLog(@"KSAdFreeGift loadGiftTaskCount:%ld",adCount);
    JceKG_multi_ad_quick_gift_webapp_GetMainPageReq *req = JceKG_multi_ad_quick_gift_webapp_GetMainPageReq.new;
    req.jce_uAdNum = (TarsUInt32)adCount;
    KSProtocolBase *protocol = [KSProtocolBase new];
    [protocol startWorkWithCommand:@"kg.multi_ad_quick_gift.webapp.get_main_page"
                         reqObject:req
                          rspClass:@"JceKG_multi_ad_quick_gift_webapp_GetMainPageRsp"
                          rspBlock:^(WnsProtocolBase *workProtocolObj, NSInteger workResult) {
        if (PROTOCOL_SUCCESS(workResult)) {
            JceKG_multi_ad_quick_gift_webapp_GetMainPageRsp *rsp = SAFE_CAST(workProtocolObj.rspObject, JceKG_multi_ad_quick_gift_webapp_GetMainPageRsp);
            self.giftTaskRsp = rsp;
            KLog(@"KSAdFreeGift loadGiftTaskCount rsp: %@", rsp);
            if (self.giftTaskRsp.jce_vecAdTask.count ==0) {
                KLog(@"KSAdFreeGift loadGiftTaskCount rsp: nil");
                [KSToast showToastAndClean:@"奖励信息获取失败"];
                return;
            }
            [self showAd];
        } else {
            NSError *error = [KSErrorHandler errorObjWithProtocolResult:workProtocolObj];
            [KSToast showToastAndClean:SAFE_STR_BM(error.userInfo[@"errorMgr"])];
            KLog(@"KSAdFreeGift loadGiftTaskCount fail: %@", workProtocolObj);
        }
    }];
}

/// 5、展示广告
- (void)showAd
{
    [self updateAdInfo];
    self.isAdClose = NO;
    [self.rewardAdManager TME_showRewardAdByRewardId:kKSAdFreeGiftAdNativePosId
                                                  vc:self.scene.vc
                                            callback:nil
                             needSaveRewardWithPodid:YES];
}

- (void)updateAdInfo
{
    ///后台说不能将任务和最终任务合并，但是最后一个广告必须是最终奖励，所以这里需要客户端把vecAdTask里面最后一个vecReward替换成FinalAdTask里面的vecReward
    ///我也不知道为啥这么设计接口，后台说架构就这么设计的🙂
    ///最后一个任务信息替换为最终的
    JceKG_multi_ad_quick_gift_webapp_AdTask *adTask = self.giftTaskRsp.jce_vecAdTask.lastObject;
    adTask.jce_vecReward = self.giftTaskRsp.jce_stFinalTask.jce_vecReward;
    
    

    self.rewardAd.rewardedVideoModel.customFlowInfo = ({
        
        NSMutableArray *config = [NSMutableArray array];
        
        NSInteger adCount = min(self.rewardAd.subAdCount, self.giftTaskRsp.jce_vecAdTask.count);
        
        
        for (NSInteger i = 0; i < adCount; i++) {
            
            JceKG_multi_ad_quick_gift_webapp_AdTask *adTask = [self.giftTaskRsp.jce_vecAdTask safeObjectAtIndex:i];
            new_task_Reward *reward = SAFE_CAST(adTask.jce_vecReward.firstObject, new_task_Reward);
            
            NSString *rewardCount = [NSString stringWithFormat:@"%d", reward.jce_uNum];
            NSString *rewardIcon = SAFE_STR_BM(reward.jce_strIcon);
            
            NSString *rewardText = reward.jce_strName;
            NSString *lockedText = [NSString stringWithFormat:@"看__UNLOCK_TIME__秒可得%@+%@", rewardText,rewardCount];
            NSString *clickLockedText = [NSString stringWithFormat:@"__UNLOCK_TIME__秒后点击卡片%@+%@", rewardText,rewardCount];
            NSString *unlockedText = [NSString stringWithFormat:@"已经获得%@+%@", rewardText,rewardCount];
            NSString *clickGuideUnlockedText = [NSString stringWithFormat:@"点击卡片%@+%@", rewardText,rewardCount];
            NSString *clickUnlockedText = [NSString stringWithFormat:@"已经获得%@+%@", rewardText,rewardCount];

            NSDictionary *item = @{
                @"rewardCount": rewardCount,
                @"rewardUnit":rewardText,
                @"rewardIcon": rewardIcon,
                @"rewardTip": @{
                    @"lockedText": lockedText,
                    @"unlockedText": unlockedText,
                    @"clickLockedText": clickLockedText,
                    @"clickGuideUnlockedText": clickGuideUnlockedText,
                    @"clickUnlockedText": clickUnlockedText
                }
            };
            [config addObject:item];
        }
        
        @{
            @"rewardGradient": @{
                @"enabled": @YES,
                @"config": config
            }
        };
    });
}

- (void)getRewardToBackpack:(TMEAdRewardedVideoAd *)rewardedVideoAd level:(NSNumber *)level
{
    JceKG_multi_ad_quick_gift_webapp_AdTask *adTask = [self.giftTaskRsp.jce_vecAdTask safeObjectAtIndex:level.integerValue-1];
    if (!adTask) {
        KLog(@"KSAdFreeGift adTask nil:%@",level);
        return;
    }
    KS_WEAK_SELF(self);
    JceKG_multi_ad_quick_gift_webapp_CheckAdTokenReq *req = JceKG_multi_ad_quick_gift_webapp_CheckAdTokenReq.new;
    req.jce_uTaskId =adTask.jce_uTaskId;
    req.jce_strPageToken = self.giftTaskRsp.jce_strPageToken;
    req.jce_strAdToken = [rewardedVideoAd adToken];
    KLog(@"KSAdFreeGift getRewardToBackpack level:%@ req:%@",level,req);
    KSProtocolBase *protocol = [KSProtocolBase new];
    [protocol startWorkWithCommand:@"kg.multi_ad_quick_gift.webapp.check_token"
                         reqObject:req
                          rspClass:@"JceKG_multi_ad_quick_gift_webapp_CheckAdTokenRsp"
                          rspBlock:^(WnsProtocolBase *workProtocolObj, NSInteger workResult) {
        CHECK_SELF_AND_RETURN();
        if (PROTOCOL_SUCCESS(workResult)) {
            JceKG_multi_ad_quick_gift_webapp_CheckAdTokenRsp *rsp = SAFE_CAST(workProtocolObj.rspObject, JceKG_multi_ad_quick_gift_webapp_CheckAdTokenRsp);
            if (rsp.jce_iRet != 0) {
                KLog(@"KSAdFreeGift getRewardToBackpack fail: %@", rsp);
                [self.rewardAd closeRewardVCWithAnimated:YES completion:nil];
                return;
            }
            self.rewardSumTime ++;
            KLog(@"KSAdFreeGift getRewardToBackpack count:%ld",self.rewardSumTime);
        } else {
            NSError *error = [KSErrorHandler errorObjWithProtocolResult:workProtocolObj];
            KLog(@"KSAdFreeGift getRewardToBackpack fail: %@", error);
            [KSToast showToast:SAFE_STR_BM(error.userInfo[@"errorMgr"])];
            [self.rewardAd closeRewardVCWithAnimated:YES completion:nil];
        }
    }];
}

// 广告关闭回调
- (void)rewardAdDidClosed:(NSNotification *)notification
{
    if (self.isAdClose) {
        return;
    }
    self.isAdClose = YES;
    NSDictionary *info = SAFE_CAST(notification.object, NSDictionary);
    NSString *posID = SAFE_CAST([info safeObjectForKey:@"posID"], NSString);
    
    KLog(@"KSAdFreeGift - Finsih watching ADs, PositionID: %@", posID);
    if (![kKSAdFreeGiftAdNativePosId isEqualToString:posID]) {
        return;
    }
    if (self.rewardSumTime == 0) {
        KLog(@"KSAdFreeGift Finsih 没获取奖励");
        !self.scene.endBlock ?: self.scene.endBlock();
        return;
    }
    KS_WEAK_SELF(self);
    
    void (^sendBlock)() = ^(){
        CHECK_SELF_AND_RETURN();
        NSMutableArray <new_task_Reward *>*array = @[].mutableCopy;
        for (int i = 0; i <self.rewardSumTime ; i++) {
            JceKG_multi_ad_quick_gift_webapp_AdTask *adTask = [self.giftTaskRsp.jce_vecAdTask safeObjectAtIndex:i];
            new_task_Reward *reward = SAFE_CAST(adTask.jce_vecReward.firstObject, new_task_Reward);
            [array safeAddObject:reward];
        }
        !self.needSendGift ?: self.needSendGift(array.copy);
        return;
    };
    
    if (self.rewardSumTime < self.giftTaskRsp.jce_vecAdTask.count) {
        //没看到最后的广告，提前退出了，奖励信息已经发到背包了
        !sendBlock ?: sendBlock();
        return;
    }
    
   //只负责领取最后一个k币礼物
    JceKG_multi_ad_quick_gift_webapp_CompleteFinalAdTaskReq *req = JceKG_multi_ad_quick_gift_webapp_CompleteFinalAdTaskReq.new;
    req.jce_strPageToken = self.giftTaskRsp.jce_strPageToken;
    KSProtocolBase *protocol = [KSProtocolBase new];
    [protocol startWorkWithCommand:@"kg.multi_ad_quick_gift.webapp.complete_final_task"
                         reqObject:req
                          rspClass:@"JceKG_multi_ad_quick_gift_webapp_CompleteFinalAdTaskRsp"
                          rspBlock:^(WnsProtocolBase *workProtocolObj, NSInteger workResult) {
        CHECK_SELF_AND_RETURN();
        if (PROTOCOL_SUCCESS(workResult)) {
            JceKG_multi_ad_quick_gift_webapp_CompleteFinalAdTaskRsp *rsp = SAFE_CAST(workProtocolObj.rspObject, JceKG_multi_ad_quick_gift_webapp_CompleteFinalAdTaskRsp);
            new_task_Reward *reward = SAFE_CAST(rsp.jce_vecReward.firstObject, new_task_Reward);
            KLog(@"KSAdFreeGift CompleteFinalAdTask rsp  reward %@",reward);
            !sendBlock ?: sendBlock();
        } else {
            KLog(@"KSAdFreeGift CompleteFinalAdTaskReq fail: %@", workProtocolObj.errorMsg);
        }
    }];
    
}

- (void)dealloc
{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}


#pragma mark - KSRewardAdManagerDelegate
// 阶梯激励广告（与普通激励广告互斥的，不会同时调用）
- (void)didKSRewardedUpdate:(TMEAdRewardedVideoAd *)rewardedVideoAd rewardExt:(NSDictionary *)ext
{
    KLog(@"KSAdFreeGift ad type: %lu 激励", self.rewardADType);
    // 业务层互斥普通和激励广告领奖
    if (self.rewardADType == KSAdFreeGiftAdNativeRewardADType_None) {
        self.rewardADType = KSAdFreeGiftAdNativeRewardADType_Step;
    } else if (self.rewardADType != KSAdFreeGiftAdNativeRewardADType_Step) {
        KLog(@"KSAdFreeGift 已领取了其他，终止激励");
        return;
    }
    NSNumber *rewardLevel = [ext objectForKey:@"rewardLevel"];
    
    [self getRewardToBackpack:rewardedVideoAd level:rewardLevel];
}

// 普通激励广告（与阶梯激励广告互斥的，不会同时调用）
- (void)didKSRewardedVideoAdDidSucceed:(TMEAdRewardedVideoAd *)rewardedVideoAd verify:(BOOL)verify
{
    KLog(@"KSAdFreeGift ad type: %lu 普通", self.rewardADType);
    // 业务层互斥普通和激励广告领奖
    if (self.rewardADType == KSAdFreeGiftAdNativeRewardADType_None) {
        self.rewardADType = KSAdFreeGiftAdNativeRewardADType_Normal;
    } else if (self.rewardADType != KSAdFreeGiftAdNativeRewardADType_Normal) {
        KLog(@"KSAdFreeGift 已领取了其他，终止普通");
        return;
    }
    [self getRewardToBackpack:rewardedVideoAd level:@(1)];
}

// 3、广告加载成功回调
- (void)didKSRewardedVideoAdDidLoad:(TMEAdRewardedVideoAd *)rewardedVideoAd
{
    self.rewardAd = rewardedVideoAd;
    [self loadGiftTaskCount];
}

// 广告加载失败回调
- (void)didKSRewardedVideoAdFailed:(TMEAdRewardedVideoAd *)rewardedVideoAd
{
    [KSToast showToast:@"未获取到广告，请稍后再试"];
    KLog(@"KSAdFreeGift VideoAdFailed: %@", rewardedVideoAd);
}

@end
