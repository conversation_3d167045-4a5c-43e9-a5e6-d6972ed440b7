//
//  KSAdFreeGiftScene.m
//  QQKSong
//
//  Created by t<PERSON> liang on 2025/2/19.
//  Copyright © 2025 Tencent. All rights reserved.
//

#import "KSAdFreeGiftScene.h"
#import "KSABTestManager.h"
#import "KSMMKVStorage.h"

static NSString *kADFreeABModule = @"freemodeGift"; // AB模块名

@interface KSAdFreeGiftScene()

@property (nonatomic, assign) KSAdFreeGiftSceneType type;
@property (nonatomic, assign, readwrite) BOOL scenceShow; // 当前场景是否展示
// AB参数
@property (nonatomic, assign, readwrite) BOOL isHippyAd; // 是否是 hippy容器，广告有 2 种样式 AB，分场景
@property (nonatomic, assign) NSInteger giftTimes; // 领奖次数
@property (nonatomic, assign, readwrite) NSInteger ad_num; // AB里面的广告数量
@property (nonatomic, assign, readwrite) NSInteger giftBtnNum; // 0不出，1固化免费送单按钮，2免费送和正常送礼按钮共存
@property (nonatomic, assign, readwrite) NSInteger feedTopNum; // 代表feed的前x个作品出引导的数量
@property (nonatomic, assign) NSInteger feedAniNum; // 代表用户一天能在关注看到动画引导的总数量

@end


@implementation KSAdFreeGiftScene

+ (instancetype)sceneWithType:(KSAdFreeGiftSceneType)type {
    KSAdFreeGiftScene *s = KSAdFreeGiftScene.new;
    s.type = type;
    [s setupWithAB];
    return s;
}

- (void)setupWithAB {
    // 9.11 看广告免费送礼迭代实验
    
    // 全局配置
    NSString *ad_type = [KSABTestManager stringConfig:kADFreeABModule key:@"ad_type"];
    if (![ad_type isEmpty]) {
        self.isHippyAd = [ad_type isEqualToString:@"1"];
    }
    self.giftTimes = [[KSABTestManager stringConfig:kADFreeABModule key:@"times"] integerValue];
    self.ad_num = [[KSABTestManager stringConfig:kADFreeABModule key:@"ad_num"] integerValue];
    self.giftBtnNum = [[KSABTestManager stringConfig:kADFreeABModule key:@"buttonNum"] integerValue];
    
    self.giftBtnNum = 1;
    
    NSDictionary<NSNumber *,NSString *> *dic = @{
        @(KSAdFreeGiftSceneType_RecFeed): @"feed",
        @(KSAdFreeGiftSceneType_FollowFeed): @"follow",
        @(KSAdFreeGiftSceneType_TimelineDetail): @"creation",
        @(KSAdFreeGiftSceneType_Personal): @"homepage",
        @(KSAdFreeGiftSceneType_GiftMessage): @"giftMessage",
        @(KSAdFreeGiftSceneType_MailMessage): @"privateMessage",
    };
    
    // 判断场景是否展示
    NSString *sceneKey = [dic ks_stringValueForKey:@(self.type)];
    if (sceneKey) {
        NSString *scenceFlag = [KSABTestManager stringConfig:kADFreeABModule key:sceneKey];
        self.scenceShow = [scenceFlag isEqualToString:@"1"];
        // 具体场景的AB参数处理
        if ([sceneKey isEqualToString:@"follow"]) {
            self.feedTopNum = [[KSABTestManager stringConfig:kADFreeABModule key:@"feedDirect"] integerValue];
            self.feedAniNum = [[KSABTestManager stringConfig:kADFreeABModule key:@"Direct"] integerValue];
        }
    }
    
    KINFO(@"[AdGift] sceneKey:%@ isHippy:%d gifttimes:%ld ad_num:%ld giftBtnNum:%ld", sceneKey, self.isHippyAd, self.giftTimes, self.ad_num, self.giftBtnNum);
}

// 各个入口有曝光频控
+ (BOOL)canShowEntranceWithType:(KSAdFreeGiftSceneType)type {
    return YES;
    
    //这2个常驻
    if (type == KSAdFreeGiftSceneType_GiftMessage ||
        type == KSAdFreeGiftSceneType_MailMessage) {
        return YES;
    }
    NSInteger totalWNS = WnsLocalServerIntegerConfig(@"AdFreeGiftTotalCount");
    NSInteger singleWNS = WnsLocalServerIntegerConfig(@"AdFreeGiftSingleCount");
    
    NSDate *saveDate = MMKV_ObjectConfig(@"AdFreeGiftSaveDate", NSDate.class, NO);
    NSMutableDictionary <NSNumber *, NSNumber *> *saveCountDic = MMKV_ObjectConfig(@"AdFreeGiftSaveCount", NSMutableDictionary.class, NO);
    
    KLog(@"AdFreeGiftScene saveDate:%@ saveCountDic:%@ totalWNS:%ld singleWNS:%ld", saveDate, saveCountDic, totalWNS, singleWNS);
    //没记录弹的时间
    if (!saveDate) {
        return YES;
    }
    
    NSTimeInterval oriDate = [saveDate timeIntervalSince1970];
    NSTimeInterval nowDate = [[NSDate date] timeIntervalSince1970];
    CGFloat diffDay = (nowDate - oriDate)/86400;
    //弹的时间超过了1天
    if (diffDay >= 1) {
        MMKV_RemoveConfig(@"AdFreeGiftSaveDate", NO);
        MMKV_RemoveConfig(@"AdFreeGiftSaveCount", NO);
        return YES;
    }
    //没有查到弹的次数
    if (!saveCountDic){
        return YES;
    }
    
    __block NSInteger totalSaveCount = 0;
    [saveCountDic.allValues enumerateObjectsUsingBlock:^(NSNumber * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        totalSaveCount += obj.integerValue;
    }];
    //总的超限额了
    if (totalSaveCount >= totalWNS) {
        return NO;
    }
    NSInteger singleSaveCount = [saveCountDic[@(type)] integerValue];
    //单个超限额
    if (singleSaveCount >= singleWNS) {
        return NO;
    }
    return YES;
}

+ (void)recordEntranceWithType:(KSAdFreeGiftSceneType)type {
    return;
    
    //这2个常驻
    if (type == KSAdFreeGiftSceneType_GiftMessage ||
        type == KSAdFreeGiftSceneType_MailMessage) {
        return;
    }
    
    NSDate *saveDate = MMKV_ObjectConfig(@"AdFreeGiftSaveDate", NSDate.class, NO);
    if (!saveDate) {
        //没查到记录时间说明超过1天或者没记录
        MMKV_SaveObjectConfig(@"AdFreeGiftSaveDate", [NSDate date], NO);
        KLog(@"AdFreeGiftScene recordDate");
    }
    NSMutableDictionary <NSNumber *,NSNumber *>*saveCountDic = MMKV_ObjectConfig(@"AdFreeGiftSaveCount", NSMutableDictionary.class, NO);
    if (!saveCountDic) {
        saveCountDic = @{}.mutableCopy;
    }
    //记录加1
    saveCountDic[@(type)] = @([saveCountDic[@(type)] integerValue] +1);
    
    MMKV_SaveObjectConfig(@"AdFreeGiftSaveCount", saveCountDic, NO);
    KLog(@"AdFreeGiftScene record type:%ld dic:%@",type,saveCountDic);
}

- (NSInteger)getGiftBtnNum {
    return self.giftBtnNum;
}
@end
