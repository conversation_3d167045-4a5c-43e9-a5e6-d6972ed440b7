//
//  KSAdFreeGiftScene.h
//  QQKSong
//
//  Created by t<PERSON> liang on 2025/2/19.
//  Copyright © 2025 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "KSIapGift.h"

typedef NS_ENUM(NSInteger, KSAdFreeGiftSceneType) {
    KSAdFreeGiftSceneType_RecFeed, //推荐 Feed
    KSAdFreeGiftSceneType_FollowFeed, //关注 Feed
    KSAdFreeGiftSceneType_TimelineDetail, // 作品详情页
    KSAdFreeGiftSceneType_Personal, // 客态个人主页
    KSAdFreeGiftSceneType_GiftMessage, // 礼物消息页
    KSAdFreeGiftSceneType_MailMessage, // 私信
};


/// 各个场景
@interface KSAdFreeGiftScene : NSObject

/// 可以送礼了
@property (nonatomic, copy) void(^sendBlock)(KSIapGift *gift, NSInteger num);
/// 结束流程，业务可以走销毁逻辑
@property (nonatomic, copy) dispatch_block_t endBlock;
//展示广告的vc
@property (nonatomic, weak) UIViewController *vc;
// 当前场景是否展示
@property (nonatomic, assign, readonly) BOOL scenceShow;
/// 是否是 hippy容器，广告有 2 种样式 AB，分场景
@property (nonatomic, readonly) BOOL isHippyAd;
/// AB里面的广告数量
@property (nonatomic, assign, readonly) NSInteger ad_num;
/// 0不出，1固化免费送单按钮，2免费送和正常送礼按钮共存
@property (nonatomic, assign, readonly) NSInteger giftBtnNum;
// 代表feed的前x个作品出引导的数量
@property (nonatomic, assign, readonly) NSInteger feedTopNum;
/// 结束流程，业务可以走销毁逻辑
@property (nonatomic, copy) NSString *from_page;
/// 各个入口有曝光频控
+ (BOOL)canShowEntranceWithType:(KSAdFreeGiftSceneType)type;
//
/// 记录次数
+ (void)recordEntranceWithType:(KSAdFreeGiftSceneType)type;

+ (instancetype)sceneWithType:(KSAdFreeGiftSceneType)type;

- (NSInteger)getGiftBtnNum;
@end
