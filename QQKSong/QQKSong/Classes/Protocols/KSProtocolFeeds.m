
//
//  KSProtocolFeeds.m
//  QQKSong
//
//  Created by <PERSON><PERSON><PERSON> on 14-6-10.
//  Copyright (c) 2014年 Tencent. All rights reserved.
//

#import "KSWnsConfigDef.h"
#import "KSProtocolFeeds.h"
#import "proto_feed_webapp_cell_game_advert_feed.h"
#import "KSimpleFeed.h"
#import "KSUserInfo+Follow.h"
#import "KSCommonModel.h"
#import "KSLoginManager.h"
#import "JceTimeline_s_picurl.h"
#import "KSProtocolCommands.h"
#import "JceTimeline_enum_advertType.h"
#import "JceTimeline_enum_filter_mask.h"
#import "KSFeedAdManager+KSFeed.h"
#import <KSReportModule/KSBeaconManager.h>
#import "GDTTangramUtils.h"
#import "KSTimelineManager.h"
#import "JcePlayReport_eReportSource.h"
#import "KSBeaconDefines.h"
#import "KSFeedLayoutConfig.h"
#import "KSJceModelBridge.h"
#import "KSTimelineForward.h"
#import "KSUserInfo.h"
#import "KSong+Common.h"
#import "JceTimeline_GPS.h"
#import "JceTimeline_GetFeedsReq.h"
#import "JceTimeline_GetFeedsRsp.h"
#import "JceTimeline_SingleFeed.h"
#import "KSTimelineDetailManager.h"
#import "JceTimeline_FlowEnginePassback.h"
#import "KSTMEAdHelper.h"
#import "KSABTestManager.h"
#import "proto_feed_webapp_enum_filter_mask_ex.h"
#import "KSDateFormatterHelper.h"
#import "KSAdFreeGiftScene.h"

#ifdef INTERNALBUILD
#import "KSChangeIPVC.h"
#import "KSDebugBusinessConfiger.h"
#endif

@implementation KSProtocolFeeds


- (id) init
{
    if (self = [super init]) {
        self.req = [[JceTimeline_GetFeedsReq alloc] init];
    }
    
    return self;
}

- (Class) GetResponsClass
{
    return [JceTimeline_GetFeedsRsp class];
}

- (id) OnPrepareSendData
{
    KLog(@"[feed]req refresh=%ld filterMask=%ld page=%ld passBack=%@ uid=%lld force=%ld pic=%ld refreshType=%ld index=%ld mapPassBack=%@",(long)self.req.jce_uRefreshTime,(long)self.req.jce_uFilterMask,(long)self.req.jce_uPageNum,self.req.jce_stFeedPassBack,self.req.jce_lUid,(long)self.req.jce_cForce,(long)self.req.jce_iPicSize,(long)self.req.jce_cRefreshType,(long)self.req.jce_uFsIndex,self.req.jce_mapPassBack);
    
    [self setUpSessionId];
    
    return self.req;
}

//每次刷新的时候更新sessionId，刷新的数据+loadmore的数据都属于同一个session
- (void)setUpSessionId
{
    if (self.req.jce_cRefreshType == 0)
    {
        NSInteger refreshTime = self.req.jce_uRefreshTime;
        if (refreshTime<=0)
        {
            refreshTime = SYSTEM_UPTIME;
        }
        NSString* sessionId = [[KSTimelineManager sharedManager] buildSessionId:refreshTime];
        [KSTimelineManager sharedManager].sesssionId = sessionId;        
    }
}

#pragma mark 客户端会过滤已删除转发源的转发feed
- (BOOL)isHiddenFeed:(KSimpleFeed*)feed
{
    if (feed.forwardFeed.strForwardId.length>0 && feed.isRemoved)
    {
        //7.0之后已删除的转发feed只有个人页个人态才展示
        if ([self.command isEqualToString:KGCMD_GET_PERSONAL_FEED] && self.req.jce_lUid == [KSLoginManager sharedInstance].curUserInfo.userId)
        {
            return NO;
        }
        else
        {
            return YES;
        }
        
    }
    else
    {
        return NO;
    }
}

- (BOOL)OnParseData:(id)resp
{
    JceTimeline_GetFeedsRsp *response = (JceTimeline_GetFeedsRsp *)resp;
    self.resp = response;
    self.uRefreshTime=response.jce_uRefreshTime;
    self.mapPassBack=[NSDictionary dictionaryWithDictionary:response.jce_mapPassBack];
    self.cRefreshType=response.jce_cHasMore;
    
    self.gps=[KSLBS new];
    self.gps.fLat=response.jce_stGpsCurUser.jce_fLat;
    self.gps.fLon=response.jce_stGpsCurUser.jce_fLon;
    self.gps.eType=response.jce_stGpsCurUser.jce_eType;
    self.gps.iAlt=response.jce_stGpsCurUser.jce_iAlt;
    
    self.strAvAudianceRole = response.jce_strAvAudianceRole;
    self.iLiveStatus = response.jce_iLiveStatus;
    self.hasMore = response.jce_cHasMore;
    self.feedsFlag = response.jce_lFlags;
    if (self.req.jce_lFlags & JceTimeline_GetFeedsFlag_GetFeedsFlag_want_voiceinfo)
    {
        self.voiceStatusInfo = response.jce_voiceinfo;
    }
    if (self.req.jce_lFilterMaskEx & proto_feed_webapp_enum_filter_mask_ex_ENUM_FILTER_MASK_FOLLOW_LAST_ENTRY)
    {
        self.entryInfo = response.jce_entryinfo;
        RUN_ON_UI_THREAD_ASYNC(^{
            KSTimelineManager.sharedManager.entryInfo = response.jce_entryinfo;
        });
    }
    
    NSInteger tmeAdCount = 0; // 当前列表TME广告条数
    if (self.req.jce_cRefreshType == 0) 
    {
        /// 刷新列表时，重置广告状态
        [[KSFeedAdManager sharedManager] resetFeedAdTypeWithFilterMask:self.req.jce_uFilterMask];
    }
    
    if (response.jce_vecFeedsData.count > 0) {
        self.canRefreshData = YES;
        NSMutableArray *resultList = [NSMutableArray array];
        for (NSInteger i = 0; i< response.jce_vecFeedsData.count; i++) {
            JceTimeline_SingleFeed *jceContent = [response.jce_vecFeedsData safeObjectAtIndex:i];
            KSimpleFeed *simpleFeed = [[KSJceModelBridge sharedInstance] translateToKSimpleFeed:jceContent];
            //增加sessionId,推荐侧做区分
            simpleFeed.simpleFeedCommon.sessionId = [KSTimelineManager sharedManager].sesssionId;
            simpleFeed.feedFilterMask = self.req.jce_uFilterMask;
            simpleFeed.loadRespTime = [[NSDate date] timeIntervalSince1970];
            simpleFeed.lbs.strDistance = [[KSJceModelBridge sharedInstance] translateToLbsStrDistance:jceContent];
            simpleFeed.isRefresh = (self.req.jce_cRefreshType == 0);
            if ((simpleFeed.feedFilterMask & JceTimeline_enum_filter_mask_ENUM_FILTER_MASK_FEED_RECOMM_CARD)==JceTimeline_enum_filter_mask_ENUM_FILTER_MASK_FEED_RECOMM_CARD)
            {
                //推荐卡片流默认白色
                simpleFeed.songinfo.strMainTextColor = @"#ffffff";
            }
            if (i == response.jce_vecFeedsData.count-1)
            {
                //记录最后一条feed的passback 后台翻页用
                self.lastFeedpassback = jceContent.jce_stFeedPassBack;
                
                if (!self.lastFeedpassback)
                {
                    [self traceReportLastFeedPassbackEmpty:response simpleFeed:simpleFeed];
                }
            }
            
            //区分feed的来源
            if ([self.command isEqualToString:KGCMD_GET_PERSONAL_FEED])
            {
                simpleFeed.feedVCType =  KSimpleFeedSourceType_UserProfile;
                simpleFeed.layoutConfig = [[KSFeedLayoutConfig alloc] initWithUserProfileVCConfig];
                if (self.req.jce_lUid ==[KSLoginManager sharedInstance].curUserInfo.userId)
                {
                    //主人态不显示送花引导
                    simpleFeed.feedParkingGuideState = ksFeedParkingGuideStateNeverShowInAppLife;
                }
            } else {
                simpleFeed.feedVCType = KSimpleFeedSourceType_TimelineRoot;
                simpleFeed.layoutConfig = [[KSFeedLayoutConfig alloc] initWithTimelineVCConfig:self.req];
            }
            simpleFeed.stFeedpassback=jceContent.jce_stFeedPassBack;
            
            if (simpleFeed.feedAd && IS_EMPTY_STR_BM(simpleFeed.feedAd.jce_advertId))
            {
                //如果是广告feed,feedID为空，客户端做过滤
                continue;
            }
            
            if (simpleFeed) {
                NSInteger filer = [KSTimelineManager sharedManager].filtrMaskRecommend;
                NSInteger filer2 = [KSTimelineManager sharedManager].filtrMaskRecommendNearBy;
                // 新增来源上报-首页推荐
                if ((self.req.jce_uFilterMask & filer) == filer)
                {
                    simpleFeed.eReportSource = JcePlayReport_eReportSource_E_RECOMMEND_TAB;
                }
                //新增来源上报-来自同城页
                if ((self.req.jce_uFilterMask & filer2) == filer2)
                {
                    simpleFeed.eReportSource = JcePlayReport_eReportSource_E_FROM_SAME_CITY;
                }
                
                if ((self.req.jce_uFilterMask &JceTimeline_enum_filter_mask_ENUM_FILTER_MASK_FEED_RECOMM_WATERFALL)== JceTimeline_enum_filter_mask_ENUM_FILTER_MASK_FEED_RECOMM_WATERFALL)
                {
                    if (simpleFeed.songinfo.songMid.length>0 || simpleFeed.isMusicMoodFeed || simpleFeed.feedAd)
                    {
                        //双流瀑布流目前只支持ugc作品展示
                        [resultList addObject:simpleFeed];
                    }
                } else if ((self.req.jce_uFilterMask & JceTimeline_enum_filter_mask_ENUM_FILTER_MASK_FEED_RECOMM_CARD) ==  JceTimeline_enum_filter_mask_ENUM_FILTER_MASK_FEED_RECOMM_CARD)
                {
                    // 大卡片数据
                    BOOL shouldAddSimpleFeed = YES;
#ifdef INTERNALBUILD
                    BOOL onlyAudioFeed = WnsSwitchBoolConfig(kWnsConfig_SwitchConfig_EnableVideolizeOnlyAudioFeed);
                    BOOL onlyVideoFeed = WnsSwitchBoolConfig(kWnsConfig_SwitchConfig_EnableVideolizeOnlyVideoFeed);
                    if ((onlyAudioFeed && onlyVideoFeed) || (!onlyAudioFeed && !onlyVideoFeed)) {
                        shouldAddSimpleFeed = YES;
                    } else if ((onlyAudioFeed && !onlyVideoFeed)) {
                        if ([simpleFeed isRecCardAudioFeed] || [simpleFeed isRecCardStyleNearByAudioFeed]) {
                            shouldAddSimpleFeed = YES;
                        } else {
                            shouldAddSimpleFeed = NO;
                        }
                    } else if ((!onlyAudioFeed && onlyVideoFeed)) {
                        if ([simpleFeed isRecCardVideoFeed]) {
                            shouldAddSimpleFeed = YES;
                        } else {
                            shouldAddSimpleFeed = NO;
                        }
                    }
#endif
                    shouldAddSimpleFeed = shouldAddSimpleFeed && (simpleFeed.songinfo.songMid.length>0 ||        // ucg作品
                                                                  simpleFeed.feedAd ||                           // 广告卡片
                                                                  [simpleFeed isKindOfLiveShowFeed] ||           // 直播卡片
                                                                  [simpleFeed isKindOfKTVRoomFeed] ||            // 歌房卡片
                                                                  [simpleFeed isKtvRoomCard] ||  // 歌房大卡片
                                                                  [simpleFeed isKindOfProductCardFeed] ||           // 推荐商品卡片
                                                                  (simpleFeed.songGod && ![[WnsConfigManager sharedInstance] isPGSH]) || // 歌神大卡片, 提审屏蔽
                                                                  simpleFeed.vipMarketingModel ||  // VIP营销卡片
                                                                  [simpleFeed isKindOfAIImageFeed]); // AI图文卡片
                    if ([simpleFeed isKindOfGameAdvertTypeFeed]) {
                        shouldAddSimpleFeed = YES;
                    }
                    
                    if (shouldAddSimpleFeed) {
                        [resultList addObject:simpleFeed];
                    }
                    
                    if (simpleFeed.feedAd && simpleFeed.adReqType == 2) {
                        // 将后台返回的广告数据格式化并放入缓存池比较
                        [self saveAdObjToCacherWithSimpleFeed:simpleFeed];
                    }
                } else {
                    if (![self isHiddenFeed:simpleFeed])
                    {
                        [resultList addObject:simpleFeed];
                    }
                }
                
                // 8.4 Feed送礼异化
                if (([simpleFeed isKindOfUgcTypeFeed] || [simpleFeed isKindOfAIImageFeed]) &&
                    [simpleFeed isFollowTabFeed]) {
                    // 有送礼异化，置为初始态。否则为默认态不展示
                    if (simpleFeed.giftGuide.isFreeAd) {
                        KSAdFreeGiftScene *scene = [KSAdFreeGiftScene sceneWithType:KSAdFreeGiftSceneType_FollowFeed];
                        if (i < scene.feedTopNum) {
                            simpleFeed.quickGiftAniStatus = KSFeedQuickGiftAniStatusInit;
                        } else {
                            simpleFeed.quickGiftAniStatus = KSFeedQuickGiftAniStatusShowed;
                        }
                    }
                }
            }
            
            if ([SAFE_STR_BM(simpleFeed.feedAd.jce_advertId) isEqualToString:@"888888"]) {
                tmeAdCount ++;
            }
            // TODO:AdLoad 该处判断是走AMS还是TMEAdSDK
            if ([KSTMEAdHelper feedsNeedShowTMEAdWithFilterMask:self.req.jce_uFilterMask])
            {
                [self dealTMEAdWithSimpleFeed:simpleFeed atArray:resultList simpleFeedIndex:i];
            }
            else
            {
                KSUnifiedNativeAdType adType = [[KSFeedAdManager sharedManager] getAdTypeFromFilterMask:self.req.jce_uFilterMask];
                // 填充 AMS 广告数据
                [KSProtocolFeeds dealAdWithSimpleFeed:simpleFeed atArray:resultList adType:adType simpleFeedIndex:i forceDeleteWhenFillFailed:NO];
            }
        }
        [self checkFeedAdNeedExchangeIndex:resultList];
        // 7.25版本adload新增字段，返回当前列表数据的pageSize与pageNum
        NSData * enginePassback = [self.mapPassBack safeObjectForKey:@"flow_engine_passback"];
        if ([enginePassback isKindOfClass:NSData.class]) {
            JceTimeline_FlowEnginePassback * passBack = [JceTimeline_FlowEnginePassback fromData:enginePassback];
            KSTMEAdFeedPos feedPos = FeedPosZero;
            feedPos.filterMask = self.req.jce_uFilterMask;
            feedPos.pageSize = passBack.jcev2_p_2_o_page_size;
            feedPos.isRefresh = (self.req.jce_cRefreshType == 0);
            feedPos.tmeAdCount = tmeAdCount;
            [KSTMEAdHelper updateFeedDataCountWithFeedPos:feedPos];
        }
        self.stGpsCurUser=response.jce_stGpsCurUser;
        self.feedsList = resultList;
        KLog(@"[FEED] receiveFeedsList resultListCount :%zd",resultList.count);
    }
    else
    {
        NSMutableDictionary *dic = [NSMutableDictionary dictionary];
        [dic safeSetObject:[NSString stringWithFormat:@"%ld",(long)response.jce_uRefreshTime] forKey:@"FeedEmpty_RefreshTime"];
        [dic safeSetObject:[NSString stringWithFormat:@"%ld",(long)response.jce_cHasMore] forKey:@"FeedEmpty_HashMore"];
        [dic safeSetObject:[NSString stringWithFormat:@"%@",response.jce_mapPassBack] forKey:@"FeedEmpty_MapPassBack"];
        [dic safeSetObject:[NSString stringWithFormat:@"%ld",(long)response.jce_uUnreadSize] forKey:@"FeedEmpty_UnreadSize"];
        [dic safeSetObject:[NSString stringWithFormat:@"%ld",(long)response.jce_uFsIndex] forKey:@"FeedEmpty_Index"];
        [[KSBeaconManager sharedManager] reportBeaconImmediatelyOnAction: @"FeedEmpty_Report" isSucceed:YES elapse:0 size:0 params:dic];
        KLog(@"[FEED]回包为空%@\n",dic);
        [KSComHelper reportData:KGCMD_TIMELINER_FEED_COUNTO ret:-2 detail:[dic toJsonString]];
    }
    
    if (response.jce_strSearchWord) {
        self.strSearchWord = response.jce_strSearchWord;
    }
    
    KLog(@"[feed]Rsp mask=%ld,refreshtime=%ld,hasmore=%ld,passBack=%@,lastFeedPassBack=%@,unreadseize=%ld,uIndex=%ld,count=%ld,livestatus=%ld,audience=%@,feedList=%@\n",(long)self.req.jce_uFilterMask,(long)self.uRefreshTime,(long)self.hasMore,self.mapPassBack,self.lastFeedpassback,(long)response.jce_uUnreadSize,(long)response.jce_uFsIndex,(long)response.jce_vecFeedsData.count,(long)response.jce_iLiveStatus,response.jce_strAvAudianceRole,self.feedsList);
    
    return YES;
}

#pragma mark 上报最后一条feed的passback为空统计
- (void)traceReportLastFeedPassbackEmpty:(JceTimeline_GetFeedsRsp *)response simpleFeed:(KSimpleFeed*)simpleFeed
{
    NSMutableDictionary *dic = [NSMutableDictionary dictionary];
    [dic safeSetObject:[NSString stringWithFormat:@"%ld",(long)response.jce_uRefreshTime] forKey:@"LastFeedPassbackEmpty_RefreshTime"];
    [dic safeSetObject:[NSString stringWithFormat:@"%ld",(long)response.jce_cHasMore] forKey:@"LastFeedPassbackEmpty_HashMore"];
    [dic safeSetObject:[NSString stringWithFormat:@"%@",response.jce_mapPassBack] forKey:@"LastFeedPassbackEmpty_MapPassBack"];
    [dic safeSetObject:[NSString stringWithFormat:@"%@",simpleFeed] forKey:@"LastFeedPassbackEmpty_Feed"];
    [[KSBeaconManager sharedManager] reportBeaconImmediatelyOnAction: @"LastFeedPassbackEmpty_Report" isSucceed:YES elapse:0 size:0 params:dic];
    KLog(@"[FEED]lastfeedPassback为空%@ feed=%@\n",dic,simpleFeed);
    [KSComHelper reportData:KGCMD_TIMELINER_LASTFEED_PASSBACK_EMPTY ret:-2 detail:[dic toJsonString]];
}
- (void)saveAdObjToCacherWithSimpleFeed:(KSimpleFeed *)simpleFeed
{
    if (!simpleFeed.feedAd) {
        return;
    }
    KSUnifiedNativeAdType adType = [[KSFeedAdManager sharedManager] getAdTypeFromFilterMask:self.req.jce_uFilterMask];
    [[KSFeedAdManager sharedManager] saveFeedAdDataToCacher:simpleFeed adType:adType];
}
// TODO:AdLoad
- (void)dealTMEAdWithSimpleFeed:(KSimpleFeed *)simpleFeed atArray:(NSMutableArray *)resultList simpleFeedIndex:(NSInteger)index
{
    if (!simpleFeed.feedAd) {
        return;
    }
    KLog(@"[TMEAdFeed] Feed广告填充，广告下标adIndex = %ld",index);
    BOOL suc = [[KSFeedAdManager sharedManager] fillTMEAdDataToKSimpleFeed:simpleFeed];
    if (suc) {
        // 设置信息流广告下标（上报使用，真的吐了）
        KSTMEAdFeedPos feedPos = FeedPosZero;
        feedPos.filterMask = self.req.jce_uFilterMask;
        feedPos.resultCount = resultList.count;
        feedPos.index = index;
        feedPos.isRefresh = (self.req.jce_cRefreshType == 0);
        [KSTMEAdHelper setFeedAdPosWithTMEAdObject:simpleFeed.tmeFeedAd tmeadFeedPos:feedPos];
        
        [[KSFeedAdManager sharedManager] handleFeedAdGold];
    }
    else{
        simpleFeed.feedAd = nil;
        [resultList removeObject:simpleFeed];
    }
}
- (void)checkFeedAdNeedExchangeIndex:(NSMutableArray *)resultList
{
    KSUnifiedNativeAdType adType = [[KSFeedAdManager sharedManager] getAdTypeFromFilterMask:self.req.jce_uFilterMask];
    if (![self.class canExchangeAdIndexWithType:adType]) {
#ifdef INTERNALBUILD
        KLog(@"[TMEAdFeed] 广告填充位置下移，不可交换的列表，不检查-%ld",adType);
#endif
        return;
    }
    if (self.req.jce_cRefreshType != 0) {
#ifdef INTERNALBUILD
        KLog(@"[TMEAdFeed] 广告填充位置下移，非首刷，不检查");
#endif
        return;
    }
    if (![resultList isKindOfClass:NSArray.class] || resultList.count == 0) {
#ifdef INTERNALBUILD
        KLog(@"[TMEAdFeed] 广告填充位置下移，列表无数据，不检查");
#endif
        return;
    }
    __block NSInteger index = -1;
    __block KSimpleFeed * simpleFeed = nil;
    [resultList enumerateObjectsUsingBlock:^(KSimpleFeed * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if (SAFE_CAST(obj, KSimpleFeed)) {
            if (obj.isNeedRecheckAdIndex) {
                simpleFeed = obj;
                index = idx;
                *stop = YES;
            }
        }
    }];
    if (index == -1 || simpleFeed == nil) {
#ifdef INTERNALBUILD
        KLog(@"[TMEAdFeed] 广告填充位置下移，没有需要检查的广告");
#endif
        return;
    }
    NSInteger canFillMinIndex = [KSFeedAdManager sharedManager].checkAdFillGap; // 可以填充的最小下标（小于这个值，滑动时就不会再尝试填充了）
    if (!simpleFeed.isRefresh) {
        // 非首刷使用原逻辑 + 1
        canFillMinIndex += 1;
    }
    // 大卡片
    if (index >= 0 && index < canFillMinIndex) {
        NSInteger maxInsertSlot = [KSTMEAdHelper getFeedAdMaxInsertSlot:simpleFeed adType:adType]; //最大填充槽位
        NSInteger maxInsertIndex = maxInsertSlot - 1; // 最大填充下标
        NSInteger targetIndex = index + 1; // 需要下移的下标
        if (targetIndex < canFillMinIndex) {
            // 插入目标小于舍弃位置，使用舍弃位置
            targetIndex = canFillMinIndex;
        }
#ifdef INTERNALBUILD
        KLog(@"[TMEAdFeed] 广告填充位置下移，判断是否可以下移填充，adIndex = %ld, targetIndex = %ld, maxIndex = %ld, listCount = %ld", (long)index,targetIndex,maxInsertIndex,resultList.count);
#endif
        // 1.最大槽位不为0；2.最大填充下标大于需要下移的下标；3.需要交换的两个下标值小于数据个数；
        if (maxInsertSlot > 0
            && maxInsertIndex >= targetIndex
            && index < resultList.count
            && targetIndex < resultList.count) {
            // 首刷广告可以顺延插入
            [resultList exchangeObjectAtIndex:index withObjectAtIndex:targetIndex];
            simpleFeed.isNeedRecheckAdIndex = NO;
            [KSTMEAdHelper recommendAdLossReport:KSFeedAdLossTypeExchange
                                  isLocalCacheAd:NO
                                      insertCode:0
                                       isRefresh:simpleFeed.isRefresh];
#ifdef INTERNALBUILD
            KLog(@"[TMEAdFeed] 广告填充位置下移，暂不删除，adIndex = %ld, targetIndex = %ld, maxIndex = %ld", (long)index,targetIndex,maxInsertIndex);
#endif
        }
        else{
            // 需要进行删除处理
            [resultList safeRemoveObjectAtIndex:index];
            // 填充失败，进行上报
            [KSTMEAdHelper recommendAdLossReport:KSFeedAdLossTypeFillFail
                                  isLocalCacheAd:NO
                                      insertCode:0
                                       isRefresh:simpleFeed.isRefresh];
#ifdef INTERNALBUILD
            KLog(@"[TMEAdFeed] 广告填充失败，需要删除，adIndex = %ld, fillGap = %ld", (long)index, canFillMinIndex);
#endif
        }
    } else {
        // 不该进入此流程
        // 进入该流程说明插入位置 == canFillMinIndex， 且是首刷，标识一下广告需要重刷
        [KSFeedAdManager sharedManager].isNeedRecheckFirstPage = YES;
        KLog(@"[TMEAdFeed] 广告填充位置下移，逻辑有误，adIndex = %ld, fillGap = %ld, listCount = %ld", (long)index,canFillMinIndex,resultList.count);
    }
}
+ (void)dealAdWithSimpleFeed:(KSimpleFeed *)simpleFeed atArray:(NSMutableArray *)resultList adType:(KSUnifiedNativeAdType)adType simpleFeedIndex:(NSInteger)index forceDeleteWhenFillFailed:(BOOL)forceDelete
{
    if (!simpleFeed.feedAd) {
        return;
    }
    KLog(@"[TMEAdFeed] Feed广告填充，广告下标adIndex = %ld",index);
    BOOL hasFillFeedAdData = NO;
    BOOL isLocalCacheAd = NO;
    NSInteger insertCode = 0;
    if ([KSFeedAdManager sharedManager].isSDKFeedType) { // AMS 开关打开
        // 保存算法策略实验 id，请求 ams 广告时要传联合实验过去
        [[KSFeedAdManager sharedManager] saveFeedAlgorithmTestId:simpleFeed.abTestReport forAdType:adType];
        // 填充 广告 数据
        if ([[KSFeedAdManager sharedManager] canFillFeedAdButHaveNotReplaceData:simpleFeed adType:adType]) {
            // 可以填充 广告 数据
            id feedAdData = [[KSFeedAdManager sharedManager] fillFeedAdDataToKSimpleFeed:simpleFeed adType:adType]; // 如果本地没有缓存等情况填充不了，FeedAdData 会返回 nil
            if ([feedAdData isKindOfClass:GDTUnifiedNativeAdDataObject.class]) 
            {
                GDTUnifiedNativeAdDataObject *amsData = (GDTUnifiedNativeAdDataObject *)feedAdData;
                // AppStore页预加载，根据itunesid缓存最多10个
                if (amsData && amsData.isAppAd) {
                    BOOL preloadStatus = [GDTTangramUtils preLoadStoreProductViewControllerWithAdData:amsData];
                    KLog(@"AMS Appstore preload = %d,id = %@", preloadStatus, amsData.adId);
                }
                KLog(@"[FeedAd] 填充AMSSDK广告，id = %@, title = %@", amsData.adId, amsData.title);
            }
            else if ([feedAdData isKindOfClass:TMEAdNativeDataObject.class])
            {
                TMEAdNativeDataObject * tmeadData = (TMEAdNativeDataObject *)feedAdData;
                if ([tmeadData.ExtraInfoDic isKindOfClass:NSDictionary.class]) {
                    isLocalCacheAd = [[tmeadData.ExtraInfoDic safeObjectForKey:@"TMEAdIsLocalAd"] boolValue];
                    insertCode = [[tmeadData.ExtraInfoDic safeObjectForKey:@"TMEAdInsertCode"] integerValue];
                }
                KLog(@"[TMEAdFeed] 填充UniSDK广告，id = %@, title = %@", tmeadData.adId, tmeadData.title);
            }
            
            hasFillFeedAdData = feedAdData ? YES : NO;

            // 技术上报：统计应该展示 ams 广告，但是此时没有 ams 数据可以填充的比例
            [KSBeaconManager trackWithEventId:KGBC_AMS_FEED_SHOW andBlock:^(NSMutableDictionary *info) {
                info[@"ksCode"] = @((hasFillFeedAdData ? 0 : -1));
            }];
#ifdef INTERNALBUILD
            if (KSDEBUG_IS_ENABLE_PERSIST(KSDebugItemTagSwitch_ADToast, nil)) {
                NSString *toastStr = hasFillFeedAdData ? KString(@"Feed 广告填充成功") : KString(@"无 Feed 广告填充");
                [KSToast showToast:toastStr];
            }
#endif
        }
        else {
#ifdef INTERNALBUILD
            if (KSDEBUG_IS_ENABLE_PERSIST(KSDebugItemTagSwitch_ADToast, nil)) {
                [KSToast showToast:KString(@"ams 开关打开，但无法填充数据")];
            }
#endif
        }
    }
    
    BOOL didDelete = NO;
    if (!hasFillFeedAdData) {
        if (forceDelete) {
            //强制要求删除
            didDelete = YES;
        } else if ([self canExchangeAdIndexWithType:adType]) {
            //大卡片 & 关注 & 好友
            if (index >= 0 && index < [KSFeedAdManager sharedManager].checkAdFillGap + 1) {
                //非首刷，根据Gap的位置，如果没有填充也要删除
                NSInteger maxInsertSlot = [KSTMEAdHelper getFeedAdMaxInsertSlot:simpleFeed adType:adType]; //最大填充槽位
                if (!simpleFeed.isRefresh || maxInsertSlot == 0) {
                    didDelete = YES;
                    // 填充失败，进行上报
                    [KSTMEAdHelper recommendAdLossReport:KSFeedAdLossTypeFillFail
                                          isLocalCacheAd:isLocalCacheAd
                                              insertCode:insertCode
                                               isRefresh:simpleFeed.isRefresh];
#ifdef INTERNALBUILD
                    KLog(@"[TMEAdFeed] 广告填充失败，非首刷需要删除，adIndex = %ld, fillGap = %ld", (long)index, [KSFeedAdManager sharedManager].checkAdFillGap + 1);
#endif
                }else {
                    simpleFeed.isNeedRecheckAdIndex = YES;
#ifdef INTERNALBUILD
                    KLog(@"[TMEAdFeed] 广告填充失败，首刷暂不删除，后续检查是否可以后插，adIndex = %ld, fillGap = %ld", (long)index, [KSFeedAdManager sharedManager].checkAdFillGap + 1);
#endif
                }
            }
        } else {
            // 非大卡片，AMS 开关关闭、未成功填充 ams 数据，都要判断后台有没请求 ams api，如果没请求则删除此广告 feed
            didDelete = YES;
        }
    }else {
        // 填充成功，进行上报
        if (adType == KSUnifiedNativeAdTypeRecBigCard) {
            [KSTMEAdHelper recommendAdLossReport:KSFeedAdLossTypeFillSuccess 
                                  isLocalCacheAd:isLocalCacheAd
                                      insertCode:insertCode
                                       isRefresh:simpleFeed.isRefresh];
        }
    }
    
    if (didDelete) {
        [KSProtocolFeeds removeAdFeedIfNeeded:simpleFeed atResultList:resultList];
    }
}
+ (BOOL)canExchangeAdIndexWithType:(KSUnifiedNativeAdType)adType
{
    switch (adType) {
        case KSUnifiedNativeAdTypeRecBigCard:
        case KSUnifiedNativeAdTypeFollow:
        case KSUnifiedNativeAdTypeFriend:
            return YES;
        default:
            break;
    }
    return NO;
}
+ (void)dealAdWithSimpleFeed:(KSimpleFeed *)simpleFeed atArray:(NSMutableArray *)resultList adType:(KSUnifiedNativeAdType)adType
{
    [KSProtocolFeeds dealAdWithSimpleFeed:simpleFeed atArray:resultList adType:adType simpleFeedIndex:-1 forceDeleteWhenFillFailed:NO];
}

/// 因为 ams 后台有频控，如果同个用户在两分钟内多次请求，只会在第一次请求返回数据。
/// 以前方案是后台及客户端都会去请求，客户端使用缓存展示。现改为后台不请求 ams api，后台只下发必要字段告知客户端要不要展示广告。
/// 所以如果后台没请求 ams api，但是下发数据告诉客户端要展示广告，而客户端此时没有 ams 数据可以填充，则删除此 feed 不展示。
+ (void)removeAdFeedIfNeeded:(KSimpleFeed *)simpleFeed atResultList:(NSMutableArray *)resultList
{
    /// AdvertTypeBackEnd 这个 type 表示后台没请求 ams api
    KLog(@"[AMSFeed] before remove feedAD is:%@, type:%d", simpleFeed.feedAd.jce_advertId, simpleFeed.feedAd.jce_advertType);
    if (simpleFeed.feedAd.jce_advertType == JceTimeline_enum_advertType_AdvertTypeBackEnd) {
        KLog(@"[AMSFeed]remove feedAd id: %@, title: %@", simpleFeed.feedAd.jce_advertId, simpleFeed.feedAd.jce_advertiserInfo.jce_name);
        simpleFeed.feedAd = nil;
        [resultList removeObject:simpleFeed];
    }
    else if((simpleFeed.feedFilterMask & JceTimeline_enum_filter_mask_ENUM_FILTER_MASK_FEED_RECOMM_CARD) ==  JceTimeline_enum_filter_mask_ENUM_FILTER_MASK_FEED_RECOMM_CARD){
        // 推荐大卡片走到这里，表示必然是客户端触发的广告请求，如果没有缓存广告，应该直接从列表删除
        KLog(@"[TMEAdFeed]remove feedAd id: %@, title: %@", simpleFeed.feedAd.jce_advertId, simpleFeed.feedAd.jce_advertiserInfo.jce_name);
        simpleFeed.feedAd = nil;
        [resultList removeObject:simpleFeed];
    }
}


#pragma mark - 测试代码  ------------------ start ------------------
#ifdef INTERNALBUILD
- (void)createFakeAdFeed:(NSMutableArray*)resultList
{
    KSimpleFeed*  feed = [self fakeAdFeed];
    if (feed.feedAd.jce_advertId.length>0)
    {
        if (resultList.count>0)
        {
            NSInteger count = min(4, resultList.count);
            NSInteger index = rand()% count;
            [resultList insertObject:feed atIndex:index];
        }
        else
        {
            [resultList addObject:feed];
        }
    }
    KSUnifiedNativeAdType adType = [[KSFeedAdManager sharedManager] getAdTypeFromFilterMask:self.req.jce_uFilterMask];
    [KSProtocolFeeds dealAdWithSimpleFeed:feed atArray:resultList adType:adType];
}

- (void)createFakeCompetitionFeed:(NSMutableArray *)resultList type:(KSimpleFeedCompetitionType)type
{
    KSimpleFeed *feed = [self fakeCompetitionFeedWithType:type];
    [resultList addObject:feed];
}

- (void)createFakeFeed:(NSMutableArray *)resultList
{
    KSimpleFeed *feed = [self fakeFriendBeat];
    if (feed.beatListFeed.feedBeatItems.count>0)
    {
        [resultList addObject:feed];
    }
    
    feed = [self fakeHotSong];
    if (feed.recSongsCellData)
    {
        [resultList addObject:feed];
    }
    
    feed = [self fakeRecFriends];
    if (feed.recFriendsFeed.recFriends.count>0)
    {
        [resultList addObject:feed];
    }
}

- (KSimpleFeed*)fakeAdFeed
{
    KSimpleFeed* feed = [[KSimpleFeed alloc] init];
    feed.simpleFeedCommon = [[KSimpleFeedCommon alloc] init];
    feed.simpleFeedCommon.strFeedId = [NSString stringWithFormat:@"fake_%u", arc4random() % 10000000];
    JceTimeline_cell_advert* adfeed = [JceTimeline_cell_advert new];
    JceTimeline_s_advertiser* userInfo = [JceTimeline_s_advertiser new];
    userInfo.jce_name = @"fake_广告测试:婚礼纪";
    userInfo.jce_logoUrl = @"https://pgdt.gtimg.cn/gdt/0/DAAH2ZQABQABQAAEBab9YBACo733ww.jpg/0?ck=1aaa0db34959251c79028d8f62ec84f6";
    
    adfeed.jce_advertId = @"45359917";
//    adfeed.jce_advertType = JceTimeline_enum_advertType_AdvertTypeVideo;
    adfeed.jce_advertType = JceTimeline_enum_advertType_AdvertTypeBackEnd; // 这个 type 表明后台不会去拉 ams api
    adfeed.jce_videoUrl = @"http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerMeltdowns.mp4";
    adfeed.jce_advertActionType = 1;
    JceTimeline_s_advertDesc * des = [JceTimeline_s_advertDesc new];
    des.jce_desc = @"别说结完婚才知道这个APP！那你就亏大了！";
    adfeed.jce_vecDesc =@[des];
    
    JceTimeline_s_advertPicInfo *advertPicInfo = [[JceTimeline_s_advertPicInfo alloc] init];
    advertPicInfo.jce_picUrl = @"https://pgdt.gtimg.cn/gdt/0/DAAH2ZQAKAAEgAAdBab-cBBnwSd6Fl.jpg/0?ck=4a626ead31d11c0d61a822023c27246e";
    adfeed.jce_vecPicUrl =@[advertPicInfo];
    
    //appstore链接
    adfeed.jce_jumpUrl =@"https://c.gdt.qq.com/gdt_mclick.fcg?viewid=g6IZTM1JcYtbAXRYPtHAhW6jebOljyNCeKdm!2SKcex0pmkk8a2Ic0zhtIM9UzpgEOeMykvApYM!BMKinTAF8YqEeuIt2a28O6xgS7N!TfRX77QrL6KMm8vwRRN2dZfXtU1E5RawlMsq9LkX1mgUX6Qk650BLxbD2IONpq5KyUncNs_Tz0ttju5mki65GH9k&jtype=0&i=1&os=1";
   
    adfeed.jce_exposureUrl = @"https://c.gdt.qq.com/gdt_mclick.fcg?viewid=g6IZTM1JcYtbAXRYPtHAhW6jebOljyNCeKdm!2SKcex0pmkk8a2Ic0zhtIM9UzpgEOeMykvApYM!BMKinTAF8YqEeuIt2a28O6xgS7N!TfRX77QrL6KMm8vwRRN2dZfXtU1E5RawlMsq9LkX1mgUX6Qk650BLxbD2IONpq5KyUncNs_Tz0ttju5mki65GH9k&jtype=0&i=1&os=1";
    
    adfeed.jce_negFeedbackUrl =@"https://nc.gdt.qq.com/gdt_report.fcg?viewid=g6IZTM1JcYtbAXRYPtHAhW6jebOljyNCeKdm!2SKcex0pmkk8a2Ic0zhtIM9UzpgEOeMykvApYM!BMKinTAF8YqEeuIt2a28O6xgS7N!TfRX77QrL6KMm8vwRRN2dZfXtU1E5RawlMsq9LkX1mgUX6Qk650BLxbD2IONpq5KyUncNs_Tz0ttju5mki65GH9k&acttype=__ACT_TYPE__";

    adfeed.jce_advertiserInfo = userInfo;
    
    feed.feedAd = adfeed;
    return feed;
}

- (KSimpleFeed*)fakeRecFriends
{
    KSimpleFeed *simpleFeed = [[KSimpleFeed alloc]init];
    if( YES) //fortest
    {
        KSimpleFeedRecFriendsCell *simpleFeedRecFriendCell = [KSimpleFeedRecFriendsCell new];
        NSMutableArray *recFriendList  = [NSMutableArray arrayWithCapacity:20];
        
        //fortest
        int count = arc4random() % 11;
        for (int i =0; i<count; i++)
        {
            KSimpleFeedFriendItem * recFriendItem  = [[KSimpleFeedFriendItem alloc] init];
            KSUserInfo*userInfo = [[KSUserInfo alloc] init];
            userInfo.userId = i==0? 212980132:56803176;
            NSString *temp = [NSString stringWithFormat:@"sheldont_%ld",(long)i];
            userInfo.nickName = i==0?@"梦中": temp;
            recFriendItem.userInfo = userInfo;//[KSLoginManager sharedInstance].curUserInfo;
            recFriendItem.strRelation = @"QQ好友，你的好友xxxx也关注了哦，快来关注他吧";
            [recFriendList addObject:recFriendItem];
        }
        
        simpleFeedRecFriendCell.recFriends = recFriendList;
        simpleFeed.recFriendsFeed = simpleFeedRecFriendCell;
    }
    
    return simpleFeed;
}

- (KSimpleFeed*)fakeFriendBeat
{
    KSimpleFeed *simpleFeed = [[KSimpleFeed alloc]init];
    if( YES) //fortest
    {
        KSimpleFeedBeatCell *simpleFeedBeat = [KSimpleFeedBeatCell new];
        NSMutableArray *beatList  = [NSMutableArray arrayWithCapacity:3];
        
        //fortest
        for (int i =0; i<13; i++)
        {
            KSimpleFeedBeatItem * beatItem  = [[KSimpleFeedBeatItem alloc] init];
            beatItem.userInfo = [KSLoginManager sharedInstance].curUserInfo;
            beatItem.strSongId =  @"004BAyvQ1FranQ"; //@"002NKPkR0O4wyw";
            beatItem.strsongName = @"你怎么舍得我难过(Live)";
           
            JceTimeline_s_picurl * image = [[JceTimeline_s_picurl alloc]init];
            image.jce_url = @"http://kg.qq.com/gtimg/music/photo/mid_album_150/L/r/001GraMO3JQQLr.jpg";
            beatItem.coverUrlsDic = @{@(200):image};
            beatItem.scoreRank = 6;
            beatItem.score = 136643;
            beatItem.ugcId = i==0? @"88066087_1478773426_567":@"62838685_1478770633_499";
            
            
            [beatList addObject:beatItem];
        }
        
        
        simpleFeedBeat.feedBeatItems = beatList;
        simpleFeed.beatListFeed = simpleFeedBeat;
    }
    
    return simpleFeed;
}

- (KSimpleFeed*)fakeHotSong
{
    KSimpleFeed *simpleFeed = [[KSimpleFeed alloc]init];
    if( YES) //fortest
    {
        NSArray* songMidArr = @[@"001a2lLp1KG6bk",@"002TdlsF3v9AHk",@"0009d3dh4FUIHi",@"001CuwWs1IxnA3",@"000QU0AF12OzvJ",@"003CVfYr0ELzBY",@"002x2Agr4LqRYV",@"0025t9D62NNGrW",@"001O822L2ZpW4g",@"004ByFPF24Gq5Y",@"0032SHiH23C0wG",@"002B1NEz1yCD2y"];
        NSArray* songAlbumMidArr = @[@"000QgFcm0v8WaF",@"002qFw3Y25NtKU",@"002aaUOS24kcwh",@"002Neh8l0uciQZ",@"003zra5K2d1i6E",@"0017vyoD4NbKoL",@"003ha6C92GRuVE",@"0045GzEt0I9c3x",@"000nGQ5j2cJv8v",@"001NABAr3AOlgL",@"002apRhZ4Bq99d",@"001nL4Z20NfhOs"];
        NSArray* btnJumpArr = @[@"qmkege://kege.com?action=record&kge_mid=001a2lLp1KG6bk&title=%E4%BD%A0%E8%BF%98%E8%A6%81%E6%88%91%E6%80%8E%E6%A0%B7",
                                @"qmkege://kege.com?action=record&kge_mid=002TdlsF3v9AHk&title=%E5%85%89%E8%BE%89%E5%B2%81%E6%9C%88",
                                @"qmkege://kege.com?action=record&kge_mid=0009d3dh4FUIHi&title=%E6%9B%B9%E6%93%8D",
                                @"qmkege://kege.com?action=record&kge_mid=001CuwWs1IxnA3&title=%E7%A8%BB%E9%A6%99",
                                @"qmkege://kege.com?action=record&kge_mid=000QU0AF12OzvJ&title=%E5%A5%BD%E7%88%B1%E5%A5%BD%E6%95%A3",
                                @"qmkege://kege.com?action=record&kge_mid=003CVfYr0ELzBY&title=%E4%BB%8E%E5%A4%B4%E5%86%8D%E6%9D%A5",
                                @"qmkege://kege.com?action=record&kge_mid=002x2Agr4LqRYV&title=%E5%81%8F%E7%88%B1",
                                @"qmkege://kege.com?action=record&kge_mid=0025t9D62NNGrW&title=%E5%82%B2%E5%AF%92",
                                @"qmkege://kege.com?action=record&kge_mid=001O822L2ZpW4g&title=%E9%82%A3%E5%B0%B1%E8%BF%99%E6%A0%B7%E5%90%A7",
                                @"qmkege://kege.com?action=record&kge_mid=004ByFPF24Gq5Y&title=%E5%88%AB%E6%80%95%E6%88%91%E4%BC%A4%E5%BF%83",
                                @"qmkege://kege.com?action=record&kge_mid=0032SHiH23C0wG&title=%E5%9D%8F%E5%A5%B3%E5%AD%A9",
                                @"qmkege://kege.com?action=record&kge_mid=002B1NEz1yCD2y&title=%E8%B5%B0%E5%9C%A8%E5%86%B7%E9%A3%8E%E4%B8%AD"
                                ];

        KSimpleFeedRecSongCellData *recSongCell = [KSimpleFeedRecSongCellData new];
        recSongCell.cellTitle = @"猜你喜欢";
        recSongCell.strMoreJumpUrl = @"qmkege://kege.com?action=selection&locate=guess";
        recSongCell.groupCount = 3;
        recSongCell.showedCount = 0;
        recSongCell.curSwitchCount = recSongCell.groupCount;
        recSongCell.needRelayout = YES;
        NSMutableArray *songList  = [NSMutableArray arrayWithCapacity:3];
        
        //fortest
        for (int i =0; i<songMidArr.count; i++)
        {
            KSimpleFeedRecSong * songItem  = [[KSimpleFeedRecSong alloc] init];
        
            KSimpleFeedAccompany *stAccompany = [[KSimpleFeedAccompany alloc] init];
            stAccompany.strSongMid = songMidArr[i];
            stAccompany.strSingerName = [NSString stringWithFormat:@"singer_%ld周杰伦呀陈奕迅孙燕姿",(long)i];
            
            
            stAccompany.mapCoverUrl = @{[NSNumber numberWithInt:200]:@"http://imgcache.qq.com/music/photo/mid_album_300/a/F/000QgFcm0v8WaF.jpg"};
            stAccompany.strAlbumMid = songAlbumMidArr[i];
            stAccompany.strSongName = [NSString stringWithFormat:@"songName_%ld_fa发发家里的发福利大厦",(long)i];
            
            songItem.stAccompany = stAccompany;
            songItem.strRecReason = [NSString stringWithFormat:@"第%d首歌曲推荐理由",i];
            songItem.strBtnJumpUrl = btnJumpArr[i];
            songItem.strCoverJumpUrl = btnJumpArr[i];
            songItem.iRecReasonType = 4;
            
            [songList addObject:songItem];
        }
        
        recSongCell.recSongList = songList;
        simpleFeed.recSongsCellData = recSongCell;
    }
    
    return simpleFeed;
}

- (KSimpleFeed*)fakeCompetitionFeedWithType:(KSimpleFeedCompetitionType)competitionType
{
    KSimpleFeed *feed = [[KSimpleFeed alloc] init];
    
    feed.simpleFeedCommon = [[KSimpleFeedCommon alloc] init];
    feed.simpleFeedCommon.uTypeid = 10;
    feed.simpleFeedCommon.uFeedTime = 1577453859;
    feed.simpleFeedCommon.actiontype = 1;
    feed.simpleFeedCommon.strFeedId = @"*********";

    feed.simpleUser = [[KSimpleFeedUser alloc] init];
    feed.simpleUser.userinfo = [[KSUserInfo alloc] init];
    feed.simpleUser.userinfo.userId = *********;
    feed.simpleUser.userinfo.avatarTimestamp = 0;
    feed.simpleUser.userinfo.nickName = @"fake_全民K歌官方活动";

    feed.competitionFeed = [[KSimpleFeedCompetition alloc] init];
    feed.competitionFeed.strFeedDesc = @"我创建了一个大赛，大家一起来玩吧！";
    feed.competitionFeed.strFeedPicUrl = @"http://imgcache.qq.com/music/common/upload/t_k_competition/1861963.jpg";
    feed.competitionFeed.strJumpUrl = @"https://kg.qq.com/dasai/detail.html?id=4770";
    feed.competitionFeed.strDesc1 = @"赢盛典门票，摩登兄弟在TMEA盛典等你";
    feed.competitionFeed.strDesc2 = @"2019-12-30结束";
    feed.competitionFeed.strDesc3 = @"";
    feed.competitionFeed.competitionType = competitionType;

//    feed.forwardFeed = [[KSTimelineForward alloc] init];
//    feed.forwardFeed.feedUserInfo = [[KSimpleFeedUser alloc] init];
//    feed.forwardFeed.feedUserInfo.userinfo = [[KSUserInfo alloc] init];
//    feed.forwardFeed.feedUserInfo.userinfo.userId = *********;
//    feed.forwardFeed.feedUserInfo.userinfo.avatarTimestamp = 0;
//    feed.forwardFeed.feedUserInfo.userinfo.nickName = @"全民K歌官方活动";
//    feed.forwardFeed.feedUserInfo.actiontype = 1003;
//    feed.forwardFeed.strForward = @"Test";
//    feed.forwardFeed.strForwardId = @"zf_*********_1577453859_669229";
//    feed.forwardFeed.forwarType = 2;
//    feed.forwardFeed.sameSourceForwardCount = 1;

    feed.relationDesc = @"好友";
    feed.displayTime = 1577496675.276654;
    feed.feedVCType = KSimpleFeedSourceType_TimelineRoot;
    feed.abTestReport = @"followFeedMilestone|#followFeeds|1_422_547#feedMongo|1_869_1076#40|1_892_1129";
    feed.feedParkingGuideState = ksFeedParkingGuideStateNotHasShow;
    
    return feed;
}
#endif

#pragma mark - 测试代码  ------------------ end ------------------

@end
