//
//  KSimpleFeed.m
//  QQKSong
//
//  Created by <PERSON><PERSON><PERSON> on 14-6-10.
//  Copyright (c) 2014年 Tencent. All rights reserved.
//

#import "KSWnsConfigDef.h"
#import "KSimpleFeed.h"
#import "KSLoginManager.h"
#import "KSLayoutableTimelineFeedCellV2+TraceReport.h"
#import "proto_feed_webapp_cell_game_advert_feed.h"
#import "KSFeedPlayback.h"
#import "KSTimelineDetailManager.h"
#import "KSNewAudioPlayerManager.h"
#import "KSNavigationManager+Scheme.h"
#import "KSMusicMoodPicItem.h"
#import "KShareContent.h"
#import "JceTimeline_enum_advertType.h"
#import "JceTimeline_EnumShowMask.h"
#import "NSDate+Utils.h"
#import "JceKSongInfo_emContentType.h"
#import "KSRankWorkContent.h"
#import "JceTimeline_cell_ugc_dianping.h"
#import "KSStringCrashManager.h"
#import "ProductUploadTask.h"
#import "KSVideolizeSwitchManager.h"
#import "KSDetailPlaybackModel.h"
#import "JceTimeline_enum_filter_mask.h"
#import "KSUgcPlayManager_Private.h"
#import "KSWorkBindPics.h"
#import "KSDetailPlaybackModel.h"
#import "JceTimeline_enum_filter_mask.h"
#import "JceFileUpload_MusicFeelPicItem.h"
#import "JceTimeline_cell_hc.h"
#import "JceTimeline_cell_ktv.h"
#import "JceTimeline_cell_ktv_mike.h"
#import "JceTimeline_cell_share.h"
#import "JceTimeline_PlayUrlInfo.h"
#import "KSAvatarDownloadManager.h"
#import "KSBitRateMethod.h"
#import "KSCommonModel.h"
#import "KSFeedLayoutConfig.h"
#import "KSGlobalPlayItem.h"
#import "KSLayoutUIManager_FeedCommonDefines.h"
#import "KSPaySoloAlbumManager.h"
#import "KSSoloAlbumCreateManager.h"
#import "KSTimelineForward.h"
#import "KSTimelineForwardInfo.h"
#import "KSTimelineManager.h"
#import "KSUgcPlayManager.h"
#import "KSUserInfo.h"
#import "KSimpleFeedMacros.h"
#import "KSong+Common.h"
#import "ProductUploadManager.h"
#import "QzoneUtil.h"
#import "GDTUnifiedNativeAdDataObject.h"
#import "KSBigCardVideolizeSwitch.h"
#import "KSTopicSummaryInfo.h"
#import "JceTimeline_HotTag.h"
#import "proto_feed_webapp_cell_media_product.h"
#import "proto_feed_webapp_MediaProduct.h"
#import "KSExpressManager.h"
#import "KSVideolizePublishContent.h"
#import "KSPublishLocalImage.h"
#import "KSABTestManager.h"
#import "proto_feed_webapp_cell_teach_info.h"
#import "JceUserSearch_RelationType.h"
#import "KSTopicTextParser.h"
#import "KSUgcPlayMgr.h"
#import "KSUgcPlayMgr+State.h"
#import "JceTimeline_cell_competition_jump_info.h"
#import "proto_feed_webapp_cell_city_ext.h"
#import "JceTimeline_cell_comm_label.h"
#import "JceTimeline_EnumAnchorPointType.h"
#import "proto_nearby_rec_REC_CONTENT_POOL.h"
#import "KSSongExtraInfo.h"
#import "KSUgcPlayManager+PreloadV2.h"
#import "KSMediaInfo.h"
#import "JceProfile_AuthKey.h"
#import "KSTmpAudioPlayManger.h"
#import "KSProtoLbs.h"
#import "KSUIABTestManager.h"
#import "KSUIABTestConstants.h"
#import "JceKG_AbtestRspItem.h"
#import "proto_feed_webapp_cell_ktv_sing_card.h"
#import "rcqmkg_SingCardShow.h"
#import "KSPlaybackRequester.h"
#import "KSUgcStarChorusManager.h"
#import "KSUserInfo+Follow.h"
#import "KSUserVip.h"
#import "KSUserInfo+Auth.h"
#import "KSUgcTypeManager.h"
#import "KSUserInfo+RoomType.h"
#import "KSPlayReport.h"
#import "JceTimeline_cell_extrainfo.h"
#import <BaseModule/UIImageView+Rank.h>
#import "JceTimeline_Detail_UgcAudio.h"
#import "proto_feed_webapp_cell_tme_town_feed.h"
#import "JceProfile_emAvatarKtvStatus.h"
#import "JceIapGift_emGiftType.h"
#import "proto_feed_webapp_game_dispatch.h"
#import "proto_feed_webapp_cell_mini_show.h"
#import "proto_ktv_recfeed_webapp_KtvItem.h"
#import "proto_feed_webapp_GuiderInfo.h"
#import "JceKSongInfo_emSuperSoundFileType.h"
#import "proto_feed_webapp_ktv_game_status.h"
#import "proto_feed_webapp_game_status_comm.h"
#import "proto_feed_webapp_cell_game.h"
#import "PROTO_UGC_WEBAPP_LiveShopTemplate.h"
#import "proto_interact_ecommerce_webapp_kg_emUgcProductStyle.h"
#import "KSAiSongRecFeedModel.h"
#import "JceTimeline_cell_mini_heat_card.h"
#import "KSAISongCommFeedModel.h"
#import "JceKG_kge_ugc_webapp_define.h"
#import "KSTimelineManager+Gift.h"


@implementation KSimpleFeedLiveShowHighlight
@end


/**
 *  KSimpleFeedListener
 */
@implementation KSimpleFeedListener

- (id)copyWithZone:(NSZone *)zone
{
    KSimpleFeedListener *copy = [[KSimpleFeedListener allocWithZone:zone] init];
    copy.number = self.number;
    copy.actiontype = self.actiontype;
    
    return copy;
}

@end



@implementation JceTimeline_cell_recommend (Copy)

- (id)copyWithZone:(NSZone *)zone
{
    JceTimeline_cell_recommend *copy = [[JceTimeline_cell_recommend allocWithZone:zone] init];
    copy.jce_uiFeedSource = self.jce_uiFeedSource;
    copy.jce_strTraceId = [self.jce_strTraceId copy];
    copy.jce_uiItemType = self.jce_uiItemType;
    copy.jce_uiAlgorithmType = self.jce_uiAlgorithmType;
    copy.jce_strAlgorithmId = [self.jce_strAlgorithmId copy];
    copy.jce_strKsongMid = [self.jce_strKsongMid copy];
    return copy;
}

@end

@implementation JceTimeline_s_advertiser(Copy)

- (id)copyWithZone:(NSZone *)zone
{
    JceTimeline_s_advertiser *copy = [[JceTimeline_s_advertiser allocWithZone:zone] init];
    copy.jce_name = [self.jce_name copy];
    copy.jce_logoUrl = [self.jce_logoUrl copy];
    return copy;
}
@end


@implementation JceTimeline_s_advertPicInfo(Copy)

- (id)copyWithZone:(NSZone *)zone
{
    JceTimeline_s_advertPicInfo *copy = [[JceTimeline_s_advertPicInfo allocWithZone:zone] init];
    copy.jce_picUrl = [self.jce_picUrl copy];
    return copy;
}
@end

@implementation JceTimeline_s_advertDesc(Copy)

- (id)copyWithZone:(NSZone *)zone
{
    JceTimeline_s_advertDesc *copy = [[JceTimeline_s_advertDesc allocWithZone:zone] init];
    copy.jce_desc = [self.jce_desc copy];
    return copy;
}
@end

@implementation JceTimeline_cell_advert (Copy)

- (id)copyWithZone:(NSZone *)zone
{
    JceTimeline_cell_advert *copy = [[JceTimeline_cell_advert allocWithZone:zone] init];
    copy.jce_advertiserInfo = [self.jce_advertiserInfo copy];
    copy.jce_advertId = [self.jce_advertId copy];
    copy.jce_advertType = self.jce_advertType;
    copy.jce_advertActionType = self.jce_advertActionType;
    copy.jce_vecDesc = [self.jce_vecDesc copy];
    copy.jce_vecPicUrl = [self.jce_vecPicUrl copy];
    copy.jce_jumpUrl = [self.jce_jumpUrl copy];
    copy.jce_exposureUrl = [self.jce_exposureUrl copy];
    copy.jce_negFeedbackUrl = [self.jce_negFeedbackUrl copy];
    return copy;
}

@end

@implementation JceTimeline_picinfo (Copy)

- (id)copyWithZone:(NSZone *)zone
{
    JceTimeline_picinfo *copy = [[JceTimeline_picinfo allocWithZone:zone] init];
    copy.jce_mPic = [self.jce_mPic copy];

    return copy;
}

@end

@implementation JceTimeline_pic_detail (Copy)

- (id)copyWithZone:(NSZone *)zone
{
    JceTimeline_pic_detail *copy = [[JceTimeline_pic_detail allocWithZone:zone] init];
    copy.jce_strUrl = [self.jce_strUrl copy];
    copy.jce_uiWidth = self.jce_uiWidth;
    copy.jce_uiHeight = self.jce_uiHeight;
    
    return copy;
}

@end

/**
 *  KSimpleFeedComment
 */
@implementation KSimpleFeedComment

- (id)copyWithZone:(NSZone *)zone
{
    KSimpleFeedComment *copy = [[KSimpleFeedComment allocWithZone:zone] init];
    copy.number = self.number;
    copy.actiontype = self.actiontype;
    
    return copy;
}

@end


/**
 *  KSimpleFeedFlower
 */
@implementation KSimpleFeedFlower

- (id)copyWithZone:(NSZone *)zone
{
    KSimpleFeedFlower *copy = [[KSimpleFeedFlower allocWithZone:zone] init];
    copy.number = self.number;
    copy.actiontype = self.actiontype;
    copy.uPackageNum = self.uPackageNum;
    copy.uPropsNum = self.uPropsNum;
    copy.strGiftItemDesc = self.strGiftItemDesc;
    return copy;
}

@end

/**
 *  KSimpleFeedGiftRank
 */
@implementation KSimpleFeedGiftRank

- (id)copyWithZone:(NSZone *)zone
{
    KSimpleFeedGiftRank *copy = [[KSimpleFeedGiftRank allocWithZone:zone] init];
    copy.giftWealth = self.giftWealth;
    copy.topRankArray = [self.topRankArray copy];
    copy.flowerRankArray = [self.flowerRankArray copy];
    copy.myGiftItemIndex = self.myGiftItemIndex;
    return copy;
}

@end

/**
 *  KSimpleFeedGiftRankItem
 */
@implementation KSimpleFeedGiftRankItem

- (id)copyWithZone:(NSZone *)zone
{
    KSimpleFeedGiftRankItem *copy = [[KSimpleFeedGiftRankItem allocWithZone:zone] init];
    copy.userInfo = [self.userInfo copy];
    copy.giftStarNum = self.giftStarNum;
    copy.flowerNum = self.flowerNum;
    copy.propsNum = self.propsNum;
    return copy;
}

@end

/**
 *  KSimpleFeedOperation
 */
@implementation KSimpleFeedOperation

- (id)copyWithZone:(NSZone *)zone
{
    KSimpleFeedOperation *copy = [[KSimpleFeedOperation allocWithZone:zone] init];
    copy.strFeedPicUrl = [self.strFeedPicUrl copy];
    copy.strJumpUrl = [self.strJumpUrl copy];
    copy.strDesc1 = [self.strDesc1 copy];
    copy.strDesc2 = [self.strDesc2 copy];
    copy.strBtnText =[self.strBtnText copy];
    copy.strBtnJumpUrl = [self.strBtnJumpUrl copy];
    copy.strRightTopText = [self.strRightTopText copy];
    copy.iExpireAt = self.iExpireAt;
    
    return copy;
}

@end

/**
 *  KSimpleFeedShortVideoItem
 */
@implementation KSimpleFeedShortVideoItem

- (id)copyWithZone:(NSZone *)zone
{
    KSimpleFeedShortVideoItem *copy = [[KSimpleFeedShortVideoItem allocWithZone:zone]init];
    copy.uid = self.uid;
    copy.ugcId = [self.ugcId copy];
    copy.bottomText = [self.bottomText copy];
    copy.coverText = [self.coverText copy];
    copy.coverUrls = [self.coverUrls copy];
    copy.playCount = self.playCount;
    copy.ugcmask = self.ugcmask;
    copy.ugcmaskex = self.ugcmaskex;
    copy.strRecDesc = [self.strRecDesc copy];
    copy.activityId = self.activityId;
    copy.scoreRank = self.scoreRank;
    copy.isSegment = self.isSegment;
    copy.segmentStart = self.segmentStart;
    copy.segmentEnd = self.segmentEnd;
    copy.vid = [self.vid copy];
    copy.ksongMid = [self.ksongMid copy];
    copy.feedSource = self.feedSource;
    copy.strTraceId = [self.strTraceId copy];
    copy.uiItemType = self.uiItemType;
    copy.uiAlgorithmType = self.uiAlgorithmType;
    copy.strAlgorithmId = [self.strAlgorithmId copy];
    return copy;
}

@end
/**
 *  KSimpleFeedShortVideoPage
 */
@implementation KSimpleFeedShortVideoPage

- (instancetype)init
{
    self = [super init];
    if (self) {
        self.hasMore = YES;
    }
    return self;
}

- (id)copyWithZone:(NSZone *)zone
{
    KSimpleFeedShortVideoPage *copy = [[KSimpleFeedShortVideoPage allocWithZone:zone]init];
    copy.recommendText = [self.recommendText copy];
    copy.recItems = [self.recItems copy];
    copy.passback = [self.passback copy];
    copy.hasMore = self.hasMore;
    copy.pageHasReported = self.pageHasReported;
    return copy;
}

@end

/**
 *  KSimpleFeedShortVideo
 */
@implementation KSimpleFeedShortVideo

- (id)copyWithZone:(NSZone *)zone
{
    KSimpleFeedShortVideo *copy = [[KSimpleFeedShortVideo allocWithZone:zone]init];
    copy.recPages = [self.recPages mutableCopy];
    copy.abTestId = self.abTestId;
    copy.title = [self.title copy];
    copy.icon = [self.icon copy];
    copy.buttonText = [self.buttonText copy];
    copy.buttonUrl = [self.buttonUrl copy];
    copy.iRecType = self.iRecType;
    
    return copy;
}

@end


@implementation KSimpleFeedRelayGame

- (id)copyWithZone:(NSZone *)zone
{
    KSimpleFeedRelayGame *copy = [[KSimpleFeedRelayGame allocWithZone:zone] init];
    copy.strRoomId = [self.strRoomId copy];
    copy.strShowId = [self.strShowId copy];
    copy.uRoomType = self.uRoomType;
    copy.strContent = [self.strContent copy];
    copy.strTitle = [self.strTitle copy];
    copy.strDesc = [self.strDesc copy];
    copy.iFeedType = self.iFeedType;
    copy.uRelayGameFriendNum = self.uRelayGameFriendNum;
    copy.strJumpUrl = [self.strJumpUrl copy];
    copy.strAudienceDesc = [self.strAudienceDesc copy];
    copy.uIsDefaultFeed = self.uIsDefaultFeed;
    
    return copy;
}

@end

/**
 *  KSimpleFeedSecretary
 */
@implementation KSimpleFeedSecretary

- (id)copyWithZone:(NSZone *)zone
{
    KSimpleFeedSecretary *copy = [[KSimpleFeedSecretary allocWithZone:zone] init];
    copy.secretaryItems = [self.secretaryItems copy];
    copy.marketTitle = [self.marketTitle copy];

    return copy;
}

@end


@implementation KSimpleFeedCompetition

- (id)copyWithZone:(NSZone *)zone
{
    KSimpleFeedCompetition *copy = [[KSimpleFeedCompetition allocWithZone:zone] init];
    copy.strFeedDesc = [self.strFeedDesc copy];
    copy.strFeedPicUrl = [self.strFeedPicUrl copy];
    copy.strJumpUrl = [self.strJumpUrl copy];
    copy.strDesc1 = [self.strDesc1 copy];
    copy.strDesc2 = [self.strDesc2 copy];
    copy.strDesc3 = [self.strDesc3 copy];
    copy.competitionType = self.competitionType;
    return copy;
}

@end


@implementation KSimpleFeedRecSinger

- (id)copyWithZone:(NSZone *)zone
{
    KSimpleFeedRecSinger *copy = [[KSimpleFeedRecSinger allocWithZone:zone] init];
    copy.singerInfo = [self.singerInfo copy];
    copy.recReason = [self.recReason copy];
    copy.avatarJumpUrl = [self.avatarJumpUrl copy];
    copy.iBtnActionType = self.iBtnActionType;
    
    return copy;
}

@end

@implementation KSimpleFeedRecSingerCellData

- (id) copyWithZone:(NSZone *)zone
{
    KSimpleFeedRecSingerCellData * copy = [[KSimpleFeedRecSingerCellData allocWithZone:zone] init];
    copy.cellTitle = [self.cellTitle copy];
    copy.recSingerList = [self.recSingerList copy];
    return copy;
}
@end


@implementation KSimpleFeedAccompany

- (id)copyWithZone:(NSZone *)zone
{
    KSimpleFeedAccompany *copy =[[KSimpleFeedAccompany allocWithZone:zone] init];
    copy.strSongMid = [self.strSongMid copy];
    copy.strSongName = [self.strSongName copy];
    copy.strSingerName = [self.strSingerName copy];
    copy.mapCoverUrl = [self.mapCoverUrl copy];
    copy.strAlbumMid = [self.strAlbumMid copy];
    return copy;
}

@end

@implementation KSimpleFeedRecSong

- (id)copyWithZone:(NSZone *)zone
{
    KSimpleFeedRecSong *copy  = [[KSimpleFeedRecSong allocWithZone:zone] init] ;
    copy.stAccompany = [self.stAccompany copy];
    copy.strRecReason = [self.strRecReason copy];
    copy.strCoverJumpUrl = [self.strCoverJumpUrl copy];
    copy.strBtnJumpUrl = [self.strBtnJumpUrl copy];
    copy.iRecReasonType = self.iRecReasonType;
    copy.mapExt = [self.mapExt copy];
    copy.itemType = [self.itemType copy];
    copy.algorithmType = [self.algorithmType copy];
    copy.algoritymId = [self.algoritymId copy];
    copy.trace_id = [self.trace_id copy];
    copy.isReportExposed = self.isReportExposed;

    return copy;
}
@end

@implementation KSimpleFeedRecSongCellData

- (id)copyWithZone:(NSZone *)zone
{
    KSimpleFeedRecSongCellData *copy = [[KSimpleFeedRecSongCellData allocWithZone:zone] init];
    copy.cellTitle = [self.cellTitle copy];
    copy.recSongList = [self.recSongList copy];
    copy.cellJumpDesc = [self.cellJumpDesc copy];
    return copy;
}

@end

@implementation KSimpleFeedLiveShow

- (id)copyWithZone:(NSZone *)zone
{
    KSimpleFeedLiveShow *copy = [[KSimpleFeedLiveShow allocWithZone:zone] init];
    copy.uOnlinePeopleCount = self.uOnlinePeopleCount;
    copy.strLiveTitle = [self.strLiveTitle copy];
    copy.strLiveRoomId = [self.strLiveRoomId copy];
    copy.strLiveCoverurl = [self.strLiveCoverurl copy];
    copy.strShowId = [self.strShowId copy];
    copy.roomType = self.roomType;
    copy.isRobMicroGame = self.isRobMicroGame;
    copy.isKingSongGame = self.isKingSongGame;
    copy.gameTheme = self.gameTheme;
    copy.anchorUid = self.anchorUid;
    copy.popularityNum = self.popularityNum;
    copy.strGroupId = [self.strGroupId copy];
    copy.anchorMuid = [self.anchorMuid copy];
    copy.mapExt = [self.mapExt copy];
    return copy;
}

// 是否是官频直播房间
- (BOOL)isOfficialChannelRoom
{
    BOOL isChannel = [[self.mapExt safeObjectForKey:@"is_official_live"] isEqualToString:@"1"];
    return isChannel;
}

@end

@implementation KSimpleFeedLastLiveShow

- (id)copyWithZone:(NSZone *)zone
{
    KSimpleFeedLastLiveShow *copy = [[KSimpleFeedLastLiveShow allocWithZone:zone] init];
    copy.uOnlinePeopleCount = self.uOnlinePeopleCount;
    copy.strLiveTitle = [self.strLiveTitle copy];
    copy.strLiveRoomId = [self.strLiveRoomId copy];
    copy.strLiveCoverurl = [self.strLiveCoverurl copy];
    copy.strShowId = [self.strShowId copy];
    copy.showDuration = self.showDuration;
    return copy;
}

@end

@implementation KSimpleFeedBeatItem

- (id)copyWithZone:(NSZone *)zone
{
    KSimpleFeedBeatItem *copy = [[KSimpleFeedBeatItem allocWithZone:zone] init];
    copy.userInfo = [self.userInfo copy];
    copy.strSongId = [self.strSongId copy];
    copy.strsongName = [self.strsongName copy];
    copy.coverUrlsDic = [self.coverUrlsDic copy];
    copy.scoreRank = self.scoreRank ;
    copy.score = self.score;
    copy.isReportExposed = self.isReportExposed;
    return copy;
}

@end

@implementation KSimpleFeedBeatCell

- (id)copyWithZone:(NSZone *)zone
{
    KSimpleFeedBeatCell *copy = [[KSimpleFeedBeatCell allocWithZone:zone] init];
    copy.feedBeatItems = [self.feedBeatItems copy];
    copy.cellTitle = [self.cellTitle copy];
    copy.cellJumpDesc = [self.cellJumpDesc copy];

    return copy;
}

@end

@implementation KSimpleFeedFriendItem

- (id)copyWithZone:(NSZone *)zone
{
    KSimpleFeedFriendItem *copy = [[KSimpleFeedFriendItem allocWithZone:zone] init];
    copy.userInfo = [self.userInfo copy];
    copy.strRelation = [self.strRelation copy];
    copy.isReportExposed = self.isReportExposed;
    return copy;
}

@end


@implementation KSimpleFeedRecFriendsCell

- (id)copyWithZone:(NSZone *)zone
{
    KSimpleFeedRecFriendsCell *copy = [[KSimpleFeedRecFriendsCell allocWithZone:zone] init];
    copy.recFriends = [self.recFriends copy];
    copy.isReported = self.isReported;
    copy.strRecDesc = [self.strRecDesc copy];
    copy.strRecTitle = [self.strRecTitle copy];
    copy.vipStatus = self.vipStatus;
    copy.hasReportRecUids = [self.hasReportRecUids copy];
    return copy;
}

@end


@implementation KSimpleFeedSubmission

- (id)copyWithZone:(NSZone *)zone
{
    KSimpleFeedSubmission *copy = [[KSimpleFeedSubmission allocWithZone:zone] init];
    copy.submissionDesc = [self.submissionDesc copy];
    return copy;
}


@end

@implementation KSimpleFeedEverydayTaskItem

- (id)copyWithZone:(NSZone *)zone
{
    KSimpleFeedEverydayTaskItem *copy = [[KSimpleFeedEverydayTaskItem allocWithZone:zone] init];
    copy.imageUrl = [self.imageUrl copy];
    copy.taskTitle = [self.taskTitle copy];
    copy.taskDetail = [self.taskDetail copy];
    copy.taskId = self.taskId;
    return copy;
}

@end

@implementation KSimpleFeedEverydayTask

- (id)copyWithZone:(NSZone *)zone
{
    KSimpleFeedEverydayTask *copy = [[KSimpleFeedEverydayTask allocWithZone:zone] init];
    copy.flowerCount = self.flowerCount;
    copy.taskArray = [self.taskArray copy];
    copy.strTaskJumpUrl = [self.strTaskJumpUrl copy];
    return copy;
}

@end

@implementation KSimpleFeedCommentItem

- (id)copyWithZone:(NSZone *)zone
{
    KSimpleFeedCommentItem *copy = [[KSimpleFeedCommentItem allocWithZone:zone] init];
    copy.feedCommentId = [self.feedCommentId copy];
    copy.feedCommentContent = [self.feedCommentContent copy];
    copy.feedReplyUser = [self.feedReplyUser copy];
    copy.feedCommentTime = self.feedCommentTime;
    copy.feedCommentUser = [self.feedCommentUser copy];
    copy.isBulletCurtain = self.isBulletCurtain;
    copy.bulletOffset = self.bulletOffset;
    copy.isForwarded = self.isForwarded;
    copy.comment_pic_id = self.comment_pic_id;
    return copy;
}

@end

// 外显展示信息
@implementation KSimpleFeedGiftGuideFeedInfo

@end

@implementation KSimpleFeedGiftGuide

@end

@implementation KSimpleFeedGiftGuideReport

@end

@implementation KSimpleFeedCellComment

- (id)copyWithZone:(NSZone *)zone
{
    KSimpleFeedCellComment *copy = [[KSimpleFeedCellComment allocWithZone:zone] init];
    copy.ugcCommentLists = [self.ugcCommentLists copy];
    return copy;
}

@end

@implementation KSimpleFeedCellGiftWorksRankItem

- (id)copyWithZone:(NSZone *)zone
{
    KSimpleFeedCellGiftWorksRankItem *copy = [[KSimpleFeedCellGiftWorksRankItem allocWithZone:zone] init];
    copy.userInfo = [self.userInfo copy];
    copy.accompany = [self.accompany copy];
    copy.ugcId = [self.ugcId copy];
    return copy;
}

@end

@implementation KSimpleFeedCellGiftWorksRank

- (id)copyWithZone:(NSZone *)zone
{
    KSimpleFeedCellGiftWorksRank *copy = [[KSimpleFeedCellGiftWorksRank allocWithZone:zone] init];
    copy.giftWorksRankLists = [self.giftWorksRankLists copy];
    copy.strRankTitle = [self.strRankTitle copy];
    copy.strRankDesc = [self.strRankDesc copy];
    copy.strDistrictCode = [self.strDistrictCode copy];
    return copy;
}

@end

@implementation KSimpleFeedFoldedFeeds

- (id)copyWithZone:(NSZone *)zone
{
    KSimpleFeedFoldedFeeds *copy = [[KSimpleFeedFoldedFeeds allocWithZone:zone] init];
    copy.title = self.title;
    copy.feedArray = [self.feedArray copy];
    copy.foldedNum = self.foldedNum;
    copy.jumpMoreUrl = self.jumpMoreUrl;
    copy.sourceType = self.sourceType;
    
    return copy;
}

@end


@implementation KSimpleFeedH5Jump

- (id)copyWithZone:(NSZone *)zone
{
    KSimpleFeedH5Jump *copy = [[KSimpleFeedH5Jump allocWithZone:zone] init];
    copy.mainFeedDesc = [self.mainFeedDesc copy];
    copy.subFeedDesc = [self.subFeedDesc copy];
    copy.picUrl = [self.picUrl copy];
    copy.jumpUrl = [self.jumpUrl copy];
    copy.h5Type = self.h5Type;
    copy.uid = self.uid;
    return copy;
}

@end


/**
 * KSimpleFeed
 */
@implementation KSimpleFeed

- (instancetype)init
{
    self = [super init];
    if (self) {
        self.playingTime = 0;
        self.ktvFeed_report_gameType = -1;
        self.guideType = -1;
        
        [[NSNotificationCenter defaultCenter] removeObserver:self name:KSVideo_Notification_DidPreloadVideo object:nil];
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(onNotifyPreloadVideo:) name:KSVideo_Notification_DidPreloadVideo object:nil];
        
        [[NSNotificationCenter defaultCenter] removeObserver:self name:KSAudioDidPreload object:nil];
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(onNotifyPreloadAudio:) name:KSAudioDidPreload object:nil];
        
        [[NSNotificationCenter defaultCenter] removeObserver:self name:KSTMEPlayerDidPreload object:nil];
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(onNotifyPreloadTMEPlayer:) name:KSTMEPlayerDidPreload object:nil];
        
        [[NSNotificationCenter defaultCenter] removeObserver:self name:KSVideo_Notification_DidParseVideoSize object:nil];
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(onVideoSizeParseNoti:) name:KSVideo_Notification_DidParseVideoSize object:nil];
    }
    return self;
}

- (instancetype)initWithFollowMultiSendItem:(KSFollowFeedMultiSendGiftWorkItem *)workItem {
    self = [super init];
    if (self) {
        _songinfo = KSong.new;
        _songinfo.name = workItem.strSongName;
        _simpleFeedCommon = KSimpleFeedCommon.new;
        _simpleFeedCommon.strFeedId = workItem.ugcid;
        _simpleUser = KSimpleFeedUser.new;
        _simpleUser.userinfo = KSUserInfo.new;
        _simpleUser.userinfo.userId = workItem.uid;
    }
    return self;
}

#pragma mark 播放器预加载会获取时长
- (void)onNotifyPreloadAudio:(NSNotification *)notification
{
    KSSongInfo* songInfo = [notification.userInfo safeObjectForKey:KSAudioStatusSongKey];
    KSSongExtraInfo* extraSongInfo = SAFE_CAST(songInfo, KSSongExtraInfo);
    //预加载时候获取的视频文件时长
    if (songInfo.durion>0 && extraSongInfo.ugcId.length >0 && [extraSongInfo.ugcId isEqualToString:self.simpleFeedCommon.strFeedId])
    {
        self.songinfo.duration = songInfo.durion;
        KDEBUG(@"[QQQ][%@]%ld",extraSongInfo.ugcId,(long)self.songinfo.duration);
    }
}

#pragma mark 播放器预加载会获取时长
- (void)onNotifyPreloadVideo:(NSNotification *)notification
{
    KSVideoPlayerItem* playerItme = SAFE_CAST(notification.object, KSVideoPlayerItem);
    //预加载时候获取的视频文件时长
    if (playerItme.songDuration>0 && playerItme.ugcId.length>0 && [playerItme.ugcId isEqualToString:self.simpleFeedCommon.strFeedId])
    {
        self.songinfo.duration = playerItme.songDuration;
        KDEBUG(@"[QQQ][%@]%ld",playerItme.ugcId,(long)self.songinfo.duration);
    }
}

#pragma mark 播放器预加载会获取时长
- (void)onNotifyPreloadTMEPlayer:(NSNotification *)notification
{
    KSMediaInfo *medianInfo = SAFE_CAST(notification.object, KSMediaInfo);
    if (medianInfo.songDurationMs > 0 && [medianInfo.ugcId isEqualToString:self.simpleFeedCommon.strFeedId]) {
        self.songinfo.duration = medianInfo.songDurationMs/1000;
        KDEBUG(@"[QQQ][%@]%ld",medianInfo.ugcId,(long)self.songinfo.duration);
        
        dispatch_async(dispatch_get_main_queue(), ^{
            [[NSNotificationCenter defaultCenter] removeObserver:self name:KSTMEPlayerDidPreload object:nil];
        });
    }
}

#pragma mark 播放器解析到视频大小
- (void)onVideoSizeParseNoti:(NSNotification *)notification
{
    KSVideoPlayerItem *playerItem = SAFE_CAST(notification.object, KSVideoPlayerItem);
    if (playerItem.ugcId.length > 0 && [self.simpleFeedCommon.strFeedId isEqualToString:playerItem.ugcId]) {
        NSValue *szieValue = SAFE_CAST([notification.userInfo objectForKey:@"size"], NSValue);
        CGSize videoSize = [szieValue CGSizeValue];
        if (videoSize.width > 0 && videoSize.height > 0 && [self isRecCardFeed]) {
            /// 针对大卡片预解析处理优化,（关注feed改变数据，刷新会跳变，暂不处理）
            self.songinfo.streamVideo_width = videoSize.width;
            self.songinfo.streamVideo_height = videoSize.height;
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            [[NSNotificationCenter defaultCenter] removeObserver:self name:KSVideo_Notification_DidParseVideoSize object:nil];
        });
    }
}

- (void)dealloc
{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}


- (id)copyWithZone:(NSZone *)zone
{
    KSimpleFeed *copy = [[KSimpleFeed allocWithZone:zone] init];
    copy.isExpandAllText = self.isExpandAllText;
    copy.isExpandForwardText = self.isExpandForwardText;
    copy.simpleFeedCommon = [self.simpleFeedCommon copy];
    copy.simpleUser = [self.simpleUser copy];
    copy.songinfo = [self.songinfo copy];
    copy.listenerInfo = [self.listenerInfo copy];
    copy.comment = [self.comment copy];
    copy.recivedFlowerInfo = [self.recivedFlowerInfo copy];
    copy.lbs = [self.lbs copy];
    copy.operationFeed = [self.operationFeed copy];
    copy.relationDesc = [self.relationDesc copy];
    copy.stFeedpassback = [self.stFeedpassback copy];
    copy.isShowShare = self.isShowShare;
    copy.isShowUploading = self.isShowUploading;
    copy.ihcNum = self.ihcNum ;
    copy.competitionFeed = [self.competitionFeed copy];
    copy.giftRankInfo = [self.giftRankInfo copy];
    copy.recommendItem = [self.recommendItem copy];
    copy.liveShow =[self.liveShow copy];
    copy.HcUserInfo =[self.HcUserInfo copy];
    copy.recFriendsFeed = [self.recFriendsFeed copy];
    copy.recSongsCellData = [self.recSongsCellData copy];
    copy.relayGameFeed = [self.relayGameFeed copy];
    copy.musicMoodFeed = [self.musicMoodFeed copy];
    copy.beatListFeed = [self.beatListFeed copy];
    copy.forwardFeed = [self.forwardFeed copy];
    copy.forwardInfo = [self.forwardInfo copy];
    copy.submissionFeed =[self.submissionFeed copy];
    copy.isRemoved = self.isRemoved;
    copy.removedMsg = [self.removedMsg copy];
    copy.giftWorksRank = [self.giftWorksRank copy];
    copy.abTestReport = [self.abTestReport copy];
    copy.feedFilterMask = self.feedFilterMask;
    copy.likeInfo = self.likeInfo;
    copy.tagsInfoV2 = self.tagsInfoV2;
    copy.topicsArray = [self.topicsArray copy];
    copy.topicFeed = [self.topicFeed copy];
    copy.loadRespTime = self.loadRespTime;
    copy.isCacheFeed = self.isCacheFeed;
    return copy;
}

//这里重写encode和decode的原因是很多属性不是继承ksbasemodel, encodeWithCoder unrecognized selector
- (void)encodeWithCoder:(NSCoder *)encoder
{
    
    [encoder encodeObject:self.lbs forKey:@"lbs"];
    [encoder encodeObject:self.simpleFeedCommon forKey:@"simpleFeedCommon"];
    [encoder encodeObject:self.simpleUser forKey:@"simpleUser"];
    [encoder encodeObject:self.songinfo forKey:@"songinfo"];
    [encoder encodeObject:self.listenerInfo forKey:@"listenerInfo"];
    [encoder encodeObject:self.cell_share forKey:@"cell_share"];
    [encoder encodeObject:self.comment forKey:@"comment"];
    [encoder encodeObject:self.recivedFlowerInfo forKey:@"recivedFlowerInfo"];
    
    [encoder encodeInteger:self.feedVCType forKey:@"feedVCType"];
    [encoder encodeObject:self.secretaryFeed forKey:@"secretaryFeed"];
    [encoder encodeObject:self.shortVideoFeed forKey:@"shortVideoFeed"];
    [encoder encodeObject:self.competitionFeed forKey:@"competitionFeed"];
    [encoder encodeObject:self.giftRankInfo forKey:@"giftRankInfo"];    
    [encoder encodeObject:self.soloAlbumInfo forKey:@"soloAlbumInfo"];
    [encoder encodeObject:self.payAlbumInfo forKey:@"payAlbumInfo"];
    [encoder encodeObject:self.recSongsCellData forKey:@"recSongsCellData"];
    [encoder encodeObject:self.recFriendsFeed forKey:@"recFriendsFeed"];
    [encoder encodeObject:self.beatListFeed forKey:@"beatListFeed"];
    [encoder encodeObject:self.cellComment forKey:@"cellComment"];
    [encoder encodeObject:self.liveShow forKey:@"liveShow"];
    [encoder encodeObject:self.ktvRoomShow forKey:@"ktvRoomShow"];
    [encoder encodeObject:self.relayGameFeed forKey:@"relayGameFeed"];
    [encoder encodeObject:self.forwardInfo forKey:@"forwardInfo"];
    [encoder encodeObject:self.forwardFeed forKey:@"forwardFeed"];
    [encoder encodeObject:self.submissionFeed forKey:@"submissionFeed"];
    [encoder encodeObject:self.traceReportPassBack forKey:@"traceReportPassBack"];
    [encoder encodeObject:self.recommendItem forKey:@"recommendItem"];
    [encoder encodeObject:self.ugcRemark forKey:@"ugcRemark"];
    [encoder encodeObject:self.playInfo forKey:@"playInfo"];
    [encoder encodeObject:self.relationDesc forKey:@"relationDesc"];
    [encoder encodeObject:self.musicMoodFeed forKey:@"musicMoodFeed"];
    [encoder encodeObject:self.abTestReport forKey:@"abTestReport"];
    // 推荐feed配置掩码
    [encoder encodeObject:@(self.hotFeedItemElementType) forKey:@"hotFeedItemElementType"];
    
    [encoder encodeObject:self.songInfoResult forKey:@"songInfoResult"];
    [encoder encodeInteger:self.feedFilterMask forKey:@"feedFilterMask"];
    
    [encoder encodeObject:self.topicsArray forKey:@"topicsArray"];
    [encoder encodeObject:self.topicFeed forKey:@"topicFeed"];
    // 视频化信息缓存
    [encoder encodeObject:self.videolizeProduct forKey:@"videolizeProduct"];
    [encoder encodeObject:self.socialGameShow forKey:@"socialGameShow"];
    [encoder encodeObject:@(self.loadRespTime) forKey:@"loadRespTime"];
}

//这里重写encode和decode的原因是很多属性不是继承ksbasemodel, encodeWithCoder unrecognized selector crash
- (id)initWithCoder:(NSCoder *)decoder
{
    self = [super initWithCoder:decoder];
    if (self)
    {
        self.lbs = [decoder decodeObjectForKey:@"lbs"];
        self.simpleFeedCommon = [decoder decodeObjectForKey:@"simpleFeedCommon"];
        self.simpleUser = [decoder decodeObjectForKey:@"simpleUser"];;
        self.songinfo = [decoder decodeObjectForKey:@"songinfo"];
        self.listenerInfo = [decoder decodeObjectForKey:@"listenerInfo"];
        self.cell_share = [decoder decodeObjectForKey:@"cell_share"];
        self.recivedFlowerInfo = [decoder decodeObjectForKey:@"recivedFlowerInfo"];
        self.comment = [decoder decodeObjectForKey:@"comment"];
        self.feedVCType = [decoder decodeIntegerForKey:@"feedVCType"];
        self.secretaryFeed = [decoder decodeObjectForKey:@"secretaryFeed"];
        self.shortVideoFeed = [decoder decodeObjectForKey:@"shortVideoFeed"];
        self.competitionFeed = [decoder decodeObjectForKey:@"competitionFeed"];
        self.giftRankInfo = [decoder decodeObjectForKey:@"giftRankInfo"];
        self.soloAlbumInfo = [decoder decodeObjectForKey:@"soloAlbumInfo"];
        self.payAlbumInfo = [decoder decodeObjectForKey:@"payAlbumInfo"];
        self.recSongsCellData = [decoder decodeObjectForKey:@"recSongsCellData"];
        self.recFriendsFeed = [decoder decodeObjectForKey:@"recFriendsFeed"];
        self.beatListFeed = [decoder decodeObjectForKey:@"beatListFeed"];
        self.cellComment = [decoder decodeObjectForKey:@"cellComment"];
        self.liveShow = [decoder decodeObjectForKey:@"liveShow"];
        self.ktvRoomShow = [decoder decodeObjectForKey:@"ktvRoomShow"];
        self.relayGameFeed = [decoder decodeObjectForKey:@"relayGameFeed"];
        self.forwardInfo = [decoder decodeObjectForKey:@"forwardInfo"];
        self.forwardFeed = [decoder decodeObjectForKey:@"forwardFeed"];
        self.submissionFeed = [decoder decodeObjectForKey:@"submissionFeed"];
        self.traceReportPassBack = [decoder decodeObjectForKey:@"traceReportPassBack"];
        self.recommendItem = [decoder decodeObjectForKey:@"recommendItem"];
        self.ugcRemark = [decoder decodeObjectForKey:@"ugcRemark"];
        self.playInfo = [decoder decodeObjectForKey:@"playInfo"];
        self.relationDesc = [decoder decodeObjectForKey:@"relationDesc"];
        self.musicMoodFeed = [decoder decodeObjectForKey:@"musicMoodFeed"];
        self.abTestReport = [decoder decodeObjectForKey:@"abTestReport"];
        // 推荐feed配置掩码
        NSNumber *hotFeedType = [decoder decodeObjectForKey:@"hotFeedItemElementType"];
        if ([hotFeedType isKindOfClass:NSNumber.class])
        {
            self.hotFeedItemElementType = [hotFeedType integerValue];
        }
        
        self.songInfoResult = [decoder decodeObjectForKey:@"songInfoResult"];
        
        self.feedFilterMask = [decoder decodeIntegerForKey:@"feedFilterMask"];
        
        self.topicsArray = [decoder decodeObjectForKey:@"topicsArray"];
        self.topicFeed = [decoder decodeObjectForKey:@"topicFeed"];
        // 视频化信息取缓存
        self.videolizeProduct = [decoder decodeObjectForKey:@"videolizeProduct"];
        self.socialGameShow = [decoder decodeObjectForKey:@"socialGameShow"];
        self.loadRespTime = [[decoder decodeObjectForKey:@"loadRespTime"] floatValue];
    }
    return self;
}

+ (KSimpleFeed *)feedWithSoloAlbumTask:(KSSoloAlbumCreateTaskInfo *)albumTask
{
    KSimpleFeed *feed = [[KSimpleFeed alloc] init];
    feed.relationDesc = KString(@"我的"); // 关系
    
    //comm
    KSimpleFeedCommon *common = [[KSimpleFeedCommon alloc] init];
    common.strShareid = albumTask.soloAlbumShareId;
    common.uFeedTime = [[NSDate date] timeIntervalSince1970];
    feed.simpleFeedCommon = common;
    
    //user
    KSimpleFeedUser *user = [[KSimpleFeedUser alloc] init];
    KSUserInfo *userInfo = [[KSUserInfo alloc] init];
    user.userinfo = userInfo;
    user.userinfo.userId = [[KSLoginManager sharedInstance] curUserInfo].userId; // uid 用于下载头像
    user.userinfo.nickName = [[KSLoginManager sharedInstance] curUserInfo].nickName; // nickname 用于绘制昵称
    user.userinfo.sAuthName = [[KSLoginManager sharedInstance] curUserInfo].sAuthName; // 用于绘制代言人图标
    feed.simpleUser = user;
    
    //soloAlbumInfo
    KSimpleFeedSoloAlbum *soloAlbumInfo = [[KSimpleFeedSoloAlbum alloc] init];
    soloAlbumInfo.strAlbumId = albumTask.albumID;
    soloAlbumInfo.strAlbumName = albumTask.albumName;
    soloAlbumInfo.strAlbumDesc = albumTask.albumDes;
    soloAlbumInfo.iUgcNum = [albumTask.albumUgcArray count];
    soloAlbumInfo.soloAlbumShareId = albumTask.soloAlbumShareId;
    
    NSInteger showCount = min(3, [albumTask.albumUgcArray count]);
    NSMutableArray *topThreeUgcNames = [[NSMutableArray alloc] init];
    for (int i = 0; i < showCount; i ++ ) {
        KSTimelineDetail *detail = [albumTask.albumUgcArray objectAtIndex:i];
        [topThreeUgcNames addObject:detail.songName];
    }
    soloAlbumInfo.topThreeUgcNames = topThreeUgcNames;

    if (albumTask.albumCoverUrl) {
        KSImage *cover = [[KSImage alloc] init];
        cover.imageUrl = albumTask.albumCoverUrl;
        soloAlbumInfo.coverurls = @{ @(200): cover };
    }
    else if(albumTask.albumCoverFilePath){
        KSImage *cover = [[KSImage alloc] init];
        cover.localFilePath = albumTask.albumCoverFilePath;
        soloAlbumInfo.coverurls = @{ @(200): cover };
    }
    feed.soloAlbumInfo = soloAlbumInfo;
    
    //num info
    KSimpleFeedListener *listener = [[KSimpleFeedListener alloc] init]; // 人数 0
    listener.number = 0;
    feed.listenerInfo = listener;
    
    KSimpleFeedComment *comment = [[KSimpleFeedComment alloc] init]; // 评论数 0
    comment.number = 0;
    feed.comment = comment;
    
    KSimpleFeedFlower *flower = [[KSimpleFeedFlower alloc] init]; // 鲜花数 0
    flower.number = 0;
    feed.recivedFlowerInfo = flower;
    
    
    return feed;
}

+ (void)cacheSong:(KSPublishContent *)publishContent
{
    [[KSNewAudioPlayerManager sharedInstance] cacheLocalFile:publishContent.songFile fileVid:[FileUploadEx songVid] fileExt:KSAudioFileExt removeSrc:NO];
    
    // 存储一份假的ugc缓存
    KSTimelineDetail *ugc = [[KSTimelineDetail alloc] init];
    ugc.userInfo = [KSLoginManager sharedInstance].curUserInfo;
    ugc.ugcId = publishContent.ugcId;
    ugc.content = publishContent.contentDesc;
    ugc.publishTime = (NSInteger)[publishContent.songDate timeIntervalSince1970];
    ugc.vid = [FileUploadEx songVid];
    ugc.ksongMid = publishContent.songMid;
    ugc.songName = publishContent.songName;
    ugc.coverImageUrl = publishContent.photoUrl;
    ugc.score = publishContent.songScore;
    ugc.numOfGift = ugc.numOfPlay = ugc.numOfComment = ugc.numOfForward =0;
    ugc.activityId = publishContent.activityId;
    ugc.shareId = [FileUploadEx shareIdForKey:publishContent.productMid];
    ugc.fileMid = publishContent.fileMid2;
    ugc.sentenceCount = publishContent.sentenceCount;
    ugc.isSegment = publishContent.bSegment;
    ugc.segmentStart = publishContent.segmentStart;
    ugc.segmentEnd = publishContent.segmentStop;
    ugc.commentList = [@[] mutableCopy];
    ugc.allowBulletCurtain = [[[[WnsConfigManager sharedInstance] appConfig] kSongSetting] SettingBarrage];
    ugc.level = [KSLoginManager sharedInstance].curUserInfo.mainLevel;
    
    if (publishContent.videolizePublishContent)
    {
        ugc.ugc_mask_ext |= [publishContent.videolizePublishContent ugc_mask_ext];
        ugc.videolizeProduct = [publishContent.videolizePublishContent toVideolizeProduct];
    }
    
    if (publishContent.fileKBitRate > KBitRateTHRESHOLD256)
    {
        ugc.jceUgcMask =  JceTimeline_Detail_KGE_UGC_MASK_BIT_KGE_UGC_MASK_HQ;
    }
    
    [[KSTimelineDetailManager sharedManager] storeOrUpdateUgc:ugc];
    // 存playback
    KSFeedPlayback *playback = [[KSFeedPlayback alloc] init];
    playback.sVid = [FileUploadEx songVid];
    playback.vFid = @[ [FileUploadEx songVid] ? [FileUploadEx songVid] : @"" ];
//    playback.vUrl = @[ @"" ];
    playback.ugcId = publishContent.ugcId;
    playback.vUrl = [@[ @""] mutableCopy];
    [KSPlaybackRequester storeOrUpdateUgcPlayback:playback];
}

/**
 根据 itemType 得到判断是否投稿类型，并且返回 eReportSource，枚举值见 JcePlayReport_eReportSource
 
 @note 注意，如果外部有赋值eReportSource，会以eReportSource中的字段为准返回
 @return ≤ 0 表示非投稿，> 0 才是投稿。值见 JcePlayReport_eReportSource
 */
- (NSInteger)getReportSourceForContribute
{
    if (self.eReportSource != 0)
    {
        return self.eReportSource;
    }
    
    NSInteger eReportSource = 0;
    if ([self.lbs.strDistance rangeOfString:KString(@"投稿")].length>0)
    {
        eReportSource = JcePlayReport_eReportSource_E_CONTRIBUTION_SOURCE;
    }
    else
    {
        eReportSource = [KSPlayReport getReportSourceForContributeWithItemType:self.recommendItem.jce_uiItemType];
    }
    
    if (eReportSource== 0)
    {
        eReportSource = [KSLayoutableTimelineFeedCellV2 getListenCountSourceAccordingCurrentVC:self];
    }
    
    return eReportSource;
}

#pragma mark 是否有效的片段 目前在大卡片用
- (BOOL)isValidSegmentTime
{
    if (self.songinfo.iStartTime < 0) {
        // 作品的录制开始时间有问题，放弃整个逻辑
        return NO;
    }
    
    // 作品录制时间包含了整个高潮片段
    // iStartTime <= chorusSegmentStart <= chorusSegmentEnd <= iEndTime
    if (self.songinfo.hasChorusSegment) {
        if (self.songinfo.chorusSegmentStart <= self.songinfo.iEndTime
            && self.songinfo.chorusSegmentStart >= self.songinfo.iStartTime
            && self.songinfo.chorusSegmentEnd <= self.songinfo.iEndTime
            && self.songinfo.chorusSegmentEnd >= self.songinfo.iStartTime
            && self.songinfo.chorusSegmentStart <= self.songinfo.chorusSegmentEnd) {
            return YES;
        }
    }
    return NO;
}

/// 获取有效的seekTime
- (CGFloat)getValidSeekTime
{
    CGFloat passSeekTime = 0.0;
    if ([self isValidSegmentTime]) {
        passSeekTime = self.songinfo.chorusSegmentStart / 1000;
        if (self.songinfo.iEndTime - passSeekTime < 3000) {
            // 如果这个 seek 点会导致播放时间不足 3s 也放弃掉
            passSeekTime = 0.0;
            
        } else {
            passSeekTime = (self.songinfo.chorusSegmentStart - self.songinfo.iStartTime) / 1000;
        }
    }
    KDEBUG(@"chorusSegmentStart = %ld", (long)self.songinfo.chorusSegmentStart);
    KDEBUG(@"chorusSegmentEnd = %ld", (long)self.songinfo.chorusSegmentEnd);
    KDEBUG(@"iStartTime = %ld", (long)self.songinfo.iStartTime);
    KDEBUG(@"iEndTime = %ld", (long)self.songinfo.iEndTime);
    KDEBUG(@"passSeekTime = %ld", (long)passSeekTime);
    return passSeekTime;
}

/// 获取有效的seekEnd
- (CGFloat)getValidSeekEndTime
{
    CGFloat passSeekEndTime = 0.0;
    if ([self isValidSegmentTime]) {
        passSeekEndTime = (self.songinfo.chorusSegmentEnd - self.songinfo.iStartTime) / 1000;;

    }
    return passSeekEndTime;
}

#pragma mark - 获取推荐大卡片直播高光时刻信息
// 是否是直播间高光时刻回放推荐
- (BOOL)isLiveShowHighlightRec
{
    return !IS_EMPTY_STR_BM(self.liveShowHighlight.highlightUrl);
}

- (KSimpleFeedLiveShowHighlight *)liveShowHighlight
{
    if ([self isKindOfLiveShowFeed]) {
        KSimpleFeedLiveShowHighlight *highlight = nil;
        if (self.liveShow.mapExt) {
            highlight = [[KSimpleFeedLiveShowHighlight alloc] init];
            NSDictionary *mapExt = self.liveShow.mapExt;
            highlight.highlightUrl = SAFE_STR_BM([mapExt safeObjectForKey:@"strHighLightPlayUrl"]);
            highlight.highlightSongName = SAFE_STR_BM([mapExt safeObjectForKey:@"strHighLightSongName"]);
            highlight.highlightStartTs = [SAFE_STR_BM([mapExt safeObjectForKey:@"uHighLightStartTs"]) integerValue];
            highlight.highlightEndTs = [SAFE_STR_BM([mapExt safeObjectForKey:@"uHighLightEndTs"]) integerValue];
            highlight.highlightRecordId = SAFE_STR_BM([mapExt safeObjectForKey:@"strHighLightRecordId"]);
            highlight.highlightRecommendType = SAFE_STR_BM([mapExt safeObjectForKey:@"strHighLightReccType"]);
            highlight.highlightTips = SAFE_STR_BM([mapExt safeObjectForKey:@"strHighLightTips"]);
            highlight.highlightSongMid = SAFE_STR_BM([mapExt safeObjectForKey:@"strHighLightKMid"]);
        }
        return highlight;
    }
    return nil;
}

#pragma mark - 是否要播放同一个vid文件
- (BOOL)hasValidVid
{
    if (self.isTemplateVideo)
    {
        return self.songinfo.strAccVideoVid.length > 0 && self.songinfo.strVId.length > 0;
    }
    else
    {
        return self.songinfo.strVId.length > 0;
    }
}

- (NSString*)description
{
    NSMutableString* temp = [NSMutableString new];
    [temp appendString:[NSString stringWithFormat:@"feedId=%@",self.simpleFeedCommon.strFeedId]];
    if (self.songinfo)
    {
        if (UGC_HALF_CHORUS(self.songinfo.ugcMask) == HALF_CHORUS_UGC)
        {
            [temp appendString:@"半成品"];
        }
        if(UGC_TYPE(self.songinfo.ugcMask) == MUSIC_VIDEO_UGC)
        {
            [temp appendString:@"视频"];
        }
        else
        {   //KTV模式用类似MV的Feed
            if ([self isKTVAudioFeed]) {
                [temp appendString:@"原曲MV视频"];
            }
            else if (UGC_IS_SHORT_AUDIO(self.songinfo.ugcMaskExt))
            {
                [temp appendString:@"短音频"];
            }
            else
            {
                if (self.musicMoodFeed) {
                    [temp appendString:@"音频(音乐心情)"];
                } else {
                    [temp appendString:@"音频"];
                }
            }
        }
        
        [temp appendString:[NSString stringWithFormat:@"song=%@,mid=%@",self.songinfo.name,self.songinfo.songMid]];
    }
    else if (self.relayGameFeed)
    {
        [temp appendString:[NSString stringWithFormat:@"relaygame=%@",self.relayGameFeed.strRoomId]];
    }
    else if (self.payAlbumInfo)
    {
        [temp appendString:[NSString stringWithFormat:@"payAlbum=%@",self.payAlbumInfo.strAlbumId]];
    }
    else if (self.soloAlbumInfo)
    {
        [temp appendString:[NSString stringWithFormat:@"soloAlbum=%@",self.soloAlbumInfo.strAlbumId]];
    }
    else if (self.liveShow)
    {
        [temp appendString:[NSString stringWithFormat:@"liveshow=%@",self.liveShow.strShowId]];
    }
    else if (self.ktvRoomShow)
    {
        [temp appendString:[NSString stringWithFormat:@"KTVshow=%@",self.ktvRoomShow.jce_strShowId]];
    }
    else if (self.ktvRoomMike)
    {
        [temp appendString:[NSString stringWithFormat:@"KTVmike=%@",self.ktvRoomMike.jce_strShowId]];
    }
    else if (self.socialGameShow)
    {
        [temp appendString:[NSString stringWithFormat:@"socialGameShow=%@",self.socialGameShow.jce_strGameAppID]];
    }
    else if (self.forwardFeed)
    {
        [temp appendString:[NSString stringWithFormat:@"Forward=%@",self.forwardFeed.strForwardId]];
    }
    else if (self.feedAd)
    {
        [temp appendString:[NSString stringWithFormat:@"feedAd=%@",self.feedAd.jce_advertId]];
    }
    else if(self.recSongsCellData)
    {
        [temp appendString:[NSString stringWithFormat:@"recSongsCellData"]];
    }
    else if(self.familyUgc)
    {
        [temp appendString:[NSString stringWithFormat:@"familyUgc"]];
    }
    else if(self.secretaryFeed)
    {
        [temp appendString:[NSString stringWithFormat:@"secretaryFeed"]];
    }
    else if(self.beatListFeed)
    {
        [temp appendString:[NSString stringWithFormat:@"beatListFeed"]];
    }
    else if(self.recFriendsFeed)
    {
        [temp appendString:[NSString stringWithFormat:@"recFriendsFeed"]];
    }
    else if(self.ugcRemark)
    {
        [temp appendString:[NSString stringWithFormat:@"ugcRemark"]];
    }
    else if(self.relayGameFeed)
    {
        [temp appendString:[NSString stringWithFormat:@"relayGameFeed"]];
    }
    else if(self.topicRecSingerFeed)
    {
        [temp appendString:[NSString stringWithFormat:@"topicRecSingerFeed"]];
    }
    else
    {
        [temp appendString:[NSString stringWithFormat:@"feedtype=%ld",self.simpleFeedCommon.uTypeid]];
    }
    
    
    if (self.songinfo.chorusSegmentStart>0)
    {
        [temp appendString:[NSString stringWithFormat:@"高潮=%ld_%ld_%ld",(long)self.songinfo.hasChorusSegment,(long)self.songinfo.chorusSegmentStart,(long)self.songinfo.chorusSegmentEnd]];
    }
    
    if (self.songinfo.playUrlInfo.jce_strPlayUrl.length>0)
    {
        [temp appendString:@"有链接"];
    }
    else
    {
        [temp appendString:@"无链接"];
    }
    
    return temp;
}


//是否跳转链接类型的feed
- (BOOL)isKindOfLinkTypeFeed
{
    if (self.competitionFeed || self.ugcRemark || self.relayGameFeed)
    {
        return YES;
    }
    else
    {
        return NO;
    }
}

//是否KTV类型的feed
- (BOOL)isKindOfKTVRoomFeed
{
    if (self.ktvRoomShow || self.ktvRoomMike)
    {
        return YES;
    }
    else
    {
        return NO;
    }
}

#pragma 是否交友歌房
- (BOOL)isFriendKTVFeed
{
    BOOL isFreindKTVFeed = NO;
    
    if ((self.ktvRoomShow.jce_iRoomType & KSKTVRoomType_Multi) == KSKTVRoomType_Multi || (self.ktvRoomMike.jce_iRoomType & KSKTVRoomType_Multi) == KSKTVRoomType_Multi)
    {
        isFreindKTVFeed = YES;
    }
    
    return isFreindKTVFeed;
}

#pragma 是否单麦歌房
- (BOOL)isSingKTVFeed
{
    BOOL isSingKTVFeed = NO;
    
    if ((self.ktvRoomShow.jce_iRoomType & KSKTVRoomType_Single) == KSKTVRoomType_Single || (self.ktvRoomMike.jce_iRoomType & KSKTVRoomType_Single) == KSKTVRoomType_Single)
    {
        isSingKTVFeed = YES;
    }
    
    return isSingKTVFeed;
}

- (BOOL)isSocialKTVFeed
{
    BOOL isSocialKTVFeed = NO;
    if ((self.ktvRoomShow.jce_iRoomType &KSKTVRoomType_Social) == KSKTVRoomType_Social) {
        isSocialKTVFeed = YES;
    }
    
    return isSocialKTVFeed;
}


- (BOOL)isKtvRoomCard
{
    if (self.bigCardKtvItem) {
        return YES;
    } else {
        return NO;
    }
}

- (BOOL)isKindOfGameAdvertTypeFeed
{
    if (self.gameAdvert) {
        return YES;
    } else {
        return NO;
    }
}

- (BOOL)isKindOfVideoAdvertisementFeed
{
    // Remember:新增视频广告类型时需要添加判断条件
    if ((self.feedAd && (self.feedAd.jce_advertType == JceTimeline_enum_advertType_AdvertTypeVideo || self.feedAd.jce_advertType == JceTimeline_enum_advertType_AdvertTypeVerticalVideo))
        || [self isRecCardAdFeed] || self.amsFeedAd.isVideoAd || [self isRecCardTMEAdFeed] || self.tmeFeedAd.isVideoAd)
    {
        return YES;
    }
    return NO;
}

- (BOOL)isKindOfVideoTypeFeed {
    if (UGC_TYPE(self.songinfo.ugcMask) == VIDEO_UGC &&
        self.songinfo.audioTransVideoPlayType != JceTimeline_EnumAudioTransVideoPlayType_eAudioTransVideo_type_native_tempalte_video) {
        return YES;
    }
    else {
        return NO;
    }
}

- (BOOL)isKindOfAudioTypeFeed
{
    if (self.songinfo && ((UGC_TYPE(self.songinfo.ugcMask) == AUDIO_UGC) || self.songinfo.audioTransVideoPlayType == JceTimeline_EnumAudioTransVideoPlayType_eAudioTransVideo_type_native_tempalte_video))
    {
        return YES;
    }
    else
    {
        return NO;
    }
}


/// 是否是ugc类型feed，除广告，直播，歌房外都算ugcfeee
/// AI歌声推荐类型不属于ugc feed
- (BOOL)isKindOfUgcTypeFeed
{
    if ([self isKindOfGameAdvertTypeFeed]) {
        return NO;
    }
    if ([self isKindOfVideoAdvertisementFeed]) {
        return NO;
    }
    if ([self isAiSongRec]) {
        return NO;
    }
    if ([self isAISongPublish]) {
        return NO;
    }
    if ([self isKindOfAIImageFeed]) {
        /// AI图文
        return NO;
    }
    
    return ([self isKindOfVideoTypeFeed] || [self isKindOfAudioTypeFeed]);
}

- (BOOL)isKindOfVideoAlbumProduct
{
    if (self.songinfo && (self.songinfo.ugcMaskExt & [KSVideolizeAlbumProduct ugc_mask_ext]))
    {
        return YES;
    }
    else
    {
        return NO;
    }
}
//是否有活动信息
- (BOOL)haveCompetitionJumpInfo
{
    if (self.competitionJumpInfoDict && self.competitionJumpInfo)
    {
        return YES;
    }
    else
    {
        return NO;
    }
}

- (BOOL)isKindOfVipMarketingFeed
{
    if (self.vipMarketingModel) {
        return YES;
    }
    return NO;
}

- (JceTimeline_CompetitionJumpInfo *)getCompetitionJumpInfoType:(JceTimeline_ENUM_COMPETITION_JUMP_ID_TYPE)type
{
    JceTimeline_CompetitionJumpInfo *competitionJumpInfo = [self.competitionJumpInfoDict.jce_mapId2CompetitionJumpInfo  objectForKey:[NSNumber numberWithInt:JceTimeline_ENUM_COMPETITION_JUMP_ID_TYPE_ENUM_COMPETITION_JUMP_ID__CLASSICAL_OLDSONG]];
    if (competitionJumpInfo)
    {
        self.competitionJumpInfo = competitionJumpInfo;
    }
    else
    {
        self.competitionJumpInfo = nil;
    }

    return self.competitionJumpInfo;

}

//锚点统一的需求需要更改数据源
- (JceTimeline_CompetitionJumpInfo*)competitionJumpInfo
{
    if (self.tagsInfoV2.jce_labelAnchorPoint.jce_iAnchorPointType == JceTimeline_EnumAnchorPointType_eAnchorPointType_activity)
    {
        JceTimeline_CompetitionJumpInfo* tempCompetion = [JceTimeline_CompetitionJumpInfo new];
        tempCompetion.jce_strDesc = self.tagsInfoV2.jce_labelAnchorPoint.jce_strText;
        tempCompetion.jce_strIconUrl = self.tagsInfoV2.jce_labelAnchorPoint.jce_strIcon;
        tempCompetion.jce_strJumpUrl = self.tagsInfoV2.jce_labelAnchorPoint.jce_strJumpUrl;
        tempCompetion.jce_strBackUpIconUrl = self.tagsInfoV2.jce_labelAnchorPoint.jce_strBackgroundPicUrl;
        return tempCompetion;
    }
    else
    {
        return _competitionJumpInfo;
    }
}

// 普通录歌中的ktv模式 (KTV模式的音频)
- (BOOL)isKTVAudioFeed {
    if ((self.songinfo.ugcMaskExt & (JceTimeline_Detail_KGE_UGC_MASK_EXT_BIT_KGE_UGC_MASK_EXT_MV_PLAY_MODEL)) == JceTimeline_Detail_KGE_UGC_MASK_EXT_BIT_KGE_UGC_MASK_EXT_MV_PLAY_MODEL) {
        return YES;
    }
    return NO;
}

- (BOOL)isKindOfFeedWithVIPTag {
    NSString *payIconStr = nil;
    if (self.payAlbumInfo)
    {
        payIconStr = [[KSPaySoloAlbumManager sharedManager] updateAndReturnPayDescWithPayRight:self.payAlbumInfo.mapRight contentID:self.simpleFeedCommon.strFeedId andPayType:KSUserPayTypeOptionAlbum];
    }
    else if (self.songinfo)
    {
        /*** 付费标识start ***/
        if (GetFlexBOOL([[KSPaySoloAlbumManager sharedManager] isPayUgcWithUgcMask:self.songinfo.ugcMask])) {
            payIconStr = [[KSPaySoloAlbumManager sharedManager] updateAndReturnPayDescWithPayRight:self.songinfo.mapRight contentID:self.simpleFeedCommon.strFeedId andPayType:KSUserPayTypeOptionUGC];
        }
        /*** 付费标识end ***/
        
    }
    
    if ([payIconStr isEqualToString:kConstVipStr]) {
        return YES;
    } else {
        return NO;
    }
}

//vip智能音效
- (BOOL)isVIPSoundEffect;
{
    BOOL bVIP = (self.songinfo.ugcMaskExt & JceTimeline_Detail_KGE_UGC_MASK_EXT_BIT_KGE_UGC_MASK_EXT_VIP_EFFECT);
    bVIP = bVIP && (self.soundEffect.jce_iReverbType == 0 && self.soundEffect.jce_strReverbName.length == 0);
    return bVIP;
}

//vip红石下发音效
- (BOOL)isVIPSoundEffectRedStone;
{
    //与vip智能音效共享同一个mask
    BOOL bVIP = (self.songinfo.ugcMaskExt & JceTimeline_Detail_KGE_UGC_MASK_EXT_BIT_KGE_UGC_MASK_EXT_VIP_EFFECT);
    bVIP = bVIP && !(self.soundEffect.jce_iReverbType == 0 && self.soundEffect.jce_strReverbName.length == 0);
    return bVIP;
}

// 好声音是否上榜
- (BOOL)isHaveGoodVoiceRank;
{
    BOOL bRet = IS_EMPTY_STR_BM(self.songinfo.goodVoiceRankContent);
    return !bRet;
}

- (BOOL)isKindOfLiveShowFeed
{
    if (self.liveShow) {
        return YES;
    }
    else
    {
        return NO;
    }
}

- (BOOL)isKindOfProductCardFeed
{
    if (self.productCardModel) {
        return YES;
    }
    else
    {
        return NO;
    }
}

- (BOOL)isKindOfAIImageFeed
{
    if (self.AiImageText) {
        return YES;
    }
    else
    {
        return NO;
    }
}


// 作品是否使用了临境音质伴奏
- (BOOL)isImmersiveQualityFeed
{
    return self.songinfo.superSoundConf.jce_nSuperSoundType == JceKSongInfo_emSuperSoundFileType_EM_SOUPER_SOUND_FILE_TYPE_IMMERSIVE;
}

// 是否使用了录唱预览页的临境音效
- (BOOL)isImmersiveEffectFeed
{
    return self.songinfo.ugcMaskExt1 & JceTimeline_Detail_KGE_UGC_MASK_EXT1_IMMERSIVE_SOUND_EFFECT;
}

// 是否是运营推流直播feed
- (BOOL)isKindOfPushStreamLiveShow
{
    return [self isKindOfLiveShowFeed] && self.liveShow.isPushStreamLive && self.liveShow.streamType != LiveFeedBigCardStreamType_VIDEO;
}

- (BOOL)isLiveFeedStreamCover
{
    NSString *cdnUrl = [self.liveShow.mapExt safeObjectForKey:@"strCdnUrl"];
    return !IS_EMPTY_STR_BM(cdnUrl);
}

- (BOOL)isLiveGameFeed
{
    return self.liveShow.gameDispatch.jce_strCdnUrl.length > 0;
}

- (NSString *)liveshowCDNUrl
{
    // 弹幕游戏双路流需求，后台提供了barrageId和stream后，大窗播放gameUrl
    NSString *playUrl = [self.liveShow.mapExt safeObjectForKey:@"strCdnUrl"];
    if ([[self.liveShow.mapExt safeObjectForKey:@"runBarrageGameId"] integerValue] > 0)
    {
        NSString *gameUrl = [self.liveShow.mapExt safeObjectForKey:@"runBarrageGameStream"];
        if ([gameUrl hasPrefix:@"http"])
        {
            playUrl = gameUrl;
            KLog(@"[弹幕双路流]：推荐大卡片返回gameUrl作为视频流地址");
        }
    }
    return playUrl;
}

- (BOOL)isKindOfTmeTownFeed {
    if (self.tmeTown) {
        return YES;
    } else {
        return NO;
    }
}

//上报用
- (NSInteger)getUgcReportType
{
    NSInteger type = 0;
    
    if (self.songinfo.contestId.length>0)
    {
        type  = [self.songinfo.contestId integerValue];
    }
    
    return type;
}

//是否欢唱(单麦)歌房
- (BOOL)isPublicSingleKtvFeed
{
    BOOL isSingKtv = NO;
    
    if ((self.ktvRoomShow.jce_iRoomType & KSKTVRoomType_Multi) == KSKTVRoomType_Multi || (self.ktvRoomShow.jce_iRoomType & KSKTVRoomType_Multi_V2) == KSKTVRoomType_Multi_V2 ||
        (self.ktvRoomMike.jce_iRoomType & KSKTVRoomType_Multi) == KSKTVRoomType_Multi || (self.ktvRoomMike.jce_iRoomType & KSKTVRoomType_Multi_V2) == KSKTVRoomType_Multi_V2
        )
    {
        // 交友歌房
        isSingKtv = NO;
    }
    else if ((self.ktvRoomShow.jce_iRoomType &KSKTVRoomType_Social) == KSKTVRoomType_Social ||
             (self.ktvRoomMike.jce_iRoomType &KSKTVRoomType_Social) == KSKTVRoomType_Social)
    {
        // 欢聚歌房
        isSingKtv = NO;
    }
    else if(self.ktvRoomShow || self.ktvRoomMike)
    {
        // 欢唱(单麦)歌房
        isSingKtv = YES;
    }
    return isSingKtv;
}

- (BOOL)isMiniHeatCardFeed {
    return self.miniHeatCardItem
           && !IS_EMPTY_STR_BM(self.miniHeatCardItem.jce_strUgcid)
           && self.simpleFeedCommon.uTypeid == FEED_TYPE_MINI_HEAT_CARD;
}

#pragma 构建分享内容
- (KShareContent*)buildShareContent
{
    KShareContent *shareContent = nil;
    if (self.isMusicMoodFeed)
    {
        shareContent = [KShareContent new];
        [shareContent updateWithMusicMoodDetail:self];
        return shareContent;
    }
    else if (self.isKindOfAIImageFeed) {
        shareContent = [KShareContent new];
        [shareContent updateWithAiImageInfo:self];
        return shareContent;
    }
    else if (self.songinfo)
    {
        shareContent = [KShareContent new];
        [shareContent updateWithUGCSimpleFeed:self];
        shareContent.forwardSourceInt1 = 0;        
        return shareContent;
    }
    else if (self.soloAlbumInfo || self.payAlbumInfo)
    {
        shareContent = [KShareContent new];
        [shareContent updateWithAlbumFeed:self];
        return shareContent;
    }
    else if (self.ktvRoomShow)
    {
        shareContent = [KShareContent new];
        [shareContent updateWithKTVRoomShowSimpleFeed:self];
        shareContent.isForbiddenShare = YES;
        return shareContent;
    }
    else if (self.ktvRoomMike) {
        shareContent = [KShareContent new];
        [shareContent updateWithKTVRoomMikeSimpleFeed:self];
        shareContent.isForbiddenShare = YES;
        return shareContent;
    }
    else if (self.liveShow) {
        shareContent = [KShareContent new];
        [shareContent updateWithLiveSimpleFeed:self];
        shareContent.isForbiddenShare = YES;
        return shareContent;
    }
    
    shareContent.shareFeed = self;
    return shareContent;
}

#pragma mark 能否接受送花的feed
- (BOOL)isCanSendFlowerFeed
{
    if (self.songinfo.songMid.length>0 || self.payAlbumInfo || self.soloAlbumInfo)
    {
        if (self.liveShow || self.ktvRoomMike || self.ktvRoomShow) {
            return NO;
        }
        return YES;
    }
    else
    {
        KLog(@"[canSendFlower = NO]%@", self);
        return NO;
    }
}

#pragma mark 判断是否进行礼物榜 5s 异化的 feed
- (BOOL)isShowDissimilateGiftRankFeed
{
    if (self.layoutConfig.isHiddenGiftRank) {
        return NO;
    }
    
    if (self.liveShow || self.ktvRoomMike || self.ktvRoomShow || self.payAlbumInfo || self.soloAlbumInfo) {
        return NO;
    }
    
    // 首页推荐大卡片不需要礼物榜异化
    if ([self isRecCardFeed]) {
        return NO;
    }
    
    if ((self.songinfo.songMid.length > 0 || self.simpleFeedCommon.uTypeid == FEED_TYPE_UGC) && self.giftRankInfo.topRankArray.count == 0 && self.simpleUser.userinfo.userId != [KSLoginManager sharedInstance].curUserInfo.userId) {
        return YES;
    }
    else
    {
        return NO;
    }
}

#pragma mark - feed 作品类型

// 是否为清唱
- (BOOL)isCappella
{
    return UGC_CAPPELLA(self.songinfo.ugcMask) == CAPPELLA_UGC;
}

// 是否为合唱半成品
- (BOOL)isHalfChorus
{
    return UGC_HALF_CHORUS(self.songinfo.ugcMask);
}

// 是否为合唱成品
- (BOOL)isFinChorus
{
    return UGC_FIN_CHORUS(self.songinfo.ugcMask);
}

// 是否为收录合唱
- (BOOL)isFavorChorus
{
    return UGC_FAVOR_CHORUS(self.songinfo.ugcMask);
}

// 是否为玩法合唱
- (BOOL)isGameChorus
{
    return self.songinfo.uPlayTemplateId > 0 && (self.songinfo.ugcMaskExt & JceTimeline_Detail_KGE_UGC_MASK_EXT_INTERACTIVE) == JceTimeline_Detail_KGE_UGC_MASK_EXT_INTERACTIVE;
}

// 是否为诗朗诵
- (BOOL)isPoetry
{
    return UGC_IS_POETRY(self.songinfo.ugcMaskExt);
}

#pragma mark - 独唱作品feed展示合唱按钮样式
- (BOOL)canSoloSongShowChorusStyle
{
    // 当前设备是否可以使用 独唱作品的合唱
    BOOL isDeviceSupport =  [KSSystemInfo isDeviceSupportJoinNormalUgcChorus];
    
    // 是否是独唱作品
    BOOL isSoloSong = [KSUgcStarChorusManager canUGCChorusWithUgcMask:self.songinfo.ugcMask extMask:self.songinfo.ugcMaskExt songMid:self.songinfo.songMid];
    
    // 是否添加过礼物 (未添加礼物不进行UI异化)
    BOOL isHaveGift = (self.hcCellItem.jce_iHasGift != 0);
    
    // 独唱是否支持合唱
    BOOL isSoloHc = (self.songinfo.iIsSoloHc == 1);
    
    return isDeviceSupport && isSoloSong && isHaveGift && isSoloHc;
}

- (BOOL)canSoloSongShowChorusBtn
{
    // 当前设备是否可以使用 独唱作品的合唱
    BOOL isDeviceSupport = [KSSystemInfo isDeviceSupportJoinNormalUgcChorus];
    
    // 是否是独唱作品
    BOOL isSoloSong = [KSUgcStarChorusManager canUGCChorusWithUgcMask:self.songinfo.ugcMask extMask:self.songinfo.ugcMaskExt songMid:self.songinfo.songMid];
    
//    BOOL isEnoughRank = self.songinfo.scoreRank >= WnsSwitchIntegerConfig(kWnsConfig_SwitchConfig_FeedJoinNormalUgcChorusRank);
    
    // 独唱是否支持合唱
    BOOL isSoloHc = (self.songinfo.iIsSoloHc == 1);

#ifdef INTERNALBUILD
    if (isDeviceSupport && isSoloSong && isSoloHc)
    {
        KINFO(@"[simple_feed] ugcid: %@, name: %@, scoreRank: %ld", self.simpleFeedCommon.strFeedId, self.songinfo.name, (long)self.songinfo.scoreRank);
    }
#endif
    
    // 约唱不要展示 否则有 UI 重叠问题
    BOOL isInviteSing = (self.inviteUserInfo.nickName.length > 0);
    
    BOOL isGuest = (self.simpleUser.userinfo.userId != [KSLoginManager sharedInstance].curUserInfo.userId);
    
//    return isDeviceSupport && isSoloSong && isSoloHc && isEnoughRank && !isInviteSing && isGuest;
    return isDeviceSupport && isSoloSong && isSoloHc && !isInviteSing && isGuest;
}

-(BOOL)isCurUser
{
    return self.simpleUser.userinfo.userId == [KSLoginManager sharedInstance].curUserInfo.userId;
}

#pragma mark 获取每一种feed应该展示的首图
- (NSString*)getImageUrlFromEachKindOfFeed
{
    /**
     1. 同城feed双流，同城feed普通feed流对于自定义用户封面不展示IFrame, 优先展示头像
     2. 低版本禁止对音乐影集的使用(7.12.0~7.14.99),不展示IFrame
     3. 视频作品（包括音频转视频即视频化作品）会下发视频本身的IFrame （全量feed）
     4. 对于MV作品部分feed场景: (关注，好友, 作品tab, 个人主页, 推荐大卡片)
               .如果ugc_mask_ext携带有KGE_UGC_MASK_EXT_MV_PLAY_MODEL, 会下发MV的封面作为IFrame
               .如果用户有上传自定义IFrame, 则使用用户上传的IFrame下发
     */
    
    NSString* coverUrlStr = nil;
    
    // 图片质量
    NSNumber *currentQualityType = [KSTimelineManager sharedManager].simpleFeedImageSizeType;
    NSNumber *lowQualityType = [NSNumber numberWithInt:kSimpleFeedImageSize320];
    NSNumber *highQualityType = [NSNumber numberWithInt:kSimpleFeedImageSize640];
    
    if ([self isRecCardFeed] || (self.feedFilterMask & JceTimeline_enum_filter_mask_ENUM_FILTER_MASK_INTRA_CITY_FEED))
    {
        //抖音大卡片推荐feed优先用首帧图,没有首帧图再用封面图
        if ([self isKindOfKTVRoomFeed]) {
            // 歌房伴奏封面图
            NSString *ktvCover = [[KSExpressManager sharedManager] imageUrlForAlbumMid:self.ktvRoomShow.jce_mikeUserInfo.jce_strAlbumId size:AlbumPicSize_500];
            return ktvCover;
        }
        else if (self.songinfo.strIFrameUrl.length>0)
        {
            return self.songinfo.strIFrameUrl;
        }
        else if (self.songinfo.strCoverOrg.length>0)
        {
            return self.songinfo.strCoverOrg;
        } else if ([self isKindOfGameAdvertTypeFeed]) {
            return self.gameAdvert.jce_strCover;
        }
    }
    
    if ([self isKindOfLiveShowFeed])
    {
        //直播封面图
        NSString* liveCover = self.liveShow.strLiveCoverurl.length > 0 ? self.liveShow.strLiveCoverurl : [[KSAvatarDownloadManager defaultManager] getAvatarUrl:self.simpleUser.userinfo.userId size:AvatarPicSize_100 timestamp:self.simpleUser.userinfo.avatarTimestamp];
        return liveCover;
    }
    else if (self.ktvRoomShow.jce_strRoomId.length>0)
    {
        //歌房封面图
        return self.ktvRoomShow.jce_strCoverUrl;
    }
    else if (self.ktvRoomMike.jce_strRoomId.length>0)
    {
        //歌房排麦封面
        return self.ktvRoomMike.jce_strCoverUrl;
    }
    else if(self.competitionFeed.strJumpUrl.length>0)
    {
        //官方大赛
        return self.competitionFeed.strFeedPicUrl;
    }
    else if(self.payAlbumInfo.strAlbumId.length>0)
    {
        //付费专辑
        KSImage *coverImage = (KSImage *)[[self.payAlbumInfo.coverurls allValues] firstObject];
        return coverImage.imageUrl;
    }
    else if(self.soloAlbumInfo.strAlbumId.length>0)
    {
        //个人专辑
        KSImage *coverImage = (KSImage *)[[self.soloAlbumInfo.coverurls allValues] firstObject];
        return coverImage.imageUrl;
    }
    else if([self isAiSongRec])
    {
        return self.aiSongFeedModel.firstFrameUrl;
    }
    else if ([self isAISongPublish])
    {
        return self.aiSongCommFeedModel.firstFrameUrl;
    }
    else if (self.isKindOfAIImageFeed) {
        /// AI图文
        KSimpleFeedAiImageInfo *imageInfo = self.AiImageText.imageArr.firstObject;
        return imageInfo.imageUrl;
    }
    
    if (self.musicMoodFeed.vecPic.count > 0)
    {
        //音乐心情如果只展示首图的时候
        JceTimeline_picinfo* picInfo = [self.musicMoodFeed.vecPic safeObjectAtIndex:0];
        JceTimeline_pic_detail* picDetail = [picInfo.jce_mPic safeObjectForKey:currentQualityType];
        if (picDetail == nil && [currentQualityType isEqualToNumber:highQualityType]) {
            picDetail = [picInfo.jce_mPic safeObjectForKey:lowQualityType];
        }
        coverUrlStr = picDetail.jce_strUrl;
    }
    else if (self.songinfo.feedImageList.count > 0)
    {
        //多图feed如果只展示首图的时候
        JceTimeline_picinfo* picInfo = [self.songinfo.feedImageList safeObjectAtIndex:0];
        JceTimeline_pic_detail* picDetail = [picInfo.jce_mPic safeObjectForKey:currentQualityType];
        // 未获取到高清图时加载低清图
        if (picDetail == nil && [currentQualityType isEqualToNumber:highQualityType]) {
            picDetail = [picInfo.jce_mPic safeObjectForKey:lowQualityType];
        }
        coverUrlStr = picDetail.jce_strUrl;
    }
    else
    {
        //没有多图的作品
        if ([self.songinfo isKTVAudioUGC])
        {
            //KTV音频作品
            KSImage *coverImage = [[self.songinfo.coverurls allValues] safeObjectAtIndex:0];
            coverUrlStr = [coverImage.imageUrl stringByReplacingOccurrencesOfString:@"120x120" withString:@"300x300"];
        }
        else
        {
            if ([currentQualityType isEqualToNumber:highQualityType])
            {
                // 高清图片
                if (self.songinfo.shareHDImageUrl.length > 0) {
                    coverUrlStr = self.songinfo.shareHDImageUrl;
                } else if (self.songinfo.coverurls.count > 0) {
                    // 未获取到高清图时加载低清图
                    if ([self.songinfo.coverurls allValues].count > 0) {
                        KSImage *coverImage = [[self.songinfo.coverurls allValues] safeObjectAtIndex:0];
                        coverUrlStr = coverImage.imageUrl;
                    }
                }
                
            }
            else
            {
                // 低清图片
                KSImage *coverImage = [[self.songinfo.coverurls allValues] safeObjectAtIndex:0];
                coverUrlStr = coverImage.imageUrl;
            }
        }
    }
    return coverUrlStr;
}

#pragma mark 双排瀑布流feed的后台给来封面图大小
- (CGSize)getCoverSizeFromServer
{
    //feed封面图真正的大小
    CGSize coverSize = CGSizeZero;
    if (self.musicMoodFeed.vecPic.count > 0)
    {
        JceTimeline_picinfo* picInfo = [self.musicMoodFeed.vecPic safeObjectAtIndex:0];
        JceTimeline_pic_detail* picDetail = [picInfo.jce_mPic safeObjectForKey:[NSNumber numberWithInt:kSimpleFeedImageSize320]];
        coverSize = CGSizeMake(picDetail.jce_uiWidth, picDetail.jce_uiHeight);
    }
    else if (self.songinfo.feedImageList.count > 0)
    {
        //限制单张图的最小最大范围
        JceTimeline_picinfo* picInfo = [self.songinfo.feedImageList safeObjectAtIndex:0];
        JceTimeline_pic_detail* picDetail = [picInfo.jce_mPic safeObjectForKey:[NSNumber numberWithInt:kSimpleFeedImageSize320]];
        coverSize = CGSizeMake(picDetail.jce_uiWidth, picDetail.jce_uiHeight);
    }
    else
    {
        //没有多图的作品
        if (UGC_IS_VIDEO(self.songinfo.ugcMask))
        {
            coverSize = CGSizeMake(self.songinfo.streamVideo_width, self.songinfo.streamVideo_height);
        }
        else
        {
            NSInteger itemWidth = (SCREEN_WIDTH - KSMargin_Dynamic_20 * 2 - kMargin10) / 2;
            coverSize = CGSizeMake(itemWidth, itemWidth);
        }
    }

    return coverSize;
}
#pragma mark 双排瀑布流feed的封面布局的size
- (CGSize)getCoverDisplaySizeFromEachKindOfFeed
{
    /**
     封面图在瀑布流中仅两种展示尺寸：4:3 or 1:1（高宽比）。2020.3.24 UPDATE: 广告增加两种尺寸 16:9 or 9:16。
     若封面图尺寸 > 4:3，则裁剪为4:3
     若封面图尺寸 < 4:3，则裁剪展示为1:1
     视频取作品尺寸，音频默认1:1，音乐心情取首图尺寸
     */
    CGSize coverSize = [self getCoverSizeFromServer];
     CGFloat width = coverSize.width;
     CGFloat height = coverSize.height;
     CGFloat kHeight2Width = 4/3.0;
     CGFloat height2Width = 0.0;  //高比宽的比例
     
     // 避免 width 的值非法
     if (width > 0)
     {
         if (self.feedAd) {
            // 广告
            JceTimeline_enum_advertType advertType = self.feedAd.jce_advertType;
            height2Width = (advertType == JceTimeline_enum_advertType_AdvertTypePicture ||
                            advertType == JceTimeline_enum_advertType_AdvertTypeVideo)
            ? (9 / 16.0) : (16 / 9.0);
        } else {
            CGFloat realHeight2Width = height / width;
            height2Width = (realHeight2Width >= kHeight2Width) ? kHeight2Width : 1;
        }
     }
     else
     {
         height2Width = 1;
     }
     
     NSInteger itemWidth = (SCREEN_WIDTH - KSMargin_Dynamic_20*2 - kMargin10)/2; // 由屏幕决定
     NSInteger coverHeight = itemWidth * height2Width;
     
     // 封面图
    coverSize = CGSizeMake(itemWidth, coverHeight);
    self.songinfo.songCoverSize = coverSize;
    
    return coverSize;
}



- (NSString*)getFeedDesc
{    
    if (self.hotFeedItemElementType & JceTimeline_EnumShowMask_eShowMask_desc) {
        NSString* contentStr = self.songinfo.songDesc;
        if (contentStr.length<=0)
        {
            contentStr = self.songinfo.name;
        }
        return [contentStr stringByReplacingOccurrencesOfString:@"\n" withString:@""];
    }
    
    return nil;
}


- (NSString*)generateVoiceOverString
{
    if(!UIAccessibilityIsVoiceOverRunning())
    {
        return @"";
    }
    
    NSMutableString *readStr = [[NSMutableString alloc] init];
    
    if (self.isShowUploading) {
        [readStr appendString:KString(@"正在上传作品:")];
        if (self.songinfo.name && ![self.songinfo.name isEqualToString:@""])
        {
            [readStr appendString:self.songinfo.name];
        }
        return readStr;
    }
    
    if (self.competitionFeed) {
        if (self.simpleUser.userinfo.nickName && ![self.simpleUser.userinfo.nickName isEqualToString:@""])
        {
            [readStr appendString:[NSString stringWithFormat:KString(@"用户:%@发起了一个大赛."),self.simpleUser.userinfo.nickName]];
        }
        
        
        if (self.competitionFeed.strFeedDesc) {
            [readStr appendString:self.competitionFeed.strFeedDesc];
        }
        if (self.competitionFeed.strDesc1) {
            [readStr appendString:self.competitionFeed.strDesc1];
        }
        if (self.competitionFeed.strDesc2) {
            [readStr appendString:self.competitionFeed.strDesc2];
        }
    }
    else if (self.soloAlbumInfo) {
        [readStr appendString:[NSString stringWithFormat:KString(@"用户:%@ 创建了一张个人专辑."),self.simpleUser.userinfo.nickName]];
        [readStr appendString:[NSString stringWithFormat:KString(@"专辑名称:%@"),self.soloAlbumInfo.strAlbumName]];
        if(self.soloAlbumInfo.strAlbumDesc) {
            [readStr appendString:[NSString stringWithFormat:KString(@"并说:%@"),self.soloAlbumInfo.strAlbumDesc]];
        }
    }
    else if (self.payAlbumInfo) {
        [readStr appendString:[NSString stringWithFormat:KString(@"用户:%@ 创建了一张付费专辑."),self.simpleUser.userinfo.nickName]];
        [readStr appendString:[NSString stringWithFormat:KString(@"专辑名称:%@"),self.payAlbumInfo.strAlbumName]];
        if(self.soloAlbumInfo.strAlbumDesc) {
            [readStr appendString:[NSString stringWithFormat:KString(@"并说:%@"),self.payAlbumInfo.strAlbumDesc]];
        }
    }
    else{
        [readStr appendString:KString(@"用户:")];
        
        if (self.simpleUser.userinfo.nickName && ![self.simpleUser.userinfo.nickName isEqualToString:@""])
        {
            [readStr appendString:self.simpleUser.userinfo.nickName];
        }
        
        
        NSDate *timeDate = [NSDate dateWithTimeIntervalSince1970:self.simpleFeedCommon.uFeedTime];
        NSString *strTime = [timeDate stringForDisplayFromDate];
        
        if (strTime && ![strTime isEqualToString:@""])
        {
            [readStr appendString:strTime];
        }
        
        NSUInteger ugcType = [self getUgcType];
        
        if(UGC_TYPE(self.songinfo.ugcMask) == MUSIC_VIDEO_UGC) {
            [readStr appendString:KString(@"发布了一首MV.")];
        }
        else if (ugcType == HALF_CHORUS_UGC) {
            [readStr appendString:KString(@"发布了一首合唱.")];
        }
        else{
            [readStr appendString:KString(@"发布了一首歌.")];
        }
        
        [readStr appendString:KString(@"歌名:")];
        if (self.songinfo.name && ![self.songinfo.name isEqualToString:@""])
        {
            [readStr appendString:self.songinfo.name];
        }
        
        if(self.songinfo.songDesc) {
            [readStr appendString:[NSString stringWithFormat:KString(@"并说:%@."),[QzoneUtil reversalFaceOfQzoneForVoiceOver:self.songinfo.songDesc]]];
        }
        
        if (ugcType == HALF_CHORUS_UGC) {
            NSString *infoText = [NSString stringWithFormat:KString(@"%@人唱过"),NSIToString(self.ihcNum)];
            [readStr appendString:infoText];
        }
        else{
            NSString *scoreRank = [KSimpleFeed rankStringWithTypeV2:(SCORE_RANK)self.songinfo.scoreRank];
            if (scoreRank) {
                [readStr appendString:[NSString stringWithFormat:KString(@"评分:%@."),scoreRank]];
            }
            NSString *infoText = [NSString stringWithFormat:KString(@"评论:%@."),NSIToString(self.comment.number)];
            [readStr appendString:infoText];
            NSString *infoText2 = [NSString stringWithFormat:KString(@"收听:%@."),NSIToString(self.listenerInfo.number)];
            [readStr appendString:infoText2];
        }
        
        if (self.recivedFlowerInfo.number > 0)
        {
            NSString *infoText = [NSString stringWithFormat:KString(@"鲜花:%@."),NSIToString(self.recivedFlowerInfo.number)];
            [readStr appendString:infoText];
        }
        
        if (self.relationDesc.length > 0) {
            NSString *relationStr = [NSString stringWithFormat:KString(@"来自%@"),self.relationDesc];
            [readStr appendString:relationStr];
        }
        else if (self.lbs.strDistance.length > 0) {
            NSString *relationStr = [NSString stringWithFormat:KString(@"来自附近%@"),self.lbs.strDistance];
            [readStr appendString:relationStr];
        }
        
    }
    
    return readStr;
}

//需要参数是ugcMask+issegment是否是片段
- (NSUInteger)getUgcType
{
    NSUInteger ugcMask = self.songinfo.ugcMask;
    NSInteger ugcMaskExt = self.songinfo.ugcMaskExt;
    BOOL isSegment = self.songinfo.bIsFrag;
    
    if ([[self.songinfo.mapExt safeObjectForKey:@"uChapterType"] isEqualToString:@"0"] && LIVE_PLAYBACK_Mask(ugcMaskExt)) {
        return LIVE_PLAYBACK;
    }
    
    return [KSUgcTypeManager getUGCTypeWithUgcMask:ugcMask ugcMaskEtx:ugcMaskExt isSegMent:isSegment];
}

+ (NSString *)rankStringWithType:(SCORE_RANK)scoreRankType
{
    NSString *rankStr = @"";
    switch (scoreRankType) {
        case SCORE_RANK_C:
            rankStr = @"v66feeds_score_C";
            break;
        case SCORE_RANK_B:
            rankStr = @"v66feeds_score_B";
            break;
        case SCORE_RANK_A:
            rankStr = @"v66feeds_score_A";
            break;
        case SCORE_RANK_S:
            rankStr = @"v66feeds_score_S";
            break;
        case SCORE_RANK_SS:
            rankStr = @"v66feeds_score_SS";
            break;
        case SCORE_RANK_SSS:
            rankStr = @"v66feeds_score_SSS";
            break;
        case SCORE_RANK_MAX:
            break;
        case SCORE_RANK_INVALID:
            break;
        default:
            break;
    }
    return rankStr;
}

+ (NSString *)rankStringWithTypeV2:(SCORE_RANK)scoreRankType
{
    NSString *rankStr = nil;
    switch (scoreRankType) {
        case SCORE_RANK_C:
            rankStr = @"C";
            break;
        case SCORE_RANK_B:
            rankStr = @"B";
            break;
        case SCORE_RANK_A:
            rankStr = @"A";
            break;
        case SCORE_RANK_S:
            rankStr = @"S";
            break;
        case SCORE_RANK_SS:
            rankStr = @"SS";
            break;
        case SCORE_RANK_SSS:
            rankStr = @"SSS";
            break;
        case SCORE_RANK_MAX:
            rankStr = @"MAX";
            break;
        default:
            break;
    }
    return rankStr;
}


+ (KSTagViewStyle)rankStyleWithType:(SCORE_RANK)scoreRankType
{
    KSTagViewStyle rankStr = KSTagViewStyle_ScoreC;
    switch (scoreRankType) {
        case SCORE_RANK_C:
            rankStr = KSTagViewStyle_ScoreC;
            break;
        case SCORE_RANK_B:
            rankStr = KSTagViewStyle_ScoreB;
            break;
        case SCORE_RANK_A:
            rankStr = KSTagViewStyle_ScoreA;
            break;
        case SCORE_RANK_S:
            rankStr = KSTagViewStyle_ScoreS;
            break;
        case SCORE_RANK_SS:
            rankStr = KSTagViewStyle_ScoreSS;
            break;
        case SCORE_RANK_SSS:
            rankStr = KSTagViewStyle_ScoreSSS;
            break;
        case SCORE_RANK_MAX:
            break;
        case SCORE_RANK_INVALID:
            break;
        default:
            break;
    }
    return rankStr;
}

- (NSString*)getUGCTypeName
{
    //4.5版本产品viviyang定义的标签展示规则 原创>付费 or VIP＞短片>说唱>清唱>片段
    NSString *imageName = nil;
    NSInteger ugcType = [self getUgcType];
    switch (ugcType)
    {
        case ORIGINAL_UGC:
            // 原创作品，如果不是认证音乐人的，就不展示标签了
            if ([KSUserInfo isAuthMusicianByInfo:self.simpleUser.userinfo])
            {
                imageName = KString(@"原创");
            }
            break;
        case PAY_UGC:
        {
            //付费或者vip
            imageName = [[KSPaySoloAlbumManager sharedManager] updateAndReturnPayDescWithPayRight:self.songinfo.mapRight contentID:self.simpleFeedCommon.strFeedId andPayType:KSUserPayTypeOptionUGC];
            break;
        }
        case SHORT_VIDEO_UGC:
        {
            imageName = KString(@"短片");
            break;
        }
            
        case MUSIC_VIDEO_UGC:  //MV
        {
            if (UGC_TOSING(self.songinfo.ugcMask) == TOSING_UGC)
            {
                imageName = KString(@"说唱");
            }
            else if(UGC_CAPPELLA(self.songinfo.ugcMask)== CAPPELLA_UGC)
            {
                imageName = KString(@"清唱");//MV清唱
            }
            else
            {   //是否是合唱bugid=76788819
                if (UGC_FIN_CHORUS(self.songinfo.ugcMask) || UGC_FAVOR_CHORUS(self.songinfo.ugcMask))
                {
                    imageName = KString(@"合唱");//合唱
                }
                else
                {
                    imageName = nil;
                }
            }
            break;
        }
        case FIN_CHORUS_UGC:  //合唱
        case FAVOR_CHORUS_UGC: //收录合唱
        {
            if (UGC_TYPE(self.songinfo.ugcMask) == VIDEO_UGC)
            {
                imageName = KString(@"MV合唱");
            }
            else
            {
                if (self.hcCellItem.jce_iHasGift) {
                    imageName = KString(@"礼物合唱");
                } else {
                    imageName = KString(@"合唱");
                }
            }
            
        }
            break;
        case HALF_CHORUS_UGC:
        { //6.12 版本 合唱半成品不需要展示合唱标签
            
        }
            break;
        case CAPPELLA_UGC:  //清唱
        {
            imageName = KString(@"清唱");
            break;
        }
        case SHORT_AUDIO_UGC:  //短音频
        {
            imageName = KString(@"快唱");
            break;
        }
        case SEGMENT_UGC:
        {
            imageName = KString(@"片段");
            break;
        }
            
        default:
            imageName = nil;
            break;
    }
    if ((imageName == nil || [imageName isEqualToString:KString(@"清唱")])
        && (UGC_TYPE(self.songinfo.ugcMask) == AUDIO_UGC && !UGC_HALF_CHORUS(self.songinfo.ugcMask) && !UGC_FIN_CHORUS(self.songinfo.ugcMask))
        && UGC_IS_POETRY(self.songinfo.ugcMaskExt))
    {
        // 因为后台对自定义的诗词朗诵也会设置清唱的标签，所以 imageName 是清唱时，也需要判断一把是不是诗朗诵
        // 最普通的音频作品才能显示朗诵标签
        imageName = KString(@"朗诵");
    }
    if (self.isMusicMoodFeed && ((self.songinfo.ugcMaskExt & JceTimeline_Detail_KGE_UGC_MASK_EXT_BIT_KGE_UGC_MASK_EXT_SHORT_AUDIO) == JceTimeline_Detail_KGE_UGC_MASK_EXT_BIT_KGE_UGC_MASK_EXT_SHORT_AUDIO))//音乐心情快唱作品 标签展示逻辑优化
    {
        imageName = KString(@"快唱");
    }
    return imageName;
}


/// v7.0 FeedUI部分作品类型不展示
- (KSTagView*)getUGCTagView
{
   
    KSTagView* tagView = nil;
    NSInteger ugcType = [self getUgcType];
    switch (ugcType)
    {
        case ORIGINAL_UGC:
            // 原创作品，如果不是认证音乐人的，就不展示标签了
            if ([KSUserInfo isAuthMusicianByInfo:self.simpleUser.userinfo])
            {
                tagView = [KSTagView tagViewWithStyle:KSTagViewStyle_Gray];
                tagView.forceTheme = [[KSDarkTheme alloc] init];
                tagView.text = KString(@"原创");
            }
            break;
        case PAY_UGC:
        {
            //付费或者vip
            NSString* ugcType = nil;
            if (GetFlexBOOL([[KSPaySoloAlbumManager sharedManager] isPayUgcWithUgcMask:self.songinfo.ugcMask]))
            {
                ugcType =  [[KSPaySoloAlbumManager sharedManager] updateAndReturnPayDescWithPayRight:self.songinfo.mapRight contentID:self.simpleFeedCommon.strFeedId andPayType:KSUserPayTypeOptionUGC];
            }
            
            if (ugcType.length>0)
            {
                if ([ugcType isEqualToString:kConstPayStr])
                {
                    tagView = [KSTagView tagViewWithStyle:KSTagViewStyle_Pay];
                }
                if ([ugcType isEqualToString:kConstVipStr])
                {
                    tagView = [KSTagView tagViewWithStyle:KSTagViewStyle_VIP];
                }
            }
            
            break;
        }
        case SHORT_VIDEO_UGC:
        {
            tagView = [KSTagView tagViewWithStyle:KSTagViewStyle_Gray];
            tagView.forceTheme = [[KSDarkTheme alloc] init];
            tagView.text = KString(@"短片");
            break;
        }
            
        case VIDEO_UGC:  //MV
        {
            if (UGC_TOSING(self.songinfo.ugcMask) == TOSING_UGC)
            {
                tagView = nil; //V7.0说唱不需要展示标签
            }
            else if(UGC_CAPPELLA(self.songinfo.ugcMask)== CAPPELLA_UGC)
            {
                // 清唱不要标签了

            }
            else
            {   //是否是合唱bugid=76788819
                tagView = [self distinguishStarOrGiftChorous];
                if (!tagView) {
                    if (UGC_FIN_CHORUS(self.songinfo.ugcMask) || UGC_FAVOR_CHORUS(self.songinfo.ugcMask))
                    {
                        tagView = [KSTagView tagViewWithStyle:KSTagViewStyle_Gray];
                        tagView.forceTheme = [[KSDarkTheme alloc] init];
                        tagView.text = KString(@"合唱");
                    }
                    else
                    {
                        tagView = nil;
                    }
                }
            }
            break;
        }
        case HALF_CHORUS_UGC: //合唱半成品
        case FIN_CHORUS_UGC:  //合唱
        case FAVOR_CHORUS_UGC: //收录合唱
        {
            tagView = [self distinguishStarOrGiftChorous];
            /// 搜索合唱入口不让展示合唱tag
            if (!tagView && !self.layoutConfig.isActionBtnInRightCorner) {
                tagView = [KSTagView tagViewWithStyle:KSTagViewStyle_Gray];
                tagView.forceTheme = [[KSDarkTheme alloc] init];
                tagView.text = KString(@"合唱");
            }
        }
            break;
        case CAPPELLA_UGC:  //清唱
        { // V7.0 清唱不需要展示标签
            tagView = nil;
        }
            break;
        case SEGMENT_UGC:   //片段
        { // V7.0 独唱支持合唱的作品需要展示标签
            tagView = [self distinguishStarOrGiftChorous];
        }
            break;
        case SHORT_AUDIO_UGC:  //短音频
        {
            tagView = [KSTagView tagViewWithStyle:KSTagViewStyle_Gray];
            tagView.forceTheme = [[KSDarkTheme alloc] init];
            tagView.text = KString(@"快唱");
            break;
        }
        case LIVE_PLAYBACK: // 付费直播回放
        {
            tagView = [KSTagView tagViewWithStyle:KSTagViewStyle_Golden];
            tagView.forceTheme = [[KSDarkTheme alloc] init];
            tagView.text = KString(@"回放");
            break;
        }
        default:
            // V7.0 独唱支持合唱的作品需要展示标签
            tagView = [self distinguishStarOrGiftChorous];
            break;
    }
    if ((tagView == nil || [tagView.text isEqualToString:KString(@"清唱")])
        && (UGC_TYPE(self.songinfo.ugcMask) == AUDIO_UGC && !UGC_HALF_CHORUS(self.songinfo.ugcMask) && !UGC_FIN_CHORUS(self.songinfo.ugcMask))
        && UGC_IS_POETRY(self.songinfo.ugcMaskExt))
    {
        // 因为后台对自定义的诗词朗诵也会设置清唱的标签，所以 imageName 是清唱时，也需要判断一把是不是诗朗诵
        // 最普通的音频作品才能显示朗诵标签
        tagView = [KSTagView tagViewWithStyle:KSTagViewStyle_Gray];
        tagView.forceTheme = [[KSDarkTheme alloc] init];
        tagView.text = KString(@"朗诵");
        
        
    }
    if (self.isMusicMoodFeed && ((self.songinfo.ugcMaskExt & JceTimeline_Detail_KGE_UGC_MASK_EXT_BIT_KGE_UGC_MASK_EXT_SHORT_AUDIO) == JceTimeline_Detail_KGE_UGC_MASK_EXT_BIT_KGE_UGC_MASK_EXT_SHORT_AUDIO))//音乐心情快唱作品 标签展示逻辑优化
    {
        tagView = [KSTagView tagViewWithStyle:KSTagViewStyle_Gray];
        tagView.forceTheme = [[KSDarkTheme alloc] init];
        tagView.text = KString(@"快唱");
    }
    
    if (self.payAlbumInfo)
    {
        //付费专辑
        NSString* ugcType = [[KSPaySoloAlbumManager sharedManager] updateAndReturnPayDescWithPayRight:self.payAlbumInfo.mapRight contentID:self.simpleFeedCommon.strFeedId andPayType:KSUserPayTypeOptionAlbum];
        if (ugcType.length>0)
        {
            if ([ugcType isEqualToString:kConstPayStr])
            {
                tagView = [KSTagView tagViewWithStyle:KSTagViewStyle_Pay];
            }
            if ([ugcType isEqualToString:kConstVipStr])
            {
                tagView = [KSTagView tagViewWithStyle:KSTagViewStyle_VIP];
            }
        }
    }
        
    if (!tagView)
    {
        //ktv作品
        if (self.songinfo.ugcMaskExt & JceTimeline_Detail_KGE_UGC_MASK_EXT_BIT_KGE_UGC_MASK_EXT_MV_PLAY_MODEL)
        {
            tagView = [KSTagView tagViewWithStyle:KSTagViewStyle_KTV];
            tagView.forceTheme = [[KSDarkTheme alloc] init];
        }
    }
    
    if (!tagView && self.soloAlbumInfo)
    {
        //歌单tag
        tagView = [KSTagView tagViewWithStyle:KSTagViewStyle_Blue];
        tagView.text = KString(@"歌单");
    }
    
    return tagView;
}

- (KSTagView *)distinguishStarOrGiftChorous
{
    /* 礼物合唱标签显示条件 */
    /* 1.半成品有礼物 */
    /* 2.成品有礼物 */
    /* 3.独唱作品有礼物 */
    KSTagView *tagView = nil;
    BOOL isStarChorusFinishContent = [KSUgcStarChorusManager isUgcStarChorusWithUgcMaskExt:self.songinfo.ugcMaskExt];
    BOOL isGiftChorus = (self.hcCellItem.jce_iHasGift > 0 || self.hcCellItem.jce_uHcFinalGiftNum > 0 || [self canSoloSongShowChorusStyle]) ? YES : NO;
    if (isStarChorusFinishContent)
    {
        tagView = [KSTagView tagViewWithStyle:KSTagViewStyle_Red];
        tagView.text = KString(@"明星合唱");
    } else if (isGiftChorus) {
        tagView = [KSTagView tagViewWithStyle:KSTagViewStyle_Orange];
        tagView.text = KString(@"礼物合唱");
    }
    return tagView;
}

// 判断用户是否正在直播状态
- (BOOL)isUserInLiveshowRoom
{
    return !IS_EMPTY_STR_BM(self.simpleUser.userinfo.strRoomID) || !IS_EMPTY_STR_BM(self.simpleUser.userinfo.pushStreamLivingUrl) || !IS_EMPTY_STR_BM(self.simpleUser.userinfo.sAuthLiveUrl);
}

// 转发feed的转发者是否在直播状态
- (BOOL)isForwardUserInLiveShowroom
{
    return !IS_EMPTY_STR_BM(self.forwardFeed.feedUserInfo.userinfo.strRoomID) || !IS_EMPTY_STR_BM(self.forwardFeed.feedUserInfo.userinfo.pushStreamLivingUrl) || !IS_EMPTY_STR_BM(self.forwardFeed.feedUserInfo.userinfo.sAuthLiveUrl);
}

#pragma mark - 推荐关注实时进房
// 判断用户是否正在歌房状态
- (BOOL)isUserInKtvShowRoom
{
    return  !IS_EMPTY_STR_BM(self.simpleUser.avatarKtvInfo.jce_strJumpUrl) && !IS_EMPTY_STR_BM(self.simpleUser.avatarKtvInfo.jce_strRoomId);
}

- (BOOL)isForwardUserInKtvShowRoom
{
    return !IS_EMPTY_STR_BM(self.forwardFeed.feedUserInfo.avatarKtvInfo.jce_strJumpUrl) && !IS_EMPTY_STR_BM(self.forwardFeed.feedUserInfo.avatarKtvInfo.jce_strRoomId);
}

// 推荐列表需要将协议拉回来的数据转换一下
- (void)updateAvatarKtvInfo:(proto_ktv_status_AvatarKtvInfo *)avatarKtvInfo
{
    self.simpleUser.avatarKtvInfo.jce_strRoomId = avatarKtvInfo.jce_strRoomId;
    self.simpleUser.avatarKtvInfo.jce_iStatus = avatarKtvInfo.jce_iStatus;
    self.simpleUser.avatarKtvInfo.jce_strJumpUrl = avatarKtvInfo.jce_strJumpUrl;
    self.simpleUser.avatarKtvInfo.jce_strDesc = avatarKtvInfo.jce_strDesc;
    self.simpleUser.avatarKtvInfo.jce_iRoomType = avatarKtvInfo.jce_iRoomType;
}

//直播feed底部的文案，在线人气 + 标签
- (NSString *)getBottomTextForLiveShow
{
    NSString *bottomText = @"";
    if (self.liveShow.uOnlinePeopleCount > 0) {
        // 直播在线人气值
        bottomText = [KSFormatHelper formatNewNumber:self.liveShow.popularityNum];
    }
    NSString *liveShowLabelStr = self.liveShow.labelArray.firstObject;
    if (!IS_EMPTY_STR_BM(liveShowLabelStr)) {
        // 标签
        liveShowLabelStr = [NSString stringWithFormat:@"# %@", liveShowLabelStr];
    }
    
    if (!IS_EMPTY_STR_BM(liveShowLabelStr)) {
        bottomText = IS_EMPTY_STR_BM(bottomText) ? liveShowLabelStr : [NSString stringWithFormat:@"%@  %@", bottomText, liveShowLabelStr];
    }
    return bottomText;
}

// 直播feed关注页底部文案，在线人数 -> 直播人气值
- (NSString *)getOnlinePeopleForLiveShow
{
    NSString *onlinePeopleText = @"";
    if (self.liveShow.popularityNum > 0) {
        // 直播在线人气值
        onlinePeopleText = [KSFormatHelper formatNewNumber:self.liveShow.popularityNum];
    }
    return onlinePeopleText;
}

// 直播feed关注页底部文案，标签
- (NSString *)getTagTextForLiveShow
{
    NSString *liveShowLabelStr = self.liveShow.labelArray.firstObject;
    if (!IS_EMPTY_STR_BM(liveShowLabelStr)) {
        // 标签
        liveShowLabelStr = [NSString stringWithFormat:@"#%@", liveShowLabelStr];
    }
    return liveShowLabelStr;
}

- (BOOL)isFeedRelatedToAcc
{
    // 是歌曲feed，就涉及伴奏。其他不涉及（例如歌单、专辑，认为不涉及伴奏）
    return self.songinfo || self.simpleFeedCommon.uTypeid == FEED_TYPE_UGC;
}

- (NSString*)getLyricVersionStr
{
    //用后台给来的lyricmap来决定是否拉歌词,但是假feed没有lyricVersionMap
    NSString* lyricVersion = [self.songinfo.lyricVersionMap objectForKey:@(JceKSongInfo_emContentType_CONTENT_QRC)];
    if (!lyricVersion || lyricVersion.length <=0)//兜底用1
    {
        lyricVersion = @"1";
    }
    return lyricVersion;
}

//大卡片场景feed是否要展示歌词
- (BOOL)isFeedNeesShowLyricV2
{
    if ([KSong isCappellaSong:self.songinfo.songMid])
    {
        //清唱不展示歌词
        return NO;
    }
    if (self.isMusicMoodFeed)
    {
        //音乐心情不展示
        return NO;
    }

    if (self.songinfo.ugcMaskExt & JceTimeline_Detail_KGE_UGC_MASK_EXT_BIT_KGE_UGC_MASK_EXT_RECITE_TXT)
    {
        //朗诵作品不展示歌词
        return NO;
    }
    if (self.songinfo.ugcMaskExt & JceTimeline_Detail_KGE_UGC_MASK_EXT_BIT_KGE_UGC_MASK_EXT_RECITE_QC)
    {
        //朗诵作品不展示歌词
        return NO;
    }
    if ((self.songinfo.notShowLyricMask & KSUgc_Not_Show_Qrc_Mask_Center_Lyric) == KSUgc_Not_Show_Qrc_Mask_Center_Lyric)
    {
        //如果是吐歌词的效果则不显示歌词,
        return NO;
    }
    if ((!self.songInfoResult.qrc && self.songInfoResult.txtLyricPath) )
    {
        //txt歌词不展示
        return NO;
    }
    if (self.songinfo.songMid.length<=0)
    {
        //没有mid不展示歌词
        return NO;
    }

    
    return YES;
}


// feed是否要展示歌词
- (BOOL)isFeedNeesShowLyric
{
    if (self.shortPlay) {
        /// 短剧不需要展示歌词
        return NO;
    }
    
    if ([self isKindOfAIImageFeed]) {
        /// Ai一键图文作品不展示歌词
        return NO;
    }
    
    if (self.recShopTemplate.jce_iHasProduct == 1) {
        // 展示推荐商品，强制隐藏歌词
        return NO;
    }
    
    // 是否需要音频转图文轮播形式
    BOOL isNeedAudioTransferToTuwen = (UGC_TYPE(self.songinfo.ugcMask) == AUDIO_UGC && self.isNearbyFeed && self.photoListFeed && self.photoListFeed.jce_vecPic.count > 0);
    if (isNeedAudioTransferToTuwen)
    {
        // 音频转图文 不显示歌词
        return NO;
    }
    
    // VIP营销推荐卡片，不展示歌词
    if (self.isKindOfVipMarketingFeed) {
        return NO;
    }
    
    if ((self.songinfo.songMid.length > 0 &&
         UGC_TYPE(self.songinfo.ugcMask) == AUDIO_UGC &&
         !(self.songinfo.ugcMaskExt & JceTimeline_Detail_KGE_UGC_MASK_EXT_BIT_KGE_UGC_MASK_EXT_RECITE_TXT) &&
         !(self.songinfo.ugcMaskExt & JceTimeline_Detail_KGE_UGC_MASK_EXT_BIT_KGE_UGC_MASK_EXT_RECITE_QC) &&
         !self.isMusicMoodFeed &&
         ![KSong isCappellaSong:self.songinfo.songMid]) ||
        self.songinfo.audioTransVideoPlayType == JceTimeline_EnumAudioTransVideoPlayType_eAudioTransVideo_type_native_tempalte_video)
    {
        //音频中非ktv、朗诵、非音乐心情、非清唱作品、有qrc歌词的才展示歌词,7.4版本卡片推荐的普通音频流也展示歌词。
        return YES;
    }
    else if ([self isRecCardVideoFeed])
    {
        //推荐卡片的视频也可以展示歌词,除了部分短视频：如果是吐歌词的效果则不显示歌词,
        if ((self.songinfo.notShowLyricMask & KSUgc_Not_Show_Qrc_Mask_Center_Lyric) == KSUgc_Not_Show_Qrc_Mask_Center_Lyric)
        {
            return NO;
        }
        else
        {
            return YES;
        }
    }
    else if ([self isRecCardAudioFeed])
    {
        return YES;
    }
    else
    {
        return NO;
    }
}

#pragma mark 是否是短音频feed
- (BOOL) isKindOfShortAudioFeed
{
    NSInteger ugcType = [self getUgcType];
    if (ugcType == SHORT_AUDIO_UGC)
    {
        return YES;
    }
    else
    {
        return NO;
    }
}

+ (KSimpleFeed*) safeCastFromObj:(NSObject*)feedObj
{
    KSimpleFeed* feed = nil;

    if ([feedObj isKindOfClass:[KSimpleFeed class]])
    {
        feed = (KSimpleFeed*)feedObj;
    }
    else if([feedObj isKindOfClass:[KSRankWorkContent class]])
    {
        KSRankWorkContent* workcontent = (KSRankWorkContent*)feedObj;
        feed = workcontent.simpleFeed;
    }
    
    return feed;
}

#pragma mark转发自己的ugc
- (BOOL)isForwarSelfUgc
{
    if (self.forwardFeed.strForwardId.length>0 && self.simpleUser.userinfo.userId == self.forwardFeed.feedUserInfo.userinfo.userId)
    {
        return YES;
    }
    else
    {
        return NO;
    }
}

#pragma mark转发别人的ugc
- (BOOL)isForwarOhtherPeopleUgc
{
    if (self.forwardFeed.strForwardId.length>0 && self.simpleUser.userinfo.userId != self.forwardFeed.feedUserInfo.userinfo.userId)
    {
        return YES;
    }
    else
    {
        return NO;
    }
}

- (NSString*)getSimpleFeedTitle
{
    NSString *title = nil;
    if (self.h5JumpFeed)
    {
        title = self.h5JumpFeed.mainFeedDesc;
    }
    
    return title;
}

// feed的用户描述
- (NSString*)getSimpleFeedDescription
{
    NSString *description = nil;
    if(self.isShowUploading) //上传的时候也要显示作品说明
    {
        description = self.songinfo.songDesc;
    }
    else if (self.isShowShare)
    {
        description = self.uploadTask.publishContent.contentDesc;
    }
    else if (self.isKindOfAIImageFeed)
    {
        if ([self isRecCardFeed]) {
            description = self.AiImageText.strCotent;
        }
        else {
            description = self.AiImageText.strTitle;
        }
    }
    else if (self.competitionFeed && self.competitionFeed.competitionType == FeedCompetitionTypeUgc) {
        description = IS_EMPTY_STR_BM(self.competitionFeed.strDesc3) ? @"我创建了一个大赛，朋友们快来参加吧" : self.competitionFeed.strDesc3;
    }
    else if (self.soloAlbumInfo) {
        description = self.soloAlbumInfo.strAlbumDesc;
    }
    else if (self.payAlbumInfo) {
        description = self.payAlbumInfo.strAlbumDesc;
    }
    else if (self.feedAd.jce_vecDesc.count>0)
    {
        JceTimeline_s_advertDesc* dec = self.feedAd.jce_vecDesc[0];
        description = dec.jce_desc;
    }
    else if (self.relayGameFeed.strContent.length>0)
    {
        description = self.relayGameFeed.strContent;
    }
    else if (self.ugcRemark)
    {
        description = self.ugcRemark.jce_strSummary;
    }
    else if (self.competitionFeed)
    {
        description = self.competitionFeed.strFeedDesc;
    }
    else if(self.liveShow)
    {
        UIFont *font = DYNAMIC_VALUE_UNIVERSAL(KSFont_t2, KSFont_t2, KSFont_t2, KSFont_t2, KSFont_t2_PAD);
        if ([self.liveShow.mapExt safeObjectForKey:@"strLiveActName"]) {
            description = [self.liveShow.mapExt safeObjectForKey:@"strLiveActName"];
        }
        else if (self.liveShow.strLiveTitle.length <= 0)
        {
            
            __block NSString *nickName = self.simpleUser.userinfo.nickName;
            __block CGSize nickNameSize = CGSizeZero;
            [[KSStringCrashManager sharedManager] protectCrashString:nickName at:__FUNCTION__ when:^(NSString *finalString) {
                nickName = finalString;
                nickNameSize = [nickName kSongSizeWithFont:font];
            }];
            if (nickNameSize.width >= 0.8 * SCREEN_WIDTH) {
                nickName = [nickName safe_SubstringToIndex:kTimeLineLiveShowTitleLimitedCount] ;
            }
            description = [NSString stringWithFormat:KString(@"%@的直播间"), nickName];
        } else {
            description = self.liveShow.strLiveTitle;
        }
        
    }
    else if (self.ktvRoomMike)
    {
        if (!IS_EMPTY_STR_BM(self.ktvRoomMike.jce_strKtvMikeTitle))
        {
            description = [NSString stringWithFormat:@"%@", self.ktvRoomMike.jce_strKtvMikeTitle];
        }
        else
        {
            if (self.ktvRoomMike.jce_uKtvMikeFriendNum > 0)
            {
                description = [NSString stringWithFormat:@"我和%d个好友都在排麦，一起来玩吧!", self.ktvRoomMike.jce_uKtvMikeFriendNum];
            }
            else
            {
                description = [NSString stringWithFormat:@"我在歌房排麦了，一起来玩吧!"];
            }
        }

    }
    else if (self.courseFeed)
    {
        description = SAFE_STR_BM(self.courseFeed.jce_strCourseFeedDesc);
    }
    else if (self.h5JumpFeed)
    {
        description = self.h5JumpFeed.subFeedDesc;
    }
    else if ([self isKindOfTmeTownFeed])
    {
        description = SAFE_STR_BM([self.tmeTown.jce_mapExt safeObjectForKey:@"share_text"]);
    } else if (!IS_EMPTY_STR_BM(self.shortPlay.jce_strContent)) {
        // 短剧文案
        if (!IS_EMPTY_STR_BM(self.shortPlay.jce_strContent)) {
            // KSRichLable内部使用 NSAttributedString, NSAttributedString 不支持 \n 换行
            description = [self.shortPlay.jce_strContent stringByReplacingOccurrencesOfString:@"\\n" withString:@"\r"];
        }
    } else {
        description = self.songinfo.songDesc;
    }
    
    description = [description trimWhitespace];
    
    return description;
}

// 话题高亮处理后的描述文本
- (NSString *)getSimpleFeedDescWithTopicFormat {
    // 获取原来的字符串
    NSString *description = [self getSimpleFeedDescription];
    
    // 如果有话题，尝试替换
    if (self.topicsArray && self.topicsArray.count > 0) {
        NSMutableDictionary *topicMap = [NSMutableDictionary dictionary];
        for (JceTimeline_s_topic *topic in self.topicsArray) {
            [topicMap safeSetObject:[NSString stringWithFormat:@"%u", topic.jce_uTopicId] forKey:topic.jce_strTopicName];
        }
        description = [KSTopicTextParser formatDescString:description topicMap:topicMap];
    }
    
    return description;
}

- (NSString *)getSimpleFeedDescWithForRecFeed {
    // 获取原来的字符串
    NSString *description = [self getSimpleFeedDescription];
    
    // 如果文本里没有后台返回的话题，可能是老版本生成的，就拼接在最后
    if (self.topicsArray && self.topicsArray.count > 0) {
        NSMutableDictionary *topicMap = [NSMutableDictionary dictionary];
        for (JceTimeline_s_topic *topic in self.topicsArray) {
            [topicMap safeSetObject:[NSString stringWithFormat:@"%u", topic.jce_uTopicId] forKey:topic.jce_strTopicName];
        }
        
        description = [KSTopicTextParser updateDescStr:description topicMap:topicMap];
    }
    
    return description;
}

- (NSString *)getSimpleFeedMapTailInfoDescription
{
    NSString *topicStr = SAFE_STR_BM([self.songinfo.mapTailInfo objectForKey:KSSTR_TAIL_TEXT]);
    
    if (topicStr.length > 0 && ![topicStr hasPrefix:@"#"])//如果没有#自动补上
    {
        topicStr = [NSString stringWithFormat:@"#%@",topicStr];
    }

    return topicStr;
}

- (NSString *)getSimpleFeedMapTailInfoJumpUrl
{
    NSString *jumpStr = SAFE_STR_BM([self.songinfo.mapTailInfo objectForKey:KSSTR_TAIL_JUMP]);

    return jumpStr;
}

//神仙翻唱 Feeds
- (KSTagView *)getFeedGodCoverView
{
    KSTagView *godCoverView = nil;
    if ((self.songinfo.ugcMaskExt & JceTimeline_Detail_KGE_UGC_MASK_EXT_FAIRY_COVER)) {
        godCoverView = [KSTagView tagViewWithStyle:KSTagViewStyle_Cover];
        godCoverView.text = WnsSwitchStringConfig(@"FairyCoverTitle") ?: @"神仙翻唱";
        godCoverView.iconImage = IMAGENAMED(@"feed_cover_mic_small");
    }
    return godCoverView;
}

- (KSTagView*)getFeedFamilyRecommendView
{
    KSTagView* familyRecommendView = nil;
    if (self.songinfo.ugcMaskExt & JceTimeline_Detail_KGE_UGC_MASK_EXT_FAMILY_RECOMMEND)
    {
        familyRecommendView = [KSTagView tagViewWithStyle:KSTagViewStyle_Purple];
        familyRecommendView.text = @"家族推荐";
    }
    return familyRecommendView;
}

//是否展示推荐
- (KSTagView*)getFeedRecommendView
{
    KSTagView* recommendView = nil;
    if (self.songinfo.ugcMaskExt & JceTimeline_Detail_KGE_UGC_MASK_EXT_BIT_KGE_UGC_MASK_EXT_RECOMMENDED)
    {
        BOOL isGoldRec = self.songinfo.ugcMaskExt1 & JceTimeline_Detail_KGE_UGC_MASK_EXT1_GOLD_MARK;
        recommendView = [KSTagView tagViewWithStyle:isGoldRec ? KSTagViewStyle_Golden : KSTagViewStyle_Purple];
        recommendView.text = @"推荐";
        recommendView.iconImage = [UIImage imageNamed:isGoldRec ? @"archlor_gold" :@"archlor"];
    }
    return recommendView;
}

//是否展示等级标签
- (KSTagView*)getFeedRankView
{
    KSTagView* rankView = nil;
    if (self.songinfo.scoreRank!= SCORE_RANK_INVALID)
    {
        KSTagViewStyle scoreRank = [KSimpleFeed rankStyleWithType:(SCORE_RANK)self.songinfo.scoreRank];
        rankView = [KSTagView tagViewWithStyle:scoreRank];
    }
    return rankView;
}

//获取歌名的统一接口
- (NSString*)getSongName
{
    NSString* songName = @"";
    if (self.isMusicMoodFeed && [self.musicMoodFeed musicMoodUgcType] != KSMusicMoodUgcType_UGC)
    {
        if ([self.musicMoodFeed musicMoodUgcType] == KSMusicMoodUgcType_ACC)
        {
            songName = self.musicMoodFeed.refSongName;
        }
        else if ([self.musicMoodFeed musicMoodUgcType] == KSMusicMoodUgcType_PIC)
        {
            songName = @"";
        }
    }
    else if([self isLiveShowHighlightRec]){
        songName = self.liveShowHighlight.highlightSongName;
    }
    else
    {
        songName = self.songinfo.name;
    }
    return songName;
}

//获取推荐大卡片歌名的统一接口
- (NSString *)getRecSongName {
    NSString* songName = @"";
    if ([self isKindOfAIImageFeed]) {
        /// AI图文作品（发布时，可以关联伴奏或者ugc作品）
        if (self.AiImageText.strRefMid.length > 0) {
            /// 关联伴奏名
            songName = self.AiImageText.strRefMidName;
        }
        else if (self.AiImageText.strRefUgcid.length > 0) {
            /// 关联作品,对应的伴奏名
            songName = self.songinfo.refSongName;
            if (songName.length == 0) {
                songName = self.songinfo.name;
            }
        }
    }
    else if ([self needShowRecAITakePic]) {
        // Ai拍同款模版名
        songName = self.AIPicSingInfo.strTemplateName;
    }
    else if ([self isFinChorus]) {
        // 如果为合唱成品
        if (self.HcUserInfo.nickName.length > 0) {
            songName = [songName stringByAppendingFormat:@"-%@发起的合唱", self.HcUserInfo.nickName];
        }
    }
    else if (self.isMusicMoodFeed && [self.musicMoodFeed musicMoodUgcType] != KSMusicMoodUgcType_UGC) {
        /// 音乐心情
        if ([self.musicMoodFeed musicMoodUgcType] == KSMusicMoodUgcType_ACC)
        {
            songName = self.musicMoodFeed.refSongName;
        }
        else if ([self.musicMoodFeed musicMoodUgcType] == KSMusicMoodUgcType_PIC)
        {
            songName = @"";
        }
    }
    else {
        if (self.songinfo.refSongName.length > 0)
        {
            songName = self.songinfo.refSongName;
        }
        else
        {
            if ([self isKindOfKTVRoomFeed]) {
                if (!IS_EMPTY_STR_BM(self.ktvRoomShow.jce_mikeUserInfo.jce_strSongName)) {
                    songName = [NSString stringWithFormat:@"%@ - %@",self.ktvRoomShow.jce_mikeUserInfo.jce_strSongName, self.ktvRoomShow.jce_mikeUserInfo.jce_strSingerName];
                }
            } else if([self isLiveShowHighlightRec]){
                if (!IS_EMPTY_STR_BM(self.liveShowHighlight.highlightSongName)) {
                    songName = [NSString stringWithFormat:@"%@ 精彩回放中",self.liveShowHighlight.highlightSongName];
                }
            } else {
                songName = self.songinfo.name;
            }
        }
    }
    return songName;
}

#pragma mark - 歌房统一接口
- (BOOL)isKtvFeed
{
    if (self.ktvRoomShow || self.ktvRoomMike) {
        return YES;
    }
    return NO;
}

- (NSString *)getKtvRoomId
{
    if (self.ktvRoomShow) {
        return self.ktvRoomShow.jce_strRoomId;
    } else if (self.ktvRoomMike) {
        return self.ktvRoomMike.jce_strRoomId;
    }
    return nil;
}

- (NSString *)getKtvShowId
{
    if (self.ktvRoomShow) {
        return self.ktvRoomShow.jce_strShowId;
    } else if (self.ktvRoomMike) {
        return self.ktvRoomMike.jce_strShowId;
    }
    return nil;
}

- (JceKG_emKtvRoomType)getKtvRoomType
{
    if (self.ktvRoomShow) {
        return self.ktvRoomShow.jce_iRoomType;
    } else if (self.ktvRoomMike) {
        return self.ktvRoomMike.jce_iRoomType;
    }
    return 0;
}

- (KSUidType)getKtvAnchorId
{
    if (self.ktvRoomShow) {
        return self.ktvRoomShow.jce_lAnchorUid;
    } else if (self.ktvRoomMike) {
        return self.ktvRoomMike.jce_lAnchorUid;
    }
    return 0;
}

#pragma mark 是否又9宫格图
- (BOOL)isHasGrid9
{
    if (self.songinfo.feedImageList.count>0 || self.musicMoodFeed)
    {
        return YES;
    }
    else
    {
        return NO;
    }
}

#pragma mark - 大卡片电商商品统一接口
// 获取电商商品信息简介
- (NSString *)getSimpleFeedMediaProductDescription
{
    if (self.mediaProduct && [self.mediaProduct.jce_vctMediaProducts count] > 0) {
        proto_feed_webapp_MediaProduct *product = [self.mediaProduct.jce_vctMediaProducts firstObject];
        return product.jce_strTitle;
    }
    return nil;
}

// 是否有优惠券
- (BOOL)isMediaProductHasCoupon
{
    if (self.mediaProduct && [self.mediaProduct.jce_vctMediaProducts count] > 0) {
        proto_feed_webapp_MediaProduct *product = [self.mediaProduct.jce_vctMediaProducts firstObject];
        return product.jce_bHasCoupon;
    }
    return NO;
}

// 获取商品h5跳转链接
- (NSString *)getSimpleFeedMediaProductH5Url
{
    if (self.mediaProduct && [self.mediaProduct.jce_vctMediaProducts count] > 0) {
        proto_feed_webapp_MediaProduct *product = [self.mediaProduct.jce_vctMediaProducts firstObject];
        return product.jce_strH5Url;
    }
    return nil;
}

// 获取商品小程序跳转链接
- (NSString *)getSimpleFeedMediaMiniProgramUrl
{
    if (self.mediaProduct && [self.mediaProduct.jce_vctMediaProducts count] > 0) {
        proto_feed_webapp_MediaProduct *product = [self.mediaProduct.jce_vctMediaProducts firstObject];
        return product.jce_strMiniProgramUrl;
    }
    return nil;
}

// 获取商品id
- (NSUInteger)getSimpleFeedMediaProductId
{
    if (self.mediaProduct && [self.mediaProduct.jce_vctMediaProducts count] > 0) {
        proto_feed_webapp_MediaProduct *product = [self.mediaProduct.jce_vctMediaProducts firstObject];
        return product.jce_lProductId;
    }
    return 0;
}

#pragma mark - 教唱课程统一接口
// 获取教唱课程名称
- (NSString *)getSimpleFeedTeachCourseName
{
    if (self.teachCourse) {
        return self.teachCourse.jce_stTeach.jce_strName;
    }
    return nil;
}

// 获取教唱课程类型
- (proto_feed_webapp_emTeachItemType)getSimpleFeedTeachCourseType
{
    return self.teachCourse.jce_stTeach.jce_uType;
}

// 获取教唱课程跳转url
- (NSString *)getSimpleFeedTeachCourseUrl
{
    return self.teachCourse.jce_stTeach.jce_strJumpUrl;
}

// 获取教唱课程作品id
- (NSString *)getSimpleFeedTeachCourseId
{
    return self.teachCourse.jce_stTeach.jce_strTeachId;
}

//该音频是否支持视频化
- (BOOL)audioShouldVideolize
{
    /// 没有ugcID, 不启用视频化
    if (IS_EMPTY_STR_BM(self.simpleFeedCommon.strFeedId)) return NO;
    /// 大卡片视频化总开关关闭，不启用视频化
    if (!KSBigCardVideolizeSwitch.share.enableVideolize) return NO;
    /// 歌词动效已开启，不启用视频化
    if (self.uEffectsID > 0) return NO;
    /// 如果是歌词频谱模版，且开关关闭，不启用视频化
    if ([self.videolizeProduct isKindOfClass:[KSVideolizeSpectrumProduct class]] && !KSBigCardVideolizeSwitch.share.enableSpectrum) return NO;
    /// 如果是影集模版，且开关关闭，不启用视频化
    if ([self.videolizeProduct isKindOfClass:[KSVideolizeAlbumProduct class]] && !KSBigCardVideolizeSwitch.share.enableAlbum) return NO;
    /// 如果是动态图模版，且开关关闭，不启用视频化
    if ([self.videolizeProduct isKindOfClass:[KSVideolizeMp4Product class]] && !KSBigCardVideolizeSwitch.share.enableMp4) return NO;
    /// 大卡片音频作品都需要视频化
    if (![self isKindOfAudioTypeFeed]) return NO;
    
    return YES;
}

//是否是V7.4版本卡片流推荐
- (BOOL)isRecCardFeed
{
    if ((self.feedFilterMask &JceTimeline_enum_filter_mask_ENUM_FILTER_MASK_FEED_RECOMM_CARD)==JceTimeline_enum_filter_mask_ENUM_FILTER_MASK_FEED_RECOMM_CARD)
    {
        return YES;
    }
    else
    {
        return NO;
    }
    
}

- (BOOL)isInteractGameInPlayingStatus
{
    // 判断是否在游戏中上座，上座会后台会返回appid（主要是如果不上座jce_stGameStatus的结构体也不为空，只能根据appid是否为空判断是否在游戏中上座）
    if (!IS_EMPTY_STR_BM(self.ktvRoomWebgameStatusInfo.jce_stComm.jce_strGameAppID)) {
        return YES;
    }
    
    return NO;
}

- (proto_feed_webapp_ktv_game_status *)ktvRoomWebgameStatusInfo
{
    if (self.ktvRoomShow) {
        return self.ktvRoomShow.jce_stGameStatus;
    } else if (self.extraKtvRoomWebgameStatus) {
        return self.extraKtvRoomWebgameStatus;
    } else {
        return nil;
    }
}


/// 是否同城feed
- (BOOL)isNearbyFeed
{
    BOOL conditon1 = ((self.feedFilterMask &JceTimeline_enum_filter_mask_ENUM_FILTER_MASK_INTRA_CITY_WATERFALL_FEED)==JceTimeline_enum_filter_mask_ENUM_FILTER_MASK_INTRA_CITY_WATERFALL_FEED);
    
    BOOL condition2 = ((self.feedFilterMask &[KSTimelineManager sharedManager].filtrMaskRecommendNearBy)==[KSTimelineManager sharedManager].filtrMaskRecommendNearBy);
    
    BOOL condition3 = ((self.feedFilterMask & JceTimeline_enum_filter_mask_ENUM_FILTER_MASK_INTRA_CITY_FEED) == JceTimeline_enum_filter_mask_ENUM_FILTER_MASK_INTRA_CITY_FEED);

    if (conditon1 || condition2 || condition3)
    {
        return YES;
    }
    else
    {
        return NO;
    }
}

// 是否需要音频feed转成音乐心情样式展示
- (BOOL)isAudioTransferToMusicMood {
    if (UGC_TYPE(self.songinfo.ugcMask) == AUDIO_UGC &&
        [self isNearbyFeed] &&
        self.photoListFeed &&
        self.photoListFeed.jce_vecPic.count > 0) {
        return YES;
    }
    return NO;
}

/// 是否同城赏金赛feed
- (BOOL)isNearByBountyGameFeed
{
    // 同城赏金赛ID
    NSString *gameId = [self getBountyGameIdWithUrl:self.h5JumpFeed.jumpUrl];
    return ([self isNearbyFeed] && !IS_EMPTY_STR_BM(gameId));
}

//7.30同城feedTab, 单feed流
- (BOOL)isSameCityFeed
{
    if ((self.feedFilterMask & JceTimeline_enum_filter_mask_ENUM_FILTER_MASK_INTRA_CITY_FEED) == JceTimeline_enum_filter_mask_ENUM_FILTER_MASK_INTRA_CITY_FEED) {
        return YES;
    } else {
        return NO;
    }
}

///7.30是否关注feedTab,单feed流
/// ⚠️该方法判断Mask值不齐全
- (BOOL)isFollowFeed
{
    if ((self.feedFilterMask & JceTimeline_enum_filter_mask_ENUM_FILTER_MASK_FRIEND_NEW) == JceTimeline_enum_filter_mask_ENUM_FILTER_MASK_FRIEND_NEW)
    {
        return YES;
    }
    else
    {
        return NO;
    }
}

// 8.4 是否关注Tab Feed 
- (BOOL)isFollowTabFeed
{
    return ((self.feedFilterMask & JceTimeline_enum_filter_mask_ENUM_FILTER_MASK_FRIEND_NEW) == JceTimeline_enum_filter_mask_ENUM_FILTER_MASK_FRIEND_NEW ||
            (self.feedFilterMask & JceTimeline_enum_filter_mask_ENUM_FILTER_MASK_QQ_WEIXIN_FRIEND) == JceTimeline_enum_filter_mask_ENUM_FILTER_MASK_QQ_WEIXIN_FRIEND ||
            (self.feedFilterMask & JceTimeline_enum_filter_mask_ENUM_FILTER_MASK_UGC_FEED_QQWX) == JceTimeline_enum_filter_mask_ENUM_FILTER_MASK_UGC_FEED_QQWX ||
            (self.feedFilterMask & JceTimeline_enum_filter_mask_ENUM_FILTER_MASK_UGC_FEED) == JceTimeline_enum_filter_mask_ENUM_FILTER_MASK_UGC_FEED);
}

//7.16是否是大卡片样式同城feed
// 7.24已废弃同城大卡片及双流形式，该方法始终为NO
- (BOOL)isRecCardStyleNearByFeed {
    return NO;    
}

// 7.24已废弃同城大卡片及双流形式，该方法始终为NO
- (BOOL)isRecCardStyleNearByAudioFeed {
    return NO;
}

//是否是V7.4版本卡片流音频推荐
- (BOOL)isRecCardAudioFeed
{
    if ((self.feedFilterMask &JceTimeline_enum_filter_mask_ENUM_FILTER_MASK_FEED_RECOMM_CARD)==JceTimeline_enum_filter_mask_ENUM_FILTER_MASK_FEED_RECOMM_CARD && [self isKindOfAudioTypeFeed] && ![self isRecCardAdFeed] && ![self isAiSongRec] && ![self isAISongPublish])
    {
        return YES;
    }
    else
    {
        return NO;
    }
    
}

//大卡片音频是否显示首帧图
- (BOOL)shouldShowRecCardAudioFeedFirstFrame
{
    if (([self isRecCardAudioFeed] || [self isRecCardStyleNearByAudioFeed]) && self.songinfo.strIFrameUrl.length > 0 && ![self audioShouldVideolize])
    {
        return YES;
    }
    return NO;
}

//是否是V7.4版本卡片推荐视频feed
- (BOOL)isRecCardVideoFeed
{
    if ((self.feedFilterMask &JceTimeline_enum_filter_mask_ENUM_FILTER_MASK_FEED_RECOMM_CARD)==JceTimeline_enum_filter_mask_ENUM_FILTER_MASK_FEED_RECOMM_CARD && [self isKindOfVideoTypeFeed] && ![self isRecCardAdFeed] && ![self isAiSongRec] && ![self isAISongPublish])
    {
        return YES;
    }
    else
    {
        return NO;
    }
    
}

//是否是V7.24版本卡片TME广告feed
- (BOOL)isRecCardTMEAdFeed
{
    if ((self.feedFilterMask & JceTimeline_enum_filter_mask_ENUM_FILTER_MASK_FEED_RECOMM_CARD)==JceTimeline_enum_filter_mask_ENUM_FILTER_MASK_FEED_RECOMM_CARD)
    {
        //大卡片广告必须是视频
        if (self.feedAd && self.tmeFeedAd && self.tmeFeedAd.isVideoAd)
        {
            return YES;
        }
    }
    return NO;
}

//是否是V7.8版本卡片广告feed
- (BOOL)isRecCardAdFeed
{
    if ((self.feedFilterMask & JceTimeline_enum_filter_mask_ENUM_FILTER_MASK_FEED_RECOMM_CARD)==JceTimeline_enum_filter_mask_ENUM_FILTER_MASK_FEED_RECOMM_CARD)
    {
        //大卡片广告必须是视频
        if (self.feedAd && self.amsFeedAd && self.amsFeedAd.isVideoAd)
        {
            return YES;
        }
    }
    return NO;
    
}

//是否是大卡片直播/歌房feed
- (BOOL)isRecCardFeedLiveOrKTVRoomFeed
{
    if ([self isRecCardFeed] && ([self isKindOfLiveShowFeed] || [self isKindOfKTVRoomFeed]))
    {
        return YES;
    }
    else
    {
        return NO;
    }
}

/// 是否跟新的全局播放流程是同一个feed
- (BOOL)isSameWithUgcPlayMgrItem
{
    KSGlobalPlayItem* playItem = [KSUgcPlayMgr sharedManager].passPlayItem;
    if (([self.simpleFeedCommon.strFeedId isEqualToString:playItem.ugcId] && playItem.ugcId.length>0)
           && ((!playItem.strForwardId) || (playItem.strForwardId.length>0 && [self.forwardFeed.strForwardId isEqualToString:playItem.strForwardId])))
       {
           return YES;
       }
       else
       {
           return NO;
       }
}

/// 是否跟新的全局播放流程是同一个feed&&在播放
- (BOOL)isSameWithUgcPlayMgrItemAndPlaying
{
    return [self isSameWithUgcPlayMgrItem] && [[KSUgcPlayMgr sharedManager] isUgcPlaying];
}

- (BOOL)isSameWithPlayItem:(KSGlobalPlayItem*)playItem
{
    if (([self.simpleFeedCommon.strFeedId isEqualToString:playItem.ugcId] && playItem.ugcId.length>0)
        && ((!playItem.strForwardId) || (playItem.strForwardId.length>0 && [self.forwardFeed.strForwardId isEqualToString:playItem.strForwardId])))
    {
        return YES;
    }
    else
    {
        return NO;
    }
}

-(NSInteger)getDurationForReport
{
    NSInteger iDuration = 0;
    if (self.songinfo) {
        iDuration = self.songinfo.iDurations;
        if (iDuration == 0) {
            // 后台下发作品有误，再次读取
            iDuration = self.songinfo.iEndTime - self.songinfo.iStartTime;
            if (iDuration < 0) {
                iDuration = 0;
            }
        }
    }
    return iDuration;
}

//是否同一个feed
- (BOOL)isSameWithSimpleFeed:(KSimpleFeed*)feed
{
    BOOL isFeedIdSame = ([self.simpleFeedCommon.strFeedId isEqualToString:feed.simpleFeedCommon.strFeedId] && feed.simpleFeedCommon.strFeedId.length>0);
    BOOL isForwarIdSame = ((feed.forwardFeed.strForwardId.length<=0 && self.forwardFeed.strForwardId.length<=0) || (feed.forwardFeed.strForwardId.length>0 && [self.forwardFeed.strForwardId isEqualToString:feed.forwardFeed.strForwardId]));
    BOOL isSameAiSongRec = self.isAiSongRec && feed.isAiSongRec && [self.aiSongFeedModel isSameModelWith:feed.aiSongFeedModel];
    
    if ((isFeedIdSame && isForwarIdSame) || isSameAiSongRec)
    {
        return YES;
    }
    else
    {
        return NO;
    }
}

//是否在全局播放
- (BOOL)isGlobalPlaying
{
    BOOL isGlobalPlaying = NO;
    if ([self isSameWithPlayItem:[KSUgcPlayManager sharedManager].passPlayItem] && [[KSUgcPlayManager sharedManager] isUgcPlaying])
    {
        return YES;
    }
    return isGlobalPlaying;
}

- (NSInteger)getHighlightBeginST
{
    if (self.songinfo.hasChorusSegment)
    {
        if (self.songinfo.chorusSegmentStart <= self.songinfo.iEndTime && self.songinfo.chorusSegmentStart >= self.songinfo.iStartTime)
        {
            return self.songinfo.chorusSegmentStart / 1000;
        }
    }
    return 0;
}

//大卡片是否展示异化话题
- (BOOL)isNeedShowHotTag
{
    return NO;//大卡片tag收归后不展示异化话题了
}

//是否是话题feed
- (BOOL)hasTopicFeedMask
{
    return (self.feedFilterMask&[KSTimelineManager sharedManager].filtrMaskTopic) == [KSTimelineManager sharedManager].filtrMaskTopic;
}

//推荐理由 在昵称上方展示推荐理由，优先级身份判断>推荐下发
- (NSString *)getNearByRecCardGenderString
{

    NSString *nearByRecCardGenderString = @"";
    NSString *cGenderStr = @"";
    NSString *iAgesStr = @"";
    if (self.simpleUser.userinfo.cGender > 0)
    {
        if (self.simpleUser.userinfo.cGender == 1)
        {
            cGenderStr = @"男";
        }
        else if(self.simpleUser.userinfo.cGender == 2)
        {
            cGenderStr = @"女";
        }
        
        if (self.simpleUser.userinfo.iAges > 0)
        {
            iAgesStr = [NSString stringWithFormat:@" %zd",self.simpleUser.userinfo.iAges];
        }
        nearByRecCardGenderString = [NSString stringWithFormat:@"%@%@",cGenderStr,iAgesStr];
    }
    else
    {
        nearByRecCardGenderString = @"";
    }

    return nearByRecCardGenderString;
}

// 性别标签文本
- (NSString *)getNearByRecCardGenderStringV2
{
    proto_feed_webapp_cell_city_ext* cityInfo = self.liveShow.cityInfo;
    NSString *cGenderStr = @"";
    NSString *iAgesStr = @"";
    if (cityInfo.jce_cGender > 0) {
        if (cityInfo.jce_cGender == 1) {
            cGenderStr = @"男";
        } else if(cityInfo.jce_cGender == 2) {
            cGenderStr = @"女";
        }
        if (cityInfo.jce_iAge > 0) {
            iAgesStr = [NSString stringWithFormat:@" %d",cityInfo.jce_iAge];
        }
    }
    return [NSString stringWithFormat:@"%@%@",cGenderStr,iAgesStr];
}



//推荐理由 在昵称上方展示推荐理由，优先级身份判断>推荐下发
- (NSString *)getNearByRecCardIPicturesString
{
    NSString *nearByRecCardIPicturesString = @"";
    if (self.simpleUser.userinfo.iPictures > 0)
    {
        nearByRecCardIPicturesString = [NSString stringWithFormat:@"%zd张照片",self.simpleUser.userinfo.iPictures];
    }
    return nearByRecCardIPicturesString;
}

//推荐理由 在昵称上方展示推荐理由，优先级身份判断>推荐下发
- (NSString *)getRecReason
{

    NSString *recommendReason = @"";
    NSString *relationStr = [self getRelationShipDesc];
    if (relationStr) {
        recommendReason = relationStr;
    } else {
        recommendReason = self.simpleFeedCommon.rightTopText;
    }
   
    return recommendReason;
}

/// 关系链+后台下发推荐理由
- (NSString*)getRelationShipDesc
{
    NSString *recommendReason = @"";
    if ((self.simpleUser.userinfo.relationType & JceUserSearch_RelationType_IS_FOLLOW) == JceUserSearch_RelationType_IS_FOLLOW)
    {
        recommendReason = KString(@"你关注的人");
    }
    else if ((self.simpleUser.userinfo.relationType & JceUserSearch_RelationType_IS_FRIEND) == JceUserSearch_RelationType_IS_FRIEND)
    {
        recommendReason = [NSString stringWithFormat:@"%@好友",(LoginAccType_WeChat == [KSLoginManager sharedInstance].curUserInfo.loginType)?@"微信":@"QQ"];
    }
    else if ((self.simpleUser.userinfo.relationType & JceUserSearch_RelationType_IS_BIND) == JceUserSearch_RelationType_IS_BIND)
    {
        recommendReason = [NSString stringWithFormat:@"%@好友",(LoginAccType_WeChat == [KSLoginManager sharedInstance].curUserInfo.loginType)?@"QQ":@"微信"];
    }
  
    return recommendReason;
}

/// 大卡片是否需要显示礼物榜入口
- (BOOL)isFeedNeedShowRankLabel
{
    if (!self.giftRankInfo) {
        return NO;
    }
    
    return ([self isHasRelationAndFollow]) && (!IS_EMPTY_STR_BM([self getShowSendGiftGuideRankLabel]));
}

- (BOOL)isFeedNeedShowNoGiftRankLabel
{
    return [self isHasRelationAndFollow];
}

- (NSString *)getShowSendGiftGuideRankLabel
{
    KSimpleFeedGiftRank *giftRank = self.giftRankInfo;
    KSimpleFeedFlower *recivedFlowerInfo = self.recivedFlowerInfo;
    NSInteger giftNum = recivedFlowerInfo.number;
    NSInteger kbNum = giftRank.giftWealth;
    if (giftNum > 0 || kbNum > 0) {
        NSString *kbNumStr = kbNum > 0 ? [[KSFormatHelper formatNewNumber:kbNum] stringByAppendingString:@"K币 "] : @"";
        NSString *giftNumStr = giftNum > 0 ? [[KSFormatHelper formatNewNumber:giftNum] stringByAppendingString:@"鲜花 "] : @"";
        return [kbNumStr stringByAppendingString:giftNumStr];
    }
    return nil;
}

- (BOOL)isHasRelationAndFollow
{
    if (![self isKindOfUgcTypeFeed]) {
        return NO;
    }
    
    BOOL hasFollow = [KSUserInfo isFollowingUser:self.simpleUser.userinfo];
    BOOL hasRelation = NO;
    if ((self.simpleUser.userinfo.relationType & JceUserSearch_RelationType_IS_FOLLOW) == JceUserSearch_RelationType_IS_FOLLOW ||
        (self.simpleUser.userinfo.relationType & JceUserSearch_RelationType_IS_FRIEND) == JceUserSearch_RelationType_IS_FRIEND ||
        (self.simpleUser.userinfo.relationType & JceUserSearch_RelationType_IS_BIND) == JceUserSearch_RelationType_IS_BIND) {
        hasRelation = YES;
    }

    return (hasRelation || hasFollow);
}

// 算法标记，立即展示关注引导
- (BOOL)hasUGCFollowALGFlag {
    return self.cellExtraInfo.jce_guiderInfo.jce_lAlgMask > 0;
}

//是否有关系链
- (BOOL)isHasRelationShipOrGodSing
{
    if ([self getRelationShipDesc].length>0 || (self.songinfo.ugcMaskExt & JceTimeline_Detail_KGE_UGC_MASK_EXT_FAIRY_COVER))
    {
        return YES;
    }
    else
    {
        return NO;
    }
}

- (BOOL)isHasRecReason
{
    NSString *recReason = self.tagsInfoV2.jce_labelRecReason.jce_strText;
    if (!IS_EMPTY_STR_BM(recReason))
    {
        return YES;
    }
    return NO;
}

/// 是否展示神仙翻唱标签
- (BOOL)isShowGodTag
{
    if ((self.songinfo.ugcMaskExt & JceTimeline_Detail_KGE_UGC_MASK_EXT_FAIRY_COVER) && self.songinfo.fairyCoverJumpUrl.length>0)
    {
        return YES;
    }
    else
    {
        return NO;
    }
        
}

//推荐理由后台做统一收归,关注的人>后台推荐理由非入口类/入口类
- (NSString *)getRecReasonV2
{
    NSString *recommendReason = @"";
    NSString *relationStr = [self getRelationShipDesc];
    if (relationStr.length > 0) {
        recommendReason = relationStr;
    } else {
        recommendReason = self.tagsInfoV2.jce_labelRecReason.jce_strText;
    }
               
    if (recommendReason.length <= 0) {
        recommendReason = self.simpleFeedCommon.rightTopText;
    }
    return recommendReason;
}

/// 当前feed是否正在预览
- (BOOL)isFeedPrviewCurrentPlayingUgc
{
    KSDetailPlaybackModel *playbackModel = [KSFeedPreviewManager sharedManager].playbackModel;
    
    //这里播放列表经常没有forwardid,导致播放状态不同步，这里放开，只有两个关注好友转发同一feed,这时候两个feed都会呈现播放状态,暂时先这样
    if (([self.simpleFeedCommon.strFeedId isEqualToString:playbackModel.playbackInfo.ugcId] || [self.simpleFeedCommon.strShareid isEqualToString:playbackModel.playbackInfo.ugcId]))
    {
        return YES;
    }
    else
    {
        return NO;
    }
}

/// 是否在播放同一首歌
- (BOOL)isFeedCurrentPlayingUgc
{
    KSGlobalPlayItem *playItem = [KSUgcPlayManager sharedManager].passPlayItem;
    
#if INTERNALBUILD
    if ([self.simpleFeedCommon.strFeedId isEqualToString:playItem.ugcId] && self.forwardFeed.strForwardId.length>0 && ![self.forwardFeed.strForwardId isEqualToString:playItem.strForwardId])
    {
        KLog(@"[TT]song=%@%@",playItem.songname,playItem.strForwardId);
    }
#endif
    
    //这里播放列表经常没有forwardid,导致播放状态不同步，这里放开，只有两个关注好友转发同一feed,这时候两个feed都会呈现播放状态,暂时先这样
    if (([self.simpleFeedCommon.strFeedId isEqualToString:playItem.ugcId] || [self.simpleFeedCommon.strShareid isEqualToString:playItem.ugcId])
        && ((!playItem.strForwardId) || (playItem.strForwardId.length>0 && [self.forwardFeed.strForwardId isEqualToString:playItem.strForwardId])))
    {
        return YES;
    }
    else
    {
        return NO;
    }
}

/// 是否同一个feed
/// @param ugcId ugcId
- (BOOL)isSameWithUgcID:(NSString *)ugcId
{
    if (!ugcId)
    {
        return NO;
    }
    if ([ugcId isEqualToString:self.simpleFeedCommon.strFeedId])
    {
        return YES;
    }
    
    return NO;
    
}

/// 是否在播放同一首歌
- (BOOL)isFeedCurrentPlayingUgcV2
{
    KSGlobalPlayItem *playItem = [KSUgcPlayMgr sharedManager].passPlayItem;
    
#if INTERNALBUILD
    if ([self.simpleFeedCommon.strFeedId isEqualToString:playItem.ugcId] && self.forwardFeed.strForwardId.length>0 && ![self.forwardFeed.strForwardId isEqualToString:playItem.strForwardId])
    {
        KLog(@"[TT]song=%@%@",playItem.songname,playItem.strForwardId);
    }
#endif
    
    //这里播放列表经常没有forwardid,导致播放状态不同步，这里放开，只有两个关注好友转发同一feed,这时候两个feed都会呈现播放状态,暂时先这样
    if (([self.simpleFeedCommon.strFeedId isEqualToString:playItem.ugcId] || [self.simpleFeedCommon.strShareid isEqualToString:playItem.ugcId])
        && ((!playItem.strForwardId) || (playItem.strForwardId.length>0 && [self.forwardFeed.strForwardId isEqualToString:playItem.strForwardId])))
    {
        return YES;
    }
    else
    {
        return NO;
    }
}

- (NSInteger)getReportCommonInt4
{
    NSInteger commonInt4 =0;
    if (UGC_HALF_CHORUS(self.songinfo.ugcMask) || [self canSoloSongShowChorusStyle] || [self canSoloSongShowChorusBtn])
    {
        commonInt4 = 2; // 加入合唱按钮曝光
    }
    else
    {
        commonInt4 = 1;
    }
    return commonInt4;
}

- (NSInteger)getReportCommonInt5 {
    //int5=区分feeds类型0其他  1 带图片  2带KTV标识
    NSInteger feedType = -1;
    
    if ([self isNearbyBubbleFeed])
    {
        feedType = 5; // 5 冒泡作品
    } else if ((self.songinfo.ugcMaskExt & (JceTimeline_Detail_KGE_UGC_MASK_EXT_BIT_KGE_UGC_MASK_EXT_MV_PLAY_MODEL)) == JceTimeline_Detail_KGE_UGC_MASK_EXT_BIT_KGE_UGC_MASK_EXT_MV_PLAY_MODEL &&UGC_TYPE(self.songinfo.ugcMask) == AUDIO_UGC) {
        feedType = 2;
    } else if(self.songinfo.feedImageList.count > 0) {
        
        feedType = 1;
        
        if (self.musicMoodFeed) // 音乐心情
        {
            feedType = (self.songinfo.feedImageList.count > 1 ? 4 : 3);
        }
        // 音频转相册的
        else if (self.isAudioChangeToPhoto)
        {
            feedType = 6;
        }
        
    } else if (UGC_TYPE(self.songinfo.ugcMask) == AUDIO_UGC) { // 新单图样式
        if (self.videolizeProduct &&
                 [self.videolizeProduct isKindOfClass:[KSVideolizeAlbumProduct class]]) // 新多图样式
        {
            feedType = 4;
        }
        else
        {
            feedType = 3;
        }
    } else {
        feedType = 0;
    }
    return feedType;
}

- (NSString*)getReportCommonStr7
{
    NSString *temp = nil;
    if (UGC_HALF_CHORUS(self.songinfo.ugcMask) == HALF_CHORUS_UGC)
    {
        temp = [NSString stringWithFormat:@"%d_%d_%d_%d",self.hcCellItem.jce_uRemainKBNum,self.hcCellItem.jce_uTotalKBGiftNum,(self.hcCellItem.jce_uTotalKBNum - self.hcCellItem.jce_uRemainKBNum),(self.hcCellItem.jce_uTotalKBGiftNum - self.hcCellItem.jce_uRemainKBGiftNum)];
    }
    else if (UGC_FIN_CHORUS(self.songinfo.ugcMask) == FIN_CHORUS_UGC)
    {
        //str7=如果是合唱半成品，则上报余额_可领人数_已领额度_已领人数，如果是合唱成品则上报 领了哪个半成品的礼物，格式为 半成品ugcid_K币数
        temp = [NSString stringWithFormat:@"%lld_%d",self.HcUserInfo.userId,self.hcCellItem.jce_uHcFinalGiftNum];
    }
    
    return temp;
}


-(BOOL)isIntooShortMVProduct
{
    return (self.songinfo.ugcMaskExt & JceTimeline_Detail_KGE_UGC_MASK_EXT_SHORT_MV);
}

/// 获取调试信息
- (NSString*)getDebugInfo
{
    if (UGC_IS_VIDEO(self.songinfo.ugcMask))
    {
        NSDictionary *dic = @{
            @(JceTimeline_EnumAudioTransVideoPlayType_eAudioTransVideo_type_invalid):@"视频",
            @(JceTimeline_EnumAudioTransVideoPlayType_eAudioTransVideo_type_single_video):@"视频后台渲染",
            @(JceTimeline_EnumAudioTransVideoPlayType_eAudioTransVideo_type_audio_and_template_video):@"视频后台模板",
            @(JceTimeline_EnumAudioTransVideoPlayType_eAudioTransVideo_type_audio_and_songmv):@"视频后台mv",
        };
        return [dic safeObjectForKey:@(self.songinfo.audioTransVideoPlayType)];;
    }
    BOOL isktv = [self isKTVAudioFeed];
    NSMutableString* videolizeInfo = [NSMutableString stringWithString:@"音频"];
    if ([self.videolizeProduct isKindOfClass:[KSVideolizeSpectrumProduct class]])
    {
        [videolizeInfo appendString:@"歌词频谱"];
    }
    else if ([self.videolizeProduct isKindOfClass:[KSVideolizeAlbumProduct class]])
    {
        [videolizeInfo appendString:@"音乐影集"];
    }
    else if ([self.videolizeProduct isKindOfClass:[KSVideolizeMp4Product class]])
    {
        [videolizeInfo appendString:@"动态图"];
    }
    else if ([self.videolizeProduct isKindOfClass:[KSVideolizeLocalProduct class]])
    {
        [videolizeInfo appendString:@"本地模板"];
    }
    
    if (isktv)
    {
        [videolizeInfo appendString:@"ktv"];
    }
    
    return videolizeInfo;
}

// 是否是同城tab下，他人冒泡的作品
- (BOOL)isNearbyBubbleFeed {
    NSInteger source = self.lbs.strSource.integerValue;
    if (self.lbs.strSource.length > 0 && source == proto_nearby_rec_REC_CONTENT_POOL_NEARBY_BUBBLE_CONTENT_POOL) {
        return YES;
    }
    return NO;
}

- (NSString *)chosedPoiInfo {
    NSString *info = @"";
    if (self.lbs.strPoiName.length > 0) {
        info = self.lbs.strPoiName;
    } else if (self.lbs.strCity.length > 0) {
        info = self.lbs.strCity;
    }
    return info;
}

#pragma mark - 左下角Bar相关
// 是否展示伴奏条
- (BOOL)showSongNameBar
{
    if (self.shortPlay || self.asyncLiveRecInfo) {
        /// 短剧、直播引导、这几种情况不出伴奏
        return NO;
    }
    
    if (self.guideType == kFeedCardGuideTypeAITakePhoto) {
        if ([KSABTestManager sharedManager].isShowRecBarAIPicSingGuide) {
            /// Ai拍同款需要展示引导条，不展示伴奏
            return NO;
        }
    }
    
    if ([self isKindOfAIImageFeed]) {
        /// AI图文feed
        /// 没有关联伴奏、ugc关联的伴奏，不出伴奏条
        if (self.AiImageText.strRefMid.length == 0 &&
            self.songinfo.songMid.length == 0) {
            return NO;
        }
    }
    
    return YES;
}

// 是否展示短剧条
- (BOOL)showShortPlayBar
{
    if (self.bottomBarInfo) {
        /// 有底部热门条不出短剧引导
        return NO;
    }
    
    return self.shortPlay ? YES : NO;
}

// 是否展示直播引导条
- (BOOL)showAsynLiveBar
{
    if (self.bottomBarInfo) {
        /// 有底部热门条不出 直播引导
        return NO;
    }
    
    if (self.asyncLiveRecInfo) {
        if (self.guideType == kFeedCardGuideTypeLiveEntry) {
            return YES;
        }
    }
    
    return NO;
}

- (CGFloat)getPlayBarHeight
{
    CGFloat sizeH = 0;
    if ([self showSongNameBar]) {
        /// 伴奏条
        sizeH = KSMargin(20);
    }
    else if ([self showShortPlayBar]) {
        /// 短剧条
        sizeH = KSMargin(40.f);
    }
    else if ([self showAsynLiveBar]) {
        /// 直播引导条
        sizeH = KSMargin(40.f);
    }
    
    return _size_mid(sizeH);
}

- (CGFloat)getPlayBarMarginBottom
{
    CGFloat bottomMargin = 2;
    if ([self showSongNameBar]) {
        /// 伴奏条
        bottomMargin = KSMargin(12);
    }
    else if ([self showShortPlayBar]) {
        /// 短剧条
        bottomMargin = KSMargin(12);
    }
    else if ([self showAsynLiveBar]) {
        /// 直播引导条
        bottomMargin = KSMargin(12);
    }
    
    return bottomMargin;
}

- (CGFloat)bottomBarHeight
{
    if (self.bottomBarInfo) {
        return _size_mid(38);
    }
    
    return 0;
}

- (BOOL)isNeedCreateSingKtv
{
    if (self.cellExtraInfo.jce_strBottomBoxText.length > 0) {
        return NO;
    }
    
    if (self.ktvSingCard.jce_emSingCardShow == rcqmkg_SingCardShow_CREATE_SING) {
        return YES;
    }
    return NO;
}

- (NSInteger)getKtvSingReportForInt2
{
    if (self.ktvSingCard.jce_emSingCardShow == rcqmkg_SingCardShow_CREATE_SING) {
        return 1;
    } else if (self.ktvSingCard.jce_emSingCardShow == rcqmkg_SingCardShow_JOIN_SING) {
        return 2;
    } else if  (self.ktvSingCard.jce_emSingCardShow == rcqmkg_SingCardShow_FEEDREC_LIVE) {
        return 3;
    }
    return -1;
}

- (NSString *)getKtvSingReportForStr10
{
    return @(self.ktvSingCard.jce_emSingCardShow).stringValue;
}

- (NSString *)getKtvSingReportForRoomType
{
    BOOL isKtvOfficialRoom = ((self.ktvRoomShow.jce_iRoomType & JceKG_emKtvRoomType_KTV_ROOM_TYPE_OFFICIAL_ROOM) == JceKG_emKtvRoomType_KTV_ROOM_TYPE_OFFICIAL_ROOM);
    NSInteger commint1 =[KSUserInfo userMapAuthID:self.simpleUser.userinfo.sMapAuth
                                    ktvanchorRoleMask:0
                                    isKtvOfficialRoom:isKtvOfficialRoom];
    return @(commint1).stringValue;
}

/// 大卡片礼物Icon数量显示文案(鲜花+Kb+道具)
- (NSString *)getGiftNumDisPlayText
{
    NSInteger giftNum = self.recivedFlowerInfo.number + self.recivedFlowerInfo.uPropsNum + self.giftRankInfo.giftWealth;
    if (giftNum > 0) {
        return [KSFormatHelper formatNewNumber:giftNum];
    }
    return nil;
}

/// 后台是否下发了大卡片锚点数据
- (BOOL)isHasAnchorPointDta
{
    // 下发了推荐商品，优先展示商品，忽略其他锚点数据
    if (self.recShopTemplate.jce_iHasProduct == 1) {
        return YES;
    }
    
    if (self.tagsInfoV2.jce_labelAnchorPoint.jce_strText.length>0 ||
        self.tagsInfoV2.jce_labelAnchorPoint.jce_strTypeText.length>0 ||
        self.tagsInfoV2.jce_labelAnchorPoint.jce_strIcon.length>0) {
        
        /// 合集锚点走ABTest
        if ([self isFeedAblumAnchorPoint] && ![KSABTestManager sharedManager].isEnableShowFeedAblum) {
            return NO;
        }
        return YES;
    }
    else
    {
        return NO;
    }
}

/// 是否合集锚点
- (BOOL)isFeedAblumAnchorPoint
{
    if (!self.tagsInfoV2.jce_labelAnchorPoint) {
        return NO;
    }
    if ((self.tagsInfoV2.jce_labelAnchorPoint.jce_iAnchorPointType == JceTimeline_EnumAnchorPointType_eAnchorPointType_collection_hot) ||
        (self.tagsInfoV2.jce_labelAnchorPoint.jce_iAnchorPointType == JceTimeline_EnumAnchorPointType_eAnchorPointType_collection_relation) ||
        (self.tagsInfoV2.jce_labelAnchorPoint.jce_iAnchorPointType == JceTimeline_EnumAnchorPointType_eAnchorPointType_collection_medley) ||
        (self.tagsInfoV2.jce_labelAnchorPoint.jce_iAnchorPointType == JceTimeline_EnumAnchorPointType_eAnchorPointType_collection_tag)) {
        return YES;
    }
    return NO;
}

/// 是否长音频feed
- (BOOL)isFeedLongAudioAnchorPoint
{
    if (!self.tagsInfoV2.jce_labelAnchorPoint) {
        return NO;
    }
    BOOL isLongAudioType = self.tagsInfoV2.jce_labelAnchorPoint.jce_iAnchorPointType == JceTimeline_EnumAnchorPointType_eAnchorPointType_long_audio_full_ver;
    return isLongAudioType && !IS_EMPTY_STR_BM(self.tagsInfoV2.jce_labelAnchorPoint.jce_strQSongMid);
}

- (KSong *)songForGotoSingAction
{
    KSong *song = [[KSong alloc] init];
    song.name       = self.songinfo.refSongName.length > 0 ? self.songinfo.refSongName : self.songinfo.name;
    song.songMid    = self.songinfo.strQcRefKSongMid.length > 0 ? self.songinfo.strQcRefKSongMid : self.songinfo.songMid;
    song.fileMid2   = self.songinfo.fileMid2;
    song.singerName = self.songinfo.singerName;
    song.lSongMask  = self.songinfo.lSongMask;
    if (self.isAudioTransVideo)
    {
        // 后台音频转成视频的情况，有两种：
        // 1. 视频化作品后台渲染
        // 2. 音频作品后台关联模板视频
        // 因为事实上是一个音频作品，所以这里强制把 video 置 0，防止后续的逻辑判断成一个视频作品
        song.ugcMask = self.songinfo.ugcMask & ~JceTimeline_Detail_KGE_UGC_MASK_BIT_KGE_UGC_MASK_VIDEO;
    }
    else
    {
        song.ugcMask = self.songinfo.ugcMask;
    }
    song.ugcMaskExt = self.songinfo.ugcMaskExt;
    song.audioTransVideoPlayType = self.songinfo.audioTransVideoPlayType;
    return song;
}

// 是否通用高光作品
- (BOOL)isHighlightUgcFeed
{
    return self.songinfo.ugcMaskExt1 & JceTimeline_Detail_KGE_UGC_MASK_EXT1_HL;
}

// 是否下架作品
- (BOOL)isOFFUgcFeed
{
    return self.songinfo.ugcMaskExt1 & JceTimeline_Detail_KGE_UGC_MASK_EXT1_OFF;
}

// 短剧UGC
- (BOOL)isShortPlay 
{
    return self.songinfo.ugcMaskExt1 & JceTimeline_Detail_KGE_UGC_MASK_EXT1_MINI_SHOW;
}

// 是否是AI歌曲推荐类型
- (BOOL)isAiSongRec {
    return self.aiSongFeedModel != nil;
}

/// 是否是AI歌曲引导发布
- (BOOL)isAISongPublish
{
    return self.aiSongCommFeedModel != nil;
}

- (BOOL)haveAICoverSongGuide
{
    NSDictionary *mapExt = self.songinfo.mapExt;
    NSString *key = @"aisong_guide";
    BOOL haveAICoverSongGuide = NO;
    if (mapExt && [mapExt hasKey:key]) {
        NSString *status = SAFE_CAST([mapExt safeObjectForKey:key], NSString);
        if (status.length && [status isEqualToString:@"1"]) {
            haveAICoverSongGuide = YES;
        }
    }
    return haveAICoverSongGuide;
}

/// 推荐feed是否需要展示AI拍同款
- (BOOL)needShowRecAITakePic
{
    if (![self isKindOfUgcTypeFeed]) {
        return NO;
    }
    BOOL hitABtest = [KSABTestManager sharedManager].isShowRecButtonAIPicSingGuide ||
                     [KSABTestManager sharedManager].isShowRecBarAIPicSingGuide;
    BOOL show = self.AIPicSingInfo && hitABtest;
    return show;
}

/// 关注feed是否需要展示AI拍同款
- (BOOL)needShowFeedAITakePic
{
    if (![self isKindOfUgcTypeFeed]) {
        return NO;
    }
    BOOL show = self.AIPicSingInfo && [KSABTestManager sharedManager].isShowFollowAIPicSingGuide;
    return show;
}

- (KSGiftPanelSimpleFeed *)toGiftSimpleFeed
{
    /**
     @property(nonatomic, strong) KSimpleFeedSoloAlbum          *soloAlbumInfo; // cell_album 歌单
     @property(nonatomic, strong) KSimpleFeedPayAlbum           *payAlbumInfo; // cell_payalbum 专辑（付费合集）
     @property (nonatomic, assign) BOOL isMusicMoodFeed;//是否是音乐心情的detail
     @property(nonatomic, strong) KSimpleFeedCommon          *simpleFeedCommon;
     @property(nonatomic, strong) KSimpleFeedUser            *simpleUser;
     @property(nonatomic, strong) KSimpleFeedMusicMood         *musicMoodFeed; //音乐心情,不仅仅是音乐心情有多图，发作品的时候也可以添加多图
     @property(nonatomic, strong) KSong            *songInfo;
     */
    KSGiftPanelSimpleFeed *feed = [[KSGiftPanelSimpleFeed alloc] init];
    feed.soloAlbumInfo = self.soloAlbumInfo;
    feed.payAlbumInfo = self.payAlbumInfo;
    feed.isMusicMoodFeed = self.isMusicMoodFeed;
    feed.simpleFeedCommon = self.simpleFeedCommon;
    feed.simpleUser = self.simpleUser;
    feed.musicMoodFeed = self.musicMoodFeed;
    feed.songinfo = self.songinfo;
    feed.recommendItem = self.recommendItem;
    return feed;

}

- (NSArray *)VIPRights {
    if (_VIPRights && _VIPRights.count) {
        return _VIPRights;
    }
    NSMutableArray *vipRightArr = [[NSMutableArray alloc] init];
    
    // 乐团陪唱
    if (self.songinfo.ugcMaskExt1 & JceKG_PROTO_UGC_WEBAPP_KGE_UGC_MASK_EXT1_AI_AUDIO_CHORUS) {
        [vipRightArr safeAddObject:@(JceKG_PROTO_UGC_WEBAPP_KGE_UGC_MASK_EXT1_AI_AUDIO_CHORUS)];
    }
    
    // DTS音效
    if (self.songinfo.ugcMaskExt1 & JceKG_PROTO_UGC_WEBAPP_KGE_UGC_MASK_EXT1_DTS_SOUND_EFFECT) {
        [vipRightArr safeAddObject:@(JceKG_PROTO_UGC_WEBAPP_KGE_UGC_MASK_EXT1_DTS_SOUND_EFFECT)];
    }
    
    // VIP作品加热
    if (self.songinfo.ugcMaskExt1 & JceTimeline_Detail_KGE_UGC_MASK_EXT1_VIP_HEAT_CARD) {
        [vipRightArr safeAddObject:@(JceTimeline_Detail_KGE_UGC_MASK_EXT1_VIP_HEAT_CARD)];
    }
    
    // 人声增强
    if (self.songinfo.ugcMaskExt1 & JceTimeline_Detail_KGE_UGC_MASK_EXT1_VOICE_ENHANCE) {
        [vipRightArr safeAddObject:@(JceTimeline_Detail_KGE_UGC_MASK_EXT1_VOICE_ENHANCE)];
    }
    
    // 智能音效/VIP音效/明星音效/临境音效
    if (self.isVIPSoundEffectRedStone || self.isVIPSoundEffect || self.songinfo.ugcMaskExt1 & JceTimeline_Detail_KGE_UGC_MASK_EXT1_STAR_SOUND_EFFECT || self.isImmersiveEffectFeed) {
        [vipRightArr safeAddObject:@(JceTimeline_Detail_KGE_UGC_MASK_EXT_BIT_KGE_UGC_MASK_EXT_VIP_EFFECT)];
    }
    
    // 录唱皮肤
    if (self.songinfo.ugcMaskExt & JceTimeline_Detail_KGE_UGC_MASK_EXT_BIT_KGE_UGC_MASK_EXT_VIP_TEMPLATE) {
        [vipRightArr safeAddObject:@(JceTimeline_Detail_KGE_UGC_MASK_EXT_BIT_KGE_UGC_MASK_EXT_VIP_TEMPLATE)];
    }
    
    // VIP 伴奏
    if (self.songinfo.ugcMaskExt1 & JceTimeline_Detail_KGE_UGC_MASK_EXT1_VIP_ACCOMPANY) {
        [vipRightArr safeAddObject:@(JceTimeline_Detail_KGE_UGC_MASK_EXT1_VIP_ACCOMPANY)];
    }
    
    // 智能和声
    if (self.songinfo.ugcMaskExt & JceTimeline_Detail_KGE_UGC_MASK_EXT_BIT_KGE_UGC_MASK_EXT_VIP_ACCOMPANY) {
        [vipRightArr safeAddObject:@(JceTimeline_Detail_KGE_UGC_MASK_EXT_BIT_KGE_UGC_MASK_EXT_VIP_ACCOMPANY)];
    }
    
    // VIP音质/临境音质/superSound
    if (self.songinfo.ugcMask & JceTimeline_Detail_KGE_UGC_MASK_BIT_KGE_UGC_MASK_HQ || self.isImmersiveQualityFeed ||
        GetFlexBOOL([KSUgcTypeManager ugcMaskHaveSuperSound:self.songinfo.ugcMaskExt1])) {
        [vipRightArr safeAddObject:@(JceTimeline_Detail_KGE_UGC_MASK_BIT_KGE_UGC_MASK_HQ)];
    }
    
    if (vipRightArr.count) {
        _VIPRights = vipRightArr.copy;
    }
    
    return _VIPRights;
}

@end

@implementation KSimpleFeed (PublishContent)

KS_DYNAMIC_PROPERTY_OBJECT(publishContent, setPublishContent, RETAIN_NONATOMIC, KSPublishContent *);


+ (KSimpleFeed *)feedWithPublishContent:(KSPublishContent *)publishContent
{
    KSimpleFeed *feed = [[KSimpleFeed alloc] init];
    
    feed.publishContent = publishContent;
    
    feed.relationDesc = KString(@"我的"); // 关系
    
    KSimpleFeedCommon *common = [[KSimpleFeedCommon alloc] init];
    common.strFeedId = publishContent.ugcId;
    common.strShareid = [FileUploadEx shareIdForKey:publishContent.productMid];
    common.uFeedTime = [[NSDate date] timeIntervalSince1970];
    if ([publishContent isChorusContent]) {
        common.halfUgcId = [publishContent.chorusUploadInfo objectForKey:CHORUS_UGCID];
    }
    common.iHaveGift = publishContent.iHaveGift;
    feed.simpleFeedCommon = common;
    
    KSimpleFeedUser *user = [[KSimpleFeedUser alloc] init];
    KSUserInfo *userInfo = [[KSUserInfo alloc] init];
    user.userinfo = userInfo;
    user.userinfo.userId = [[KSLoginManager sharedInstance] curUserInfo].userId; // uid 用于下载头像
    user.userinfo.nickName = [[KSLoginManager sharedInstance] curUserInfo].nickName; // nickname 用于绘制昵称
    user.userinfo.sAuthName = [[KSLoginManager sharedInstance] curUserInfo].sAuthName; // 用于绘制代言人图标
    user.userinfo.sMapAuth = [[KSLoginManager sharedInstance] curUserInfo].sMapAuth;
    user.userinfo.userVipInfo = [KSUserVip userVipStatus:user.userinfo.sMapAuth];
    feed.simpleUser = user;
    
    KSong *song = [[KSong alloc] init]; // 歌曲信息 主要是用来展示封面
    if (publishContent.photoUrl) {
        KSImage *cover = [[KSImage alloc] init];
        cover.imageUrl = publishContent.photoUrl;
        song.coverurls = @{ @(200): cover };
    }
    else if(publishContent.photoFile){
        KSImage *cover = [[KSImage alloc] init];
        cover.localFilePath = publishContent.photoFile;
        song.coverurls = @{ @(200): cover };
    }
    song.cutterCoverImageUrl = SAFE_CAST(publishContent.strCoverFaceUrl, NSString);
    song.coverIsCustomedPic = (publishContent.coverType == KSPublishContentCoverTypeNetAlbum) ||
                               (publishContent.coverType == KSPublishContentCoverTypeSystemPhoto);
    
    if (publishContent.workPics.imgs.count > 0)
    {
        NSMutableArray* temp = [NSMutableArray new];
        BOOL isMultiPics = publishContent.bindableWorkPics.count > 1;
        JceUInt32 smallPicHeight = kIMAGELISTSINGLEPICWIDTH;
        JceUInt32 smallPicWidth = kIMAGELISTSINGLEPICWIDTH;
        JceUInt32 picHeight = 640;
        JceUInt32 picWidth = 640;
        for (NSString* picUrl in publishContent.bindableWorkPics)
        {
            NSDictionary* param = [[KSNavigationManager sharedManager] getURLParam:picUrl];
            JceUInt32 contentPicHeight = (JceUInt32)[[param safeObjectForKey:@"h"] integerValue];
            if (contentPicHeight>0)
            {
                picHeight = contentPicHeight;
            }
            
            JceUInt32 contentPicWidth = (JceUInt32)[[param safeObjectForKey:@"w"] integerValue];
            if (contentPicWidth>0)
            {
                picWidth = contentPicWidth;
            }
            JceTimeline_picinfo* picInfo = [JceTimeline_picinfo new];
            
            JceTimeline_pic_detail *smallPicDetail = [JceTimeline_pic_detail new];
            smallPicDetail.jce_uiWidth = isMultiPics? smallPicWidth:picWidth;
            smallPicDetail.jce_uiHeight = isMultiPics? smallPicHeight:picHeight;
            smallPicDetail.jce_strUrl = picUrl;
            
            JceTimeline_pic_detail *picDetail = [JceTimeline_pic_detail new];
            picDetail.jce_uiWidth = picWidth;
            picDetail.jce_uiHeight = picHeight;
            picDetail.jce_strUrl = picUrl;
            
            picInfo.jce_mPic = @{@(0):smallPicDetail,@(1):picDetail};
            [temp addObject:picInfo];
        }
        
        song.feedImageList = temp;
        song.streamVideo_width = 9;
        song.streamVideo_height = (publishContent.workPics.ratio == KSPublishVideolizeRatio_1_1) ? 9 : 16;
    }
    
    if (publishContent.poetryRecordingInfo && [[publishContent.poetryRecordingInfo allKeys] containsObject:KSPoetryRecordingWorkTitleKey]) {
        // K诗名
        song.name = [publishContent.poetryRecordingInfo objectForKey:KSPoetryRecordingWorkTitleKey];
    }
    else if (publishContent.cappellaUploadInfo && [[publishContent.cappellaUploadInfo allKeys] containsObject:STRQCNAME]) {
        // 清唱作品名
        song.name = [publishContent.cappellaUploadInfo objectForKey:STRQCNAME];
    }
    else if(publishContent.customSongName){
        // 7.0本地视频上传业务，如果选择了伴奏，又设置了作品名， 则作品名存在这。
        song.name = publishContent.customSongName;
    }
    else{
        song.name = publishContent.songName;
    }

    
    song.bIsFrag = publishContent.bSegment;
    song.iStartTime = publishContent.segmentStart;
    song.iEndTime = publishContent.segmentStop;

    song.ugcMask = [publishContent getUgcMaskOfPublishContent];
    song.ugcMaskExt = [publishContent getExtUgcMaskOfPublishCotent];
    song.songMid = publishContent.songMid;
    song.ksongQrcId = publishContent.ksongQrcId;
    song.songDesc = publishContent.contentDesc;
    song.shareDesc = publishContent.contentDesc;
    song.video_width = (JceInt32)publishContent.videoWidth;
    song.video_height = (JceInt32)publishContent.videoHeight;
    song.shareId = [FileUploadEx shareIdForKey:publishContent.productMid]; // shareId多保存至song中
    song.score = publishContent.songScore;
    song.scoreRank = publishContent.needHideRank ? 0 : publishContent.scoreRank;
    song.fLoudness = publishContent.loudness;
    song.strMagicColor = publishContent.magicColor;//本地算的魔法色带过来
    BOOL isPostKKShowProduct = NO;
    // KKShow后处理背景素材不为空，说明是kk秀后处理音频视频化作品
    if (publishContent.inventoryItem) {
        isPostKKShowProduct = YES;
    }
    
    if (isPostKKShowProduct || (!publishContent.isVideoMode && KSABTestManager.sharedManager.videolizeOptimizeStrategy == KSVideolizeOptimizeStrategy_Videolize))
    {
        song.audioTransVideoPlayType = JceTimeline_EnumAudioTransVideoPlayType_eAudioTransVideo_type_native_tempalte_video;
    }
    
    if (publishContent.shortAudioUploadInfo && [[publishContent.shortAudioUploadInfo allKeys] containsObject:KSShortAudioSegmentIdKey])
    {
        song.songDesc = [publishContent.shortAudioUploadInfo objectForKey:KSShortAudioPublishLyricKey];
        song.strSegmentId = [publishContent.shortAudioUploadInfo objectForKey:KSShortAudioSegmentIdKey];
    }
    song.lyricVersionMap = @{@(JceKSongInfo_emContentType_CONTENT_QRC):@"1"};//假feed默认拉歌词版本
    feed.songinfo = song;
    
    KSimpleFeedListener *listener = [[KSimpleFeedListener alloc] init]; // 人数 0
    listener.number = 0;
    feed.listenerInfo = listener;
    
    KSimpleFeedComment *comment = [[KSimpleFeedComment alloc] init]; // 评论数 0
    comment.number = 0;
    feed.comment = comment;
    
    KSimpleFeedFlower *flower = [[KSimpleFeedFlower alloc] init]; // 鲜花数 0
    flower.number = 0;
    feed.recivedFlowerInfo = flower;
    
    //合唱信息
    feed.HcUserInfo = publishContent.userInfo;
    
    // 构造音乐心情假feed
    if (publishContent.publishContentType == KSPublishContentTypePictureUgc)
    {
        KSimpleFeedMusicMood *fakeMusicMood = [[KSimpleFeedMusicMood alloc] init];

        BOOL isMultiPics = (publishContent.uploadedUgcPhotos.count > 1);
        JceUInt32 smallPicHeight = kIMAGELISTSINGLEPICWIDTH;
        JceUInt32 smallPicWidth = kIMAGELISTSINGLEPICWIDTH;
        NSMutableArray<JceTimeline_picinfo *> *picInfoArr = [NSMutableArray array];
        NSArray<KSMusicMoodPicItem *> *uploadedPhotoArr = (publishContent.uploadStep != KSProductUploadStepFinished) ? publishContent.fakeUploadedUgcPhotos : publishContent.uploadedUgcPhotos;

        [uploadedPhotoArr enumerateObjectsUsingBlock:^(KSMusicMoodPicItem * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
            JceTimeline_picinfo* picInfo = [JceTimeline_picinfo new];
            
            JceTimeline_pic_detail *smallPicDetail = [JceTimeline_pic_detail new];
            smallPicDetail.jce_uiWidth = isMultiPics? smallPicWidth:obj.fileUploadMusicFeelPicItem.jce_width;
            smallPicDetail.jce_uiHeight = isMultiPics? smallPicHeight:obj.fileUploadMusicFeelPicItem.jce_height;
            smallPicDetail.jce_strUrl = obj.url;
            
            JceTimeline_pic_detail *picDetail = [JceTimeline_pic_detail new];
            picDetail.jce_uiWidth = obj.fileUploadMusicFeelPicItem.jce_width;
            picDetail.jce_uiHeight = obj.fileUploadMusicFeelPicItem.jce_height;
            picDetail.jce_strUrl = obj.url;
            
            picInfo.jce_mPic = @{@(0):smallPicDetail,@(1):picDetail};
            [picInfoArr addObject:picInfo];
        }];
        
        fakeMusicMood.refUgcId = publishContent.musicMoodUgcId;
        if (IS_EMPTY_STR_BM(fakeMusicMood.refUgcId))
        {
            fakeMusicMood.refMid = publishContent.songMid;
            fakeMusicMood.refSongName = publishContent.songName;
        }
        
        fakeMusicMood.vecPic = picInfoArr;
        fakeMusicMood.strContent = publishContent.contentDesc;
        fakeMusicMood.shareId = publishContent.musicMoodShareId;
        
        feed.isMusicMoodFeed = YES;
        feed.musicMoodFeed = fakeMusicMood;
    }
    
    /// 给假 feed 填充视频化的信息
    if (publishContent.videolizePublishContent)
    {
        feed.videolizeProduct = [publishContent.videolizePublishContent toVideolizeProduct];
        feed.songinfo.streamVideo_width = 9;
        if (feed.videolizeProduct.ratio == KSPublishVideolizeRatio_16_9)
        {
            feed.songinfo.streamVideo_height = 16;
        }
        else
        {
            feed.songinfo.streamVideo_height = 9;
        }
        feed.songinfo.ugcMaskExt |= [publishContent.videolizePublishContent ugc_mask_ext];
        if (publishContent.videolizeSquareFrame.url)
        {
            feed.songinfo.strIFrameUrl = publishContent.videolizeSquareFrame.url;
        }
        else if (publishContent.videolizeSquareFrame.localPath)
        {
            feed.songinfo.strIFrameUrl = [UIImageView generateFakeUrlForLocalImage:publishContent.videolizeSquareFrame.localPath];
        }
    }
    
    // 填充话题信息
    if (publishContent.topicList.count > 0) {
        NSMutableArray *tmp = [NSMutableArray array];
        for (KSTopicSummaryInfo *topicInfo in publishContent.topicList) {
            [tmp safeAddObject:[topicInfo createFakeFeedTopicInfo]];
        }
        feed.topicsArray = [tmp copy];
        feed.topicFeed = [JceTimeline_cell_topic new];
        feed.topicFeed.jce_vctTopics = feed.topicsArray;
    }
    
    KSLBS *lbs = [KSLBS new];
    lbs.fLat = publishContent.location.lat;
    lbs.fLon = publishContent.location.lon;
    lbs.strPoiId = publishContent.location.poiId;
    lbs.strPoiName = publishContent.location.poiName;
    lbs.strPoiNameAddress = publishContent.location.poiAddress;
    lbs.nDistrictCode = publishContent.cityCode;
    lbs.strCity = publishContent.cityName;
    if (lbs.strPoiName.length > 0) {
        lbs.enPositionType = 1;
    } else if (lbs.strCity.length > 0) {
        lbs.enPositionType = 3;
    }
    feed.lbs = lbs;
    
    return feed;
}

@end

@implementation KSimpleFeed (ReCard)

- (BOOL)isAudioTransVideo
{
    return self.isBackendRenderVideo || self.isTemplateVideo;
}

- (BOOL)isBackendRenderVideo
{
    return self.songinfo.audioTransVideoPlayType & JceTimeline_EnumAudioTransVideoPlayType_eAudioTransVideo_type_single_video;
}

- (BOOL)isTemplateVideo
{
    return self.songinfo.audioTransVideoPlayType & JceTimeline_EnumAudioTransVideoPlayType_eAudioTransVideo_type_audio_and_template_video;
}

- (NSString *)templateVideoVid
{
    return self.songinfo.strAccVideoVid;
}

@end

@implementation KSimpleFeed (KSFakePlayback)

- (KSFeedPlayback *)fakeUgcPlayback
{
    KSFeedPlayback *playback = [KSFeedPlayback new];
    return playback;
}

- (KSFeedPlayback *)fakeTemplateVideoPlayback
{
    KSFeedPlayback *playback = [KSFeedPlayback new];
    return playback;
}

@end

// 
@implementation KSimpleFeed (KSNearByBountyGame)

// 解析url中的字符串获取赏金赛gameId
- (nullable NSString *)getBountyGameIdWithUrl:(NSString *)strJumpUrl
{
    if (IS_EMPTY_STR_BM(strJumpUrl)) return nil;
    
    NSURL *jumpUrl = [NSURL URLWithString:strJumpUrl];
    NSDictionary *params = [NSString dictionaryFromQuery:jumpUrl.query usingEncoding:NSUTF8StringEncoding];
    NSString* king = @"king";
    NSString* r = @"moneyPKRace";
    if ([king isEqualToString:[params objectForKey:@"hippy"]] &&
        [r isEqualToString:[params objectForKey:@"r"]])
    {
        return [params objectForKey:@"gameid"];
    }
    return nil;
}

@end

#pragma mark - 学唱专区
@implementation KSimpleFeed (KSStudySing)

//是否是左边底部具有透明条提示feed,目前用于学唱专区引流（类似--story=870920789 也歌房底部样式）
- (BOOL)isBottomBoxFeed;
{
    return self.cellExtraInfo.jce_strBottomBoxText.length > 0;
}

//是否有想学按钮
- (BOOL)isAddStudySingFeed;
{
    return self.cellExtraInfo.jce_eEIType == proto_feed_webapp_ENUM_EXTRA_INFO_TYPE_ENUM_EI_TYPE_LEARN_SING_JUMP;
}

//是否ta合唱
- (BOOL)isChorusWithItFeed
{
    return self.cellExtraInfo.jce_eEIType == proto_feed_webapp_ENUM_EXTRA_INFO_TYPE_ENUM_EI_TYPE_SOLO_HC;
}

- (BOOL)isAnchorTagAnimation {
    return self.tagsInfoV2.jce_labelAnchorPoint.jce_iHasAnimation == 1;
}

@end


#pragma mark - 看广告送礼
@implementation KSimpleFeed (FreeADGift)

// 覆盖下主类的实现
- (KSimpleFeedGiftGuide *)giftGuide {
    if (([self isRecCardFeed] || [self isFollowTabFeed]) &&
        [KSTimelineManager sharedManager].adFreeGift) {
        return [KSTimelineManager sharedManager].adFreeGift;
    } else {
        return _giftGuide;
    }
}

- (proto_feed_webapp_GetDataAfterExposureRsp *)followGiftBtnInfo {
    if (([self isRecCardFeed] || [self isFollowTabFeed]) &&
        [KSTimelineManager sharedManager].adFreeGiftBtnInfo) {
        return [KSTimelineManager sharedManager].adFreeGiftBtnInfo;
    } else {
        return _followGiftBtnInfo;
    }
}

@end
