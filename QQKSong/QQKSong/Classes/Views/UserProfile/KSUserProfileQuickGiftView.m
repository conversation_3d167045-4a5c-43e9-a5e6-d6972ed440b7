//
//  KSUserProfileQuickGiftView.m
//  QQKSong
//
//  Created by jamywu on 2023/7/24.
//  Copyright © 2023 Tencent. All rights reserved.
//

#import "KSUserProfileQuickGiftView.h"
#import "proto_new_gift_Gift.h"
#import "KSIapGiftManager.h"
#import "KSIapGift.h"
#import "proto_quick_gift_webapp_QueryQuickSendInfoWebRsp.h"
#import "proto_quick_gift_webapp_QuickSendInfo.h"
#import "proto_quick_gift_webapp_QUICK_CONSUME.h"
#import "KSAdFreeGiftScene.h"
#import "KSAdFreeGiftManager.h"
// 9.11 看广告免费送礼迭代实验
#import "UIImageView+KSImageLoader.h"
#import "KSABTestManager.h"

@interface KSUserProfileQuickGiftView ()

@property (nonatomic, strong) UILabel *sendGiftTitle; // 送礼文案，调出送礼面板的

@property (nonatomic, strong) UIView *centerAnchorView;//居中锚点

@property (nonatomic, strong) UIImageView *giftIcon; // 礼物图标
@property (nonatomic, strong) UILabel *priceTxt; // 价格
@property (nonatomic, strong) UILabel *descTxt; // 快捷送礼文案
@property (nonatomic, strong) UILabel *assetTxt; // 资产来源
@property (nonatomic, strong) UIImageView *assetIcon; // 资产背景图
@property (nonatomic, strong) CAGradientLayer *gradientLayer; // 底色渐变效果
// 9.11 看广告免费送礼迭代实验
@property (nonatomic, strong) UIView *dualButtonContainerView;
@property (nonatomic, strong) UIButton *normalGiftButton;
@property (nonatomic, strong) UIButton *freeGiftButton;
@property (nonatomic, strong) UIImageView *freeGiftIcon;
@property (nonatomic, strong) NSString *adGiftIconUrl;

@end

@implementation KSUserProfileQuickGiftView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setupView];
        [self setupTap];
    }
    return self;
}

#pragma mark - Layout
- (void)setupView {
    self.clipsToBounds = YES;
    self.layer.cornerRadius = self.height / 2.0;
    self.backgroundColor = [UIColor ks_colorWithRGBString:@"#FE4F4F"];
    
    self.centerAnchorView =({
        UIView *v = [UIImageView new];
        v.backgroundColor = UIColor.clearColor;
        [self addSubview:v];
        [v mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(self);
        }];
        v;
    });

    self.gradientLayer = [CAGradientLayer new];
    self.gradientLayer.colors = @[
        (__bridge id)[UIColor ks_colorWithRGBString:@"#FE4F4F"].CGColor,
        (__bridge id)[[UIColor ks_colorWithRGBString:@"#FE4F4F"] colorWithAlphaComponent:0.6].CGColor,
        (__bridge id)[UIColor ks_colorWithRGBString:@"#FE4F4F"].CGColor
    ];
    self.gradientLayer.locations = @[@(0.0), @(0.35), @(1.0)];
    self.gradientLayer.startPoint = CGPointMake(0.15, 0.5);
    self.gradientLayer.endPoint = CGPointMake(0.75, 0.5);
    self.gradientLayer.frame = self.bounds;
    [self.layer addSublayer:self.gradientLayer];
    
    self.sendGiftTitle = ({
        UILabel *title = [UILabel new];
        title.text = @"送礼";
        title.font = [UIFont ks_fontWithFontType:KSFontType_MiddleBold];
        title.textColor = [UIColor ks_whiteColor];
        title.textAlignment = NSTextAlignmentCenter;
        [self addSubview:title];
        title;
    });
    [self.sendGiftTitle mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self);
    }];
    
    self.giftIcon = ({
        UIImageView *icon = [UIImageView new];
        icon.contentMode = UIViewContentModeScaleAspectFit;
        icon.hidden = YES;
        [self addSubview:icon];
        icon;
    });
    [self.giftIcon mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(CGSizeMake(30, 30));
        make.left.equalTo(self.mas_right).multipliedBy(0.25);
        make.top.equalTo(self).offset(-2);
    }];
    
    self.priceTxt = ({
        UILabel *label = [UILabel new];
        label.font = [UIFont systemFontOfSize:8 weight:UIFontWeightSemibold];
        label.textColor = [[UIColor ks_whiteColor] colorWithAlphaComponent:0.8];
        label.textAlignment = NSTextAlignmentCenter;
        label.hidden = YES;
        [self addSubview:label];
        label;
    });
    [self.priceTxt mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.giftIcon.mas_bottom).offset(-2);
        make.centerX.equalTo(self.giftIcon);
    }];
    
    self.descTxt = ({
        UILabel *label = [UILabel new];
        label.font = [UIFont ks_fontWithFontType:KSFontType_MiddleBold];
        label.textColor = [UIColor ks_whiteColor];
        label.textAlignment = NSTextAlignmentCenter;
        [self addSubview:label];
        label.hidden = YES;
        label;
    });
    [self.descTxt mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.giftIcon.mas_right).offset(6);
        make.centerY.equalTo(self);
    }];
    
    self.assetIcon = ({
        UIImageView *icon = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"feed_rec_badge_bg_v2"]];
        icon.contentMode = UIViewContentModeScaleAspectFit;
        icon.hidden = YES;
        [self addSubview:icon];
        icon;
    });
    [self.assetIcon mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(CGSizeMake(30, 11));
        make.top.equalTo(self);
        make.right.equalTo(self);
    }];
    
    self.assetTxt = ({
        UILabel *label = [UILabel new];
        label.font = [UIFont systemFontOfSize:7 weight:UIFontWeightSemibold];
        label.textColor = [UIColor ks_colorWithRGBString:@"#FE4F4F"];
        label.hidden = YES;
        label.textAlignment = NSTextAlignmentCenter;
        [self addSubview:label];
        label;
    });
    [self.assetTxt mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.assetIcon).offset(-2);
        make.centerY.equalTo(self.assetIcon);
        make.size.mas_equalTo(CGSizeMake(30, 11));
    }];
}

- (void)updateLayerFrame {
    self.gradientLayer.frame = self.bounds;
    self.layer.cornerRadius = self.height / 2.0;
}

- (void)updateViewWithGiftRsp:(proto_quick_gift_webapp_QueryQuickSendInfoWebRsp *)resp {
    if (!resp) {
        return;
    }
    KLog(@"[UserProfile QuickGift] loaded gift info: %@", resp.jce_stInfo);
    
    // 9.11 看广告免费送礼迭代实验
    [self checkExperimentConfig];
    if (resp.jce_stInfo.jce_stAdvertGift && ![resp.jce_stInfo.jce_stAdvertGift.jcev2_p_2_o_strLogo isEmpty]) {
        self.adGiftIconUrl = [[WnsConfigManager sharedInstance].appConfig.urlConfig getIapGiftPicUrlByStrLogo:resp.jce_stInfo.jce_stAdvertGift.jcev2_p_2_o_strLogo];
    } else {
        self.adGiftIconUrl = nil;
    }
    
    if (self.sceneShow && resp.jce_stInfo.jce_uIsAdvertGift == 1  && self.giftBtnNum != 0) {
        [self setupFreeGiftExperimentView:resp];
        return;
    }
    
    if (resp.jce_stInfo.jce_uIsAdvertGift == 1 && [KSAdFreeGiftScene canShowEntranceWithType:KSAdFreeGiftSceneType_Personal]) {
        //看视频送礼
        self.descTxt.alpha = 0;
        self.giftIcon.alpha = 0;
        self.descTxt.hidden = NO;
        self.giftIcon.hidden = NO;
        
        if (self.adGiftIconUrl) {
            [self.giftIcon ks_setImageWithUrl:self.adGiftIconUrl];
        } else {
            self.giftIcon.image = [UIImage imageNamed:@"ad_free_gift_icon"];
        }
        self.descTxt.text = @"看视频免费送";
        if (kScreenWidthBM() == 320) {
            //系统放大模式
            self.descTxt.text = @"免费送";
        }
//        CGSize textSize = [self.descTxt.text kSongSizeWithFont:self.descTxt.font];
        [self.giftIcon mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.size.mas_equalTo(CGSizeMake(25, 25));
            make.left.equalTo(self.mas_right).multipliedBy(0.15);
            make.centerY.equalTo(self).offset(2);
        }];
        [UIView animateWithDuration:0.3 animations:^{
            self.descTxt.alpha = 1;
            self.giftIcon.alpha = 1;
            self.sendGiftTitle.alpha = 0;
        } completion:^(BOOL finished) {
            // 隐藏主送礼
            self.sendGiftTitle.alpha = 1;
            self.sendGiftTitle.hidden = YES;
        }];
        self.giftType = KSUserProfileRecommendHeadViewGiftType_FreeAd;
        
        [KSAdFreeGiftManager addShakeAnimationToGiftIcon:self.giftIcon];
        [KSAdFreeGiftScene recordEntranceWithType:KSAdFreeGiftSceneType_Personal];
        !self.exposureBlock ?: self.exposureBlock(self.giftType);
        return;
    }
    // 隐藏主送礼
    self.sendGiftTitle.hidden = YES;
    self.giftType = KSUserProfileRecommendHeadViewGiftType_QuickGift;
    !self.exposureBlock ?: self.exposureBlock(self.giftType);
    
    proto_new_gift_Gift *kbGift = resp.jce_stInfo.jce_stGift;
    
    // 显示快捷送礼
    NSString *logoUrl = [WnsConfigManager.sharedInstance.appConfig.urlConfig getIapGiftPicUrlByStrLogo:kbGift.jce_strLogo];
    [self.giftIcon setImageWithUrl:logoUrl];
    self.giftIcon.hidden = NO;
    self.priceTxt.text = [NSString stringWithFormat:@"%uK币", kbGift.jce_uPrice];
    self.priceTxt.hidden = NO;
    self.descTxt.text = resp.jce_stInfo.jce_strButtonStrategyCopy;
    if ([WnsConfigManager sharedInstance].isPGSH) {
        self.descTxt.text = @"立即送";
    }
    self.descTxt.hidden = NO;
    self.assetTxt.text = resp.jce_stInfo.jce_strGiftCopy;
    // 有资产信息再展示资产信息
    BOOL shouldShow = !IS_EMPTY_STR_BM(resp.jce_stInfo.jce_strGiftCopy);
    self.assetTxt.hidden = !shouldShow;
    self.assetIcon.hidden = !shouldShow;
}


#pragma mark - Action
- (void)setupTap {
    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(onTapView)];
    [self addGestureRecognizer:tap];
}

- (void)onTapView {
    if (self.sceneShow && self.giftBtnNum == 2) {
        return;
    }
    
    if (self.clickedBlock) {
        self.clickedBlock(self.giftType);
    }
    [self changeToNormal];
}

- (CGFloat)showTime;
{
    switch (self.giftType) {
        case KSUserProfileRecommendHeadViewGiftType_QuickGift:
            return 10;
        case KSUserProfileRecommendHeadViewGiftType_FreeAd:
            return 5;
        case KSUserProfileRecommendHeadViewGiftType_Normal:
        default:
            return 0;
    }
}

// 切换默认底色，"送礼"居中
- (void)changeToNormal {
    KLog(@"[UserProfile QuickGift] change to normal, isQuickGift: %d", self.giftType);
    if (self.giftType == KSUserProfileRecommendHeadViewGiftType_Normal) {
        return;
    }
    BOOL isFreeAd = self.giftType == KSUserProfileRecommendHeadViewGiftType_FreeAd;
    BOOL isDualButtonMode = self.sceneShow && self.giftBtnNum == 2;
    
    self.giftType = KSUserProfileRecommendHeadViewGiftType_Normal;
    !self.exposureBlock ?: self.exposureBlock(self.giftType);
    [self.gradientLayer removeFromSuperlayer];
    self.backgroundColor = [UIColor ks_colorWithRGBString:@"#FE4F4F"];
    
    if (isDualButtonMode) {
        self.sendGiftTitle.hidden = NO;
        self.sendGiftTitle.alpha = 0;
        [UIView animateWithDuration:0.3 animations:^{
            self.dualButtonContainerView.alpha = 0;
            self.sendGiftTitle.alpha = 1;
        } completion:^(BOOL finished) {
            self.dualButtonContainerView.hidden = YES;
            self.dualButtonContainerView.alpha = 1;
        }];
        return;
    }

    if (isFreeAd) {
        self.sendGiftTitle.hidden = NO;
        self.sendGiftTitle.alpha = 0;
        [UIView animateWithDuration:0.3 animations:^{
            // 隐藏快捷送礼
            self.descTxt.alpha = 0;
            self.giftIcon.alpha = 0;
            self.assetIcon.alpha = 0;
            self.assetTxt.alpha = 0;
            self.priceTxt.alpha = 0;
            self.sendGiftTitle.alpha = 1;
        } completion:^(BOOL finished) {
            
            // 隐藏快捷送礼
            self.descTxt.hidden = YES;
            self.giftIcon.hidden = YES;
            self.assetIcon.hidden = YES;
            self.assetTxt.hidden = YES;
            self.priceTxt.hidden = YES;
            self.descTxt.alpha = 1;
            self.giftIcon.alpha = 1;
            self.assetIcon.alpha = 1;
            self.assetTxt.alpha = 1;
            self.priceTxt.alpha = 1;
        }];
        return;
    }
    self.sendGiftTitle.hidden = NO;

    // 隐藏快捷送礼
    self.descTxt.hidden = YES;
    self.giftIcon.hidden = YES;
    self.assetIcon.hidden = YES;
    self.assetTxt.hidden = YES;
    self.priceTxt.hidden = YES;
}

#pragma mark - 9.11 看广告免费送礼迭代实验
- (void)checkExperimentConfig {
    KSAdFreeGiftScene *personalScene = [KSAdFreeGiftScene sceneWithType:KSAdFreeGiftSceneType_Personal];
    self.sceneShow = personalScene.scenceShow;
    self.giftBtnNum = [personalScene getGiftBtnNum];
}

- (void)setupFreeGiftExperimentView:(proto_quick_gift_webapp_QueryQuickSendInfoWebRsp *)resp {
    self.giftType = KSUserProfileRecommendHeadViewGiftType_FreeAd;
    if (self.sceneShow) {
        //命中实验
        self.giftBtnNum = 2;
        if (self.giftBtnNum == 1) {
            // 方案A：固化免费送单按钮
            [self setupSchemeA:resp];
        } else if (self.giftBtnNum == 2) {
            // 方案B：双按钮共存
            [self setupSchemeB:resp];
    }
    } else {
        KLog(@"[看广告免费送礼迭代实验] Fallback to old logic, buttonScheme: %ld", (long)self.giftBtnNum);
        return;
    }
    
    [KSAdFreeGiftScene recordEntranceWithType:KSAdFreeGiftSceneType_Personal];
    !self.exposureBlock ?: self.exposureBlock(self.giftType);
}

- (void)setupSchemeA:(proto_quick_gift_webapp_QueryQuickSendInfoWebRsp *)resp {
    
    self.sendGiftTitle.hidden = YES;
    self.sendGiftTitle.alpha = 0;
    
    self.descTxt.alpha = 0;
    self.giftIcon.alpha = 0;
    self.descTxt.hidden = NO;
    self.giftIcon.hidden = NO;
    
    if (self.adGiftIconUrl) {
        [self.giftIcon ks_setImageWithUrl:self.adGiftIconUrl];
    } else {
        self.giftIcon.image = [UIImage imageNamed:@"ad_free_gift_icon"];
    }
    
    self.descTxt.text = @"看视频免费送";
    if (kScreenWidthBM() == 320) {
        self.descTxt.text = @"免费送";
    }
    
    [self.giftIcon mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(CGSizeMake(24, 24));
        make.left.equalTo(self.mas_right).multipliedBy(0.15);
        make.centerY.equalTo(self).offset(2);
    }];
    
    [UIView animateWithDuration:0.3 animations:^{
        self.descTxt.alpha = 1;
        self.giftIcon.alpha = 1;
    } completion:^(BOOL finished) {
        [KSAdFreeGiftManager addShakeAnimationToGiftIcon:self.giftIcon];
    }];
}

- (void)setupSchemeB:(proto_quick_gift_webapp_QueryQuickSendInfoWebRsp *)resp {
    self.sendGiftTitle.hidden = YES;
    self.sendGiftTitle.alpha = 0;
    
    self.descTxt.alpha = 0;
    self.giftIcon.alpha = 0;
    self.descTxt.hidden = NO;
    self.giftIcon.hidden = NO;
    
    if (self.adGiftIconUrl) {
        [self.giftIcon ks_setImageWithUrl:self.adGiftIconUrl];
    } else {
        self.giftIcon.image = [UIImage imageNamed:@"ad_free_gift_icon"];
    }
    
    self.descTxt.text = @"免费送";
    
    [self.giftIcon mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(CGSizeMake(24, 24));
        make.left.equalTo(self.mas_right).multipliedBy(0.15);
        make.centerY.equalTo(self).offset(2);
    }];
    
    [UIView animateWithDuration:0.3 animations:^{
        self.descTxt.alpha = 1;
        self.giftIcon.alpha = 1;
    } completion:^(BOOL finished) {
        [KSAdFreeGiftManager addShakeAnimationToGiftIcon:self.giftIcon];
    }];
}

- (void)hideSingleButtonViews {
    self.descTxt.hidden = YES;
    self.giftIcon.hidden = YES;
    self.priceTxt.hidden = YES;
    self.assetTxt.hidden = YES;
    self.assetIcon.hidden = YES;
}

- (void)onTapNormalGiftButton {
    self.giftType = KSUserProfileRecommendHeadViewGiftType_Normal;
    if (self.clickedBlock) {
        self.clickedBlock(self.giftType);
    }
    [self changeToNormal];
}

- (void)onTapFreeGiftButton {
    self.giftType = KSUserProfileRecommendHeadViewGiftType_FreeAd;
    if (self.clickedBlock) {
        self.clickedBlock(self.giftType);
    }
    [self changeToNormal];
}

@end
