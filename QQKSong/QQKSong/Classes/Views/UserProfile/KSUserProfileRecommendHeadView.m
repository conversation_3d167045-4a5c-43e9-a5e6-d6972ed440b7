//
//  KSUserProfileRecommendHeadView.m
//  QQKSong
//
//  Created by mac on 2018/5/24.
//  Copyright © 2018年 Tencent. All rights reserved.
//

#import "KSUserProfileRecommendHeadView.h"
#import "KSTraceReportManager+BuyKB.h"
#import "KSABTestManager.h"
#import "KSUserProfileInfo.h"
#import "KSAdFreeGiftScene.h"
#import "KSTraceReportManager+BuyKB.h"
#import "KSUserProfileQuickGiftView.h"
#define KSFontBlackColorV2 [UIColor ks_colorWithHexString:@"#333333"]   // 黑色
#define KSFontGrayColorV2 [UIColor ks_colorWithHexString:@"#999999"]   // 灰色

static NSString *const kProfileVCBdayAnimationShownKey = @"kProfileVCBdayAnimationShownKey";

static const CGFloat kDefaultMargin = 20.0f;
static const CGFloat kItemSpacing = 8.0f;
#define KItemSizeW ((SCREEN_WIDTH - kDefaultMargin * 2 - kItemSpacing * 2) / 4.0)
#define KItemSizeH 36

#define kShowRankRightMargin 16
#define kShowRankItemSizeW 36
#define kRankViewSizeW (SCREEN_WIDTH <= 320 ? 110 : 147.0)
#define kShowRankItemSpacing ((SCREEN_WIDTH - kDefaultMargin - kShowRankRightMargin - kRankViewSizeW - KItemSizeW - kShowRankItemSizeW * 2) / 3.0)

@interface KSUserProfileRecommendHeadView()

@property (nonatomic, assign) BOOL follow;
@property (nonatomic, assign) BOOL unSettledInStar;

@property (nonatomic, strong) UIView *buttonSeparator;
@property (nonatomic, strong) UIButton *followButton;
@property (nonatomic, strong) UIButton *giftButton;
@property (nonatomic, strong) UIButton *messageButton;
@property (nonatomic, strong) UIButton *floatTipViewButton;

@property (nonatomic, assign) BOOL showAnimation;
@property (nonatomic, strong) KSUserProfileQuickGiftView *quickGiftView;

@property (nonatomic, strong) UIView *redView;

@property (nonatomic, strong)CAShapeLayer *shapeLayer;
@property (nonatomic, strong)CAGradientLayer *gradientLayer;

@property (nonatomic, assign) BOOL sceneShow;
@property (nonatomic, assign) NSInteger giftBtnNum;
@end

@implementation KSUserProfileRecommendHeadView


- (instancetype)initWithFrame:(CGRect)frame
{
    if (self = [super initWithFrame:frame]) {
        self.unSettledInStar = NO;
        [self bulidSubViews];
    }
    return self;
}


- (void)bulidSubViews
{
    self.backgroundColor = [UIColor clearColor];
    
    // 默认2:1:1，未入住的1:1
    CGFloat itemW = KItemSizeW;
    CGFloat itemH = KItemSizeH;
    
    // 送礼
    KSBaseButton *giftButton = [[KSBaseButton alloc] init];
    [giftButton addTarget:self action:@selector(giftButtonClick) forControlEvents:UIControlEventTouchUpInside];
    [giftButton.titleLabel setFont:[UIFont ks_fontWithFontType:KSFontType_MiddleBold]];
    [giftButton setTitle:KString(@"送礼") forState:UIControlStateNormal];
    [giftButton setBackgroundColor:UIColor.clearColor forState:UIControlStateNormal];
    [giftButton setTitleColor:UIColor.ks_surfaceBGColor forState:UIControlStateNormal];
    giftButton.frame = CGRectMake(kDefaultMargin, 0, itemW, itemH);
    giftButton.layer.borderColor = UIColor.ks_whiteColor.CGColor;
    giftButton.layer.borderWidth = 1;
    giftButton.layer.cornerRadius = itemH / 2.0;
    giftButton.layer.masksToBounds = YES;
    [self addSubview:giftButton];
    giftButton.isAccessibilityElement = YES;
    giftButton.accessibilityLabel = @"送礼";
    giftButton.accessibilityTraits = UIAccessibilityTraitNone;
    
    self.giftButton = giftButton;
    
    // 私信
    KSBaseButton *messageButton = [[KSBaseButton alloc] init];
    messageButton.frame = CGRectMake(self.giftButton.right + kItemSpacing, 0, itemW, itemH);
    [messageButton.titleLabel setFont:[UIFont ks_fontWithFontType:KSFontType_MiddleBold]];
    [messageButton setTitle:KString(@"私信") forState:UIControlStateNormal];
    [messageButton setBackgroundColor:UIColor.clearColor forState:UIControlStateNormal];
    [messageButton setTitleColor:UIColor.ks_surfaceBGColor forState:UIControlStateNormal];
    messageButton.layer.cornerRadius = itemH / 2.0;
    messageButton.layer.masksToBounds = YES;
    messageButton.layer.borderColor = UIColor.ks_whiteColor.CGColor;
    messageButton.layer.borderWidth = 1;
    [messageButton addTarget:self action:@selector(sendMessageButtonClick:) forControlEvents:UIControlEventTouchUpInside];
    [self addSubview:messageButton];
    messageButton.isAccessibilityElement = YES;
    messageButton.accessibilityLabel = @"私信";
    messageButton.accessibilityTraits = UIAccessibilityTraitNone;
    self.messageButton = messageButton;
    
    // 关注
    KSBaseButton *followButton = [[KSBaseButton alloc] init];
    followButton.frame = CGRectMake(self.messageButton.right + kItemSpacing, 0, itemW * 2, itemH);
    [followButton.titleLabel setFont:[UIFont ks_fontWithFontType:KSFontType_MiddleBold]];
    [followButton setTitle:KString(@"关注") forState:UIControlStateNormal];
    [followButton setTitle:KString(@"已关注") forState:UIControlStateSelected];
    [followButton setBackgroundColor:[UIColor ks_colorWithHexString:@"#fe4f4f"] forState:UIControlStateNormal];
    [followButton setBackgroundColor:UIColor.clearColor forState:UIControlStateSelected];
    [followButton setTitleColor:UIColor.ks_surfaceBGColor forState:UIControlStateNormal];
    [followButton setTitleColor:UIColor.ks_surfaceBGColor forState:UIControlStateSelected];
    followButton.layer.cornerRadius = itemH / 2.0;
    followButton.layer.masksToBounds = YES;
    [followButton addTarget:self action:@selector(followBtnClick:) forControlEvents:UIControlEventTouchUpInside];
    followButton.isAccessibilityElement = YES;
    followButton.accessibilityLabel = @"关注";
    followButton.accessibilityTraits = UIAccessibilityTraitNone;
    [self addSubview:followButton];
    self.followButton = followButton;
    
    [self updateSubViews];
}

- (void)setReverseFollow:(BOOL)reverseFollow {
    if (reverseFollow) {
        [self.followButton setTitle:KString(@"回粉") forState:UIControlStateNormal];
        [self.followButton setTitle:KString(@"互相关注") forState:UIControlStateSelected];
    } else {
        [self.followButton setTitle:KString(@"关注") forState:UIControlStateNormal];
        [self.followButton setTitle:KString(@"已关注") forState:UIControlStateSelected];
    }
    
    self.followButton.accessibilityLabel = self.followButton.selected ? [self.followButton titleForState:UIControlStateSelected] : [self.followButton titleForState:UIControlStateNormal];
}

- (void)setFollow:(BOOL)follow animation:(BOOL)animation
{
    _follow = follow;
    _showAnimation = animation;
    [self updateSubViews];
}

- (BOOL)isFollow
{
    return _follow;
}

- (void)setUserProfileInfo:(KSUserProfileInfo *)userProfileInfo
{
    _userProfileInfo = userProfileInfo;
    self.unSettledInStar = (userProfileInfo.userInfo.isStar && !userProfileInfo.userInfo.isStarSettledIn);
    [self updateSubViews];
}


- (void)updateSubViews {
  
    if (self.follow) {
        self.followButton.selected = YES;
        self.followButton.layer.borderColor = UIColor.ks_whiteColor.CGColor;
        self.followButton.layer.borderWidth = 1;
        self.giftButton.layer.borderWidth = 0;
        [self.giftButton setBackgroundColor:[UIColor ks_colorWithHexString:@"#fe4f4f"]];
    } else {
        self.followButton.selected = NO;
        self.followButton.layer.borderColor = nil;
        self.followButton.layer.borderWidth = 0;
        self.giftButton.layer.borderWidth = 1;
        [self.giftButton setBackgroundColor:UIColor.clearColor];
    }
    
    self.followButton.accessibilityLabel = self.followButton.selected ? [self.followButton titleForState:UIControlStateSelected] : [self.followButton titleForState:UIControlStateNormal];
    
    // 默认2:1:1，未入住的1:1
    CGFloat itemW = KItemSizeW;
    CGFloat itemH = KItemSizeH;
    KSAdFreeGiftScene *personalScene = [KSAdFreeGiftScene sceneWithType:KSAdFreeGiftSceneType_Personal];
    self.sceneShow = personalScene.scenceShow;
    self.giftBtnNum = [personalScene getGiftBtnNum];
    self.giftBtnNum = 2;
    if (self.unSettledInStar) {
        // 未入住的1:1
        self.messageButton.hidden = YES;
        itemW = (SCREEN_WIDTH - kDefaultMargin * 2 - kItemSpacing) / 3.0;
        if (self.follow) {
            self.giftButton.frame = CGRectMake(kDefaultMargin, 0, itemW * 2, itemH);
            self.followButton.frame = CGRectMake(self.giftButton.right + kItemSpacing, 0, itemW, itemH);
        } else {
            self.giftButton.frame = CGRectMake(kDefaultMargin, 0, itemW, itemH);
            self.followButton.frame = CGRectMake(self.giftButton.right + kItemSpacing, 0, itemW * 2, itemH);
        }
    } else {
        self.messageButton.hidden = NO;
        if (self.follow) {
            self.giftButton.frame = CGRectMake(kDefaultMargin, 0, itemW * 2, itemH);
            self.messageButton.frame = CGRectMake(self.giftButton.right + kItemSpacing, 0, itemW, itemH);
            self.followButton.frame = CGRectMake(self.messageButton.right + kItemSpacing , 0, itemW, itemH);
        } else {
            self.giftButton.frame = CGRectMake(kDefaultMargin, 0, itemW, itemH);
            self.messageButton.frame = CGRectMake(self.giftButton.right + kItemSpacing, 0, itemW, itemH);
            self.followButton.frame = CGRectMake(self.messageButton.right + kItemSpacing , 0, itemW * 2, itemH);
        }
    }
    
    if (self.sceneShow && self.giftBtnNum == 2) {
        //B实验新样式
        [self setupSchemeB];
    }
    [self checkQuickGiftShowIfNeed];
}

- (void)showGiftTipViewWithTips:(NSString*)showContent
{
    // 先移除旧气泡
    [self removeGiftTipViewBubble];
    self.floatTipViewButton = [KSBaseButton buttonWithTitle:showContent
                                                     target:self
                                                     action:@selector(removeGiftTipViewBubble)
                                                       rect:CGRectZero
                                                        tag:0];
    self.floatTipViewButton.backgroundColor = [UIColor ks_colorWithHexString:@"#FC1717"];
    [self.floatTipViewButton setTitleColor:KSFontColor_c3 forState:UIControlStateNormal];
    [self.floatTipViewButton.titleLabel setFont:KSFont_t3];
    self.floatTipViewButton.layer.cornerRadius = 4;
    self.floatTipViewButton.clipsToBounds = YES;
    CGRect rect = [showContent boundingRectWithSize:CGSizeMake(SCREEN_WIDTH, 30) options:NSStringDrawingUsesFontLeading attributes:@{NSFontAttributeName:KSFont_t3} context:nil];
    CGFloat floatingTipView_Width = rect.size.width + 30;
    CGFloat floatingTipView_Height = 40;
    
    CGFloat floatTipViewX = self.giftButton.centerX - floatingTipView_Width/2;
    CGFloat floatTipViewY = self.giftButton.top - floatingTipView_Height - 10 - 8;
    
    // 调整气泡位置，居中对齐送礼按钮
    if ((SCREEN_WIDTH - floatingTipView_Width - 20) < floatTipViewX)
    {
        floatTipViewX = SCREEN_WIDTH - floatingTipView_Width - 20;
    }
    // 调整气泡位置
    self.floatTipViewButton.frame = CGRectMake(floatTipViewX, floatTipViewY, floatingTipView_Width, floatingTipView_Height);
    [self.giftButton.superview addSubview:self.floatTipViewButton];
    
    // 计算箭头位置
    UIImage *arrowImage = [UIImage imageNamed:@"profile_bubble_arrow_red_icon"];
    UIImageView *arrowImageView = [[UIImageView alloc] initWithImage:arrowImage];
    CGFloat arrowImageViewX = (self.floatTipViewButton.centerX < self.giftButton.centerX) ? (self.floatTipViewButton.width - self.giftButton.width/2 - arrowImage.size.width/2) : self.floatTipViewButton.width/2 - arrowImage.size.width/2;
    arrowImageView.frame = CGRectMake(arrowImageViewX, self.floatTipViewButton.height, arrowImage.size.width, arrowImage.size.height);
    [self.floatTipViewButton addSubview:arrowImageView];
    self.floatTipViewButton.clipsToBounds = NO;
    
    // 添加气泡动画
    [self.floatTipViewButton ks_setAnchorPoint:CGPointMake(arrowImageView.centerX/self.floatTipViewButton.width, 1)];
    CAKeyframeAnimation* floatTipAnimation = [KSAnimationSupport showBubbleViewAnimationValues:@[@0.01, @1.1, @1] keyTimes:@[@0, @0.5, @1] duration:0.5 repeatCount:1 delegate:nil];
    [self.floatTipViewButton.layer addAnimation:floatTipAnimation forKey:@"showGuide"];
}

- (void)removeGiftTipViewBubble
{
    if (self.floatTipViewButton) {
        [self.floatTipViewButton removeAllSubviews];
        [self.floatTipViewButton removeFromSuperview];
    }
}

#pragma mark - 快捷送礼

- (void)checkQuickGiftShowIfNeed
{
    if (self.userProfileInfo.userInfo) {
        // 部分后台明星名单,未入驻明星,不需要显示快捷送礼入口
        BOOL isLogicAuthStar = [self.userProfileInfo.userInfo isLogicAuthStar];
        KLog(@"[UserProfile QuickGift] isLogicAuthStar: %d, isUnSettledInStar: %d, follow: %d",
             isLogicAuthStar, self.unSettledInStar, self.follow);
        
        if (!isLogicAuthStar && !self.unSettledInStar && self.follow) {
            [self showQuickGiftView];
        } else {
            self.quickGiftView.hidden = YES;
        }
    }
    
    if (self.quickGiftView.hidden) {
        self.giftButton.titleLabel.hidden = NO;
    } else {
        self.giftButton.titleLabel.hidden = YES;
    }
}

- (KSUserProfileQuickGiftView *)quickGiftView {
    CGFloat itemW = KItemSizeW * 0.61;
    CGFloat itemH = KItemSizeH;
    CGFloat freeBtnW = itemW * 3.2; //免费送预留位置
    if (!_quickGiftView) {
        _quickGiftView = [[KSUserProfileQuickGiftView alloc] initWithFrame:self.giftButton.frame];
        if (self.sceneShow && self.giftBtnNum == 2) {
            // B实验新图标样式
            _quickGiftView = [[KSUserProfileQuickGiftView alloc] initWithFrame:CGRectMake(kDefaultMargin, 0, freeBtnW, itemH)];
        }
        _quickGiftView.hidden = YES;
        KS_WEAK_SELF(self);
        _quickGiftView.clickedBlock = ^(KSUserProfileRecommendHeadViewGiftType giftType) {
            CHECK_SELF_AND_RETURN();
            if ([self.delegate respondsToSelector:@selector(didClickQuickGiftWithRecommendHeadView:giftType:)]) {
                [self.delegate didClickQuickGiftWithRecommendHeadView:self giftType:giftType];
            }
        };
        _quickGiftView.exposureBlock = ^(KSUserProfileRecommendHeadViewGiftType giftType) {
            CHECK_SELF_AND_RETURN();
            if ([self.delegate respondsToSelector:@selector(exposureQuickGiftWithRecommendHeadView:giftType:)]) {
                [self.delegate exposureQuickGiftWithRecommendHeadView:self giftType:giftType];
            }
        };
        
        [self addSubview:_quickGiftView];
    }
    return _quickGiftView;
}

- (void)showQuickGiftView {
    if (!self.quickGiftView.isHidden) {
        return;
    }
    if (self.quickGiftView.giftType != KSUserProfileRecommendHeadViewGiftType_Normal
        && [self.delegate respondsToSelector:@selector(quickGiftChangedWithRecommendHeadView:isShow:)]) {
        [self.delegate quickGiftChangedWithRecommendHeadView:self isShow:YES];
        
        if (self.giftBtnNum != 2) {
            self.quickGiftView.frame = self.giftButton.frame;
            //B方案在setupSchemeB中初始化了quickGift
        }
        [self.quickGiftView updateLayerFrame];
        KLog(@"[UserProfile QuickGift] will show bubble view");
        self.quickGiftView.hidden = NO;
        if (self.quickGiftView.sceneShow && self.quickGiftView.giftBtnNum != 0) {
            return;
        }
        KS_WEAK_SELF(self);
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(self.quickGiftView.showTime * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            CHECK_SELF_AND_RETURN();
            [self.quickGiftView changeToNormal];
        });
    }
}

- (void)updateQuickGiftViewWithRsp:(proto_quick_gift_webapp_QueryQuickSendInfoWebRsp *)rsp {
    KLog(@"[UserProfile QuickGift] update queryQuickSendInfoWebRsp");
    [self.quickGiftView updateViewWithGiftRsp:rsp];
    [self checkQuickGiftShowIfNeed];
}

- (void)setupSchemeB {
    CGFloat itemW = KItemSizeW * 0.61;
    CGFloat itemH = KItemSizeH;
    CGFloat freeBtnW = itemW * 3.2; //免费送预留位置
    self.giftButton.backgroundColor = UIColor.clearColor;
    self.giftButton.layer.borderWidth = 1.0;
    self.giftButton.layer.borderColor = UIColor.whiteColor.CGColor;
    
    // 设置图标
    [self.followButton setTitle:@"" forState:UIControlStateSelected];
    [self.giftButton setTitle:@"" forState:UIControlStateNormal];
    [self.messageButton setTitle:@"" forState:UIControlStateNormal];
    [self.followButton setImage:[UIImage imageNamed:@"user_profile_follow_icon"] forState:UIControlStateSelected];
    [self.giftButton setImage:[UIImage imageNamed:@"user_profile_gift_icon"] forState:UIControlStateNormal];
    [self.messageButton setImage:[UIImage imageNamed:@"user_profile_message_icon"] forState:UIControlStateNormal];
    
    if (self.unSettledInStar) {
        // B实验：关注→送礼
        if (self.follow) {
            self.followButton.frame = CGRectMake(kDefaultMargin, 0, itemW, itemH);
            self.giftButton.frame = CGRectMake(self.followButton.right + kItemSpacing, 0, itemW * 2, itemH);
        } else {
            self.followButton.frame = CGRectMake(kDefaultMargin, 0, itemW * 2, itemH);
            self.giftButton.frame = CGRectMake(self.followButton.right + kItemSpacing, 0, itemW, itemH);
        }
    } else {
        if (self.sceneShow && self.giftBtnNum == 2) {
            // B实验：关注→送礼→私信
            if (self.follow) {
                // 送礼→关注→私信
                self.giftButton.frame = CGRectMake(kDefaultMargin + freeBtnW + kItemSpacing , 0, itemW, itemH);
                self.followButton.frame = CGRectMake(self.giftButton.right + kItemSpacing, 0, itemW, itemH);
                self.messageButton.frame = CGRectMake(self.followButton.right + kItemSpacing, 0, itemW, itemH);
            } else {
                self.followButton.frame = CGRectMake(kDefaultMargin, 0, freeBtnW + kItemSpacing + itemW , itemH);
                self.giftButton.frame = CGRectMake(self.followButton.right + kItemSpacing, 0, itemW, itemH);
                self.messageButton.frame = CGRectMake(self.giftButton.right + kItemSpacing, 0, itemW, itemH);
            }
        }
    }
}
#pragma mark - Arrow image

- (UIImage *)whiteDownArrow {
    return [UIImage imageNamed:@"arrow_down_white_v2"];
}

- (UIImage *)whiteUpArrow {
    return [UIImage imageWithCGImage:self.whiteDownArrow.CGImage scale:self.whiteDownArrow.scale orientation:UIImageOrientationDownMirrored];
}

- (UIImage *)blackDownArrow {
    return [UIImage imageWithCGImage:self.blackUpArrow.CGImage scale:self.blackUpArrow.scale orientation:UIImageOrientationDownMirrored];
}

- (UIImage *)blackUpArrow {
    return [UIImage imageNamed:@"arrow_up_black_v2"];
}

#pragma mark - action

- (void)followBtnClick:(id)sender {
    if (self.sceneShow && self.giftBtnNum == 2 && !self.follow) {
        // 非关注态到关注态
        [self executeFollowAnimationSequence];
        return;
    }
    if ([self.delegate respondsToSelector:@selector(didClickfollowButtonWithRecommendHeadView:follow:)]) {
        [self.delegate didClickfollowButtonWithRecommendHeadView:self follow:self.follow];
    }
}

- (void)sendMessageButtonClick:(id)sender {
    if ([self.delegate respondsToSelector:@selector(didClickMessageButtonWithRecommendHeadView:MessageButton:)]) {
        [self.delegate didClickMessageButtonWithRecommendHeadView:self MessageButton:(UIButton *)sender];
    }
}

- (void)giftButtonClick {
    if ([self.delegate respondsToSelector:@selector(didClickGiftButtonWithRecommendHeadView:)]) {
        [self.delegate didClickGiftButtonWithRecommendHeadView:self];
    }
}

- (UIView *)hitTest:(CGPoint)point withEvent:(UIEvent *)event
{
    UIView *hitView = [super hitTest:point withEvent:event];
    
    if (!hitView) {
        // 判断点击超出了 self 范围
        if ([self isViewClickable:self.floatTipViewButton]) {
            // 转换坐标
            CGPoint clickPoint = [self.floatTipViewButton convertPoint:point fromView:self];
            // 判断点击的点是否在区域内，并且没有隐藏
            // 重写 hitTest 来响应关注按钮事件
            if (CGRectContainsPoint(self.floatTipViewButton.bounds, clickPoint))
            {
                hitView = self.floatTipViewButton;
            }
        }
    }
    return hitView;
}

- (BOOL)isViewClickable:(UIView *)targetView
{
    BOOL couldClick = targetView.superview && !targetView.hidden && targetView.alpha > 0 && targetView.userInteractionEnabled;
    return couldClick;
}

- (CGRect)getFollowButtonFrame {
    return self.followButton.frame;
}

-(void) executeFollowAnimationSequence {
    CGFloat itemW = KItemSizeW * 0.61;
    CGFloat itemH = KItemSizeH;
    CGFloat freeBtnW = itemW * 3.2;
    
    CGRect finalFollowFrame = CGRectMake(kDefaultMargin, 0, freeBtnW, itemH);
    CGRect newGiftFrame = CGRectMake(finalFollowFrame.origin.x + freeBtnW + kItemSpacing, 0, itemW, itemH);
    CGRect tempBtnFrame = CGRectMake(newGiftFrame.origin.x + itemW + kItemSpacing, 0, itemW, itemH);
    // 第一步：关注按钮变暗和文案变化
    
    
    UIButton *tempBtn = [[UIButton alloc] initWithFrame:finalFollowFrame];
    [tempBtn setImage:[UIImage imageNamed:@"user_profile_follow_icon"] forState:UIControlStateNormal];
    [tempBtn setBackgroundColor:[UIColor clearColor]];
    tempBtn.alpha = 0;
    [UIView animateWithDuration:2.0 animations:^{
        [self.followButton setTitle:@"已关注" forState:UIControlStateNormal];
    } completion:^(BOOL finished) {
        [UIView animateWithDuration:1.0 animations:^{
            self.giftButton.frame = newGiftFrame;
            self.followButton.frame = finalFollowFrame;
            
        } completion:^(BOOL finished) {
            //处理关注逻辑
            [self.followButton setTitle:@"" forState:UIControlStateNormal];
            if ([self.delegate respondsToSelector:@selector(didClickfollowButtonWithRecommendHeadView:follow:)]) {
                [self.delegate didClickfollowButtonWithRecommendHeadView:self follow:self.follow];
            }
        }];
    }];
}
@end
