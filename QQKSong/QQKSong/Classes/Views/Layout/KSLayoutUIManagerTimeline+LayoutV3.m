//
//  KSLayoutUIManagerTimeline+LayoutV3.m
//  QQKSong
//
//  Created by SY on 2022/3/16.
//  Copyright © 2022 Tencent. All rights reserved.
//

#import "KSLayoutUIManagerTimeline+LayoutV3.h"
#import "KSLayoutUIManagerTimeline+LayoutV2.h"
#import "KSimpleFeed.h"
#import "KSong+Common.h"
#import "KSLayoutInfo.h"
#import "KSLayoutUIManager.h"
#import "KSCommonModel.h"
#import "KSKeyAreaRect.h"
#import "KSUIItem.h"
#import "KSUgcStarChorusManager.h"
#import "KSTraceReportModel_V2.h"
#import "KSTraceReportManager.h"
#import "KSTraceReportHelper.h"
#import "KSDrawItemAction.h"
#import "KSUserInfo.h"
#import "KSLiveVideoCacheManager.h"
#import "KSUIItem.h"
#import "KSAvatarDownloadManager.h"
#import "KSStringCrashManager.h"
#import "KSTimelineForward.h"
#import "KSCellUser.h"
#import "KSPaySoloAlbumManager.h"
#import "NSDate+Utils.h"
#import "KSUserVip.h"
#import "KSIconConfig.h"
#import "KSVipManager.h"
#import "KSNickIconView.h"
#import "YPStyle.h"
#import "KSLottieExposureControlManager.h"
#import "KSLoginManager.h"
#import "KSABTestManager.h"
#import <KSongLayout/KSBaseUIItem.h>
#import <KSongLayout/KSTextItemParser.h>
#import "JceTimeline_cell_hc.h"
#import "JceTimeline_enum_rec_item_type.h"
#import "JceTimeline_cell_status.h"
#import "JceTimeline_cell_ktv.h"
#import "JceTimeline_cell_ktv_mike.h"
#import "JceTimeline_cell_like.h"
#import "JcePayAlbum_emBlockType.h"
#import "JceTimeline_s_picurl.h"
#import "JceTimeline_KTV_ROOM_STATUS_TYPE_ENUM.h"
#import "proto_teaching_course_webapp_emCoursePayMask.h"
#import "proto_feed_webapp_PUBLISHER_STATUS.h"
#import "proto_feed_webapp_cell_magic_color.h"
#import "proto_feed_webapp_cell_tme_town_feed.h"
#import "proto_feed_webapp_ktv_game_status.h"
#import "proto_feed_webapp_game_status_comm.h"
#import "proto_feed_webapp_game_status_webview.h"
#import "proto_feed_webapp_game_status_profile.h"
#import "proto_feed_webapp_cell_game.h"
#import "KSBirthdayGiftRequestManager.h"
#import "KSUserAuthBadgeUtils.h"
#import "JceTimeline_enum_filter_mask.h"
#import "KSLayoutableTimelineFeedCellV2+CustomView.h"
#import "KSUserInfo+giftActivityInfo.h"
#import "KSPendantAvatarItem.h"
#import "KSLayoutUIManager+Common.h"
#import "KSTraceReportManager+KGCommon.h"
#import "KSUserAuthBadgeUtils.h"
#import "KSUserInfo+Common.h"
#import "KSFollowFeedViewModel.h"
#import "KSUIABTestManager.h"
#import "KSGlobalSizeManager+Small.h"
#import "JceTimeline_cell_mini_heat_card.h"
#import "JceTimeline_s_user.h"
#import "JceTimeline_cell_userinfo.h"
#import "KSIconConfig+Unification.h"
#import "KSFeedLayoutConfig.h"
#import "KSAdFreeGiftScene.h"
#import "KSTimelineManager+Gift.h"

#define kSongInfoAreaHeight _size_mid(40) // 音频、专辑/歌单、课程样式cell的信息区域高度

// 异化的功能按钮类型
typedef NS_ENUM(NSUInteger, KSBottomFucBtnType) {
    KSBottomFucBtnType_none = 0, // 不展示
//    KSBottomFucBtnType_like = 1, // 点赞
    KSBottomFucBtnType_hc = 2, // 合唱
    KSBottomFucBtnType_gift_hc = 3, // 礼物合唱
    KSBottomFucBtnType_vote = 4, // 投票
//    KSBottomFucBtnType_buy_vip = 5, // 开通vip  这个按钮干掉了
    KSBottomFucBtnType_purchase = 6, // 购买（专辑之类的）
};

@implementation KSLayoutUIManagerTimeline (LayoutV3)

// 「音频」布局
+ (void)layoutAudioFeedInfo_2022:(KSimpleFeed *)simpleFeed info:(KSLayoutInfo *)info {
    NSInteger startX = KSMargin_Dynamic_70;
    CGFloat customViewWidth  = SCREEN_WIDTH - startX - KSMargin_Dynamic_20;
    CGFloat customViewHeight = ceil(customViewWidth / 3.0); // 长宽比3:1
    UIColor *magicColor = [self calculate2022FeedMagicColor:simpleFeed];
    UIColor *endColor = [UIColor ks_colorWithRGBHex:0xF2F4F6];
    // 背景区域
    NSInteger bgX = startX;
    CGRect bgRect = CGRectMake(bgX, [info currentY], customViewWidth, customViewHeight);
    NSArray *colors = @[(__bridge id)magicColor.CGColor,
                        (__bridge id)magicColor.CGColor,
                        (__bridge id)endColor.CGColor,
                        (__bridge id)endColor.CGColor];
    [self layoutGradientLayerViewWithColors:colors
                                  locations:@[@(0.1),@(0.15),@(0.57),@1] // 这个数据调了一万年，心累
                                 startPoint:CGPointMake(0, 0)
                                   endPoint:CGPointMake(1, 0.2)
                                       info:info
                                       rect:bgRect
                                        tag:TimelineFeedCellMagicColorView
                                      block:^(KSGradientLayerViewItem *layoutItem) {
        layoutItem.ksCornerRadius = kFeedImageRadiusCorner;
        layoutItem.uiItemClipsToBounds = YES;
        layoutItem.clipCorner = UIRectCornerAllCorners;
    }];
    
    // 封面图右边的小半圆(先画小半圆再画封面，让封面压住小半圆，防止算错半圆圆心角角度而导致小半圆盖住封面)
    CGFloat radius = customViewHeight * 1.086 / 2;
    CGFloat angle = 0.18;
    UIBezierPath *path = [[UIBezierPath alloc] init];
    [path addArcWithCenter:CGPointMake(radius, customViewHeight / 2) radius:radius startAngle: M_PI * angle endAngle:angle * M_PI * -1 clockwise:NO];
    [path closePath];
    [self layoutBezierPathViewWithPath:path
                                  info:info
                                  rect:CGRectMake(startX, [info currentY], customViewHeight, customViewHeight) // 与封面保持一致的rect
                                   tag:TimelineFeedCellHalfCircle
                                 block:^(KSBezierPathViewItem *layoutItem) {
        layoutItem.color = magicColor;
    }];
    
    // 歌曲封面图
    CGFloat imageX = startX;
    CGFloat imageY = [info currentY];
    CGFloat imageWidthAndHeight = customViewHeight;
    
    NSString *pictureUrl = nil;
    NSString *localFilePath = nil;
    KSKeyAreaRect *keyAreaRect = nil;
    
    // 选取音频封面图
    KSImage *coverImage = (KSImage *)[[simpleFeed.songinfo.coverurls allValues] firstObject];
    if (coverImage.imageUrl) {
        pictureUrl = coverImage.imageUrl;
    } else if (coverImage.localFilePath) {
        localFilePath = coverImage.localFilePath;
    }
    if (coverImage.keyAreas.count > 0) {
        keyAreaRect = coverImage.keyAreas.firstObject;
    }
    if (localFilePath && [QMFileHelper fileIsExistWithPath:localFilePath]) {
        NSData *imageData = [NSData dataWithContentsOfFile:localFilePath];
        [self layoutLocalImage:[UIImage imageWithData:imageData]
                        toInfo:info
                          rect:CGRectMake(imageX, imageY, imageWidthAndHeight, imageWidthAndHeight)
                           tag:TimelineFeedCellSongCover
                         block:^(KSUIItemLocalImage *layoutItem) {
            layoutItem.ksCornerRadius = kFeedCoverRadiusCornerV2;
            layoutItem.uiItemClipsToBounds = YES;
            layoutItem.clipCorner = UIRectCornerAllCorners;
            layoutItem.contentMode = UIViewContentModeScaleAspectFill;
        }];
    } else {
        [self layoutOriginalImage:pictureUrl
                      placeHolder:[KSLayoutUIManager cahceImageWithName:@"DefaultAlbum"]
                           action:nil
                           toInfo:info
                             rect:CGRectMake(imageX, imageY, imageWidthAndHeight, imageWidthAndHeight)
                              tag:TimelineFeedCellSongCover
                            block:^(KSUIOriginalImageItem *layoutItem) {
            layoutItem.ksCornerRadius = kFeedCoverRadiusCornerV2;
            layoutItem.uiItemClipsToBounds = YES;
            layoutItem.clipCorner = UIRectCornerAllCorners;
            layoutItem.contentMode = UIViewContentModeScaleAspectFill;
            if (keyAreaRect != nil) {
                layoutItem.iLeftTopX = keyAreaRect.iLeftTopX;
                layoutItem.iLeftTopY = keyAreaRect.iLeftTopY;
                layoutItem.iWidth = keyAreaRect.iWidth;
                layoutItem.iHeight = keyAreaRect.iHeight;
            }
        }];
    }
    
    if ([simpleFeed isMiniHeatCardFeed]) {
        // simpleFeed.miniHeatCardItem.jce_userinfo.jce_user.jce_lUid
        NSString *picUrl = [[KSAvatarDownloadManager defaultManager] getAvatarUrl:simpleFeed.miniHeatCardItem.jce_userinfo.jce_user.jce_lUid size:AvatarPicSize_100 timestamp:simpleFeed.miniHeatCardItem.jce_userinfo.jce_user.jce_timestamp];
        CGFloat picX = imageX + imageWidthAndHeight - 30;
        CGFloat picY = imageY + imageWidthAndHeight - 30;
        CGFloat picWidth = 24;
        CGFloat picHeight = 24;
        [self layoutOriginalImage:picUrl
                      placeHolder:[KSLayoutUIManager cahceImageWithName:@"DefaultPortrait"]
                           action:nil
                           toInfo:info
                             rect:CGRectMake(picX, picY, picWidth, picHeight)
                              tag:TimelineFeedCellMiniHeatCardHeader
                            block:^(KSUIOriginalImageItem *layoutItem) {
            layoutItem.ksCornerRadius = picWidth / 2;
            layoutItem.clipCorner = UIRectCornerAllCorners;
            layoutItem.borderColor = UIColor.whiteColor;
            layoutItem.borderWidth = 1.5;
            layoutItem.uiItemClipsToBounds = YES;
        }];
    }

    // 封面图左上角标签布局
    [self calculateCoverLeftTopImage:simpleFeed layoutIfNeed:info rect:CGRectMake(bgX + 5, [info currentY] + 5, 0, 0)];
    
    // 临境音质需要布局对应的标记视图，相对于播放按钮，居中显示
    if ([simpleFeed isImmersiveQualityFeed] || [simpleFeed isImmersiveEffectFeed]) {
        UIImage *image = [UIImage imageNamed:@"feed_timeline_immersive_quality"];
        CGSize imageSize = image.size;// 图片的长宽比例为 160 * 92，对齐播放按钮的中心点位置为
        imageSize = CGSizeMake(160.0, 92.0);
        CGFloat realImageHeight = customViewHeight;
        CGFloat realImageWidth = imageSize.width * realImageHeight / imageSize.height;
        CGFloat realImageX = startX + customViewWidth - realImageWidth;
        CGFloat imageY = [info currentY];
        
        [self layoutLocalImage:image
                        toInfo:info
                          rect:CGRectMake(realImageX, imageY, realImageWidth, realImageHeight)
                           tag:TimelineFeedCellImmersiveMarkImage
                         block:^(KSUIItemLocalImage *layoutItem) {
            layoutItem.ksCornerRadius = kFeedCoverRadiusCornerV2;
            layoutItem.uiItemClipsToBounds = YES;
            layoutItem.clipCorner = UIRectCornerAllCorners;
            layoutItem.contentMode = UIViewContentModeScaleAspectFill;
        }];
    }
    
    // 歌曲区域内的文字画布容器(歌名、标签等)
    CGFloat containerX = bgX + imageWidthAndHeight; // 容器的x
    CGRect containerRect = CGRectMake(containerX, [info currentY], customViewWidth - imageWidthAndHeight - 50, customViewHeight);
    if (simpleFeed.layoutConfig.isActionBtnInRightCorner) {
        //搜索合唱右边合唱按钮常驻，需要缩减容器区域
        containerRect = CGRectMake(containerX, [info currentY], customViewWidth - imageWidthAndHeight - 100, customViewHeight);
    }
    // 布局画布
    [self layoutKSLayoutableTextViewItem:info rect:containerRect tag:TimelineFeedCellAudioSongContainerView block:^(KSUIItem *layoutItem) {
        layoutItem.normalBackgroundColor = [UIColor clearColor];
    }];
    
    // 音频播放按钮区域 视觉不是固定的且交互比较复杂 cell里面自己布局
    if (!simpleFeed.isRefUGCUnavailable) {
        // 私密作品不展示播放按钮
        KSUIItemCustomAddView *item = [[KSUIItemCustomAddView alloc] init];
        CGFloat itemHeight = _size_mid(28.0);
        CGFloat itemWidth = _size_mid(28.0);
        CGFloat itemX = startX + customViewWidth - 14 - itemWidth;
        CGFloat itemY = imageY + (customViewHeight - itemWidth) / 2;
        
        // 临境音质/音效水印图展示的时候，需要调整播放按钮的位置，使得播放按钮在水印中间
        if (([simpleFeed isImmersiveQualityFeed] || [simpleFeed isImmersiveEffectFeed])) {
            CGSize imageSize = CGSizeMake(160.0, 92.0);// 图片资源的大小
            CGFloat realImageHeight = customViewHeight;
            CGFloat realImageWidth = imageSize.width * realImageHeight / imageSize.height;
            CGFloat imageRight = startX + customViewWidth;
            
            CGPoint centPointRato = CGPointMake(0.825, 0.5326); //播放按钮中心点在图片的中心点比例[132/160 49/92]
            CGFloat centerPointX = (imageRight - ((1.0 - centPointRato.x) * realImageWidth));
            CGFloat centerPointY = (imageY + centPointRato.y * realImageHeight);
            itemX = centerPointX - itemWidth / 2.0;
            itemY = centerPointY - itemHeight / 2.0;
        }
        
        item.rect = CGRectMake(itemX, itemY, itemWidth, itemHeight);
        item.viewType = DRAWITEM_CUSTOM_ADD_AUDIO_PLAY;
        item.tag = TimelineFeedCellUgcAudioCustomView;
        [info addItem:item];
    }
         
    // 歌曲信息展示区域布局（包括歌名、收听量、合唱量 等其他信息） 类容器是TimelineFeedCellAudioSongContainerView
    // 布局歌名
    [self layoutSongName_2022:simpleFeed info:info rect:containerRect];
    // 布局标签 收听数
    [self layoutLevelAndListenNumberInfo_2022:simpleFeed info:info rect:containerRect];
    
    [info increaseHeight:customViewHeight];
}

// 布局2022新版【歌名】(父类容器是TimelineFeedCellAudioSongContainerView)
+ (void)layoutSongName_2022:(KSimpleFeed *)simpleFeed info:(KSLayoutInfo *)info rect:(CGRect)layoutRect {
    
    CGFloat startX = 22;
    CGFloat startY = (layoutRect.size.height - kSongInfoAreaHeight) / 2.0; // 歌名+标签，做整体竖直居中
    //歌名
    UIFont *songNameFont = _font_big([UIFont ks_fontWithFontType:KSFontType_LargeBold]);
    CGFloat songNameMaxwidth = layoutRect.size.width - startX - KSMargin_10; // 歌名最大长度
    CGSize songNameSize = [simpleFeed.songinfo.name kSongSizeWithFont:songNameFont];
    CGFloat songNameReal_width = min(songNameSize.width, songNameMaxwidth); // 防止长歌名UI错乱
    [self layoutText:simpleFeed.songinfo.name
              toInfo:info
             maxLine:1
                   x:startX
                   y:startY
                   w:songNameReal_width
                   f:songNameFont
                   c:[UIColor ks_colorWithRGBHex:0x323333]
         layoutBlock:^(KSLayoutRichText *layoutTextItems) {
        [layoutTextItems.layoutList enumerateObjectsUsingBlock:^(KSUIItemText *txtItem, NSUInteger idx, BOOL * _Nonnull stop) {
            txtItem.canvasViewTag = TimelineFeedCellAudioSongContainerView;
        }];
    } prelayoutBlock:nil];
}

+ (void)layoutLevelAndListenNumberInfo_2022:(KSimpleFeed *)simpleFeed info:(KSLayoutInfo *)info rect:(CGRect)layoutRect {
    // 音频 标签优先级 “推荐 sss ss A B C”
    UIImage *levelTagImage = [self calculateFeedLevelImage:simpleFeed];
    CGFloat usedX = 24;
    if (levelTagImage) {
        CGFloat levelTagImageHeight = _size_mid(13);
        CGFloat levelTagImageWidth = levelTagImageHeight * levelTagImage.size.width / levelTagImage.size.height;
        CGFloat levelTagImageY = layoutRect.size.height - ((layoutRect.size.height - kSongInfoAreaHeight) / 2.0) - levelTagImageHeight;
        [self layoutLocalImage:levelTagImage toInfo:info rect:CGRectMake(usedX, levelTagImageY, levelTagImageWidth, levelTagImageHeight) tag:TimelineFeedCellRecTagIcon block:^(KSUIItemLocalImage *layoutItem) {
            layoutItem.parentViewTag = TimelineFeedCellAudioSongContainerView;
        }];
        usedX = usedX + levelTagImageWidth + 7; // 7是padding
    }
    
    // 是否为合唱半成品
    UIImage *listenNumIconImage = nil;
    NSString *numberString = nil;
    if ([simpleFeed getUgcType] == HALF_CHORUS_UGC) {
        // 合唱半成品icon
        listenNumIconImage = [UIImage imageNamed:@"feed_timeline_chorus_number_black"];
        numberString = [NSString stringWithFormat:KString(@"%@"), [KSFormatHelper formatNewNumber:simpleFeed.ihcNum]];
    } else {
        // 播放icon
        listenNumIconImage = [UIImage imageNamed:@"feed_timeline_listen_number_black"];
        numberString = [NSString stringWithFormat:KString(@"%@"), [KSFormatHelper formatNewNumber:simpleFeed.listenerInfo.number]];
    }
    // 收听数icon
    CGFloat listenIconWidth = _size_mid(13);
    CGFloat listenNumIconImageY = layoutRect.size.height - ((layoutRect.size.height - kSongInfoAreaHeight) / 2.0) - listenIconWidth;
    [self layoutLocalImage:listenNumIconImage toInfo:info rect:CGRectMake(usedX, listenNumIconImageY, listenIconWidth, listenIconWidth) tag:TimelineFeedCellListenNumberIcon block:^(KSUIItemLocalImage *layoutItem) {
        layoutItem.parentViewTag = TimelineFeedCellAudioSongContainerView;
    }];
    usedX = usedX + listenIconWidth + 3; // 3是padding
    // 收听数/播放数 数字
    UIFont *numberFont = _font_big([self getAvantFontWithSize:12 bold:YES]);
    CGFloat listenIconMidY = listenNumIconImageY + listenIconWidth / 2.0;
    CGSize numberStringSize = [numberString kSongSizeWithFont:numberFont];
    [self layoutText:numberString
              toInfo:info
             maxLine:1
                   x:usedX
                   y:listenIconMidY - numberStringSize.height / 2.0
                   w:layoutRect.size.width - usedX
                   f:numberFont
                   c:[[UIColor ks_colorWithRGBHex:0x383939] colorWithAlphaComponent:0.7]
         layoutBlock:^(KSLayoutRichText *layoutTextItems) {
        [layoutTextItems.layoutList enumerateObjectsUsingBlock:^(KSUIItemText *txtItem, NSUInteger idx, BOOL * _Nonnull stop) {
            txtItem.canvasViewTag = TimelineFeedCellAudioSongContainerView;
        }];
    } prelayoutBlock:nil];
}

// 「直播」2022 feed布局
+ (void)layoutLiveShowInfo_2022:(KSimpleFeed *)simpleFeed info:(KSLayoutInfo *)info {
    if (simpleFeed.recommendItem && simpleFeed.recommendItem.jce_uiItemType == JceTimeline_enum_rec_item_type_enum_rec_live_contribution) {
        // 直播投稿上报
        [KSTraceReportHelper viewActionWithBlock:^(TraceReportView *info) {
            info.reserves = ReportL3LiveContributionFeedNearExpose;
            info.touid = simpleFeed.simpleUser.userinfo.userId;
            info.commstr3 = simpleFeed.liveShow.strShowId;
            [KSTraceReportManager updateTraceViewInfo:info withFeed:simpleFeed];
        }];
    }
    
    CGFloat startY = [info currentY];
    CGFloat startX = KSMargin_Dynamic_70;
    CGFloat bgLength = SCREEN_WIDTH - KSMargin_Dynamic_70 - KSMargin_Dynamic_20; // padding 左70 右20
    CGFloat bgHeight = bgLength; // 直播间尺寸 1:1
    
    // Action
    KSDrawItemAction *action = [KSDrawItemAction new];
    action.type = DRAWITEM_ACTION_TYPE_LIVESHOWFEED;
    action.busiData = simpleFeed;
    
    // 直播大图
    startY = [info currentY];
    NSString *coverURL = [simpleFeed getImageUrlFromEachKindOfFeed];
    [self layoutOriginalImage:coverURL
                  placeHolder:[KSLayoutUIManager cahceImageWithName:@"DefaultAlbum"]
                       action:action
                       toInfo:info
                         rect:CGRectMake(startX, startY, bgLength, bgHeight)
                          tag:TimelineFeedCellLiveShowCoverImage
                        block:^(KSUIOriginalImageItem *layoutItem) {
                            layoutItem.ksCornerRadius = kFeedImageRadiusCorner;
                            layoutItem.uiItemClipsToBounds = YES;
                            layoutItem.clipCorner = UIRectCornerAllCorners;
                            layoutItem.contentMode = UIViewContentModeScaleAspectFill;
                        }];
    
    
    int coverType = [[simpleFeed.liveShow.mapExt safeObjectForKey:@"mp4_cover_type"] intValue];
    BOOL isSoungOn = [[simpleFeed.liveShow.mapExt safeObjectForKey:@"mp4_cover_sound_on"] boolValue];
#ifdef DEBUG
    isSoungOn = YES;
#endif
    if (coverType == KSTimelineLiveFeedCoverType_Video ||
        coverType == KSTimelineLiveFeedCoverType_MultiPicture ||
        coverType == KSTimelineLiveFeedCoverType_curAIVideo ||
        coverType == KSTimelineLiveFeedCoverType_threeMonthAIVideo)
    {
        NSString *videoUrl = [simpleFeed.liveShow.mapExt safeObjectForKey:@"mp4_cover"];
        NSString *soundVideoUrl = [simpleFeed.liveShow.mapExt safeObjectForKey:@"mp4_cover_with_sound"];
        if (isSoungOn && soundVideoUrl.length > 0) {
            videoUrl = soundVideoUrl;
        }
        if (videoUrl.length > 0) {
            [[KSLiveVideoCacheManager sharedManager] downloadVideoWithUrl:videoUrl refreshCache:NO];
            BOOL isAICover = (coverType == KSTimelineLiveFeedCoverType_curAIVideo ||
                              coverType == KSTimelineLiveFeedCoverType_threeMonthAIVideo);
            [self layoutVideoDisplayView:videoUrl diskPath:[KSLiveVideoCacheManager getFilePathForVideoWithUrl:videoUrl] toInfo:info rect:CGRectMake(startX, startY, bgLength, bgHeight) tag:TimelineFeedCellLiveVideoShowView block:^(KSVideoDisplayItem *layoutItem) {
                layoutItem.ksCornerRadius = kFeedImageRadiusCorner;
                layoutItem.uiItemClipsToBounds = YES;
                layoutItem.videoWidthRatio = isAICover ? 3/4.0 : 1.0;// 设置封面比例，AI封面为3:4，其余为正方形
                layoutItem.clipCorner = UIRectCornerAllCorners;
                layoutItem.viewType = !isSoungOn ? DRAWITEM_CUSTOM_ADD_LiveFeedNoVoice : DRAWITEM_CUSTOM_ADD_LiveFeedVoice;
                layoutItem.mutePlay = !isSoungOn;
            }];
        }
    } else if (coverType == KSTimelineLiveFeedCoverType_liveStream) {
        // 实时流
        NSString *cdnUrl = [simpleFeed.liveShow.mapExt ks_stringValueForKey:@"strCdnUrl"];
        if (!IS_EMPTY_STR_BM(cdnUrl)) {
            [self layoutVideoDisplayView:cdnUrl diskPath:nil toInfo:info rect:CGRectMake(startX, startY, bgLength, bgHeight) tag:TimelineFeedCellLiveVideoShowView block:^(KSVideoDisplayItem *layoutItem) {
                layoutItem.ksCornerRadius = kFeedImageRadiusCorner;
                layoutItem.uiItemClipsToBounds = YES;
                layoutItem.videoWidthRatio = 1;
                layoutItem.clipCorner = UIRectCornerAllCorners;
                layoutItem.useTXPlayer = YES;
                layoutItem.onlinePlayOnly = YES;
                layoutItem.viewType = !isSoungOn ?DRAWITEM_CUSTOM_ADD_LiveFeedNoVoice : DRAWITEM_CUSTOM_ADD_LiveFeedVoice;
                layoutItem.mutePlay = !isSoungOn;
            }];
        }
    }
    
    // 底部遮罩
    [self layoutMaskView:info rect:CGRectMake(startX, startY + bgHeight - KSMargin_Dynamic(60), bgLength, KSMargin_Dynamic(60)) tag:TimelineFeedCellSonglistBackground block:^(KSMaskViewItem *item) {
        item.maskType = KSMaskType_GradientBottomToTop;
        item.ksCornerRadius = kFeedImageRadiusCorner;
        item.clipCorner = UIRectCornerBottomLeft | UIRectCornerBottomRight;
        item.uiItemClipsToBounds = YES;
    }];
    
    // 直播间标题 (后台若没有下发默认标题客户端拼接)
    simpleFeed.liveShow.strLiveTitle = [simpleFeed getSimpleFeedDescription];
    UIFont *titleFont = _font_big([UIFont ks_fontWithFontType:KSFontType_LargeBold]);
    CGSize titleSize = [simpleFeed.liveShow.strLiveTitle kSongSizeWithFont:titleFont];
    CGFloat titleX = startX + KSMargin_Dynamic(14);
    
    CGFloat iconNumberSizeH = _size_big(KSMargin_Dynamic(12)); /// icon和人数区域高度
    CGFloat titleIconSpacing = 8;
    CGFloat titleY = startY + bgHeight - iconNumberSizeH - titleSize.height - titleIconSpacing - KSMargin_Dynamic(12);
    CGFloat titleMaxWidth = SCREEN_WIDTH - titleX - 30 - 11 - KSMargin_Dynamic_20; // 右下角有个声音开关，icon宽度30，icon右padding11
    [self layoutText:simpleFeed.liveShow.strLiveTitle toInfo:info maxLine:1 x:titleX y:titleY w:titleMaxWidth f:titleFont c:[UIColor whiteColor]];
    
    // 左下角状态：在线人气 + 当前的歌曲
    CGFloat bottomLeftViewX = titleX;
    CGFloat bottomLeftViewY = titleY + titleSize.height + titleIconSpacing;
    CGFloat usedX = bottomLeftViewX;
    
    // 在线人气
    CGFloat peopleIconWidth = ceil(iconNumberSizeH * 37 / 45);
    BOOL hasOnlinePeople = simpleFeed.liveShow.popularityNum > 0;
    if (hasOnlinePeople) {
        NSString *onlinePeopleText = [simpleFeed getOnlinePeopleForLiveShow];
        UIFont *onlinePeopleFont = _font_big([self getAvantFontWithSize:(12) bold:YES]);
        CGFloat onlinePeopleTextLength = [onlinePeopleText kSongSizeWithFont:onlinePeopleFont].width;

        // 在线人气图标
        [self layoutLocalImage:[UIImage imageNamed:@"feed_timeline_live_people_icon"]
                        toInfo:info
                          rect:CGRectMake(usedX, bottomLeftViewY, peopleIconWidth, iconNumberSizeH)
                           tag:TimelineFeedCellLiveShowOnLinePeopleImage
                         block:nil];
        
        usedX = usedX + peopleIconWidth + KSMargin_Dynamic(3); // 3为padding
        // 在线人气文本
        [self layoutOneLineText:onlinePeopleText
                         toInfo:info
                              x:usedX
                              y:bottomLeftViewY
                              w:onlinePeopleTextLength
                              f:onlinePeopleFont
                              c:[UIColor ks_colorWithRGBHex:0xFFFFFF alpha:0.75]];
        
        usedX = usedX + onlinePeopleTextLength + KSMargin_Dynamic(10); // 10为padding
    }
    
    // 当前正在播放的歌曲
    if (simpleFeed.liveShow.strCurrSongName.length > 0) {
        // 歌曲图标
        CGFloat musicIconWidth = iconNumberSizeH;
        [self layoutLocalImage:[UIImage imageNamed:@"feed_timeline_live_music_icon"]
                        toInfo:info
                          rect:CGRectMake(usedX, bottomLeftViewY + 1/*视觉还原*/, musicIconWidth, iconNumberSizeH)
                           tag:TimelineFeedCellLiveShowCurMusicImage
                         block:nil];
        
        usedX = usedX + musicIconWidth + KSMargin_Dynamic(2); // 2为padding
        
        // 歌曲名称
        CGFloat songNameMaxWidth = SCREEN_WIDTH - usedX - 30 - 11 - KSMargin_Dynamic_20; // 右下角有个声音开关，icon宽度30，icon右padding11
        [self layoutOneLineText:simpleFeed.liveShow.strCurrSongName
                         toInfo:info
                              x:usedX
                              y:bottomLeftViewY
                              w:songNameMaxWidth
                              f:_font_big([UIFont ks_fontWithFontType:KSFontType_SmallBold])
                              c:[UIColor ks_colorWithRGBHex:0xFFFFFF alpha:0.75]];
        
        usedX = usedX + songNameMaxWidth;
    }
    
    // 左上角状态条：直播动效 + 直播间状态
    NSString *liveCourseId = [simpleFeed.liveShow.mapExt safeObjectForKey:@"strCourseId"]; // 教程付费直播id
    BOOL liveIsFree = [[simpleFeed.liveShow.mapExt safeObjectForKey:@"iIsFree"] boolValue];
    BOOL isPayLiveShow = liveCourseId.length && !liveIsFree;

    CGFloat topBarSizeH = _size_mid(KSMargin_Dynamic(22));
    UIFont *liveTextFont = _font_mid(KSBoldFontOfSize_Dynamic(12));
    CGFloat liveTextHeight = topBarSizeH * 0.636; // "直播"文字图片按比例计算的高度
    CGFloat liveTextWidth = 0;
    NSString *liveText = nil;
    if (isPayLiveShow) {
        liveTextWidth = liveTextHeight * (192 / 52.0);
        liveText = @"付费直播";
    } else {
        liveTextWidth = liveTextHeight * (96 / 52.0);
        liveText = @"直播";
    }
    
    CGFloat normalStatusWidth = liveTextWidth + KSMargin_Dynamic(30);
    
    JceTimeline_cell_status *liveStatus = simpleFeed.liveShow.liveStatusArray.firstObject;
    CGFloat totalWidth = 0; // 状态条总长度
    if (liveStatus) {
        // 如果有直播中的状态
        NSString *liveStatusText = liveStatus.jce_strTitle;
        // 状态文字的size
        CGSize liveStatusTextSize = [liveStatusText kSongSizeWithFont:liveTextFont];
        CGFloat liveStatusTextRealWidth = min(liveStatusTextSize.width, bgLength / 2);

        totalWidth = normalStatusWidth + liveStatusTextRealWidth + KSMargin_Dynamic(7) * 2; // 状态文字左右padding给7
    } else {
        totalWidth = normalStatusWidth; // 没有直播状态，就只展示”直播“长度
    }
    
    CGFloat topViewX = startX + KSMargin_Dynamic(8);
    CGFloat topViewY = startY + KSMargin_Dynamic(10);
    // 布局背景色
    [self layoutUIViewItem:info
                      rect:CGRectMake(topViewX, topViewY, totalWidth, topBarSizeH)
                       tag:TimelineFeedCellLiveShowStatusMask
                     block:^(KSUIItem *layoutItem) {
        layoutItem.normalBackgroundColor = [UIColor ks_colorWithHexString:@"FE4F4F" alpha:0.9f];
        layoutItem.uiItemClipsToBounds = YES;
        layoutItem.ksCornerRadius = KSMargin_Dynamic(5);
    }];
    
    // 布局直播动效
    CGFloat lottieViewH = _size_mid(KSMargin_Dynamic(9));
    CGFloat lottieViewY = topViewY + (topBarSizeH - lottieViewH) * 0.5;
    [self layoutLocalLottieViewWithAnimationName:@"timeline_live_feed_playing"
                                          toInfo:info
                                            rect:CGRectMake(topViewX + KSMargin_Dynamic(7), lottieViewY, KSMargin_Dynamic(12), lottieViewH)
                                             tag:TimelineFeedCellLiveShowRectAnimation
                                           block:^(KSLottieAnimationItem *layoutItem) {
        layoutItem.autoStart = YES;
        layoutItem.loopAnimation = YES;
    }];
    
    // 布局”直播“文字图片（这里的新字体对文字无效，应该是字体包被阉割了，所以这里UI让用图片实现）
    [self layoutText:liveText toInfo:info x:topViewX + KSMargin_Dynamic(23) y:topViewY + (topBarSizeH - liveTextHeight) / 2 w:liveTextWidth f:liveTextFont c:UIColor.whiteColor];
    
    // 布局直播状态背景色
    if (liveStatus) {
        [self layoutUIViewItem:info
                          rect:CGRectMake(topViewX + normalStatusWidth, topViewY, totalWidth - normalStatusWidth, topBarSizeH)
                           tag:TimelineFeedCellLiveShowRightStatusMask
                         block:^(KSUIItem *layoutItem) {
            layoutItem.normalBackgroundColor = [[UIColor ks_colorWithRGBHex:000000] colorWithAlphaComponent:0.05];
            layoutItem.uiItemClipsToBounds = YES;
            layoutItem.ksCornerRadius = KSMargin_Dynamic(5);
            layoutItem.clipCorner = UIRectCornerBottomRight | UIRectCornerTopRight;
        }];
        
        CGSize liveStatusSize = [liveStatus.jce_strTitle kSongSizeWithFont:liveTextFont];
        // 布局直播状态文字
        [self layoutOneLineText:liveStatus.jce_strTitle
                         toInfo:info
                              x:topViewX + normalStatusWidth + KSMargin_Dynamic(7)
                              y:topViewY + (topBarSizeH - liveStatusSize.height) / 2
                              w:totalWidth - normalStatusWidth - KSMargin_Dynamic(7) * 2
                              f:liveTextFont
                              c:[UIColor whiteColor]];
    }
    
    [info increaseHeight:bgHeight];
}

// 「歌房」 + 「排麦」 2022feed布局
+ (void)layoutKtvRoomShow_2022:(KSimpleFeed *)simpleFeed info:(KSLayoutInfo *)info {
    // 后台若没有下发默认标题客户端拼接
    if (simpleFeed.ktvRoomShow) {
        if (simpleFeed.ktvRoomShow.jce_strLiveTitle.length <= 0) {
            __block NSString *nickName = simpleFeed.simpleUser.userinfo.nickName;
            __block CGSize nickNameSize = CGSizeZero;
            [[KSStringCrashManager sharedManager] protectCrashString:nickName at:__FUNCTION__ when:^(NSString *finalString) {
                nickName = finalString;
                nickNameSize = [nickName kSongSizeWithFont:KSFont2];
            }];
            if (nickNameSize.width >= 0.8 * SCREEN_WIDTH) {
                nickName = [nickName safe_SubstringToIndex:kTimeLineLiveShowTitleLimitedCount] ;
            }
            simpleFeed.ktvRoomShow.jce_strLiveTitle = [NSString stringWithFormat:KString(@"%@的歌房"), nickName];
        }
        simpleFeed.ktvRoomShow.jce_strLiveTitle = [simpleFeed.ktvRoomShow.jce_strLiveTitle trimWhitespace];
    }
    
    CGFloat startX = KSMargin_Dynamic_70;
    CGFloat startY = [info currentY];
    CGFloat bgLength = SCREEN_WIDTH - KSMargin_Dynamic_70 - KSMargin_Dynamic_20;
    CGFloat bgHeight = bgLength;
    KSDrawItemAction *action = [KSDrawItemAction new];
    NSString *strAudience = nil;        // 观众数
    NSString *strMic = nil;             // mic数
    NSInteger publisherStatusType = 0;    // 发布者状态
    NSString *currentSongName = nil;     // 歌房内歌名
    NSString *game_name = nil;          // 歌房游戏玩法名
    NSString *coverUrl = nil;           // 封面URL
    NSInteger roomType = 0;             // 歌房类型
    NSInteger onlineNum = 0;           // 好友歌房在线人数
    NSInteger gifStatus = 0;           // 歌房游戏名
    NSInteger roomStatus = 0;           // 歌房游戏名
    BOOL isInteractGameType = [simpleFeed isInteractGameInPlayingStatus]; // 是否是互动游戏状态
    
    if (simpleFeed.ktvRoomShow) { // 歌房
        if ((simpleFeed.ktvRoomShow.jce_iRoomType & KSKTVRoomType_Multi) == KSKTVRoomType_Multi) { // 多麦歌房
            action.type = DRAWITEM_ACTION_TYPE_MULTIKTVROOMSHOWFEED;
            strAudience = [KSFormatHelper formatNewNumber:simpleFeed.ktvRoomShow.jce_uOnlineNum];
            strMic = [KSFormatHelper formatNewNumber:simpleFeed.ktvRoomShow.jce_uOnMikeNum];
            publisherStatusType = [[simpleFeed.ktvRoomShow.jce_mapExt safeObjectForKey:@"publisher_status_type"] intValue];
        } else if ((simpleFeed.ktvRoomShow.jce_iRoomType & KSKTVRoomType_Social) == KSKTVRoomType_Social) { // 欢聚歌房
            action.type = DRAWITEM_ACTION_TYPE_SOCIALKTVROOMSHOWFEED;
            strAudience = [KSFormatHelper formatNewNumber:[simpleFeed.ktvRoomShow.jce_mapExt[@"social_ktv_member_size"] integerValue]];
            strMic = [KSFormatHelper formatNewNumber:[simpleFeed.ktvRoomShow.jce_mapExt[@"social_ktv_song_size"] integerValue]];
            publisherStatusType = [[simpleFeed.ktvRoomShow.jce_mapExt safeObjectForKey:@"social_ktv_status"] intValue];
            onlineNum = simpleFeed.ktvRoomShow.jce_uOnlineNum;
        } else { // 单麦歌房
            action.type = DRAWITEM_ACTION_TYPE_KTVROOMSHOWFEED;
            strAudience = [KSFormatHelper formatNewNumber:simpleFeed.ktvRoomShow.jce_uOnlineNum];
            strMic = [KSFormatHelper formatNewNumber:simpleFeed.ktvRoomShow.jce_uWaitMikeNum];
            publisherStatusType = [[simpleFeed.ktvRoomShow.jce_mapExt safeObjectForKey:@"publisher_status_type"] intValue];
        }
        currentSongName = simpleFeed.ktvRoomShow.jce_strCurrSongName;
        game_name = [simpleFeed.ktvRoomShow.jce_mapExt safeObjectForKey:@"game_name"];
        coverUrl = !IS_EMPTY_STR_BM(simpleFeed.ktvRoomWebgameStatusInfo.jce_stGameWebview.jce_strBgURL) ? simpleFeed.ktvRoomWebgameStatusInfo.jce_stGameWebview.jce_strBgURL : simpleFeed.ktvRoomShow.jce_strCoverUrl;
        roomType = simpleFeed.ktvRoomShow.jce_iRoomType;
        gifStatus = [[simpleFeed.ktvRoomShow.jce_mapExt safeObjectForKey:@"gif_status"] intValue];
        roomStatus = [[simpleFeed.ktvRoomShow.jce_mapExt safeObjectForKey:@"room_status_type"] intValue];
    } else if (simpleFeed.ktvRoomMike) { // 排麦
        action.type = (simpleFeed.ktvRoomMike.jce_iRoomType & KSKTVRoomType_Multi) == KSKTVRoomType_Multi ? DRAWITEM_ACTION_TYPE_MULTIKTVROOMMIKEFEED : DRAWITEM_ACTION_TYPE_KTVROOMMIKEFEED;
        strAudience = [KSFormatHelper formatNewNumber:simpleFeed.ktvRoomMike.jce_uOnlineNum];
        
        if ((simpleFeed.ktvRoomMike.jce_iRoomType & KSKTVRoomType_Multi) == KSKTVRoomType_Multi) {
            strMic = [KSFormatHelper formatNewNumber:simpleFeed.ktvRoomMike.jce_uOnMikeNum];
        } else {
            strMic = [KSFormatHelper formatNewNumber:simpleFeed.ktvRoomMike.jce_uWaitMikeNum];
        }
        publisherStatusType = [[simpleFeed.ktvRoomMike.jce_mapExt safeObjectForKey:@"publisher_status_type"] intValue];
        currentSongName = simpleFeed.ktvRoomMike.jce_strSongName;
        game_name = [simpleFeed.ktvRoomMike.jce_mapExt safeObjectForKey:@"game_name"];
        coverUrl = simpleFeed.ktvRoomMike.jce_strCoverUrl;
        roomType = simpleFeed.ktvRoomMike.jce_iRoomType;
        gifStatus = [[simpleFeed.ktvRoomMike.jce_mapExt safeObjectForKey:@"gif_status"] intValue];
        roomStatus = [[simpleFeed.ktvRoomMike.jce_mapExt safeObjectForKey:@"room_status_type"] intValue];
    } else if([simpleFeed isKindOfTmeTownFeed]) { // TMETown
        action.type = DRAWITEM_ACTION_TYPE_KTV_TME_TOWN;
        coverUrl = SAFE_STR_BM([simpleFeed.tmeTown.jce_mapExt safeObjectForKey:@"room_cover"]);
    }
    if (isInteractGameType) {
        NSString *url = !IS_EMPTY_STR_BM(simpleFeed.ktvRoomWebgameStatusInfo.jce_stGameWebview.jce_strBgURL) ? simpleFeed.ktvRoomWebgameStatusInfo.jce_stGameWebview.jce_strBgURL : simpleFeed.ktvRoomShow.jce_strCoverUrl;
        if (!IS_EMPTY_STR_BM(url)) {
            coverUrl = url;
        }
    }
    action.busiData = simpleFeed;

    // 描述文案
    NSString *descText = nil;
    if (!IS_EMPTY_STR_BM(simpleFeed.ktvRoomShow.jce_strLiveDesc)) {
        descText = simpleFeed.ktvRoomShow.jce_strLiveDesc;
    }
    else if (![self isLayoutDescriptionInfo:simpleFeed info:info]) {
        descText = simpleFeed.ktvRoomWebgameStatusInfo.jce_stComm.jce_strFeedsDoc;
    }
    if (!IS_EMPTY_STR_BM(descText)) {
        [info increaseHeight:KSMargin_Dynamic_10];
        CGFloat descHeight = 0.0;
        NSInteger maxLine = simpleFeed.isExpandAllText == NO ? 3 : INT_MAX;
        descHeight = [self layoutText:descText
                               toInfo:info
                              maxLine:maxLine
                                    x:startX
                                    y:startY
                                    w:SCREEN_WIDTH - startX - KSMargin_Dynamic_20
                                    f:kDescTxtFont
                                    c:UIColor.ks_primaryTextColor
                          layoutBlock:nil
                       prelayoutBlock:^(KSLayoutRichText *layoutTextItems) {
            layoutTextItems.lastlineExpandStr = (simpleFeed.isExpandAllText == NO)? KString(@"展开"):KString(@"收起");
            
            DRAWITEM_ACTION_TYPE actionType = (simpleFeed.isExpandAllText == NO)? DRAWITEM_ACTION_TYPE_FEED_EXPAND_ALL_TEXT : DRAWITEM_ACTION_TYPE_FEED_COLLAPSE_ALL_TEXT;
            KSDrawItemAction *action = [KSDrawItemAction actionWithType:(int)actionType];
            action.busiData = simpleFeed;
            layoutTextItems.lastlineExpandAction = action;
        }];
        
        [info increaseHeight:descHeight];
        startY = [info currentY];
    }
    
    // 歌房封面大图
    [self layoutOriginalImage:coverUrl
                  placeHolder:[KSLayoutUIManager cahceImageWithName:@"DefaultAlbum"]
                       action:action
                       toInfo:info
                         rect:CGRectMake(startX, startY, bgLength, bgHeight)
                          tag:TimelineFeedCellLiveShowCoverImage
                        block:^(KSUIOriginalImageItem *layoutItem) {
        layoutItem.contentMode = UIViewContentModeScaleAspectFill;
        layoutItem.ksCornerRadius = kFeedImageRadiusCorner;
        layoutItem.uiItemClipsToBounds = YES;
        layoutItem.clipCorner = UIRectCornerAllCorners;
    }];
    
    if([simpleFeed isKindOfTmeTownFeed]) {
        // TMETown 不需要遮罩
    } else {
        // 底部遮罩
        [self layoutMaskView:info rect:CGRectMake(startX, startY + bgHeight - KSMargin_Dynamic(50), bgLength, KSMargin_Dynamic(50)) tag:TimelineFeedCellSonglistBackground block:^(KSMaskViewItem *item) {
            item.maskType = KSMaskType_GradientBottomToTop;
            item.ksCornerRadius = kFeedImageRadiusCorner;
            item.clipCorner = UIRectCornerBottomLeft | UIRectCornerBottomRight;
            item.uiItemClipsToBounds = YES;
        }];
    }
    
    // LiteKtv container
    if([simpleFeed isPublicSingleKtvFeed])
    {
        [self layoutLiteKtv:simpleFeed info:info];
    }
    
    // 歌房标题
    NSString *roomTitle = @"";
    BOOL isSocialRoom = NO; // 是否为欢聚歌房
    if (simpleFeed.ktvRoomShow) { // 歌房
        roomTitle = simpleFeed.ktvRoomShow.jce_strLiveTitle;
        if (simpleFeed.ktvRoomShow.jce_iRoomType & KSKTVRoomType_Social) {
            isSocialRoom = YES;
        }
    } else if (simpleFeed.ktvRoomMike) { // 排麦
        roomTitle = simpleFeed.ktvRoomMike.jce_strRoomTitle;
        if (simpleFeed.ktvRoomMike.jce_iRoomType & KSKTVRoomType_Social) {
            isSocialRoom = YES;
        }
    } else if([simpleFeed isKindOfTmeTownFeed]) { // TMETown
        roomTitle = SAFE_STR_BM([simpleFeed.tmeTown.jce_mapExt safeObjectForKey:@"room_name"]);
    }
    
    if (roomTitle.length == 0) {
        __block NSString *nickName = simpleFeed.simpleUser.userinfo.nickName;
        __block CGSize nickNameSize = CGSizeZero;
        [[KSStringCrashManager sharedManager] protectCrashString:nickName at:__FUNCTION__ when:^(NSString *finalString) {
            nickName = finalString;
            nickNameSize = [nickName kSongSizeWithFont:[UIFont ks_fontWithFontType:KSFontType_LargeBold]];
        }];
        if (nickNameSize.width >= 0.8 * SCREEN_WIDTH) {
            nickName = [nickName safe_SubstringToIndex:kTimeLineLiveShowTitleLimitedCount] ;
        }
        if (isSocialRoom) {
            roomTitle = [NSString stringWithFormat:KString(@"%@的欢聚歌房"), nickName];
        } else {
            roomTitle = [NSString stringWithFormat:KString(@"%@的歌房"), nickName];
        }
    }

    CGFloat iconNumberSizeH = _size_big(KSMargin_Dynamic(12)); /// icon和人数区域高度
    UIFont *titleFont = _font_big([UIFont ks_fontWithFontType:KSFontType_LargeBold]);
    CGSize titleSize = [roomTitle kSongSizeWithFont:titleFont];
    CGFloat titleX = startX + KSMargin_Dynamic(14);
    CGFloat titleIconSpacing = 8;
    CGFloat titleY = startY + bgHeight - iconNumberSizeH - titleSize.height - titleIconSpacing - KSMargin_Dynamic(12);
    CGFloat titleMaxWidth = SCREEN_WIDTH - titleX - KSMargin_Dynamic(20) - KSMargin_Dynamic(14) - KSMargin_Dynamic(28);
    if ([simpleFeed isKindOfTmeTownFeed]) { // TMETown
        titleMaxWidth = SCREEN_WIDTH - titleX - KSMargin_Dynamic(80) - KSMargin_Dynamic(12);
    }
    if ([simpleFeed isInteractGameInPlayingStatus]) {
        titleMaxWidth = bgLength - KSMargin_Dynamic(14) - KSMargin_Dynamic(80) - 20 - KSMargin_Dynamic(12);
    }
    [self layoutOneLineLabel:roomTitle
                      toInfo:info
                           x:titleX
                           y:titleY
                           w:titleMaxWidth
                           f:titleFont
                           c:[UIColor whiteColor]
                         tag:TimelineFeedCellKTVRoomTitleLabel
                       block:^(KSUIItemLabel *layoutItem) {
        layoutItem.normalBackgroundColor = [UIColor clearColor];
    }];
    
    
    // 信息区域
    NSInteger usedX = startX + KSMargin_Dynamic(14);
    CGFloat bottomLeftViewY = titleY + titleSize.height + titleIconSpacing;
    
    // 观众人数+排麦人数+当前歌曲名 信息展示
    if([simpleFeed isPublicSingleKtvFeed])
    {
        // 【欢唱歌房】 SCREEN_WIDTH - KSMargin_Dynamic_70 - KSMargin_Dynamic_20
        // 欢唱玩法需要根据玩法数据实时更新，这里相无插入一个container,后面增加相应的view
        CGRect viewContainerRect = CGRectMake(usedX, bottomLeftViewY, SCREEN_WIDTH - KSMargin_Dynamic_70 - KSMargin_Dynamic_20 - KSMargin_Dynamic(14), iconNumberSizeH);
        [self layoutLiteKtvOnlineNumLableContainer:simpleFeed rect:viewContainerRect info:info];
    }
    else
    {
        // 【欢聚歌房+交友歌房+TMETown+其他】
        // 观众人数
        NSInteger audienceNum = [strAudience integerValue];
        CGFloat iconWidth = iconNumberSizeH;
        UIFont *numberFont = _font_big([self getAvantFontWithSize:12 bold:YES]);
        if (audienceNum > 0 && !isInteractGameType) {
            CGFloat strAudienceTextLength = [strAudience kSongSizeWithFont:numberFont].width;

            CGFloat peopleImageHeight = iconWidth; // 视觉还原
            CGFloat peopleImageWidth = peopleImageHeight * 9 / 11; // 视觉还原
            // 观众人数图标
            [self layoutLocalImage:[UIImage imageNamed:@"feed_timeline_live_people_icon"]
                            toInfo:info
                              rect:CGRectMake(usedX, bottomLeftViewY, peopleImageWidth, peopleImageHeight)
                               tag:TimelineFeedCellLiveShowOnLinePeopleImage
                             block:nil];
            
            usedX = usedX + peopleImageWidth + KSMargin_Dynamic(3); // 3为padding
            // 观众人数文本
            [self layoutOneLineText:strAudience
                             toInfo:info
                             x:usedX
                             y:bottomLeftViewY + 2  // 视觉还原
                             w:strAudienceTextLength
                             f:numberFont
                             c:[UIColor ks_colorWithRGBHex:0xFFFFFF alpha:0.75]];
            
            usedX = usedX + strAudienceTextLength + KSMargin_Dynamic(10); // 10为padding
        }
        
        // 麦克人数
        NSInteger mikeNum = [strMic integerValue];
        if (mikeNum > 0 && !isInteractGameType) {
            CGFloat strMicTextLength = [strMic kSongSizeWithFont:numberFont].width;

            // 麦克人数图标
            [self layoutLocalImage:[UIImage imageNamed:@"feed_timeline_live_mic_icon"]
                            toInfo:info
                              rect:CGRectMake(usedX, bottomLeftViewY, iconWidth, iconWidth)
                               tag:TimelineFeedCellKTVMICImage
                             block:nil];
            
            usedX = usedX + iconWidth + KSMargin_Dynamic(2); // 2为padding
            // 麦克人数文本
            [self layoutOneLineText:strMic
                             toInfo:info
                             x:usedX
                             y:bottomLeftViewY + 2  // 视觉还原
                             w:strMicTextLength
                             f:numberFont
                             c:[UIColor ks_colorWithRGBHex:0xFFFFFF alpha:0.75]];
            
            usedX = usedX + strMicTextLength + KSMargin_Dynamic(10); // 10为padding
        }
        
        if ([simpleFeed isKindOfTmeTownFeed] && audienceNum > 0){  // TMETown 在线人数
            CGFloat memberX = startX + KSMargin_Dynamic(11) + KSMargin_Dynamic(3);
            [self layoutLocalImage:[UIImage imageNamed:@"feed_town_member_icon_v8"]
                            toInfo:info
                              rect:CGRectMake(memberX, bottomLeftViewY, iconWidth, iconWidth)
                               tag:TimelineFeedCellTownMemberIcon
                             block:nil];
                    
            strAudience = [KSFormatHelper formatNewNumber:[[simpleFeed.tmeTown.jce_mapExt safeObjectForKey:@"online_num"] integerValue]];
            UIColor* txtColor = [UIColor colorWithWhite:1 alpha:0.7];
            NSString *strAudienceText = [NSString stringWithFormat:@"%@",strAudience];
            CGFloat audienceTextLength = [strAudienceText kSongSizeWithFont:numberFont].width;
            
            [self layoutOneLineText:strAudienceText
                             toInfo:info
                                  x:memberX + iconWidth + KSMargin_Dynamic(2)
                                  y:bottomLeftViewY + 2
                                  w:audienceTextLength +  KSMargin_Dynamic(2)
                                  f:numberFont
                                  c:txtColor];
        }
        
        // 当前歌名
        if (!IS_EMPTY_STR_BM(currentSongName) && !isInteractGameType) {
            UIFont *songNameFont = _font_big([UIFont ks_fontWithFontType:KSFontType_SmallBold]);
            CGFloat currentSongNameTextLength = [currentSongName kSongSizeWithFont:songNameFont].width;
            // 歌名能用的最大长度
            CGFloat currentSongNameMaxLength = usedX - iconWidth - KSMargin_Dynamic(2) - KSMargin_Dynamic(14);
            currentSongNameTextLength = min(currentSongNameTextLength, currentSongNameMaxLength);
            
            // 歌名图标
            [self layoutLocalImage:[UIImage imageNamed:@"feed_timeline_live_music_icon"]
                            toInfo:info
                              rect:CGRectMake(usedX, bottomLeftViewY, iconWidth, iconWidth)
                               tag:TimelineFeedCellKTVSongNameIcon
                             block:nil];
            
            usedX = usedX + iconWidth + KSMargin_Dynamic(2); // 2为padding
            // 歌名文本
            [self layoutOneLineText:currentSongName
                             toInfo:info
                                  x:usedX
                                  y:bottomLeftViewY - 1 // 1为视觉还原
                                  w:currentSongNameTextLength
                                  f:songNameFont
                                  c:[UIColor ks_colorWithRGBHex:0xFFFFFF alpha:0.75]];
            
            usedX = usedX + currentSongNameTextLength + KSMargin_Dynamic(10); // 10为padding
        }
        
        if (isInteractGameType && !IS_EMPTY_STR_BM([simpleFeed.ktvRoomWebgameStatusInfo.jce_stComm.jce_mapExt safeObjectForKey:@"strTicketDoc"])) {
            [self layoutText:[simpleFeed.ktvRoomWebgameStatusInfo.jce_stComm.jce_mapExt safeObjectForKey:@"strTicketDoc"]
                      toInfo:info
                     maxLine:1
                           x:titleX
                           y:titleY+titleSize.height
                           w:titleMaxWidth
                           f:_font_big([UIFont ks_fontWithFontType:KSFontType_SmallBold])
                           c:[UIColor colorWithWhite:1 alpha:0.75]];
        }
    }

    // 左上角动效
    if([simpleFeed isKindOfTmeTownFeed]) { // TMETown
        NSString *topKtvType = SAFE_STR_BM([simpleFeed.tmeTown.jce_mapExt safeObjectForKey:@"logo_text"]);
        if (IS_EMPTY_STR_BM(topKtvType)) {
            topKtvType = @"虚拟房间";
        }
        CGFloat topViewX = startX + KSMargin_Dynamic(8);
        CGFloat topViewY = startY + KSMargin_Dynamic(8);
        CGSize roomTextSize = [topKtvType kSongSizeWithFont:KSBoldFontOfSize_Dynamic(12)];
        CGFloat totalWidth = roomTextSize.width + KSMargin_Dynamic(8) + KSMargin_Dynamic(25);
        
        // 布局黑色背景色
        [self layoutUIViewItem:info
                          rect:CGRectMake(topViewX, topViewY, totalWidth, KSMargin_Dynamic(22))
                           tag:TimelineFeedCellKTVStatusMask
                         block:^(KSUIItem *layoutItem) {
            layoutItem.normalBackgroundColor = [UIColor ks_colorWithRGBHex:0x000000];
            layoutItem.uiItemClipsToBounds = YES;
            layoutItem.ksCornerRadius = KSMargin_Dynamic(3);
        }];
        
        [self layoutLocalImage:[UIImage imageNamed:@"feed_town_top_icon_new"]
                        toInfo:info
                          rect:CGRectMake(topViewX + KSMargin_Dynamic(4), topViewY + KSMargin_Dynamic(4), KSMargin_Dynamic(18), KSMargin_Dynamic(12))
                           tag:0];
        
        [self layoutOneLineText:topKtvType
                         toInfo:info
                              x:topViewX + KSMargin_Dynamic(26)
                              y:topViewY + (KSMargin_Dynamic(22) - roomTextSize.height) / 2
                              w:roomTextSize.width + KSMargin_Dynamic(8)
                              f:KSBoldFontOfSize_Dynamic(12)
                              c:[UIColor whiteColor]];
        
    } else {
        // 左上角状态条：动效 + 歌房状态
        CGFloat bgSizeH = _size_mid(KSMargin_Dynamic(22));
        CGFloat textImageHeight = bgSizeH * 0.636; // "歌房"文字图片按比例计算的高度
        CGFloat textImageWidth = 0;
        UIImage *textImage = nil;
        if (isSocialRoom) {
            textImageWidth = textImageHeight * (192 / 52.0);
            textImage = [UIImage imageNamed:@"feed_timeline_huanjugefang_text"];
        } else {
            textImageWidth = textImageHeight * (96 / 52.0);
            textImage = [UIImage imageNamed:@"feed_timeline_gefang_text"];
        }
        
        CGFloat normalStatusWidth = textImageWidth + KSMargin_Dynamic(30);
        
        NSString *roomStatusText = @""; // 歌房状态文字 "欢唱中" 猜歌王者"
        if ((roomType & KSKTVRoomType_Social) == KSKTVRoomType_Social) {
            if (isInteractGameType) {
                roomStatusText = @"游戏中";
            }
            else if (onlineNum > 0) {
                roomStatusText = [NSString stringWithFormat:@"%@人", [KSFormatHelper formatNewNumber:onlineNum]];
            }
        } else {
            if (isInteractGameType) {
                roomStatusText = @"游戏中";
            } else if (!IS_EMPTY_STR_BM(game_name)) {
                roomStatusText = game_name;
            } else if ((roomStatus == JceTimeline_KTV_ROOM_STATUS_TYPE_ENUM_WAITING_MIKE ||
                roomStatus == JceTimeline_KTV_ROOM_STATUS_TYPE_ENUM_PLAYING_SONG ||
                roomStatus == JceTimeline_KTV_ROOM_STATUS_TYPE_ENUM_PLAYING_KTV_GAME ||
                gifStatus == KTVLottieSourceTypeInKtvSing ||
                simpleFeed.ktvRoomMike) &&
                !IS_EMPTY_STR_BM(currentSongName)) { // 这里跟安卓的逻辑保持一致
                roomStatusText = @"欢唱中";
            } else if (gifStatus == KTVLottieSourceTypeInGuessSongGame) {
                roomStatusText = @"猜歌王者";
            } else if (gifStatus == KTVLottieSourceTypeInCpGame) {
                roomStatusText = @"心动配对";
            } else if (gifStatus == KTVLottieSourceTypeIn21PokerGame) {
                roomStatusText = @"21点牌王";
            } else if (gifStatus == KTVLottieSourceTypenGiftChallenge) {
                roomStatusText = @"挑战中";
            } else if (gifStatus == KTVLottieSourceTypeInGrabSingGame) {
                roomStatusText = @"抢麦中";
            }
        }
        UIFont *statusTextFont = _font_mid([self getAvantFontWithSize:12 bold:YES]);
        CGFloat totalWidth = 0; // 状态条总长度
        if (roomStatusText.length > 0) { // 如有状态
            // 状态文字的size
            CGSize roomStatusTextSize = [roomStatusText kSongSizeWithFont:statusTextFont];
            totalWidth = normalStatusWidth + roomStatusTextSize.width + KSMargin_Dynamic(7) * 2; // 状态文字左右padding给7
        } else {
            totalWidth = normalStatusWidth; // 没有直播状态，就只展示”歌房“长度
        }
        
        CGFloat topViewX = startX + KSMargin_Dynamic(8);
        CGFloat topViewY = startY + KSMargin_Dynamic(10);
        // 布局橘黄色背景色
        [self layoutUIViewItem:info
                        rect:CGRectMake(topViewX, topViewY, totalWidth, bgSizeH)
                        tag:TimelineFeedCellKTVStatusMask
                        block:^(KSUIItem *layoutItem) {
            layoutItem.normalBackgroundColor = [UIColor ks_colorWithRGBHex:0xf6b134];
            layoutItem.uiItemClipsToBounds = YES;
            layoutItem.ksCornerRadius = KSMargin_Dynamic(5);
        }];
        
        // 布局歌房、排麦动效
        CGFloat lottieViewH = _size_mid(KSMargin_Dynamic(9));
        CGFloat lottieViewY = topViewY + (bgSizeH - lottieViewH) * 0.5;
        [self layoutLocalLottieViewWithAnimationName:@"timeline_live_feed_playing"
                                            toInfo:info
                                                rect:CGRectMake(topViewX + KSMargin_Dynamic(7), lottieViewY, KSMargin_Dynamic(12), lottieViewH)
                                                tag:TimelineFeedCellLiveShowRectAnimation
                                            block:^(KSLottieAnimationItem *layoutItem) {
            layoutItem.autoStart = YES;
            layoutItem.loopAnimation = YES;
        }];
        
        // 布局”歌房“文字图片（这里的新字体对文字无效，应该是字体包被阉割了，所以这里UI让用图片实现）
        [self layoutLocalImage:textImage
                        toInfo:info
                        rect:CGRectMake(topViewX + KSMargin_Dynamic(23), topViewY + (bgSizeH - textImageHeight) / 2, textImageWidth, textImageHeight)
                        tag:TimelineFeedCellLiveShowLeftTopTypeIcon
                        block:nil];
        
        // 布局歌房游戏状态文案
        if([simpleFeed isPublicSingleKtvFeed])
        {
            // 【欢唱歌房】
            // 欢唱玩法需要根据玩法数据实时更新玩法状态
            CGRect labelContainerRect = CGRectMake(topViewX + normalStatusWidth, topViewY, normalStatusWidth, bgSizeH);
            [self layoutLiteKtvGameStatusLabelContainer:simpleFeed rect:labelContainerRect info:info];
        }
        else{
            // 非【欢唱歌房】
            // 布局歌房状态背景色
            if (roomStatusText.length > 0) {
                [self layoutUIViewItem:info
                                  rect:CGRectMake(topViewX + normalStatusWidth, topViewY, totalWidth - normalStatusWidth, bgSizeH)
                                   tag:TimelineFeedCellKTVRightStatusMask
                                 block:^(KSUIItem *layoutItem) {
                    layoutItem.normalBackgroundColor = [[UIColor ks_colorWithRGBHex:0x000000] colorWithAlphaComponent:0.1];
                    layoutItem.uiItemClipsToBounds = YES;
                    layoutItem.ksCornerRadius = KSMargin_Dynamic(5);
                    layoutItem.clipCorner = UIRectCornerBottomRight | UIRectCornerTopRight;
                }];
                
                // 布局歌房状态文字 “21点”等等
                CGSize roomStatusTextSize = [roomStatusText kSongSizeWithFont:statusTextFont];
                [self layoutOneLineText:roomStatusText
                                 toInfo:info
                                      x:topViewX + normalStatusWidth + KSMargin_Dynamic(7)
                                      y:topViewY + (bgSizeH - roomStatusTextSize.height) / 2
                                      w:totalWidth - normalStatusWidth - KSMargin_Dynamic(7) * 2
                                      f:statusTextFont
                                      c:[UIColor whiteColor]];
            }
        }
  
    }
    
    // TMETown 虚拟人物
    if ([simpleFeed isKindOfTmeTownFeed]) {
        NSString *tmeAvatarUrl = SAFE_STR_BM([simpleFeed.tmeTown.jce_mapExt safeObjectForKey:@"user_avatar_url"]);
        CGFloat tmeTownViewWidth = KSMargin_Dynamic(47);
        CGFloat tmeTownViewHeight = KSMargin_Dynamic(106);
        CGFloat tmeTownViewX = startX + (bgLength - tmeTownViewWidth - KSMargin_Dynamic(12));
        CGFloat tmeTownViewY = startY + (bgHeight - tmeTownViewHeight - KSMargin_Dynamic(10));

        if (!IS_EMPTY_STR_BM(tmeAvatarUrl)) {
            [self layoutOriginalImage:tmeAvatarUrl
                          placeHolder:nil
                               action:action
                               toInfo:info
                                 rect:CGRectMake(tmeTownViewX, tmeTownViewY, tmeTownViewWidth, tmeTownViewHeight)
                                  tag:TimelineFeedCellKtvRoomLevel
                                block:^(KSUIOriginalImageItem *layoutItem) {
                layoutItem.contentMode = UIViewContentModeScaleAspectFill;
                layoutItem.uiItemClipsToBounds = YES;
                layoutItem.clipCorner = UIRectCornerAllCorners;
            }];
        }
    }
    
    if([simpleFeed isPublicSingleKtvFeed])
    {
        // 布局InteractGameContainer
        [self layoutInteractGameContainer:simpleFeed rect:CGRectMake(KSMargin_Dynamic_70, [info currentY], bgLength, bgLength) info:info];
    }
    else
    {
        if (isInteractGameType) {
            CGSize buttonSize = CGSizeMake(KSMargin_Dynamic(80), KSMargin_Dynamic(28));
            CGFloat buttonY = startY + bgHeight - KSMargin_Dynamic(12) - buttonSize.height;
            CGFloat centerX = startX + bgLength*0.5;
            CGFloat centerY = startY + bgHeight * 0.5;
            
            [self layoutInteractGameStatus:simpleFeed info:info centerX:centerX centerY:centerY buttonSize:buttonSize buttonStartY:buttonY bgLength:bgLength];
        }
    }

    [info increaseHeight:bgHeight];
}

// 互动游戏feed样式
+ (void)layoutInteractGameStatus:(KSimpleFeed *)simpleFeed info:(KSLayoutInfo *)info centerX:(CGFloat)centerX centerY:(CGFloat)centerY buttonSize:(CGSize)buttonSize buttonStartY:(CGFloat)buttonStartY bgLength:(CGFloat)bgLength
{
    CGFloat standardRatio = bgLength/287.0;
    [self layoutVideoAnimationViewResourceId:1579 toInfo:info rect:CGRectMake(KSMargin_Dynamic_70, [info currentY], bgLength, bgLength) tag:TimelineFeedCellInteractGamePKVideoAnimation block:^(KSVideoAnimationItem *layoutItem) {
        layoutItem.loopAnimation = YES;
        layoutItem.autoStart = YES;
    }];
    
    CGSize avatarSize = CGSizeMake(bgLength*0.2, bgLength*0.2);
    NSInteger leftCount = MIN(simpleFeed.ktvRoomWebgameStatusInfo.jce_stGameWebview.jce_vecLeftUsers.count, 3);
    NSInteger rightCount = MIN(simpleFeed.ktvRoomWebgameStatusInfo.jce_stGameWebview.jce_vecRightUsers.count, 3);
    NSUInteger avatarTag = TimelineFeedCellInteractGamePKAvatarImage + 2424 + leftCount + rightCount;
    
    NSString *leftSegmentIcon = nil;
    NSString *leftSegment = nil;
    CGFloat leftStartX = centerX - 27*standardRatio - avatarSize.width - bgLength*0.09*(leftCount-1);
    for (int i=0; i<leftCount; i++) {
        proto_feed_webapp_game_status_profile *profile = SAFE_CAST(simpleFeed.ktvRoomWebgameStatusInfo.jce_stGameWebview.jce_vecLeftUsers[i], [proto_feed_webapp_game_status_profile class]);
        KSCellUser *cellUser = [[KSCellUser alloc] init];
        cellUser.userId = profile.jce_uUid;
        cellUser.avatarUrl = profile.jce_strAvatar;
        [self layoutHead:cellUser action:nil toInfo:info x:leftStartX y:centerY - avatarSize.height*0.5 w:avatarSize.width tag:avatarTag-- borderColor:[UIColor ks_whiteColor] borderWidth:KSMargin_Dynamic(2)];
        leftStartX += bgLength*0.09;
        if (i == leftCount-1) {
            leftSegment = profile.jce_strSegment;
            leftSegmentIcon = profile.jce_strSegmentIcon;
        }
    }
    
    NSString *rightSegmentIcon = nil;
    NSString *rightSegment = nil;
    CGFloat rightStartX = centerX + 27*standardRatio + bgLength*0.09*(rightCount-1);
    for (int i=(int)(rightCount-1); i>=0; i--) {
        proto_feed_webapp_game_status_profile *profile = SAFE_CAST(simpleFeed.ktvRoomWebgameStatusInfo.jce_stGameWebview.jce_vecRightUsers[i], [proto_feed_webapp_game_status_profile class]);
        KSCellUser *cellUser = [[KSCellUser alloc] init];
        cellUser.userId = profile.jce_uUid;
        cellUser.avatarUrl = profile.jce_strAvatar;
        [self layoutHead:cellUser action:nil toInfo:info x:rightStartX y:centerY - avatarSize.height*0.5 w:avatarSize.width tag:avatarTag-- borderColor:[UIColor ks_whiteColor] borderWidth:KSMargin_Dynamic(2)];
        rightStartX -= bgLength*0.09;
        if (i == 0) {
            rightSegment = profile.jce_strSegment;
            rightSegmentIcon = profile.jce_strSegmentIcon;
        }
    }
    
    if (rightCount <= 0) {
        rightStartX = centerX + 27*standardRatio;
        CGSize joinIconSize = CGSizeMake(KSMargin_Dynamic(18), KSMargin_Dynamic(18));
        [self layoutUIViewItem:info
                        rect:CGRectMake(rightStartX, centerY - avatarSize.height*0.5, avatarSize.width, avatarSize.height)
                        tag:TimelineFeedCellInteractGamePKAddBackgroud
                        block:^(KSUIItem *layoutItem) {
            layoutItem.normalBackgroundColor = [UIColor ks_colorWithRGBHex:0xF2F4F6];
            layoutItem.uiItemClipsToBounds = YES;
            layoutItem.ksCornerRadius = avatarSize.width*0.5;
            layoutItem.borderWidth = KSMargin_Dynamic(2);
            layoutItem.borderColor = [UIColor whiteColor];
        }];
        
        [self layoutLocalImage:[UIImage imageNamed:@"feed_timeline_interactGame_addIcon"]
                        toInfo:info
                          rect:CGRectMake(rightStartX+(avatarSize.width-joinIconSize.width)*0.5, centerY-joinIconSize.width*0.5, joinIconSize.width, joinIconSize.height)
                           tag:TimelineFeedCellInteractGamePKAddIcon block:^(KSUIItemLocalImage *layoutItem) {
            layoutItem.uiItemClipsToBounds = YES;
            layoutItem.clipCorner = UIRectCornerAllCorners;
            layoutItem.contentMode = UIViewContentModeScaleAspectFill;
        }];
    }
    
    CGSize segmentIconSize = CGSizeMake(KSMargin_Dynamic(12), KSMargin_Dynamic(12));
    NSUInteger segmentTag = TimelineFeedCellInteractGameSegmentIcon + 1741;
    if (!IS_EMPTY_STR_BM(leftSegment) && !IS_EMPTY_STR_BM(leftSegmentIcon)) {
        CGSize strSegmentSize = [leftSegment kSongSizeWithFont:KSMediumFontOfSize_Dynamic(11)];
        CGFloat leftSegmentStartX = centerX - 27*standardRatio - avatarSize.width*0.5 - (segmentIconSize.width+KSMargin_Dynamic(3.5)+strSegmentSize.width)*0.5;
        [self layoutOriginalImage:leftSegmentIcon
                      placeHolder:[KSLayoutUIManager cahceImageWithName:@"DefaultAlbum"]
                           action:nil
                           toInfo:info
                             rect:CGRectMake(leftSegmentStartX, centerY+avatarSize.height*0.5+KSMargin_Dynamic(6), segmentIconSize.width, segmentIconSize.height)
                              tag:segmentTag++
                            block:^(KSUIOriginalImageItem *layoutItem) {
            layoutItem.contentMode = UIViewContentModeScaleAspectFill;
            layoutItem.clipCorner = UIRectCornerAllCorners;
        }];
        
        [self layoutOneLineText:leftSegment
                         toInfo:info
                              x:leftSegmentStartX+segmentIconSize.width+KSMargin_Dynamic(3.5)
                              y:centerY+avatarSize.height*0.5+KSMargin_Dynamic(5)
                              w:strSegmentSize.width
                              f:KSMediumFontOfSize_Dynamic(11)
                              c:[UIColor ks_colorWithRGBString:@"#BC6C29"]];
    }
    
    if (!IS_EMPTY_STR_BM(rightSegment) && !IS_EMPTY_STR_BM(rightSegmentIcon)) {
        CGSize strSegmentSize = [rightSegment kSongSizeWithFont:KSMediumFontOfSize_Dynamic(11)];
        CGFloat rightSegmentStartX = centerX + 27*standardRatio + avatarSize.width*0.5 - (segmentIconSize.width+KSMargin_Dynamic(3.5)+strSegmentSize.width)*0.5;
        [self layoutOriginalImage:rightSegmentIcon
                      placeHolder:[KSLayoutUIManager cahceImageWithName:@"DefaultAlbum"]
                           action:nil
                           toInfo:info
                             rect:CGRectMake(rightSegmentStartX, centerY+avatarSize.height*0.5+KSMargin_Dynamic(6), segmentIconSize.width, segmentIconSize.height)
                              tag:segmentTag++
                            block:^(KSUIOriginalImageItem *layoutItem) {
            layoutItem.contentMode = UIViewContentModeScaleAspectFill;
            layoutItem.clipCorner = UIRectCornerAllCorners;
        }];
        
        [self layoutOneLineText:rightSegment
                         toInfo:info
                              x:rightSegmentStartX+segmentIconSize.width+KSMargin_Dynamic(3.5)
                              y:centerY+avatarSize.height*0.5+KSMargin_Dynamic(5)
                              w:strSegmentSize.width
                              f:KSMediumFontOfSize_Dynamic(11)
                              c:[UIColor ks_colorWithRGBString:@"#BC6C29"]];
    }
    
    KSDrawItemAction *operactAction = [KSDrawItemAction new];
    BOOL isGamePlaying = [[simpleFeed.ktvRoomWebgameStatusInfo.jce_stComm.jce_mapExt safeObjectForKey:@"uStatus"] integerValue] == 2;
    operactAction.type = isGamePlaying? DRAWITEM_ACTION_TYPE_INTERACTGAME_STARTMATCH : DRAWITEM_ACTION_TYPE_INTERACTGAME_JOINGAME;
    operactAction.busiData = simpleFeed;
    NSString *btnText = isGamePlaying ? @"我也要玩" :@"立即加入";
    [self layoutButton:operactAction toInfo:info rect:CGRectMake(KSMargin_Dynamic_70 + bgLength - buttonSize.width - 12, buttonStartY, buttonSize.width, buttonSize.height) tag:TimelineFeedCellInteractGameWatch block:^(KSUIItemButton *layoutItem) {
        layoutItem.normalBackgroundColor = [UIColor ks_colorWithRGBHex:0xFE4F4F];
        layoutItem.titleText = btnText;
        layoutItem.titleDictForStates = @{@(UIControlStateNormal):btnText};
        layoutItem.titleFont = KSFont_T12_Medium_Dynamic;
        layoutItem.titleColorDictForStates = @{@(UIControlStateNormal):[UIColor ks_whiteColor],
                                               @(UIControlStateHighlighted):[UIColor ks_whiteColor]};
        layoutItem.ksCornerRadius = buttonSize.height/2;
        layoutItem.enable = YES;
    }];
}

// 异步游戏feed
+ (void)layoutSocialGameShow_2023:(KSimpleFeed *)simpleFeed info:(KSLayoutInfo *)info
{
    CGFloat startX = KSMargin_Dynamic_70;
    CGFloat startY = [info currentY];
    CGFloat bgLength = SCREEN_WIDTH - KSMargin_Dynamic_70 - KSMargin_Dynamic_20;
    CGFloat bgHeight = KSMargin_Dynamic(218);
    
    // 描述文案
    if (!IS_EMPTY_STR_BM(simpleFeed.socialGameShow.jce_strFeedsDoc)) {
        [info increaseHeight:KSMargin_Dynamic_10];
        CGFloat descHeight = 0.0;
        NSInteger maxLine = simpleFeed.isExpandAllText == NO ? 3 : INT_MAX;
        descHeight = [self layoutText:simpleFeed.socialGameShow.jce_strFeedsDoc
                               toInfo:info
                              maxLine:maxLine
                                    x:startX
                                    y:startY
                                    w:SCREEN_WIDTH - startX - KSMargin_Dynamic_20
                                    f:kDescTxtFont
                                    c:UIColor.ks_primaryTextColor
                          layoutBlock:nil
                       prelayoutBlock:^(KSLayoutRichText *layoutTextItems) {
            layoutTextItems.lastlineExpandStr = (simpleFeed.isExpandAllText == NO)? KString(@"展开"):KString(@"收起");
            
            DRAWITEM_ACTION_TYPE actionType = (simpleFeed.isExpandAllText == NO)? DRAWITEM_ACTION_TYPE_FEED_EXPAND_ALL_TEXT : DRAWITEM_ACTION_TYPE_FEED_COLLAPSE_ALL_TEXT;
            KSDrawItemAction *action = [KSDrawItemAction actionWithType:(int)actionType];
            action.busiData = simpleFeed;
            layoutTextItems.lastlineExpandAction = action;
        }];
        
        [info increaseHeight:descHeight];
        startY = [info currentY];
    }
    
    KSDrawItemAction *action = [KSDrawItemAction new];
    action.type = DRAWITEM_ACTION_TYPE_SOCIALGAMESHOWFEED;
    action.busiData = simpleFeed;
    // 歌房封面大图
    [self layoutOriginalImage:simpleFeed.socialGameShow.jce_strBgURL
                  placeHolder:[KSLayoutUIManager cahceImageWithName:@"DefaultAlbum"]
                       action:action
                       toInfo:info
                         rect:CGRectMake(startX, startY, bgLength, bgHeight)
                          tag:TimelineFeedCellLiveShowCoverImage
                        block:^(KSUIOriginalImageItem *layoutItem) {
        layoutItem.contentMode = UIViewContentModeScaleAspectFill;
        layoutItem.ksCornerRadius = kFeedImageRadiusCorner;
        layoutItem.uiItemClipsToBounds = YES;
        layoutItem.clipCorner = UIRectCornerAllCorners;
    }];
    
    
    CGFloat topViewX = startX + KSMargin_Dynamic(8);
    CGFloat topViewY = startY + KSMargin_Dynamic(10);
    UIColor *magicColor = [UIColor ks_colorWithRGBHex:0x000000 alpha:0.8];
    UIColor *endColor = [UIColor ks_colorWithRGBHex:0x000000 alpha:0];
    NSArray *colors = @[(__bridge id)magicColor.CGColor,
                        (__bridge id)magicColor.CGColor,
                        (__bridge id)endColor.CGColor,
                        (__bridge id)endColor.CGColor];
    [self layoutGradientLayerViewWithColors:colors
                                  locations:@[@(0.0),@(0.15),@(0.57),@1] // 这个数据调了一万年，心累
                                 startPoint:CGPointMake(0, 0)
                                   endPoint:CGPointMake(1, 0.2)
                                       info:info
                                       rect:CGRectMake(topViewX, topViewY, KSMargin_Dynamic(170), KSMargin_Dynamic(35))
                                        tag:TimelineFeedCellMagicColorView
                                      block:^(KSGradientLayerViewItem *layoutItem) {
        layoutItem.ksCornerRadius = KSMargin_Dynamic(35)*0.5;
        layoutItem.uiItemClipsToBounds = YES;
        layoutItem.clipCorner = UIRectCornerAllCorners;
    }];
    
    KSCellUser *cellUser = [[KSCellUser alloc] init];
    cellUser.userId = simpleFeed.socialGameShow.jce_uUild;
    CGSize avatarSize = CGSizeMake(KSMargin_Dynamic(31), KSMargin_Dynamic(31));
    [self layoutHead:cellUser
              action:nil
              toInfo:info
                   x:topViewX+KSMargin_Dynamic(2)
                   y:topViewY+KSMargin_Dynamic(2)
                   w:avatarSize.width
                 tag:TimelineFeedCellSocialGameAvatarImage
         borderColor:[UIColor ks_whiteColor]
         borderWidth:KSMargin_Dynamic(1.5)];
    
    if (!IS_EMPTY_STR_BM(simpleFeed.socialGameShow.jce_strLeftDoc)) {
        CGSize strLeftDocSize = [simpleFeed.socialGameShow.jce_strLeftDoc kSongSizeWithFont:KSFont_T12_Medium_Dynamic];
        [self layoutOneLineText:simpleFeed.socialGameShow.jce_strLeftDoc
                         toInfo:info
                              x:topViewX+KSMargin_Dynamic(2)+avatarSize.width+KSMargin_Dynamic(8)
                              y:topViewY+(KSMargin_Dynamic(35)-strLeftDocSize.height)*0.5
                              w:strLeftDocSize.width
                              f:KSFont_T12_Medium_Dynamic
                              c:[UIColor ks_colorWithRGBString:@"#FFDB4F"]];
    }
    
    CGSize btnSize = CGSizeMake(KSMargin_Dynamic(211), KSMargin_Dynamic(32));
    CGFloat btnX = startX+(bgLength-btnSize.width)*0.5;
    CGFloat btnY = startY+bgHeight-KSMargin_Dynamic(16)-btnSize.height;
    if (!IS_EMPTY_STR_BM(simpleFeed.socialGameShow.jce_strButtonDoc)) {
        KSDrawItemAction *btnAction = [KSDrawItemAction new];
        btnAction.type = DRAWITEM_ACTION_TYPE_SOCIALGAMECLICKGETAWARD;
        btnAction.busiData = simpleFeed;
        [self layoutButton:btnAction
                    toInfo:info
                      rect:CGRectMake(btnX, btnY, btnSize.width, btnSize.height)
                       tag:TimelineFeedCellSocialGameGetAwardBtn
                     block:^(KSUIItemButton *layoutItem) {
            layoutItem.normalBackgroundColor = [UIColor ks_colorWithRGBHex:0xFE4F4F];
            layoutItem.titleText = simpleFeed.socialGameShow.jce_strButtonDoc;
            layoutItem.titleDictForStates = @{@(UIControlStateNormal):simpleFeed.socialGameShow.jce_strButtonDoc};
            layoutItem.titleFont = KSFont_T14_Medium_Dynamic;
            layoutItem.titleColorDictForStates = @{@(UIControlStateNormal):[UIColor ks_whiteColor],
                                                   @(UIControlStateHighlighted):[UIColor ks_whiteColor]};
            layoutItem.ksCornerRadius =btnSize.height/2;
            layoutItem.enable = YES;
        }];
    }
    
    
    [info increaseHeight:bgHeight];
}

// 布局feed「底部」（TMETown按钮）
+ (void)layoutBottomTmeTownBtn_2022:(KSimpleFeed *)simpleFeed info:(KSLayoutInfo *)info {
    if (simpleFeed.layoutConfig.isHiddenActionBtns) {
        //根据配置是否显示底部的操作按钮
        return;
    }
    
    NSString *titleText = SAFE_STR_BM([simpleFeed.tmeTown.jce_mapExt safeObjectForKey:@"button_text"]);
    if (IS_EMPTY_STR_BM(titleText)) {
        titleText = @"进入虚拟房间";
    }
    
    CGFloat usedX = KSMargin_Dynamic_70;
    CGSize fadeTextSize = [titleText kSongSizeWithFont:[UIFont ks_fontWithSize:12 bold:YES]];
    CGFloat fadeButtonWidth = fadeTextSize.width + KSMargin_Dynamic(24);
    CGFloat fadeButtonHeight = KSMargin_Dynamic(30);
    
    KSDrawItemAction *action = [KSDrawItemAction new];
    action.type = DRAWITEM_ACTION_TYPE_KTV_TME_TOWN;
    action.busiData = simpleFeed;
    [self layoutButton:action toInfo:info rect:CGRectMake(usedX, [info currentY] + KSMargin_Dynamic(14), fadeButtonWidth, fadeButtonHeight) tag:TimelineFeedCellTownActionButton block:^(KSUIItemButton *layoutItem) {
        
        layoutItem.normalBackgroundColor = [UIColor ks_whiteColor];
        layoutItem.titleText = titleText;
        layoutItem.titleDictForStates = @{@(UIControlStateNormal):titleText};
        layoutItem.titleFont = KSBoldFontOfSize(12);
        layoutItem.titleColorDictForStates = @{@(UIControlStateNormal):[UIColor ks_colorWithRGBHex:0x323333],
                                               @(UIControlStateHighlighted):[UIColor ks_colorWithRGBHex:0x323333]};
        layoutItem.ksCornerRadius = fadeButtonHeight/2;
        layoutItem.enable = YES;
        layoutItem.normalBackgroundColor = [UIColor ks_colorWithRGBHex:0xF3F4F6];
    }];
    
    [info increaseHeight:KSMargin_Dynamic(30) + KSMargin_Dynamic(20)]; //视觉稿上标注15，视觉说大了，这里做个小的偏移
}
    
// 布局feed「底部」（送礼 点赞 评论 转发 投票 购买 合唱等等按钮）
+ (void)layoutBottomFunctionBtn_2022:(KSimpleFeed *)simpleFeed info:(KSLayoutInfo *)info {
    if (simpleFeed.layoutConfig.isHiddenActionBtns) {
        //根据配置是否显示底部的操作按钮
        return;
    }
    
    BOOL isShowInteractGameEnterBottom = [simpleFeed isInteractGameInPlayingStatus] || simpleFeed.socialGameShow;
    
    if ((simpleFeed.ktvRoomShow.jce_iRoomType & KSKTVRoomType_Social) == KSKTVRoomType_Social && !isShowInteractGameEnterBottom) {
        // 欢聚歌房，底部什么都不展示
        [info increaseHeight:15];
        return;
    }
    
    [info increaseHeight:12]; // 操作区域上部padding

    // 默认展示送礼 点赞 评论 转发
    KSBottomFucBtnType buttonType = KSBottomFucBtnType_none; // 是否有异化的按钮
    BOOL showGift = NO; // 默认不展示送礼按钮
    BOOL showComment = YES; // 默认展示评论按钮
    BOOL showLike = YES; // 默认展示点赞按钮
 
    NSString *ugcTypeString = nil;
    if (GetFlexBOOL([[KSPaySoloAlbumManager sharedManager] isPayUgcWithUgcMask:simpleFeed.songinfo.ugcMask])) {
        ugcTypeString = [[KSPaySoloAlbumManager sharedManager] updateAndReturnPayDescWithPayRight:simpleFeed.songinfo.mapRight contentID:simpleFeed.simpleFeedCommon.strFeedId andPayType:KSUserPayTypeOptionUGC];
    }
    
    if ([self isShowVoteBtnWithFeed:simpleFeed]) { // 有投票按钮的feed
        buttonType = KSBottomFucBtnType_vote;
    } else if (simpleFeed.payAlbumInfo) { // 专辑feed
        if ([[KSPaySoloAlbumManager sharedManager] getPayIconMask:simpleFeed.payAlbumInfo.mapRight] == KSPayIconPriorityVIP) {
            // vip专享专辑 , 判断是不是vip
            if (!GetFlexBOOL([[KSLoginManager sharedInstance].curUserInfo.userVipInfo isKSVip])) {
                buttonType = KSBottomFucBtnType_none; // 不显示购买vip按钮了
            }
        } else if (simpleFeed.payAlbumInfo.lPlayMask) {
            // 付费专辑
            buttonType = KSBottomFucBtnType_purchase; // 购买按钮
        }
        showLike = NO; // 专辑没有点赞 没有评论
        showComment = NO;
    } else if (UGC_HALF_CHORUS(simpleFeed.songinfo.ugcMask) || [simpleFeed canSoloSongShowChorusStyle]) {
        // 合唱 && 礼物合唱
        /* 礼物合唱按钮显示条件 */
        /* 1.是合唱半成品并且有礼物 */
        /* 2.满足条件的独唱作品并且有礼物 */
        if (simpleFeed.hcCellItem.jce_iHasGift > 0) {
            // 礼物合唱
            buttonType = KSBottomFucBtnType_gift_hc;
        } else {
            // 普通合唱
            buttonType = KSBottomFucBtnType_hc;
        }
    } else if ([simpleFeed isKindOfLiveShowFeed]) { // 直播
        showComment = NO;
        showLike = NO;
    } else if (simpleFeed.ktvRoomShow || simpleFeed.ktvRoomMike) { // 歌房
        showComment = NO;
        showLike = NO;
    } else if (simpleFeed.soloAlbumInfo || simpleFeed.simpleFeedCommon.uTypeid == FEED_TYPE_USER_ALBUM) { // 歌单
        showComment = NO;
        showLike = NO;
    } else if ([simpleFeed isFollowTabFeed]) {
        KSAdFreeGiftScene *followScene = [KSAdFreeGiftScene sceneWithType:KSAdFreeGiftSceneType_FollowFeed];
        showGift = followScene.scenceShow && followScene.giftBtnNum == 2 && [KSTimelineManager sharedManager].adFreeGift; // 看广告免费送双按钮样式
    }
    
    if ([simpleFeed isPublicSingleKtvFeed]) {
        NSString *gameName = [simpleFeed isInteractGameInPlayingStatus] ? simpleFeed.ktvRoomWebgameStatusInfo.jce_stComm.jce_strGameName : simpleFeed.socialGameShow.jce_strGameName;
        NSString *gameIcon = [simpleFeed isInteractGameInPlayingStatus] ? simpleFeed.ktvRoomWebgameStatusInfo.jce_stComm.jce_strIcon : simpleFeed.socialGameShow.jce_strIcon;
        [self layoutWebGameEnterButton:simpleFeed info:info gameName:gameName strIcon:gameIcon];
        
        [self layoutGiftButton_2022:simpleFeed info:info buttonType:buttonType];
    } else if (simpleFeed.needShowFeedAITakePic) {
        /// AI 拍同款
        [self layoutAiTakePicButton:simpleFeed info:info];
    } else {
        // 布局送礼，到这里的所有feed都有送礼
        if (isShowInteractGameEnterBottom) {
            NSString *gameName = [simpleFeed isInteractGameInPlayingStatus] ? simpleFeed.ktvRoomWebgameStatusInfo.jce_stComm.jce_strGameName : simpleFeed.socialGameShow.jce_strGameName;
            NSString *gameIcon = [simpleFeed isInteractGameInPlayingStatus] ? simpleFeed.ktvRoomWebgameStatusInfo.jce_stComm.jce_strIcon : simpleFeed.socialGameShow.jce_strIcon;
            [self layoutWebGameEnterButton:simpleFeed info:info gameName:gameName strIcon:gameIcon];
        } else {
            [self layoutGiftButton_2022:simpleFeed info:info buttonType:buttonType];
        }
    }
    
    // 布局社交功能按钮 送礼（圈层用户的ab命中情况下会有） 点赞 评论 转发
    if (!simpleFeed.socialGameShow) {
        [self layoutSocialFuncBtn:simpleFeed
                             info:info
                   showGiftButton:showGift
                   showLikeButton:showLike
                showCommentButton:showComment];
    }
    
    [info increaseHeight:_size_mid_320(32)]; // 操作区域高度
    [info increaseHeight:17]; // 操作区域下部padding （因操作区域下部一般没有东西了，所以最后的padding加在这里比较好）
    
#ifdef INTERNALBUILD
    [self layoutDebugInfo_2022:simpleFeed info:info];
#endif
}

+ (void)layoutWebGameEnterButton:(KSimpleFeed *)simpleFeed info:(KSLayoutInfo *)info gameName:(NSString *)gameName strIcon:(NSString *)strIcon
{
    CGSize gameIconSize = CGSizeMake(18, 18);
    UIFont *gameNameFont = _font_mid_320([UIFont ks_fontWithFontType:KSFontType_SmallBold]);
    CGSize gameNameSize = [gameName kSongSizeWithFont:gameNameFont];
    
    CGFloat usedX = KSMargin_Dynamic_70;
    CGFloat enterGameButtonWidth = 13 + gameIconSize.width + 5 + gameNameSize.width + 12;
    CGFloat enterGameButtonHeight = _size_mid_320(32);
    [self layoutUIViewItem:info
                      rect:CGRectMake(usedX, [info currentY], enterGameButtonWidth, enterGameButtonHeight)
                       tag:TimelineFeedCellInteractGameJumpGameBackground
                     block:^(KSUIItem *layoutItem) {
        layoutItem.normalBackgroundColor = [UIColor ks_colorWithRGBHex:0xf2f4f6];
        layoutItem.ksCornerRadius = enterGameButtonHeight * 0.5;
        layoutItem.uiItemClipsToBounds = YES;
        layoutItem.clipCorner = UIRectCornerAllCorners;
    }];
    
    CGFloat subViewStartX = 0;
    subViewStartX = subViewStartX + 13;
    
    [self layoutOriginalImage:strIcon
                  placeHolder:[KSLayoutUIManager cahceImageWithName:@"DefaultAlbum"]
                       action:nil
                       toInfo:info
                         rect:CGRectMake(subViewStartX, (enterGameButtonHeight-gameIconSize.height)*0.5, gameIconSize.width, gameIconSize.height)
                          tag:TimelineFeedCellInteractGameJumpGameIcon
                        block:^(KSUIOriginalImageItem *layoutItem) {
        layoutItem.uiItemClipsToBounds = YES;
        layoutItem.contentMode = UIViewContentModeScaleAspectFit;
        layoutItem.clipCorner = UIRectCornerAllCorners;
        layoutItem.ksCornerRadius = 4;
        layoutItem.parentViewTag = TimelineFeedCellInteractGameJumpGameBackground;
        layoutItem.canvasViewTag = TimelineFeedCellInteractGameJumpGameBackground;
    }];
    subViewStartX = subViewStartX + gameIconSize.width + 5;
    
    [self layoutOneLineLabel:gameName
                      toInfo:info
                           x:subViewStartX
                           y:(enterGameButtonHeight-gameNameSize.height)*0.5
                           w:gameNameSize.width
                           f:gameNameFont
                           c:[UIColor ks_blackColor]
                         tag:TimelineFeedCellInteractGameBottomGiftTextLabel
                       block:^(KSUIItemLabel *layoutItem) {
        layoutItem.normalBackgroundColor = [UIColor clearColor];
        layoutItem.parentViewTag = TimelineFeedCellInteractGameJumpGameBackground;
        layoutItem.canvasViewTag = TimelineFeedCellInteractGameJumpGameBackground;
    }];
    
    // 布局点击事件按钮
    KSDrawItemAction *action = [KSDrawItemAction new];
    action.type = DRAWITEM_ACTION_TYPE_INTERACTGAME_ENTERGAMELOBBY;
    action.busiData = simpleFeed;
    [self layoutButton:action
                toInfo:info
                  rect:CGRectMake(KSMargin_Dynamic_70, [info currentY], enterGameButtonWidth, enterGameButtonHeight)
                   tag:TimelineFeedCellInteractGameJumpGameActionButton
                 block:^(KSUIItemButton *layoutItem) {
        layoutItem.normalBackgroundColor = [UIColor clearColor];
    }];
}

/// AI拍同款占位
+ (void)layoutAiTakePicButton:(KSimpleFeed *)simpleFeed info:(KSLayoutInfo *)info
{
    CGFloat usedX = KSMargin_Dynamic_70; /// userX根据整个cell来计算
    CGFloat backgroundSizeH = _size_mid_320(32);
    [self layoutUIViewItem:info
                      rect:CGRectMake(usedX, [info currentY], 98, backgroundSizeH)
                       tag:TimelineFeedCellFollowFeedAITakePic
                     block:^(KSUIItem *layoutItem) {

    }];
}

+ (void)layoutGiftButton_2022:(KSimpleFeed *)simpleFeed info:(KSLayoutInfo *)info buttonType:(KSBottomFucBtnType)buttonType {
   
    BOOL isShowInteractGameEnterBottom = [simpleFeed isInteractGameInPlayingStatus] || simpleFeed.socialGameShow;
    isShowInteractGameEnterBottom = NO;
    
    BOOL hasFucBtn = buttonType == KSBottomFucBtnType_none ? NO : YES;
    // 礼物总数
    UIFont *giftFont = _font_mid_320([UIFont systemFontOfSize:13 weight:UIFontWeightBold]);
    NSString *totalGiftStr = @"送礼";
    CGSize totalGiftStrSize = [totalGiftStr kSongSizeWithFont:giftFont];
    CGFloat giftIconWidth = _size_mid_320(20);
    NSString *fucBtnString = [self calculateFucBtnDesc:buttonType]; // "礼物合唱"
    CGSize fucBtnStringSize = [fucBtnString kSongSizeWithFont:giftFont];
    
    CGFloat usedX = KSMargin_Dynamic_70; /// userX根据整个cell来计算
    // 整个礼物按钮 + 右边异化按钮的长度
    CGFloat fadeGiftButtonWidth = 0;
    
    if (buttonType == KSBottomFucBtnType_none) {
        // 礼物按钮右边没有异化按钮
        fadeGiftButtonWidth = KSMargin_Dynamic(14) + giftIconWidth + KSMargin_Dynamic(4) + totalGiftStrSize.width + KSMargin_Dynamic(14);
    } else {
        // 有异化按钮
        fadeGiftButtonWidth = KSMargin_Dynamic(14) + giftIconWidth + KSMargin_Dynamic(4) + totalGiftStrSize.width + KSMargin_Dynamic(9) + 1 + KSMargin_Dynamic(9) + fucBtnStringSize.width + KSMargin_Dynamic(14);
    }
    
    // 布局礼物区域背景
    CGFloat backgroundSizeH = _size_mid_320(32);
    [self layoutUIViewItem:info
                      rect:CGRectMake(usedX, [info currentY], fadeGiftButtonWidth, backgroundSizeH)
                       tag:TimelineFeedCellFadeGiftView
                     block:^(KSUIItem *layoutItem) {
        layoutItem.normalBackgroundColor = [UIColor ks_colorWithHexString:@"F2F4F6"];
        layoutItem.ksCornerRadius = backgroundSizeH * 0.5;
        layoutItem.uiItemClipsToBounds = YES;
        layoutItem.clipCorner = UIRectCornerAllCorners;
    }];
    usedX = usedX + KSMargin_Dynamic(14);
    
    CGFloat subViewStartX = 0; /// subViewStartX根据礼物栏来算
    subViewStartX = subViewStartX + KSMargin_Dynamic(14);
    
    // 布局礼物icon
    CGFloat giftIconY = (backgroundSizeH - giftIconWidth) * 0.5;
    [self layoutLocalImage:[UIImage imageNamed:@"feed_timeline_gift"]
                    toInfo:info
                      rect:CGRectMake(subViewStartX, giftIconY, giftIconWidth, giftIconWidth)
                       tag:TimelineFeedCellGiftIcon
                     block:^(KSUIItemLocalImage *layoutItem) {
        layoutItem.parentViewTag = TimelineFeedCellFadeGiftView;
        layoutItem.canvasViewTag = TimelineFeedCellFadeGiftView;
    }];
    subViewStartX = subViewStartX + giftIconWidth + KSMargin_Dynamic(4);
    usedX = usedX + giftIconWidth + KSMargin_Dynamic(4);
    
    // 布局礼物文字
    CGFloat giftLabelY = (backgroundSizeH - giftFont.lineHeight) * 0.5;
    [self layoutOneLineLabel:totalGiftStr
                      toInfo:info
                           x:subViewStartX
                           y:giftLabelY
                           w:totalGiftStrSize.width
                           f:giftFont
                           c:UIColor.ks_primaryTextColor
                         tag:TimelineFeedCellGiftTextLabel
                       block:^(KSUIItemLabel *layoutItem) {
        layoutItem.normalBackgroundColor = [UIColor clearColor];
        layoutItem.parentViewTag = TimelineFeedCellFadeGiftView;
        layoutItem.canvasViewTag = TimelineFeedCellFadeGiftView;
    }];
    
    // 布局礼物图片及文字点击事件按钮
    KSDrawItemAction *action = [KSDrawItemAction new];
    action.type = DRAWITEM_ACTION_TYPE_SEND_PRESENT; // 弹出送礼界面点击事件
    action.busiData = simpleFeed;
    CGFloat clickGiftBtnWidth = KSMargin_Dynamic(14) + giftIconWidth + KSMargin_Dynamic(4) + totalGiftStrSize.width;
    clickGiftBtnWidth = clickGiftBtnWidth + (hasFucBtn ? KSMargin_Dynamic(9) : KSMargin_Dynamic(14));
    [self layoutButton:action
                toInfo:info
                  rect:CGRectMake(KSMargin_Dynamic_70, [info currentY], clickGiftBtnWidth, backgroundSizeH)
                   tag:TimelineFeedCellGiftAreaActionButton
                 block:^(KSUIItemButton *layoutItem) {
        layoutItem.normalBackgroundColor = [UIColor clearColor];
    }];
    
    subViewStartX = subViewStartX + totalGiftStrSize.width + (hasFucBtn ? KSMargin_Dynamic(9) : KSMargin_Dynamic(14));
    usedX = usedX + totalGiftStrSize.width + (hasFucBtn ? KSMargin_Dynamic(9) : KSMargin_Dynamic(14));
    
    // 布局礼物右侧异化按钮 "合唱" "开通vip"等
    if (hasFucBtn) {
        // 分割线
        CGFloat lineSizeH = _size_mid_320(14);
        CGFloat lineY = (backgroundSizeH - lineSizeH) * 0.5;
        [self layoutUIViewItem:info
                          rect:CGRectMake(subViewStartX, lineY, 1, lineSizeH)
                           tag:TimelineFeedCellGiftButtonLineView
                         block:^(KSUIItem *layoutItem) {
            layoutItem.normalBackgroundColor = [UIColor ks_colorWithRGBHex:0x000000 alpha:0.12];
            layoutItem.parentViewTag = TimelineFeedCellFadeGiftView;
            layoutItem.canvasViewTag = TimelineFeedCellFadeGiftView;
        }];
        
        // 布局异化按钮点击事件按钮
        KSDrawItemAction *action = [KSDrawItemAction new];
        action.type = [self calculateFucBtnActionType:buttonType];
        action.busiData = simpleFeed;
        CGRect funcBtnRect = CGRectMake(usedX, [info currentY], KSMargin_Dynamic(9) + fucBtnStringSize.width + KSMargin_Dynamic(14), backgroundSizeH);
        [self layoutButton:action
                    toInfo:info
                      rect:funcBtnRect
                       tag:TimelineFeedCellFucTypeActionButton
                     block:^(KSUIItemButton *layoutItem) {
            layoutItem.normalBackgroundColor = [UIColor clearColor];
        }];
        subViewStartX = subViewStartX + 1 + KSMargin_Dynamic(9);
        
        // 异化按钮文案
        CGFloat fucLabelY = (backgroundSizeH - giftFont.lineHeight) * 0.5;
        [self layoutOneLineLabel:fucBtnString
                          toInfo:info
                               x:subViewStartX
                               y:fucLabelY
                               w:fucBtnStringSize.width
                               f:giftFont
                               c:[UIColor ks_colorWithRGBHex:0x323333]
                             tag:TimelineFeedCellGiftFucTextLabel
                           block:^(KSUIItemLabel *layoutItem) {
            layoutItem.normalBackgroundColor = [UIColor clearColor];
            layoutItem.parentViewTag = TimelineFeedCellFadeGiftView;
            layoutItem.canvasViewTag = TimelineFeedCellFadeGiftView;
        }];
        
        subViewStartX = subViewStartX + fucBtnStringSize.width + KSMargin_Dynamic(14);
        
        if (buttonType == KSBottomFucBtnType_hc) {
            // 如果是合唱按钮，需要判断是否要展示vip or 限免标志
            KSButtonVipBadgeType badgeType = [UIButton buttonVipBadgeTypeFromUgcMaskExt:simpleFeed.songinfo.ugcMaskExt];
            CGFloat iconWidth = _size_mid_320(15);
            if (badgeType == KSButtonVipBadgeTypeVip) {
                // vip
                [self layoutLocalImage:[UIImage imageNamed:@"feed_timeline_corner_vip"]
                                                    toInfo:info
                                                      rect:CGRectMake(subViewStartX - iconWidth, 0, iconWidth, iconWidth)
                                   tag:TimelineFeedCellHcVipIcon block:^(KSUIItemLocalImage *layoutItem) {
                    layoutItem.parentViewTag = TimelineFeedCellFadeGiftView;
                    layoutItem.canvasViewTag = TimelineFeedCellFadeGiftView;
                }];
            } else if (badgeType == KSButtonVipBadgeTypeTimeLimitedFree) {
                // 限免
                [self layoutLocalImage:[UIImage imageNamed:@"feed_timeline_xianmian"]
                                                    toInfo:info
                                                      rect:CGRectMake(subViewStartX - iconWidth, 0, iconWidth, iconWidth)
                                   tag:TimelineFeedCellHcVipIcon block:^(KSUIItemLocalImage *layoutItem) {
                    layoutItem.parentViewTag = TimelineFeedCellFadeGiftView;
                    layoutItem.canvasViewTag = TimelineFeedCellFadeGiftView;
                }];
            }
        }
    }
}

// MARK: 布局Feed社交功能按钮
+ (void)layoutSocialFuncBtn:(KSimpleFeed *)simpleFeed
                       info:(KSLayoutInfo *)info
             showGiftButton:(BOOL)showGiftButton
             showLikeButton:(BOOL)showLikeButton
          showCommentButton:(BOOL)showCommentButton {
    if (simpleFeed.songinfo.songMid.length > 0 && simpleFeed.simpleFeedCommon.strFeedId.length <= 0) {
        // 做个保护,假feed发布失败的时候songinfo存在，ugcId没有，导致送花,分享等互动操作失败
        return;
    }
    
    CGFloat giftBtnSizeH = _size_mid_320(32);
    BOOL needMinSpacing = (giftBtnSizeH > 32) && (SCREEN_WIDTH <= 375); /// 大字模式，且屏幕小于375
    CGFloat iconPaddingBetweenEach = needMinSpacing ? KSMargin_Dynamic(20) : KSMargin_Dynamic(24);
    CGFloat iconWidth = KSMargin_Dynamic(24);
    CGFloat iconOriginY = [info currentY] + (giftBtnSizeH - iconWidth) * 0.5; /// 跟礼物按钮居中
    CGFloat reverseUsedX = SCREEN_WIDTH - KSMargin_Dynamic_20 - iconWidth;

    UIFont *numberFont = [self getAvantFontWithSize:10 bold:NO];
    CGFloat numberLabelY = iconOriginY + iconWidth - numberFont.lineHeight;

    // 布局转发/分享图片
    [self layoutLocalImage:[UIImage imageNamed:@"feed_timeline_transfer_new"]
                    toInfo:info
                      rect:CGRectMake(reverseUsedX, iconOriginY, iconWidth, iconWidth)
                       tag:TimelineFeedCellForwardFeedButton
                     block:nil];
    // 布局转发/分享图片背后的按钮，因为要扩大热区，所以这里单独做按钮比较方便
    KSDrawItemAction *action = [KSDrawItemAction new];
    action.busiData = simpleFeed;
    action.type = DRAWITEM_ACTION_TYPE_SHARE_FEED;
    [self layoutButton:action
                toInfo:info
                  rect:CGRectMake(reverseUsedX, [info currentY], SCREEN_WIDTH - reverseUsedX, iconWidth + 3)
                   tag:TimelineFeedCellTransferBgButton
                 block:^(KSUIItemButton *layoutItem) {
        layoutItem.normalBackgroundColor = [UIColor clearColor];
        layoutItem.highlightColor = [UIColor clearColor];
    }];
    reverseUsedX = reverseUsedX - iconPaddingBetweenEach - iconWidth;
    
    if (showCommentButton) {
        // 布局评论
        UIImage *commentImage = nil;
        KSDrawItemAction *commentAction = [KSDrawItemAction new];
        commentAction.busiData = simpleFeed;
        
        if (simpleFeed.comment.number > 0) {
            // 有评论时
            commentImage = [UIImage imageNamed:@"feed_timeline_comment_number"];
            commentAction.type = DRAWITEM_ACTION_TYPE_BUTTON_SHOW_COMMENTSHOWVC;
        } else {
            // 无评论时
            commentImage = [UIImage imageNamed:@"feed_timeline_comment_no_number"];
            // 2022feed改版改成点击就是弹出评论面板，没有快捷评论的逻辑了
            commentAction.type = DRAWITEM_ACTION_TYPE_BUTTON_SHOW_COMMENTSHOWVC; // DRAWITEM_ACTION_TYPE_BUTTON_REPLY
        }
        [self layoutLocalImage:commentImage
                        toInfo:info
                          rect:CGRectMake(reverseUsedX, iconOriginY, iconWidth, iconWidth)
                           tag:TimelineFeedCellCommentButton
                         block:^(KSUIItemLocalImage *layoutItem) {
            layoutItem.action = commentAction;
        }];
        
        // 评论数
        NSString *commentNumString = @"";
        if (simpleFeed.comment.number > 0) {
            commentNumString = [KSFormatHelper stringFormatNumber:simpleFeed.comment.number];
        }
        CGSize commentNumStringSize = [commentNumString kSongSizeWithFont:[self getAvantFontWithSize:10 bold:NO]];
        [self layoutOneLineLabel:commentNumString
                          toInfo:info
                               x:reverseUsedX + 17
                               y:numberLabelY
                               w:commentNumStringSize.width
                               f:numberFont
                               c:[UIColor ks_colorWithRGBHex:0x888888]
                             tag:TimelineFeedCellCommentCount
                           block:^(KSUIItemLabel *layoutItem) {
            layoutItem.normalBackgroundColor = [UIColor clearColor];
            layoutItem.highlightColor = [UIColor clearColor];
        }];
        
        reverseUsedX = reverseUsedX - iconPaddingBetweenEach - iconWidth;
    }
    
    if (showLikeButton) {
        // 布局点赞
        UIImage *likeImage = nil;
        UIColor *likeNumberColor = [UIColor ks_colorWithRGBHex:0x888888];
        if (simpleFeed.likeInfo.jce_num > 0) {
            likeImage = [UIImage imageNamed:@"feed_timeline_like_number"];
        } else {
            likeImage = [UIImage imageNamed:@"feed_timeline_like_no_number"];
        }
        if (simpleFeed.likeInfo && simpleFeed.likeInfo.jce_status == 0) { // 已点赞
            likeImage = [UIImage imageNamed:@"feed_timeline_like_number_red"];
            likeNumberColor = [UIColor ks_colorWithRGBHex:0xff4d4b];
        }
        [self layoutLocalImage:likeImage
                        toInfo:info
                          rect:CGRectMake(reverseUsedX, iconOriginY, iconWidth, iconWidth)
                           tag:TimelineFeedCellLikeButton
                         block:^(KSUIItemLocalImage *layoutItem) {
            KSDrawItemAction *action = [KSDrawItemAction new];
            action.busiData = simpleFeed;
            action.type = DRAWITEM_ACTION_TYPE_LIKE;
            layoutItem.action = action;
        }];
        
        // 点赞数
        NSString *likeNumString = @"";
        if (simpleFeed.likeInfo.jce_num > 0) {
            likeNumString = [KSFormatHelper stringFormatNumber:simpleFeed.likeInfo.jce_num];
        }
        CGSize likeNumStringSize = [likeNumString kSongSizeWithFont:[self getAvantFontWithSize:10 bold:NO]];
        
        [self layoutOneLineLabel:likeNumString
                          toInfo:info
                               x:reverseUsedX + 16
                               y:numberLabelY
                               w:likeNumStringSize.width
                               f:numberFont
                               c:likeNumberColor
                             tag:TimelineFeedCellLikeCount
                           block:^(KSUIItemLabel *layoutItem) {
            layoutItem.normalBackgroundColor = [UIColor clearColor];
            layoutItem.highlightColor = [UIColor clearColor];
        }];
        
        reverseUsedX = reverseUsedX - iconPaddingBetweenEach - iconWidth;
    }
    
    if (showGiftButton) {
        // 布局双送礼按钮中的基础送礼按钮
        KSDrawItemAction *action = [KSDrawItemAction new];
        action.busiData = simpleFeed;
        action.type = DRAWITEM_ACTION_TYPE_MAIL_GIFT;
        [self layoutLocalImage:[UIImage imageNamed:@"feed_timeline_gift_new"]
                        toInfo:info
                          rect:CGRectMake(reverseUsedX, iconOriginY, iconWidth, iconWidth)
                           tag:TimelineFeedCellDoubleGiftBaseGiftTag
                         block:^(KSUIItemLocalImage *layoutItem) {
            layoutItem.action = action;
        }];
    }
}

// 「转发」类feed布局
+ (KSLayoutInfo *)layoutWithForwardFeedInfo_2022:(KSimpleFeed *)simpleFeed {
    KSLayoutInfo *info = [[KSLayoutInfo alloc] init];
    if (simpleFeed.isRemoved) {
        // 如果转发内容被删除了，就直接布局头部信息 + 描述文案 + 删除提示
        [self layoutUserInfoAndDescriptionWithFeed:simpleFeed Info:info];
        
        // 删除提示
        NSString *msgInfo = [simpleFeed.removedMsg trimWhitespace];
        CGFloat maxWidth = SCREEN_WIDTH - KSMargin_Dynamic_70 - KSMargin_Dynamic_20 - 25 - 25;
        CGSize msgInfoSize = [msgInfo kSongSizeWithFont:_font_big([UIFont ks_fontWithFontType:KSFontType_Middle]) forWidth:maxWidth lineBreakMode:NSLineBreakByWordWrapping];
        // 布局灰色背景
        [self layoutUIViewItem:info rect:CGRectMake(KSMargin_Dynamic_70, [info currentY], SCREEN_WIDTH - KSMargin_Dynamic_70 - KSMargin_Dynamic_20, msgInfoSize.height + 15 + 15) tag:TimelineFeedCellRemovedFeedBackground block:^(KSUIItem *layoutItem) {
            layoutItem.normalBackgroundColor = [UIColor ks_colorWithRGBHex:0xF5F5F5];
            layoutItem.ksCornerRadius = kFeedImageRadiusCorner;
            layoutItem.uiItemClipsToBounds = YES;
            layoutItem.clipCorner = UIRectCornerAllCorners;
        }];
        // 文字
        [self layoutText:msgInfo toInfo:info x:KSMargin_Dynamic_70 + 25 y:[info currentY] + 15 w:maxWidth f:_font_big([UIFont ks_fontWithFontType:KSFontType_Middle]) c:[UIColor ks_colorWithRGBHex:0x888888]];
        [info increaseHeight:msgInfoSize.height + 15 + 15];
        [info increaseHeight:15];
    } else {
        // 嵌套的ugc信息
        [self layoutWithUgcTimelineFeed:simpleFeed info:info layoutRect:CGRectMake(0, 0, SCREEN_WIDTH, 0)];
    }
     
    simpleFeed.layoutInfo = info;
    return info;
}

// 布局转发feed的头部内容（头像、昵称、时间、转发xxx的作品）
+ (void)layoutForwardSourceFeedUserInfo_2022:(KSimpleFeed *)simpleFeed info:(KSLayoutInfo *)info {
    // 布局头像
    CGFloat headX = kAvatarX;
    CGFloat headY = [info currentY];
    KSCellUser *cellUser = [[KSCellUser alloc] init];
    cellUser.userId = simpleFeed.forwardFeed.feedUserInfo.userinfo.userId;
    cellUser.timeStamp = simpleFeed.forwardFeed.feedUserInfo.userinfo.avatarTimestamp;
    cellUser.nickName = simpleFeed.forwardFeed.feedUserInfo.userinfo.nickName;
    cellUser.strRoomId = simpleFeed.forwardFeed.feedUserInfo.userinfo.strRoomID;
    [self layoutPendantAvatar:cellUser
                      mapAuth:simpleFeed.forwardFeed.feedUserInfo.userinfo.sMapAuth
                       toInfo:info
                            x:headX
                            y:headY
                            w:kTimeLineUserHeadPendantWidth
                            h:kTimeLineUserHeadPendantHeight
                          tag:TimelineFeedCellPendantAvatar
          avatarActionBizData:nil
                         feed:simpleFeed];
    
    // 布局删除按钮
    CGRect deleteBtnSize = [self layoutDeleteBtn:simpleFeed info:info];
    
    //根据vip标志、计算nickName文本长度，再布局昵称
    CGFloat nickNameX = KSMargin_Dynamic_70;
    CGFloat nickNameMaxWidth = deleteBtnSize.origin.x - KSMargin_Dynamic_70;
    if (deleteBtnSize.origin.x >= SCREEN_WIDTH) {
        // 没有删除按钮时 padding 20
        nickNameMaxWidth -= 20;
    } else {
        nickNameMaxWidth -= 10;
    }
    UIColor *nickNameColor = kNickColor;
    UIFont *nickFont = kNickFontBold;
    
    // VIP标志
    if (GetFlexBOOL([simpleFeed.forwardFeed.feedUserInfo.userinfo.userVipInfo isKSVip])) {
        nickNameColor = KSBtnRedNormal;
    }
    // 昵称后icon排版
    KSIconConfig *iconConfig = [KSIconConfig new];
    [iconConfig customizeiconLimitCnt:3 emphasizeLimitCnt:1];
    [iconConfig setupWithUserInfo:simpleFeed.forwardFeed.feedUserInfo.userinfo bizScence:IconConfigBizScence_Regular];
    KSNickIconView *iconView = [[KSNickIconView alloc] init];
    [iconView updateIconConfig:iconConfig textConfig:nil maxWidth:0];
    nickNameMaxWidth -= iconView.width;
    
    NSString *strFriendlinessIconUrl = simpleFeed.forwardFeed ?  simpleFeed.forwardFeed.feedUserInfo.userinfo.strFriendlinessIconUrl : simpleFeed.simpleUser.userinfo.strFriendlinessIconUrl;
    if (!IS_EMPTY_STR_BM(strFriendlinessIconUrl)) {
        simpleFeed.layoutConfig.isShowIntimateIcon = [iconView hasShowIntimateIcon];
    }
    
    // 最后布局昵称
    NSString *nickName = [NSString stringWithFormat:@"%@", simpleFeed.forwardFeed.feedUserInfo.userinfo.nickName];
    CGSize nickLayoutSize = [self layoutNickName:nickName
                                    busiData:cellUser
                                      toInfo:info
                                           x:nickNameX
                                           y:headY
                                           w:nickNameMaxWidth
                                           f:nickFont
                                           c:nickNameColor];
    // 昵称后icon布局
    CGFloat startX = nickNameX + nickLayoutSize.width + KSMargin_5;
    CGFloat iconStartY = headY + nickLayoutSize.height / 2.0f - iconView.height / 2.0f - 2;
    [self layoutVipIconView:iconConfig
                     toInfo:info
                       rect:CGRectMake(startX, iconStartY, iconView.width, iconView.height)
                        tag:TimelineFeedCellForwardFeedVipICon
                      block:nil];
    
    // 排行榜曝光
    NSString *rankListName = [simpleFeed.forwardFeed.feedUserInfo.userinfo.sMapAuth ks_stringValueForKey:@(JceProfile_AuthKey_AUTH_RANK_NAME)];
    NSString *rankListNum = [simpleFeed.forwardFeed.feedUserInfo.userinfo.sMapAuth ks_stringValueForKey:@(JceProfile_AuthKey_AUTH_RANK_NTH)];
    if (!IS_EMPTY_STR_BM(rankListName)) {
        [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
            reportModel.key = @"ranking_function#ranking_display#null#exposure#0";
            reportModel.commonInt1 = 2;
            reportModel.commonStr1 = rankListName;
            reportModel.commonStr2 = !IS_EMPTY_STR_BM(rankListNum) ? rankListNum : @"-1";
        }];
    }
    
    // 增加昵称高度
    [info increaseHeight:kNickFontBold.lineHeight + 4];
    
    CGFloat usedX = KSMargin_Dynamic_70;
    
    // 布局时间
    NSDate *timeDate = [NSDate dateWithTimeIntervalSince1970:simpleFeed.simpleFeedCommon.uFeedTime];
    NSString *strTime = [timeDate stringForPlayingHistoryFromDate];
    __block CGRect timeRealRect = CGRectZero;
    CGFloat timeMaxWidth = SCREEN_WIDTH - usedX;
    CGFloat timeWidth = min([strTime kSongSizeWithFont:kTimeSTFont].width, timeMaxWidth);
    [self layoutText:strTime toInfo:info maxLine:1 x:usedX y:[info currentY] w:timeWidth f:kTimeSTFont c:UIColor.ks_secondaryTextColor layoutBlock:^(KSLayoutRichText *layoutTextItems) {
        timeRealRect = layoutTextItems.rect;
    } prelayoutBlock:nil];
    
    usedX = usedX + timeRealRect.size.width + 8;
    
    // 布局”转发xxx的yy作品“
    NSString *typeString = @"的作品";
    if (simpleFeed.liveShow) {
        typeString = @"的直播";
    } else if (simpleFeed.ktvRoomShow || simpleFeed.ktvRoomMike) {
        typeString = @"的歌房";
    } else if (simpleFeed.h5JumpFeed ||
               simpleFeed.soloAlbumInfo || // 歌单
               simpleFeed.simpleFeedCommon.uTypeid == FEED_TYPE_USER_ALBUM || // 歌单
               simpleFeed.payAlbumInfo || // 专辑
               simpleFeed.competitionFeed || // 大赛
               simpleFeed.simpleFeedCommon.uTypeid == FEED_TYPE_COMPETETION) { // 大赛
        typeString = @"的分享";
    }
    __block NSString *userNickName = simpleFeed.simpleUser.userinfo.nickName;
    [[KSStringCrashManager sharedManager] protectCrashString:userNickName at:__FUNCTION__ when:^(NSString *finalString) {
        userNickName = finalString;
    }];
    
    // 如果是自己的，需要展示"自己的"
    if (simpleFeed.simpleUser.userinfo.userId == simpleFeed.forwardFeed.feedUserInfo.userinfo.userId &&
        simpleFeed.forwardFeed &&
        !simpleFeed.h5JumpFeed) {
        userNickName = @"自己";
    }
    
    NSString *sourceText = [NSString stringWithFormat:@"转发%@", userNickName];
    CGSize typeStringSize = [typeString kSongSizeWithFont:kTimeSTFont];
    CGFloat sourceTextMaxWidth = SCREEN_WIDTH - usedX - typeStringSize.width - 20;
    CGSize sourceTextSize = [self layoutNickName:sourceText busiData:nil toInfo:info x:usedX y:[info currentY] w:sourceTextMaxWidth f:kTimeSTFont c:UIColor.ks_secondaryTextColor action:nil];
    usedX = usedX + sourceTextSize.width;
    __block CGRect typeStringRect = CGRectZero;
    [self layoutText:typeString toInfo:info maxLine:1 x:usedX y:[info currentY] w:typeStringSize.width f:kTimeSTFont c:UIColor.ks_secondaryTextColor layoutBlock:^(KSLayoutRichText *layoutTextItems) {
        typeStringRect = layoutTextItems.rect;
    } prelayoutBlock:nil];
    
    usedX = usedX + typeStringRect.size.width + 8;
    
    if (!IS_EMPTY_STR_BM(simpleFeed.ktvRoomWebgameStatusInfo.jce_stComm.jce_strSegment)) {
        CGSize strSegmentSize = [simpleFeed.ktvRoomWebgameStatusInfo.jce_stComm.jce_strSegment kSongSizeWithFont:kTimeSTFont];
        [self layoutText:simpleFeed.ktvRoomWebgameStatusInfo.jce_stComm.jce_strSegment toInfo:info maxLine:1 x:usedX y:[info currentY] w:strSegmentSize.width f:kTimeSTFont c:UIColor.ks_secondaryTextColor layoutBlock:nil prelayoutBlock:nil];
    }
    
    [info increaseHeight:kTimeSTFont.lineHeight];
    [info increaseHeight:KSMargin_Dynamic(12)];
}

// 布局正常feed的描述文案
+ (CGFloat)layoutNormalFeedDescriptionInfo_2022:(KSimpleFeed *)simpleFeed info:(KSLayoutInfo *)info {
    if (simpleFeed.layoutConfig.isHiddenDescription) {
        return 0.0;
    }
    CGFloat txtWidth = SCREEN_WIDTH - KSMargin_Dynamic_70 - KSMargin_Dynamic_20 + KSMargin_Dynamic_2;
    CGFloat descX = KSMargin_Dynamic_70;
    // 9.0 版本弱化小尾巴，把小尾巴整合进描述末尾
    NSString *description = [simpleFeed getSimpleFeedDescWithTopicFormat];
    NSString *tailStr = [KSFollowFeedViewModel buildUpRichTailWithFeed:simpleFeed];
    if (!IS_EMPTY_STR_BM(description)) {
        tailStr = [NSString stringWithFormat:@" %@", tailStr];
    }
    description = [description stringByAppendingString:tailStr];
    // 限制最多显示3行
    CGFloat height = 0.0;
    NSInteger maxLine = simpleFeed.isExpandAllText == NO ? 3 : INT_MAX;
    
    if (description && [description length] > 0) {
        CGFloat descW = txtWidth;
        height = [self layoutText:description toInfo:info maxLine:maxLine x:descX y:[info currentY] w:descW f:kDescTxtFont c:UIColor.ks_primaryTextColor layoutBlock:^(KSLayoutRichText *layoutTextItems) {
            if (simpleFeed.feedAd.jce_advertId.length > 0) {
                KSDrawItemAction *action = [KSDrawItemAction actionWithType:DRAWITEM_ACTION_TYPE_ADVERTISE_DESC_CLICK];
                action.busiData = simpleFeed;
                for (KSUIItem *item in layoutTextItems.layoutList) {
                    item.action = action;
                }
            }
        } prelayoutBlock:^(KSLayoutRichText *layoutTextItems) {
            layoutTextItems.emotionHeight = _size_big(18);
            layoutTextItems.lastlineExpandStr = (simpleFeed.isExpandAllText == NO) ? KString(@"展开") : KString(@"收起");
            DRAWITEM_ACTION_TYPE actionType = (simpleFeed.isExpandAllText == NO) ? DRAWITEM_ACTION_TYPE_FEED_EXPAND_ALL_TEXT : DRAWITEM_ACTION_TYPE_FEED_COLLAPSE_ALL_TEXT;
            KSDrawItemAction *action = [KSDrawItemAction actionWithType:(int)actionType];
            action.busiData = simpleFeed;
            layoutTextItems.lastlineExpandAction = action;
        }];

        KSUIItem *descLastItem = [info.items lastObject];
        simpleFeed.descLastItem = descLastItem;
        [info increaseHeight:height + DYNAMIC_VALUE_ForAllScreen2(12)];
    }
    return height;
}

// 布局转发feed的描述文案
+ (CGFloat)layoutForwardFeedDescriptionInfo_2022:(KSimpleFeed *)simpleFeed info:(KSLayoutInfo *)info {
    NSInteger maxLine = simpleFeed.isExpandForwardText == NO ? 3 : INT_MAX;
    NSString *description = [simpleFeed.forwardFeed.strForward trimWhitespace];
    CGFloat descHeight = 0.0;
    if (description && [description length] > 0) {
        descHeight = [self layoutText:description
                               toInfo:info
                              maxLine:maxLine
                                    x:KSMargin_Dynamic_70
                                    y:[info currentY]
                                    w:SCREEN_WIDTH - KSMargin_Dynamic_70 - KSMargin_Dynamic_20
                                    f:kDescTxtFont
                                    c:UIColor.ks_primaryTextColor
                          layoutBlock:^(KSLayoutRichText *layoutTextItems) {
            
        } prelayoutBlock:^(KSLayoutRichText *layoutTextItems) {
            layoutTextItems.emotionHeight = _size_big(18);
            layoutTextItems.lastlineExpandStr = (simpleFeed.isExpandForwardText == NO) ? KString(@"展开") : KString(@"收起");
            DRAWITEM_ACTION_TYPE actionType = (simpleFeed.isExpandForwardText == NO) ?  DRAWITEM_ACTION_TYPE_FEED_EXPAND_FORWARD_TEXT : DRAWITEM_ACTION_TYPE_FEED_COLLAPSE_FORWARD_TEXT;
            KSDrawItemAction *action = [KSDrawItemAction actionWithType:(int)actionType];
            action.busiData = simpleFeed;
            layoutTextItems.lastlineExpandAction = action;
        }];
        [info increaseHeight:descHeight];
    }
    [info increaseHeight:DYNAMIC_VALUE_ForAllScreen2(12)];
    return descHeight;
}

//「世界杯破窗广告」feed描述文案 & IPLogo
+ (CGFloat)layoutBrokenFrameAdvertiseBottomInfo:(KSimpleFeed *)simpleFeed info:(KSLayoutInfo *)info
{
    CGFloat imgWidth = KSMargin_Dynamic(90.0);
    CGFloat imgHeight = KSMargin_Dynamic(30.0);
    CGFloat imgX = SCREEN_WIDTH - KSMargin_Dynamic_20 - imgWidth + KSMargin_Dynamic_2;
    CGFloat hasImageHeight = KSMargin_Dynamic(8.0) + imgHeight;
    
    CGFloat txtWidth = SCREEN_WIDTH - KSMargin_Dynamic_70 - KSMargin_Dynamic_20 - KSMargin_Dynamic(8.0) - imgWidth + KSMargin_Dynamic_2;
    CGFloat descX = KSMargin_Dynamic_70;
    
    CGFloat height = 0.0;
    // 描述文案
    NSString *description = [simpleFeed getSimpleFeedDescWithTopicFormat];
    if (description && [description length] > 0) {
        CGFloat descW = txtWidth;
        CGFloat desHeight = [self layoutOneLineLabel:description
                                              toInfo:info
                                                   x:descX
                                                   y:[info currentY]
                                                   w:descW
                                                   f:kDescTxtFont
                                                   c:UIColor.ks_primaryTextColor
                                                 tag:TimelineFeedCellAdBrokenFrameDesLabelTag
                                               block:^(KSUIItemLabel *layoutItem) {
            if (simpleFeed.feedAd.jce_advertId.length > 0) {
                KSDrawItemAction *action = [KSDrawItemAction actionWithType:DRAWITEM_ACTION_TYPE_ADVERTISE_DESC_CLICK];
                action.busiData = simpleFeed;
                layoutItem.action = action;
            }
            layoutItem.normalBackgroundColor = [UIColor clearColor];
            layoutItem.highlightColor = [UIColor clearColor];
        }];
        height += desHeight;
        
        KSUIItem *descLastItem = [info.items lastObject];
        simpleFeed.descLastItem = descLastItem;
    }
    
    // 描述副文案
    NSString *subDes = simpleFeed.tmeFeedAd.subDesc;
    if ([subDes isKindOfClass:NSString.class] && [subDes length] > 0) {
        height += KSMargin_Dynamic(3);
        CGFloat subDescW = txtWidth;
        CGFloat subdesHeight = [self layoutOneLineLabel:subDes
                                                 toInfo:info
                                                      x:descX
                                                      y:[info currentY] + height
                                                      w:subDescW
                                                      f:kListenCoutnFont
                                                      c:UIColor.ks_secondaryTextColor
                                                    tag:TimelineFeedCellAdBrokenFrameSubDesLabelTag
                                                  block:^(KSUIItemLabel *layoutItem) {
            if (simpleFeed.feedAd.jce_advertId.length > 0) {
                KSDrawItemAction *action = [KSDrawItemAction actionWithType:DRAWITEM_ACTION_TYPE_ADVERTISE_DESC_CLICK];
                action.busiData = simpleFeed;
                layoutItem.action = action;
            }
            layoutItem.normalBackgroundColor = [UIColor clearColor];
            layoutItem.highlightColor = [UIColor clearColor];
        }];
        height += subdesHeight;
    }
    
    // IPLogo
    NSString *ipLogoUrl = simpleFeed.tmeFeedAd.ipIconUrl;
    if ([ipLogoUrl isKindOfClass:NSString.class] && ipLogoUrl.length > 0) {
        [self layoutOriginalImage:ipLogoUrl
                      placeHolder:nil
                           action:nil
                           toInfo:info
                             rect:CGRectMake(imgX, [info currentY] + KSMargin_Dynamic(2.0), imgWidth, imgHeight)
                              tag:TimelineFeedCellAdBrokenFrameIPLogo
                            block:^(KSUIOriginalImageItem *layoutItem) {
            layoutItem.disbleMaskLayer = YES;
            layoutItem.contentMode = UIViewContentModeScaleAspectFit;
            layoutItem.normalBackgroundColor = [UIColor clearColor];
            layoutItem.highlightColor = [UIColor clearColor];
        }];
        height = MAX(hasImageHeight, height);
    }
    [info increaseHeight:height + KSMargin_Dynamic(13)]; // 文案高度
    return height;
}

// 布局「歌单」/「专辑」feed
+ (void)layoutSoloAlbumFeedInfo_2022:(KSimpleFeed *)simpleFeed info:(KSLayoutInfo *)info {
    BOOL isPayAlbum = simpleFeed.payAlbumInfo ? YES : NO; // 是否为专辑
    
    NSInteger startX = KSMargin_Dynamic_70;
    NSInteger startY = [info currentY];
    
    CGFloat customViewWidth  = SCREEN_WIDTH - startX - KSMargin_Dynamic_20;
    CGFloat customViewHeight = ceil(customViewWidth / 3.0); // 长宽比3:1
    
    // 背景渐变效果
    CGRect bgRect = CGRectMake(startX, [info currentY], customViewWidth, customViewHeight);
    UIColor *magicColor = [self calculate2022FeedMagicColor:simpleFeed];
    UIColor *endColor = [UIColor ks_colorWithRGBHex:0xF2F4F6];
    NSArray *colors = @[(__bridge id)magicColor.CGColor,
                        (__bridge id)magicColor.CGColor,
                        (__bridge id)endColor.CGColor,
                        (__bridge id)endColor.CGColor];
    [self layoutGradientLayerViewWithColors:colors
                                  locations:@[@(0.0),@(0.15),@(0.57),@1] // 这个数据调了一万年，心累
                                 startPoint:CGPointMake(0, 0)
                                   endPoint:CGPointMake(1, 0.2)
                                       info:info
                                       rect:bgRect
                                        tag:TimelineFeedCellMagicColorView
                                      block:^(KSGradientLayerViewItem *layoutItem) {
        layoutItem.ksCornerRadius = kFeedImageRadiusCorner;
        layoutItem.uiItemClipsToBounds = YES;
        layoutItem.clipCorner = UIRectCornerAllCorners;
    }];
    
    // 封面图右边的小半圆(先画小半圆再画封面，让封面压住小半圆，防止算错半圆圆心角角度而导致小半圆盖住封面)
    CGFloat circleHeight = customViewHeight * 0.714;
    CGFloat radius = kFeedImageRadiusCorner;
    UIBezierPath *path = [[UIBezierPath alloc] init];
    CGPoint topArcCenter = CGPointMake(customViewHeight, (customViewHeight - circleHeight) / 2 + 8);
    [path addArcWithCenter:topArcCenter radius:radius startAngle: M_PI * 0 endAngle:-0.5 * M_PI clockwise:NO];
    CGPoint bottomArcCenter = CGPointMake(customViewHeight, customViewHeight - (customViewHeight - circleHeight) / 2 - 8);
    [path addArcWithCenter:bottomArcCenter radius:radius startAngle: M_PI * 0.5 endAngle:0 * M_PI clockwise:NO];
    [path closePath];
    [self layoutBezierPathViewWithPath:path
                                  info:info
                                  rect:CGRectMake(startX, [info currentY], customViewHeight, customViewHeight) // 与封面保持一致的rect
                                   tag:TimelineFeedCellHalfCircle
                                 block:^(KSBezierPathViewItem *layoutItem) {
        layoutItem.color = magicColor;
    }];
    
    // 专辑封面
    NSString *pictureUrl = nil;
    NSString *localFilePath = nil;
    KSImage *coverImage = nil;
    if (isPayAlbum) {
        coverImage = (KSImage *)[[simpleFeed.payAlbumInfo.coverurls allValues] firstObject];
    } else {
        coverImage = (KSImage *)[[simpleFeed.soloAlbumInfo.coverurls allValues] firstObject];
    }
    if (coverImage.imageUrl) {
        pictureUrl = coverImage.imageUrl;
    } else if (coverImage.localFilePath) {
        localFilePath = coverImage.localFilePath;
    }
    
    if (localFilePath && [QMFileHelper fileIsExistWithPath:localFilePath]) {
        NSData *imageData = [NSData dataWithContentsOfFile:localFilePath];
        [self layoutLocalImage:[UIImage imageWithData:imageData]
                        toInfo:info
                          rect:CGRectMake(startX, [info currentY], customViewHeight, customViewHeight)
                           tag:TimelineFeedCellSoloCover
                         block:^(KSUIItemLocalImage *layoutItem) {
            layoutItem.ksCornerRadius = kFeedImageRadiusCorner;
            layoutItem.uiItemClipsToBounds = YES;
            layoutItem.clipCorner = UIRectCornerAllCorners;
        }];
    } else {
        [self layoutOriginalImage:pictureUrl
                      placeHolder:[KSLayoutUIManager cahceImageWithName:@"soloAlbumDefault"]
                           action:nil
                           toInfo:info
                             rect:CGRectMake(startX, [info currentY], customViewHeight, customViewHeight)
                              tag:TimelineFeedCellSoloCover
                            block:^(KSUIOriginalImageItem *layoutItem) {
            layoutItem.ksCornerRadius = kFeedImageRadiusCorner;
            layoutItem.uiItemClipsToBounds = YES;
            layoutItem.clipCorner = UIRectCornerAllCorners;
        }];
    }

    // 专辑名
    CGFloat nameStartX = startX + customViewHeight + 22;
    CGFloat nameStartY = startY + (customViewHeight - kSongInfoAreaHeight) / 2.0; // 名+标签，做整体竖直居中
    NSString *nameString = isPayAlbum ? simpleFeed.payAlbumInfo.strAlbumName : simpleFeed.soloAlbumInfo.strAlbumName;
    [self layoutOneLineText:nameString
                     toInfo:info
                          x:nameStartX
                          y:nameStartY
                          w:customViewWidth - customViewHeight - 18
                          f:[UIFont ks_fontWithFontType:KSFontType_LargeBold]
                          c:[UIColor ks_colorWithRGBHex:0x323333]];
    
    // 专辑信息
    CGFloat usedX = nameStartX + 2;
    CGFloat listenNumIconImageY = startY + customViewHeight - ((customViewHeight - kSongInfoAreaHeight) / 2.0) - 13 + 1;
    
    NSString *singNumString = @"";
    BOOL showListenNum = NO;
    if (isPayAlbum) {
        if (!simpleFeed.payAlbumInfo.lPlayMask && simpleFeed.payAlbumInfo.sellNum <= 0) {
            // 如果专辑不在售卖，且销量为0，则展示传唱度
            singNumString = [NSString stringWithFormat:KString(@"%@"), [KSFormatHelper formatNewNumber:simpleFeed.payAlbumInfo.iSungDegree]];
            showListenNum = YES;
        } else {
            showListenNum = NO;
        }
    } else {
        // 歌单
        singNumString = [NSString stringWithFormat:KString(@"%@"), [KSFormatHelper formatNewNumber:simpleFeed.listenerInfo.number]];
        showListenNum = YES;
    }
    if (showListenNum) {
        CGSize singNumStringSize = [singNumString kSongSizeWithFont:[UIFont ks_fontWithFontType:KSFontType_SmallBold]];
        [self layoutLocalImage:[UIImage imageNamed:@"feed_timeline_listen_number_black"]
                        toInfo:info
                          rect:CGRectMake(usedX, listenNumIconImageY, 13, 13)
                           tag:TimelineFeedCellPayAlbumSingCountIcon
                         block:^(KSUIItemLocalImage *layoutItem) {
            layoutItem.contentMode = UIViewContentModeScaleAspectFill;
        }];
        usedX = usedX + 13 + 3; // 13为width 3为padding

        [self layoutOneLineText:singNumString
                         toInfo:info
                              x:usedX
                              y:listenNumIconImageY
                              w:SCREEN_WIDTH - usedX - KSMargin_Dynamic_20
                              f:[UIFont ks_fontWithFontType:KSFontType_SmallBold]
                              c:[UIColor ks_colorWithRGBHex:0x323333]];
        usedX = usedX + singNumStringSize.width + 10;
    }

    // 歌曲数目
    [self layoutLocalImage:[UIImage imageNamed:@"feed_timeline_album_song_count_black"]
                    toInfo:info
                      rect:CGRectMake(usedX, listenNumIconImageY, 13, 13)
                       tag:TimelineFeedCellAlbumSongCountIcon
                     block:^(KSUIItemLocalImage *layoutItem) {
        layoutItem.contentMode = UIViewContentModeScaleAspectFill;
    }];
    usedX = usedX + 13 + 3; // 13为width 3为padding
    
    NSInteger songCount = isPayAlbum ? simpleFeed.payAlbumInfo.iUgcNum : simpleFeed.soloAlbumInfo.iUgcNum;
    NSString *songCountStr = [NSString stringWithFormat:@"共%ld首", songCount];
    [self layoutOneLineText:songCountStr
                     toInfo:info
                          x:usedX
                          y:listenNumIconImageY - 1 // 视觉还原
                          w:SCREEN_WIDTH - usedX - KSMargin_Dynamic_20
                          f:[UIFont ks_fontWithFontType:KSFontType_SmallBold]
                          c:[UIColor ks_colorWithRGBHex:0x323333]];

    // 歌单标签
    [self calculateCoverLeftTopImage:simpleFeed layoutIfNeed:info rect:CGRectMake(startX + 5, startY + 5, 0, 0)];
    [info increaseHeight:customViewHeight];
}

// 「音乐心情」「图文」
+ (void)layoutFeedPhotoList_2022:(KSimpleFeed *)simpleFeed info:(KSLayoutInfo *)info {
    CGFloat witdth = SCREEN_WIDTH - KSMargin_Dynamic_70 - KSMargin_Dynamic_20; // 图片的长和宽
    KSUIItemCustomAddView *item = [[KSUIItemCustomAddView alloc] init];
    item.rect = CGRectMake(KSMargin_Dynamic_70, [info currentY], witdth, witdth);
    item.viewType = DRAWITEM_CUSTOM_ADD_PhotoList_2022;
    item.tag = TimelineFeedCellUgcPhotoListCycleScrollView;
    [info addItem:item];
    [info increaseHeight:witdth];
}

// Ai图文
+ (void)layoutFeedAiImageText:(KSimpleFeed *)simpleFeed info:(KSLayoutInfo *)info
{
    KSimpleFeedAiImageTextInfo *AiImageText = simpleFeed.AiImageText;
    NSInteger count = AiImageText.imageArr.count;
    if (count == 0) {
        return;
    }
    
    CGFloat sizeW = SCREEN_WIDTH - KSMargin_Dynamic_70 - KSMargin_Dynamic_20; /// 图片区域宽度
    CGFloat sizeH = sizeW; /// 图片区域高度
    CGFloat spacing = 8;
    if (count == 2) {
        /// 两张图
        sizeH = (sizeW - spacing) / 2;
    }
    else if (count >= 3) {
        /// 3张图
        sizeH = (sizeW - 2 * spacing) / 3;
    }
    
    KSUIItemCustomAddView *item = [[KSUIItemCustomAddView alloc] init];
    item.rect = CGRectMake(KSMargin_Dynamic_70, [info currentY], sizeW, sizeH);
    item.viewType = DRAWITEM_CUSTOM_ADD_Feed_Ai_Image_Grid;
    item.tag = TimelineFeedCellAiImageGridTag;
    [info addItem:item];
    [info increaseHeight:sizeH];
}

// 「课程」
+ (void)layoutCourseFeedInfo_2022:(KSimpleFeed *)simpleFeed info:(KSLayoutInfo *)info {
    NSInteger startX = KSMargin_Dynamic_70;
    NSInteger startY = [info currentY];
    
    CGFloat customViewWidth  = SCREEN_WIDTH - startX - KSMargin_Dynamic_20;
    CGFloat customViewHeight = ceil(customViewWidth / 3.0); // 长宽比3:1
    
    // 背景渐变效果
    CGRect bgRect = CGRectMake(startX, [info currentY], customViewWidth, customViewHeight);
    UIColor *magicColor = [self calculate2022FeedMagicColor:simpleFeed];
    UIColor *endColor = [UIColor ks_colorWithRGBHex:0xF2F4F6];
    NSArray *colors = @[(__bridge id)magicColor.CGColor,
                        (__bridge id)magicColor.CGColor,
                        (__bridge id)endColor.CGColor,
                        (__bridge id)endColor.CGColor];
    [self layoutGradientLayerViewWithColors:colors
                                  locations:@[@(0.0),@(0.15),@(0.57),@1] // 这个数据调了一万年，心累
                                 startPoint:CGPointMake(0, 0)
                                   endPoint:CGPointMake(1, 0.2)
                                       info:info
                                       rect:bgRect
                                        tag:TimelineFeedCellMagicColorView
                                      block:^(KSGradientLayerViewItem *layoutItem) {
        layoutItem.ksCornerRadius = kFeedImageRadiusCorner;
        layoutItem.uiItemClipsToBounds = YES;
        layoutItem.clipCorner = UIRectCornerAllCorners;
    }];
    
    // 封面图右边的小半圆(先画小半圆再画封面，让封面压住小半圆，防止算错半圆圆心角角度而导致小半圆盖住封面)
    CGFloat radius = customViewHeight * 1.086 / 2;
    CGFloat angle = 0.18;
    UIBezierPath *path = [[UIBezierPath alloc] init];
    [path addArcWithCenter:CGPointMake(radius, customViewHeight / 2) radius:radius startAngle: M_PI * angle endAngle:angle * M_PI * -1 clockwise:NO];
    [path closePath];
    [self layoutBezierPathViewWithPath:path
                                  info:info
                                  rect:CGRectMake(startX, [info currentY], customViewHeight, customViewHeight) // 与封面保持一致的rect
                                   tag:TimelineFeedCellHalfCircle
                                 block:^(KSBezierPathViewItem *layoutItem) {
        layoutItem.color = magicColor;
    }];
    
    // 课程封面
    JceTimeline_s_picurl *picurlModel = SAFE_CAST([[simpleFeed.courseFeed.jce_coverurl allValues] firstObject], JceTimeline_s_picurl);
    NSString *pictureUrl = picurlModel.jce_url;
    [self layoutOriginalImage:pictureUrl
                  placeHolder:[KSLayoutUIManager cahceImageWithName:@"soloAlbumDefault"]
                       action:nil
                       toInfo:info
                         rect:CGRectMake(startX, [info currentY], customViewHeight, customViewHeight)
                          tag:TimelineFeedCellSoloCover block:^(KSUIOriginalImageItem *layoutItem) {
        layoutItem.contentMode = UIViewContentModeScaleAspectFill;
        layoutItem.ksCornerRadius = kFeedImageRadiusCorner;
        layoutItem.uiItemClipsToBounds = YES;
        layoutItem.clipCorner = UIRectCornerAllCorners;
    }];

    // 课程名
    CGFloat nameStartX = startX + customViewHeight + 22;
    CGFloat nameStartY = startY + (customViewHeight - kSongInfoAreaHeight) / 2.0; // 名+标签，做整体竖直居中
    [self layoutOneLineText:simpleFeed.courseFeed.jce_strCourseName
                     toInfo:info
                          x:nameStartX
                          y:nameStartY
                          w:customViewWidth - customViewHeight - 18
                          f:[UIFont ks_fontWithFontType:KSFontType_LargeBold]
                          c:[UIColor ks_colorWithRGBHex:0x323333]];
    
    // 专辑信息
    CGFloat usedX = nameStartX;
    CGFloat listenNumIconImageY = startY + customViewHeight - ((customViewHeight - kSongInfoAreaHeight) / 2.0) - 13 + 1;
    // 课程数目
    [self layoutLocalImage:[UIImage imageNamed:@"feed_timeline_album_song_count_black"]
                    toInfo:info
                      rect:CGRectMake(usedX, listenNumIconImageY, 9, 13)
                       tag:TimelineFeedCellAlbumSongCountIcon
                     block:^(KSUIItemLocalImage *layoutItem) {
        layoutItem.contentMode = UIViewContentModeScaleAspectFill;
    }];
    usedX = usedX + 9 + 3; // 9为width 3为padding
    NSString *courseCountString = [NSString stringWithFormat:KString(@"共%ld节课程"), (long)simpleFeed.courseFeed.jce_uChapterNum];
    [self layoutOneLineText:courseCountString
                     toInfo:info
                          x:usedX
                          y:listenNumIconImageY - 1 // -1视觉还原
                          w:SCREEN_WIDTH - usedX - KSMargin_Dynamic_20
                          f:[UIFont ks_fontWithFontType:KSFontType_SmallBold]
                          c:[UIColor ks_colorWithRGBHex:0x323333]];

    // 左上角 "付费课程"标签
    [self calculateCoverLeftTopImage:simpleFeed layoutIfNeed:info rect:CGRectMake(startX + 5, startY + 5, 0, 0)];

    [info increaseHeight:customViewHeight];
}

// 礼物榜
+ (void)layoutGiftRankInfo_2022:(KSimpleFeed *)simpleFeed info:(KSLayoutInfo *)info {
    if (simpleFeed.layoutConfig.isHiddenGiftRank || [simpleFeed isInteractGameInPlayingStatus]) {
        //业务层决定不显示礼物榜
        KINFO(@"[Feed] no GiftRank:BizHide");
        return;
    }
    
    if ([simpleFeed.simpleUser.userinfo isLogicAuthStar] || [simpleFeed.forwardFeed.feedUserInfo.userinfo isLogicAuthStar]) {
        KINFO(@"[Feed] no GiftRank:AuthStar");
        return;
    }
    
    // 获取礼物榜展示头像数量
    NSInteger giftListShowCount = min(simpleFeed.giftRankInfo.topRankArray.count, GiftRankHeadIconCount);
    if (giftListShowCount == 0 && ![simpleFeed isShowDissimilateGiftRankFeed]) {
        return;
    }
    
    if (giftListShowCount == 0 && ![[KSUIABTestManager sharedManager] shouldShowEmptyGiftRank]) {
        KINFO(@"[Feed] no EmptyGiftRank:ABT");
        return;
    }
        
    if ((simpleFeed.ktvRoomShow.jce_iRoomType & KSKTVRoomType_Social) == KSKTVRoomType_Social) {
        // 欢聚歌房 没有送礼
        return;
    }
    
    UIFont *textFont = _font_big([UIFont systemFontOfSize:13]);
    CGFloat usedX = KSMargin_Dynamic_70;
    CGFloat textLineHeight = [textFont lineHeight];
    CGFloat headIconInternal = (-5);
    CGFloat topMargin = KSMargin_Dynamic(12);
    CGFloat buttomMargin = 0;
    NSInteger totoalGiftY = [info currentY] + topMargin;
    
    //排版礼物
    NSUInteger giftTag = TimelineFeedCellGiftListHeadIconImage + 1000 + giftListShowCount;
    
    //拉取数据的时候已经分拆鲜花数组直接显示
    if (giftListShowCount > 0) {
        BOOL shouldUseAcrossRankForLive = NO;

        //计算尾巴那个View的x坐标
        CGFloat lastViewX = usedX + (kTimeLineGiftItemUserHeadSmallWidth + headIconInternal) * (giftListShowCount - 1);
        
        // 从后面开始添加头像，做出重叠效果
        if (simpleFeed.liveShow) {
            shouldUseAcrossRankForLive = [SAFE_STR_BM(simpleFeed.liveShow.mapExt[@"iUseNewRank"]) boolValue];
        }
        for (NSInteger i = giftListShowCount - 1; i >= 0; i--) {
            KSimpleFeedGiftRankItem *rankItem = [simpleFeed.giftRankInfo.topRankArray objectAtIndex:i];
            if (rankItem) {
                KSCellUser *cellUser = [[KSCellUser alloc] init];
                cellUser.userId = rankItem.userInfo.userId;
                cellUser.timeStamp = rankItem.userInfo.avatarTimestamp;
                cellUser.userInfo = rankItem.userInfo; // 这里实际用的是父类KSBaseUserInfo
                if (shouldUseAcrossRankForLive) {
                    cellUser.avatarUrl = rankItem.userInfo.avatarUrl;
                    cellUser.ExtraInfoDic = [[NSMutableDictionary alloc] initWithDictionary:@{
                        @"shouldUseAcrossRankForLive": @(shouldUseAcrossRankForLive)
                    }];
                }
                [self layoutHead:cellUser action:nil toInfo:info x:lastViewX y:[info currentY] + topMargin w:kTimeLineGiftItemUserHeadSmallWidth tag:giftTag--];
                lastViewX -= (kTimeLineGiftItemUserHeadSmallWidth + headIconInternal);
            }
        }
        
        // 布局 K币 鲜花 礼物 数量文案
        usedX += (kTimeLineGiftItemUserHeadSmallWidth + headIconInternal) * giftListShowCount - headIconInternal + 7;
        
        NSString *totalGiftStr = @"";
        if (simpleFeed.giftRankInfo.giftWealth > 0) {
            totalGiftStr = [NSString stringWithFormat:@"%@ K币", [KSFormatHelper formatNewNumber:simpleFeed.giftRankInfo.giftWealth]];
        }
        if (!shouldUseAcrossRankForLive && simpleFeed.recivedFlowerInfo.number > 0) {
            if (simpleFeed.giftRankInfo.giftWealth == 0) {
                totalGiftStr = [NSString stringWithFormat:@"%@鲜花",[KSFormatHelper formatNewNumber:simpleFeed.recivedFlowerInfo.number]];
            } else {
                totalGiftStr = [totalGiftStr stringByAppendingString:[NSString stringWithFormat:@"  %@鲜花", [KSFormatHelper formatNewNumber:simpleFeed.recivedFlowerInfo.number]]];
            }
        } else if (shouldUseAcrossRankForLive && simpleFeed.recivedFlowerInfo.uPropsNum > 0) {
            if (simpleFeed.giftRankInfo.giftWealth == 0) {
                totalGiftStr = [NSString stringWithFormat:@"%@礼物", [KSFormatHelper formatNewNumber:simpleFeed.recivedFlowerInfo.uPropsNum]];
            } else {
                totalGiftStr = [totalGiftStr stringByAppendingString:[NSString stringWithFormat:@"  %@礼物", [KSFormatHelper formatNewNumber:simpleFeed.recivedFlowerInfo.uPropsNum]]];
            }
        }
        // 💎
        if (!WnsLocalServerBoolConfig(@"DisableBackPackDiamond") && simpleFeed.recivedFlowerInfo.diamondNum > 0) {            
            totalGiftStr = [totalGiftStr stringByAppendingString:[NSString stringWithFormat:@"  %@钻石", [KSFormatHelper formatNewNumber:simpleFeed.recivedFlowerInfo.diamondNum]]];
        }
        
        // 合规需求，直播和歌房不展示送礼数量
        if ([simpleFeed isKindOfLiveShowFeed]) {
            totalGiftStr = @"真爱粉丝";
        } else if ([simpleFeed isKindOfKTVRoomFeed]) {
            totalGiftStr = @"真爱歌友";
        }
        
        if (totalGiftStr.length > 0) {
            NSInteger totalGiftStrWitdh = [totalGiftStr kSongSizeWithFont:textFont].width;
            CGFloat maxWidth = SCREEN_WIDTH - usedX - KSMargin_Dynamic_15;
            totalGiftStrWitdh = min(maxWidth, totalGiftStrWitdh);
            // 布局送礼数量文案
            [self layoutOneLineLabel:totalGiftStr
                              toInfo:info
                                   x:usedX
                                   y:totoalGiftY + (kTimeLineGiftItemUserHeadSmallWidth - textLineHeight) / 2
                                   w:totalGiftStrWitdh
                                   f:textFont
                                   c:[UIColor ks_colorWithRGBHex:0x000000 alpha:0.5]
                                 tag:TimelineFeedCellGiftRankTextView
                               block:^(KSUIItemLabel *layoutItem) {
                layoutItem.normalBackgroundColor = [UIColor clearColor];
                layoutItem.highlightColor = [UIColor clearColor];
            }];
            usedX += totalGiftStrWitdh;
        }
    } else {
        // 没有人送礼
        UIImage *giftImg = [UIImage imageNamed:@"feed_giftRank_goldChair_new"];
        CGFloat giftImgWidth = KSMargin_Dynamic(16);
        CGFloat giftImgY = [info currentY] + topMargin;
        CGRect giftRect = CGRectMake(usedX + 4, giftImgY, giftImgWidth, giftImgWidth);
        [self layoutLocalImage:giftImg toInfo:info rect:giftRect tag:TimelineFeedCellGiftRankImgView];
        usedX = usedX + giftImgWidth + 7;
        NSString *giftRankStr = (simpleFeed.feedParkingGuideState == ksFeedParkingGuideStateShowing) ? KString(@"送礼上榜") : KString(@"暂无人送礼，点击鼓励一下吧");
        NSInteger giftRankStrWidth = [giftRankStr kSongSizeWithFont:textFont].width;
        CGFloat maxWidth = SCREEN_WIDTH - usedX - KSMargin_Dynamic_15;
        giftRankStrWidth = min(maxWidth, giftRankStrWidth);
        
        [self layoutOneLineLabel:giftRankStr
                          toInfo:info
                               x:usedX
                               y:giftImgY + (giftImgWidth - textLineHeight) / 2
                               w:giftRankStrWidth
                               f:textFont
                               c:UIColor.ks_secondaryTextColor
                             tag:TimelineFeedCellGiftRankTextView
                           block:^(KSUIItemLabel *layoutItem) {
            layoutItem.normalBackgroundColor = [UIColor clearColor];
            layoutItem.highlightColor = [UIColor clearColor];
        }];
        
        usedX += giftRankStrWidth;
    }
    
    // 礼物榜点击区域
    KSDrawItemAction *action = [KSDrawItemAction new];
    action.type = DRAWITEM_ACTION_TYPE_ENTER_GIFT_RANK_VC;
    action.busiData = simpleFeed;
    
    CGFloat giftAreaH = topMargin + kTimeLineGiftItemUserHeadSmallWidth + buttomMargin;
    if (giftListShowCount <= 0) {
        giftAreaH -= 8; // 无人送礼的时候调矮高度
    }
    
    [self layoutButton:action toInfo:info rect:CGRectMake(KSMargin_Dynamic_70, [info currentY], usedX - KSMargin_Dynamic_70, giftAreaH) tag:TimelineFeedCellGiftListClickMaskView block:^(KSUIItemButton *layoutItem) {
        layoutItem.normalBackgroundColor = [UIColor clearColor];
        layoutItem.highlightColor = [UIColor clearColor];
    }];

    CGFloat witdth = SCREEN_WIDTH - KSMargin_Dynamic_70 - KSMargin_Dynamic_20; // 图片的长和宽
    
    [self layoutUIViewItem:info rect:CGRectMake(KSMargin_Dynamic_70, [info currentY], witdth, giftAreaH) tag:TimelineFeedCellGiftListFadeView block:^(KSUIItem *layoutItem) {
        layoutItem.normalBackgroundColor = [UIColor clearColor];
        layoutItem.highlightColor = [UIColor clearColor];
    }];
    
    [info increaseHeight:giftAreaH];
}

+ (void)layoutDebugInfo_2022:(KSimpleFeed *)simpleFeed info:(KSLayoutInfo *)info {
    NSMutableString *temp = [NSMutableString new];
    if (simpleFeed.musicMoodFeed) {
        [temp appendString:@"音频(音乐心情)"];
    } else if (simpleFeed.songinfo) {
        if (UGC_HALF_CHORUS(simpleFeed.songinfo.ugcMask) == HALF_CHORUS_UGC) {
            [temp appendString:@"半成品"];
        }
        if (UGC_TYPE(simpleFeed.songinfo.ugcMask) == MUSIC_VIDEO_UGC) {
            [temp appendString:@"视频"]; // 视频
        } else { // 音频
            if ([simpleFeed isKTVAudioFeed]) {
                [temp appendString:@"KTV模式音频"];
            } else if (UGC_IS_SHORT_AUDIO(simpleFeed.songinfo.ugcMaskExt)) {
                [temp appendString:@"短音频"];
            } else {
                [temp appendString:@"音频"];
            }
        }
    } else if (simpleFeed.relayGameFeed) {
        [temp appendString:@"relayGameFeed"];
    } else if (simpleFeed.payAlbumInfo) {
        [temp appendString:@"专辑"];
    } else if (simpleFeed.soloAlbumInfo) {
        [temp appendString:@"歌单"];
    } else if (simpleFeed.liveShow) {
        [temp appendString:@"直播"];
    } else if (simpleFeed.ktvRoomShow) {
        [temp appendString:@"歌房"];
    } else if (simpleFeed.ktvRoomMike) {
        [temp appendString:@"歌房(排麦)"];
    } else {
        [temp appendString:@"未知"];
    }
    [temp appendString:@" - "];
    [temp appendString:simpleFeed.simpleFeedCommon.strFeedId ? : @"无ugcId"];
    CGSize tempSize = [temp kSongSizeWithFont:[UIFont ks_fontWithFontType:KSFontType_Tiny]];
    [self layoutOneLineText:temp
                     toInfo:info
                          x:SCREEN_WIDTH - tempSize.width
                          y:[info currentY] - 15
                          w:tempSize.width
                          f:[UIFont ks_fontWithFontType:KSFontType_Tiny]
                          c:[UIColor ks_colorWithRGBHex:0x000000 alpha:0.8]];
}

// 多人生日送礼
+ (KSLayoutInfo *)layoutMultipleSendGiftWithFeed:(KSimpleFeed *)simpleFeed
{
    if (simpleFeed.layoutInfo) {
        return simpleFeed.layoutInfo;
    }
    // 开始排版
    KSLayoutInfo* info = [[KSLayoutInfo alloc] init];
    if ([KSBirthdayGiftRequestManager getBirthdaySendGiftSwitch] !=1)
    {
        return info;
    }
    
    CGFloat topMargin = KSMargin_Dynamic(15);
    [info increaseHeight:topMargin];
    
    //用户信息
    [self layoutUserInfo:simpleFeed info:info];
    
    CGFloat witdth = SCREEN_WIDTH - KSMargin_Dynamic_70 - KSMargin_Dynamic_20;
    CGFloat height = 220;
    KSUIItemCustomAddView *item = [[KSUIItemCustomAddView alloc] init];
    item.rect = CGRectMake(KSMargin_Dynamic_70, [info currentY], witdth, height);
    item.viewType = DRAWITEM_CUSTOM_ADD_MultipleSendGift;
    [info addItem:item];
    [info increaseHeight:height];
    return info;
}

// 单人生日送礼
+ (KSLayoutInfo *)layoutSingleSendGiftWithFeed:(KSimpleFeed *)simpleFeed
{
    if (simpleFeed.layoutInfo) {
        return simpleFeed.layoutInfo;
    }
    // 开始排版
    KSLayoutInfo* info = [[KSLayoutInfo alloc] init];
    if ([KSBirthdayGiftRequestManager getBirthdaySendGiftSwitch] !=1)
    {
        return info;
    }
    
    CGFloat topMargin = KSMargin_Dynamic(15);
    [info increaseHeight:topMargin];
    
    //用户信息
    [self layoutUserInfo:simpleFeed info:info];
    
    CGFloat witdth = SCREEN_WIDTH - KSMargin_Dynamic_70 - KSMargin_Dynamic_20;
    CGFloat height = (simpleFeed.singleSendGiftFeed.jce_vecUserMsg.count > 0 ?  200 : 174);
    KSUIItemCustomAddView *item = [[KSUIItemCustomAddView alloc] init];
    item.rect = CGRectMake(KSMargin_Dynamic_70, [info currentY], witdth, height);
    item.viewType = DRAWITEM_CUSTOM_ADD_SingleSendGift;
    [info addItem:item];
    [info increaseHeight:height];
    
    [info increaseHeight:10]; // 底部间距
    return info;
}

// 「关注」「KTV」
+ (void)layoutLiteKtv:(KSimpleFeed *)simpleFeed info:(KSLayoutInfo *)info {
    CGFloat witdth = SCREEN_WIDTH - KSMargin_Dynamic_70 - KSMargin_Dynamic_20; // 图片的长和宽
    CGFloat height = witdth;
    KSUIItemCustomAddView *item = [[KSUIItemCustomAddView alloc] init];
    item.rect = CGRectMake(KSMargin_Dynamic_70, [info currentY], witdth, height);
    item.viewType = DRAWITEM_CUSTOM_ADD_LiteKtv;
    item.tag = TimelineFeedCellLiteKTVContainer;
    [info addItem:item];
}

// 「关注」「KTV」「PublicKTV」「OnlineNumLable」
+ (void)layoutLiteKtvOnlineNumLableContainer:(KSimpleFeed *)simpleFeed
                                        rect:(CGRect)rect
                                        info:(KSLayoutInfo *)info {
    KSUIItemCustomAddView *item = [[KSUIItemCustomAddView alloc] init];
    item.rect = rect;
    item.viewType = DRAWITEM_CUSTOM_ADD_LiteKtv_OnlineNum;
    item.tag = TimelineFeedCellLiteKTVOnlineNumContainer;
    [info addItem:item];
}

// 「关注」「KTV」「PublicKTV」「GameStatusLabel」
+ (void)layoutLiteKtvGameStatusLabelContainer:(KSimpleFeed *)simpleFeed
                                        rect:(CGRect)rect
                                        info:(KSLayoutInfo *)info {
    KSUIItemCustomAddView *item = [[KSUIItemCustomAddView alloc] init];
    item.rect = rect;
    item.viewType = DRAWITEM_CUSTOM_ADD_LiteKtv_GameStatus;
    item.tag = TimelineFeedCellLiteKTVGameStatusLabel;
    [info addItem:item];
}

// 「关注」「KTV」「PublicKTV」「OnlineNumLable」
+ (void)layoutInteractGameContainer:(KSimpleFeed *)simpleFeed
                                        rect:(CGRect)rect
                                        info:(KSLayoutInfo *)info {
    KSUIItemCustomAddView *item = [[KSUIItemCustomAddView alloc] init];
    item.rect = rect;
    item.viewType = DRAWITEM_CUSTOM_ADD_Interact_GameContainer;
    item.tag = TimelineFeedCellInteractGameContainer;
    [info addItem:item];
}


// 关注Feed多人送礼
+ (KSLayoutInfo *)layoutFollowMultiSendGift:(KSimpleFeed *)simpleFeed {
    if (simpleFeed.layoutInfo) {
        return simpleFeed.layoutInfo;
    }
    // 开始排版
    KSLayoutInfo* info = KSLayoutInfo.new;
    
    CGFloat topMargin = KSMargin_Dynamic(15);
    [info increaseHeight:topMargin];
    
    CGFloat headX = kAvatarX;
    CGFloat headY = [info currentY];
    
    [self layoutLocalImage:[UIImage imageNamed:@"official_noti_head_image"] toInfo:info rect:CGRectMake(headX, headY, kTimeLineUserHeadPendantWidth, kTimeLineUserHeadPendantHeight) tag:TimelineFeedCellOfficialNotiHeadImage];
    
    [info increaseHeight:kTimeLineUserHeadPendantHeight + 5];
    
    NSString *name = @"好友更新";
    CGSize nameSize = [name sizeWithFont:KSFontType1MediumCallout];
    
    CGFloat nameY = headY + (kTimeLineUserHeadPendantHeight - nameSize.height) / 2;
    
    [self layoutText:name toInfo:info x:KSMargin_Dynamic_70 y:nameY w:nameSize.width f:KSFontType1MediumCallout c:UIColor.blackColor];
    
    //描述
    [self layoutSimpleFeedDescriptionInfo:simpleFeed info:info];
    
    CGFloat witdth = SCREEN_WIDTH - KSMargin_Dynamic_70 - KSMargin_Dynamic_20;
    CGFloat height = (48 + 16) * simpleFeed.followMultiSendGiftModel.itemArray.count + 30 + 10;
    KSUIItemCustomAddView *item = [[KSUIItemCustomAddView alloc] init];
    item.rect = CGRectMake(KSMargin_Dynamic_70, [info currentY], witdth, height);
    item.viewType = DRAWITEM_CUSTOM_ADD_Follow_Feed_Multi_Send_Gift;
    [info addItem:item];
    [info increaseHeight:height];
    return info;
}

// 计算视频长宽
+ (CGRect)calculateCoverRect_2022:(KSimpleFeed *)simpleFeed info:(KSLayoutInfo *)info {
    CGFloat height, width;
    CGFloat layoutWidth = SCREEN_WIDTH - KSMargin_Dynamic_70 - KSMargin_Dynamic_20 ; // 左70 右20
    /*
     视频宽高比 >1:1（横版），则裁剪为4/3
     视频宽高比 <1:1（竖版），则裁剪为3/4
     其他1:1方图
     */
    if ([simpleFeed.songinfo getVideoContentSize].width > [simpleFeed.songinfo getVideoContentSize].height) {
        //横屏
        width = layoutWidth;
        height = layoutWidth * kVideoWidthHeightRate;
    } else if ([simpleFeed.songinfo getVideoContentSize].width < [simpleFeed.songinfo getVideoContentSize].height) {
        //竖屏 (左70 右60)
        layoutWidth = SCREEN_WIDTH - KSMargin_Dynamic_70 - KSMargin_Dynamic_60;
        width = layoutWidth;
        height = layoutWidth / kVideoWidthHeightRate;
    } else {
        //1:1显示,
        height = layoutWidth;
        width = layoutWidth;
    }
    return CGRectMake(KSMargin_Dynamic_70, [info currentY], width, height);
}

+ (UIImage *)calculateFeedLevelImage:(KSimpleFeed *)simpleFeed {
    // 标签优先级
    NSString *imageName = nil;
    if (simpleFeed.songinfo.ugcMaskExt1 & JceTimeline_Detail_KGE_UGC_MASK_EXT1_GOLD_RECOMMENDED) {
        // 金标推荐
        imageName = @"feed_timeline_golden_rec";
        if ([simpleFeed isFollowTabFeed]) {
            [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                reportModel.key = @"feed_following#goldugctask#null#exposure#0";
            }];
        } else {
            [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
                reportModel.key = @"homepage_me_trends#goldugctask#null#exposure#0";
                reportModel.commonInt1 = simpleFeed.isGuest ? 2 : 1;
            }];
        }
    } else if (simpleFeed.songinfo.iIsBeater) {
        // 好友擂主
        imageName = @"feed_timeline_new_beater_icon";
    } else if ((simpleFeed.songinfo.ugcMaskExt & JceTimeline_Detail_KGE_UGC_MASK_EXT_FAIRY_COVER)) {
        // 神仙翻唱
        imageName = @"feed_timeline_new_fanchang_icon";
    } else if (simpleFeed.songinfo.ugcMaskExt & JceTimeline_Detail_KGE_UGC_MASK_EXT_FAMILY_RECOMMEND) {
        // 家族推荐
        imageName = @"feed_timeline_new_jiazu_icon";
    } else if (simpleFeed.songinfo.ugcMaskExt & JceTimeline_Detail_KGE_UGC_MASK_EXT_BIT_KGE_UGC_MASK_EXT_RECOMMENDED) {
        // 推荐
        imageName = @"feed_timeline_tag_recomment";
        // 金标推荐
        if (simpleFeed.songinfo.ugcMaskExt1 & JceTimeline_Detail_KGE_UGC_MASK_EXT1_GOLD_MARK)
        {
            imageName = @"feed_timeline_tag_recomment_gold";
        }
    } else if (simpleFeed.songinfo.scoreRank!= SCORE_RANK_INVALID) {
        // 等级
        switch (simpleFeed.songinfo.scoreRank) {
            case SCORE_RANK_C:
                imageName = @"feed_timeline_tag_c";
                break;
            case SCORE_RANK_B:
                imageName = @"feed_timeline_tag_b";
                break;
            case SCORE_RANK_A:
                imageName = @"feed_timeline_tag_a";
                break;
            case SCORE_RANK_S:
                imageName = @"feed_timeline_tag_s";
                break;
            case SCORE_RANK_SS:
                imageName = @"feed_timeline_tag_ss";
                break;
            case SCORE_RANK_SSS:
                imageName = @"feed_timeline_tag_sss";
                break;
            case SCORE_RANK_MAX:
            case SCORE_RANK_INVALID:
            default:
                imageName = @"feed_timeline_tag_c";
                break;
        }
    }
    return [UIImage imageNamed:imageName];
}

// 「封面左上角标签」
// 2022.4.19 全集： 合唱  礼物合唱  明星合唱  教唱  付费课程  专辑  付费专辑  VIP专辑  歌单  回放  付费
// 计算并返回音频、视频封面左上角展示的标签 （比如礼物合唱，教唱），如果需要，可以顺带帮你布局
// 如果info有值，不会有返回值；如果info没值，返回值为标签view
// 优先级： 回放 付费 教唱 其他(其他暂时没给优先级)
+ (UIView *)calculateCoverLeftTopImage:(KSimpleFeed *)simpleFeed layoutIfNeed:(KSLayoutInfo *)info rect:(CGRect)rect {
    NSString *tagBgImage = nil;
    NSString *tagTextImage = nil;
    CGFloat tagWidth = 38;
    
    // 明星合唱
    BOOL isStarChorusFinishContent = [KSUgcStarChorusManager isUgcStarChorusWithUgcMaskExt:simpleFeed.songinfo.ugcMaskExt];
    // 礼物合唱
    BOOL isGiftChorus = (simpleFeed.hcCellItem.jce_iHasGift > 0 || simpleFeed.hcCellItem.jce_uHcFinalGiftNum > 0 || [simpleFeed canSoloSongShowChorusStyle]) ? YES : NO;
    // 是否付费
    BOOL isPayFeed = NO;
    NSString *payUgcString = nil;
    if (GetFlexBOOL([[KSPaySoloAlbumManager sharedManager] isPayUgcWithUgcMask:simpleFeed.songinfo.ugcMask])) {
        payUgcString =  [[KSPaySoloAlbumManager sharedManager] updateAndReturnPayDescWithPayRight:simpleFeed.songinfo.mapRight contentID:simpleFeed.simpleFeedCommon.strFeedId andPayType:KSUserPayTypeOptionUGC];
    }
    if ([payUgcString isEqualToString:kConstPayStr]) {
        isPayFeed = YES;
    }
    

    NSUInteger ugcType = [simpleFeed getUgcType];
    if (ugcType == LIVE_PLAYBACK) {
        // 直播回放(付费课程直播结束后形成的回放视频)
        tagBgImage = nil;
        tagTextImage = @"feed_timeline_huifang";
        tagWidth = 38;
    } else if (ugcType == PAY_UGC && isPayFeed) {
        // 付费
        // ps：如果要判断vip的话，就判 ugcType == PAY_UGC && payUgcString == kConstVipStr
        tagBgImage = @"feed_timeline_pay_feed_bg_short";
        tagTextImage = @"feed_timeline_fufei";
        tagWidth = 38;
    } else if (simpleFeed.songinfo.ugcMaskExt & JceTimeline_Detail_KGE_UGC_MASK_EXT_BIT_KGE_UGC_MASK_EXT_TEACH) {
        // 教唱
        tagBgImage = nil;
        tagTextImage = @"feed_timeline_teach_course_icon";
        tagWidth = 38;
    } else if (simpleFeed.courseFeed) {
        // 付费课程
        tagBgImage = @"feed_timeline_pay_feed_bg";
        tagTextImage = @"feed_timeline_pay_course_icon";
        tagWidth = 62;
    } else if (simpleFeed.payAlbumInfo) {
        if ([[KSPaySoloAlbumManager sharedManager] getPayIconMask:simpleFeed.payAlbumInfo.mapRight] == KSPayIconPriorityVIP) {
            // vip专享专辑
            tagBgImage = @"feed_timeline_vip_feed_bg";
            tagTextImage = @"feed_timeline_vip_album_icon";
            tagWidth = 62;
        } else if (simpleFeed.payAlbumInfo.lPlayMask) {
            // 付费专辑
            tagBgImage = @"feed_timeline_pay_feed_bg";
            tagTextImage = @"feed_timeline_pay_album_icon";
            tagWidth = 62;
        } else {
            // 免费专辑
            tagBgImage = nil;
            tagTextImage = @"feed_timeline_free_pay_album_icon";
            tagWidth = 38;
        }
    } else if (simpleFeed.soloAlbumInfo) {
        // 歌单
        tagBgImage = nil;
        tagTextImage = @"feed_timeline_gedan_icon";
        tagWidth = 38;
    } else if (isStarChorusFinishContent) {
        // 明星合唱
        tagBgImage = nil;
        tagTextImage = @"feed_timeline_new_star_hc_icon";
        tagWidth = 62;
    } else if (isGiftChorus) {
        // 礼物合唱
        tagBgImage = nil;
        tagTextImage = @"feed_timeline_new_gift_hc_icon";
        tagWidth = 62;
    }
    
    if (!tagTextImage) {
        switch ([simpleFeed getUgcType]) {
            case VIDEO_UGC: { // 视频
                if (UGC_FIN_CHORUS(simpleFeed.songinfo.ugcMask) || // 合唱成品
                    UGC_FAVOR_CHORUS(simpleFeed.songinfo.ugcMask) || // 合唱收录作品
                    UGC_HALF_CHORUS(simpleFeed.songinfo.ugcMask)) { // 合唱半成品
                    // 合唱
                    tagBgImage = nil;
                    tagTextImage = @"feed_timeline_new_hc_icon";
                    tagWidth = 38;
                }
                break;
            }
            case HALF_CHORUS_UGC: // 合唱半成品
            case FIN_CHORUS_UGC:  // 合唱
            case FAVOR_CHORUS_UGC: { // 收录合唱
                /// 搜索合唱入口不让展示合唱tag
                if (!simpleFeed.layoutConfig.isActionBtnInRightCorner) {
                    // 合唱
                    tagBgImage = nil;
                    tagTextImage = @"feed_timeline_new_hc_icon";
                    tagWidth = 38;
                }
            }
                break;
            default:
                break;
        }
    }
    
    if (!tagTextImage || tagTextImage.length == 0) {
        // 没有需要展示的左上角标签
        return nil;
    }
    
    if (info) {
        CGRect finalRect = CGRectMake(rect.origin.x, rect.origin.y, _size_mid(tagWidth), _size_mid(22));
        if (!tagBgImage) { // 这里有两种UI，一种是半透明背景，上面贴文字图片；另一种是背景和文字都是现成的图片（这UI做的还敢再BT一点吗...）
            // 本来这里半透明背景应该用高斯模糊的，但是高斯模糊放到cell上会有滑动时闪动和变色问题，暂时不知道怎么解
            [self layoutUIViewItem:info
                              rect:finalRect
                               tag:TimelineFeedCellLeftTopTagBlurView2
                             block:^(KSUIItem *layoutItem) {
                layoutItem.normalBackgroundColor = [UIColor ks_colorWithRGBHex:0x000000 alpha:0.35];
                layoutItem.ksCornerRadius = 5;
                layoutItem.uiItemClipsToBounds = YES;
                layoutItem.clipCorner = UIRectCornerAllCorners;
            }];
        } else {
            [self layoutLocalImage:[UIImage imageNamed:tagBgImage]
                            toInfo:info
                              rect:finalRect
                               tag:TimelineFeedCellLeftTopTagBgView
                             block:^(KSUIItemLocalImage *layoutItem) {
                layoutItem.contentMode = UIViewContentModeScaleAspectFill;
            }];
        }
        [self layoutLocalImage:[UIImage imageNamed:tagTextImage]
                        toInfo:info
                          rect:finalRect
                           tag:TimelineFeedCellLeftTopTagTextView
                         block:^(KSUIItemLocalImage *layoutItem) {
            layoutItem.contentMode = UIViewContentModeScaleAspectFill;
        }];
    } else {
        // 返回生成的图片
        UIView *contentView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, _size_mid(tagWidth), _size_mid(22))];
        contentView.layer.cornerRadius = 5;
        contentView.clipsToBounds = YES;
        if (tagBgImage) {
            // 如果提供了背景图片,则用背景图片
            UIImageView *bgImageView = [[UIImageView alloc] initWithImage:[UIImage imageNamed:tagBgImage]];
            bgImageView.frame = contentView.bounds;
            [contentView addSubview:bgImageView];
        } else {
            // 没有背景图片 纯黑0.35
            UIView *bg1 = [[UIView alloc] initWithFrame:contentView.bounds];
            bg1.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.35];
            [contentView addSubview:bg1];
        }
        // 贴文字
        UIImageView *textImageView = [[UIImageView alloc] initWithImage:[UIImage imageNamed:tagTextImage]];
        textImageView.frame = contentView.bounds;
        [contentView addSubview:textImageView];
        return contentView;
    }
    return nil;
}

+ (NSString *)calculateFucBtnDesc:(KSBottomFucBtnType)buttonType {
    switch (buttonType) {
        case KSBottomFucBtnType_none:
            return @"";
        case KSBottomFucBtnType_hc:
            return @"合唱";
        case KSBottomFucBtnType_gift_hc:
            return @"礼物合唱";
        case KSBottomFucBtnType_vote:
            return @"投票";
        case KSBottomFucBtnType_purchase:
            return @"购买";
        default:
            return @"";
    }
    return @"";
}

+ (DRAWITEM_ACTION_TYPE)calculateFucBtnActionType:(KSBottomFucBtnType)buttonType {
    switch (buttonType) {
        case KSBottomFucBtnType_hc:
            return DRAWITEM_ACTION_TYPE_CHORUS_JOIN;
        case KSBottomFucBtnType_gift_hc:
            return DRAWITEM_ACTION_TYPE_CHORUS_JOIN;
        case KSBottomFucBtnType_vote:
            return DRAWITEM_ACTION_TYPE_DAOJU_COMPETITION_VOTE;
        case KSBottomFucBtnType_purchase:
            return DRAWITEM_ACTION_TYPE_PAYALBUME_BUY;
        default:
            break;
    }
    return DRAWITEM_ACTION_TYPE_NONE;
}

+ (UIColor *)calculate2022FeedMagicColor:(KSimpleFeed *)simpleFeed {
    NSString *colorString = @"#F2F4F6";
    if (simpleFeed.magicColor.jce_strMagicColor.length > 0) {
        colorString = simpleFeed.magicColor.jce_strMagicColor;
    }
    UIColor *magicColor = [UIColor ks_colorWithHexString:colorString];
#ifdef DEBUG
//    magicColor = [UIColor ks_colorWithRGBHex:0xB9E3D7];
#endif
    return magicColor;
}
    
// 2022feed改版引入的新字体
+ (UIFont *)getAvantFontWithSize:(CGFloat)size bold:(BOOL)bold {
    UIFont *font = nil;
    if (bold) {
        font = KSFontType3BlackWithSize(size);
    } else {
        font = KSFontType3BoldWithSize(size);
    }
    if (!font) {
        NSAssert(NO, @"注意字体缺失，请排查");
        font = [UIFont systemFontOfSize:size];
    }
    return font;
}

@end
