//
//  KSRecBottomSendGiftView.m
//  QQKSong
//
//  Created by hongyusong on 2022/8/30.
//  Copyright © 2022 Tencent. All rights reserved.
//

#import "KSRecBottomSendGiftView.h"
#import "YPStyle.h"

@interface KSRecBottomSendGiftView ()

@property (nonatomic, strong) UIView *barView;
@property (nonatomic, strong) UILabel *barDescLabel; // 主文案

@property (nonatomic, strong) UIView *sendGiftView; // 送礼按钮
@property (nonatomic, strong) UIImageView *giftIcon; // 礼物图标
@property (nonatomic, strong) UILabel *priceLabel; // 价格
@property (nonatomic, strong) UILabel *sendTitleLabel; // 送礼文案

@property (nonatomic, strong) UIImageView *badgeImgView; // 角标
@property (nonatomic, strong) UILabel *badgeLabel; // 角标文案

@end


@implementation KSRecBottomSendGiftView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        [self barView];
        [self sendGiftView];
        [self barDescLabel];
        [self giftIcon];
        [self priceLabel];
    }
    return self;
}

#pragma mark - Lazy

- (UIView *)barView
{
    if (!_barView) {
        _barView = [[UIView alloc] initWithFrame:self.bounds];
        _barView.layer.cornerRadius = 8;
        _barView.layer.masksToBounds = YES;
        _barView.backgroundColor = [UIColor colorWithWhite:1 alpha:0.2];
        
        [self addSubview:_barView];
    }
    return _barView;
}

- (UILabel *)barDescLabel
{
    if (!_barDescLabel) {
        _barDescLabel = [[UILabel alloc] init];
        _barDescLabel.textColor = UIColor.whiteColor;
        _barDescLabel.textAlignment = NSTextAlignmentLeft;
        _barDescLabel.font = _font_big([UIFont systemFontOfSize:14]);
        _barDescLabel.adjustsFontSizeToFitWidth = YES;
        
        [self addSubview:_barDescLabel];
        [_barDescLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.mas_offset(12);
            make.centerY.mas_offset(0);
            make.right.mas_equalTo(self.sendGiftView.mas_left).offset(-5.f);
        }];
    }
    return _barDescLabel;
}

- (UIView *)sendGiftView {
    if (!_sendGiftView) {
        _sendGiftView = [[UIView alloc] init];
        _sendGiftView.layer.cornerRadius = 6;
        _sendGiftView.clipsToBounds = YES;
        _sendGiftView.backgroundColor = [UIColor colorWithWhite:1 alpha:0.2];
        
        [self addSubview:_sendGiftView];
        [_sendGiftView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.mas_offset(0);
            make.right.mas_offset(-4);
            make.height.mas_equalTo(32);
            make.width.mas_equalTo(78);
        }];
        
        UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(onClickSendGiftAction)];
        [_sendGiftView addGestureRecognizer:tap];
    }
    return _sendGiftView;
}

- (UIImageView *)giftIcon
{
    if (!_giftIcon) {
        _giftIcon = [[UIImageView alloc] init];
        _giftIcon.contentMode = UIViewContentModeScaleAspectFill;
        
        [self.sendGiftView addSubview:_giftIcon];
        [_giftIcon mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.mas_offset(8);
            make.top.mas_offset(0);
            make.width.mas_equalTo(22);
            make.height.mas_equalTo(22);
        }];
        
    }
    return _giftIcon;
}

- (UILabel *)priceLabel
{
    if (!_priceLabel) {
        _priceLabel = [[UILabel alloc] init];
        _priceLabel.textColor = UIColor.whiteColor;
        _priceLabel.textAlignment = NSTextAlignmentCenter;
        _priceLabel.font = [UIFont systemFontOfSize:8];
        _priceLabel.adjustsFontSizeToFitWidth = YES;
        
        [self.sendGiftView addSubview:_priceLabel];
        [_priceLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.mas_equalTo(self.giftIcon);
            make.top.mas_equalTo(self.giftIcon.mas_bottom).mas_offset(-2);
            make.width.mas_lessThanOrEqualTo(30);
        }];
    }
    return _priceLabel;
}

- (UILabel *)sendTitleLabel
{
    if (!_sendTitleLabel) {
        _sendTitleLabel = [[UILabel alloc] init];
        _sendTitleLabel.textColor = UIColor.whiteColor;
        _sendTitleLabel.textAlignment = NSTextAlignmentCenter;
        _sendTitleLabel.font = [UIFont systemFontOfSize:12 weight:UIFontWeightBold];
        _sendTitleLabel.adjustsFontSizeToFitWidth = YES;
        
        [self.sendGiftView addSubview:_sendTitleLabel];
        [_sendTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.mas_offset(0);
            make.left.mas_offset(34);
            make.right.mas_offset(-6);
        }];
    }
    return _sendTitleLabel;
}

- (UIImageView *)badgeImgView
{
    if (!_badgeImgView) {
        _badgeImgView = [[UIImageView alloc] init];
        _badgeImgView.contentMode = UIViewContentModeScaleAspectFit;
        _badgeImgView.image = [UIImage imageNamed:@"feed_rec_badge_bg"];

        [self addSubview:_badgeImgView];
        [_badgeImgView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.mas_offset(3);
            make.top.mas_offset(-3);
            make.width.mas_equalTo(24);
            make.height.mas_equalTo(12);
        }];
        
    }
    return _badgeImgView;
}

- (UILabel *)badgeLabel
{
    if (!_badgeLabel) {
        _badgeLabel = [[UILabel alloc] init];
        _badgeLabel.textColor = [UIColor ks_colorWithHexString:@"#FE4F4F"];
        _badgeLabel.textAlignment = NSTextAlignmentCenter;
        _badgeLabel.adjustsFontSizeToFitWidth = YES;
        _badgeLabel.font = [UIFont systemFontOfSize:8 weight:UIFontWeightMedium];
        
        [self.badgeImgView addSubview:_badgeLabel];
        [_badgeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.mas_offset(0);
            make.centerY.mas_offset(0);
            make.width.mas_equalTo(self.badgeImgView);
        }];
    }
    return _badgeLabel;
}

#pragma mark - Publick
- (void)updateWithSimpleFeed:(KSimpleFeed *)simpleFeed {
    [self setupRecBottomGiftAnimationBtn:simpleFeed.giftGuide];
}

- (void)setupRecBottomGiftAnimationBtn:(KSimpleFeedGiftGuide *)giftGuideInfo {
    if (giftGuideInfo.isFreeAd) {
        NSString *logoImgUrl = [[WnsConfigManager sharedInstance].appConfig.urlConfig getIapGiftPicUrlByStrLogo:giftGuideInfo.logoStr];
        // 左边主文案
        self.barDescLabel.text = @"免费礼物鼓励一下吧";
        
        if (IS_EMPTY_STR_BM(logoImgUrl) || giftGuideInfo.price == 0) {
            self.sendGiftView.hidden = YES;
            return;
        }
        
        self.sendGiftView.hidden = NO;
        
        // 礼物icon
        [self.giftIcon setImageWithUrl:logoImgUrl];
        // 价格
        self.priceLabel.text = [NSString stringWithFormat:@"%@K币", [KSFormatHelper formatNewNumber:giftGuideInfo.price]];
        
        // 送礼文案
        NSString *sendTitle = @"免费送";
        if ([WnsConfigManager sharedInstance].isPGSH) {
            sendTitle = @"立即送";
        }
        self.sendTitleLabel.text = sendTitle;
        CGFloat sendTitleSizeW = [sendTitle kSongSizeWithFont:self.sendTitleLabel.font
                                                     forWidth:40
                                                lineBreakMode:NSLineBreakByWordWrapping].width;
        
        [self.sendGiftView mas_updateConstraints :^(MASConstraintMaker *make) {
            make.width.mas_equalTo(sendTitleSizeW + 40);
        }];
        
        // 角标
        self.badgeImgView.hidden = YES;
        if (!IS_EMPTY_STR_BM(giftGuideInfo.badgeStr)) {
            self.badgeImgView.hidden = NO;
            self.badgeLabel.text = giftGuideInfo.badgeStr;
        }
    } else {
        NSString *logoImgUrl = [[WnsConfigManager sharedInstance].appConfig.urlConfig getIapGiftPicUrlByStrLogo:giftGuideInfo.logoStr];
        NSString *descText = giftGuideInfo.giftDesc;
        // 左边主文案
        self.barDescLabel.text = descText;
        
        if (IS_EMPTY_STR_BM(logoImgUrl) || giftGuideInfo.price == 0) {
            self.sendGiftView.hidden = YES;
            return;
        }
        
        self.sendGiftView.hidden = NO;
        
        // 礼物icon
        [self.giftIcon setImageWithUrl:logoImgUrl];
        // 价格
        self.priceLabel.text = [NSString stringWithFormat:@"%@K币", [KSFormatHelper formatNewNumber:giftGuideInfo.price]];
        
        // 送礼文案
        NSString *sendTitle = giftGuideInfo.isMinorHeatCard ? giftGuideInfo.botttomSubTitle : giftGuideInfo.buttoneStrategyStr;
        if ([WnsConfigManager sharedInstance].isPGSH) {
            sendTitle = @"立即送";
        }
        self.sendTitleLabel.text = sendTitle;
        CGFloat sendTitleSizeW = [sendTitle kSongSizeWithFont:self.sendTitleLabel.font
                                                     forWidth:40
                                                lineBreakMode:NSLineBreakByWordWrapping].width;
        
        [self.sendGiftView mas_updateConstraints :^(MASConstraintMaker *make) {
            make.width.mas_equalTo(sendTitleSizeW + 40);
        }];
        
        // 角标
        self.badgeImgView.hidden = YES;
        if (!IS_EMPTY_STR_BM(giftGuideInfo.badgeStr)) {
            self.badgeImgView.hidden = NO;
            self.badgeLabel.text = giftGuideInfo.badgeStr;
        }
    }
}

#pragma mark - Action

- (void)onClickSendGiftAction
{
    if (self.clickBlock) {
        self.clickBlock();
    }
}

@end
