//
//  KSFeedCellFreeAdButton.m
//  QQKSong
//
//  Created by welkin on 2025/2/17.
//  Copyright © 2025 Tencent. All rights reserved.
//

#import "KSFeedCellFreeAdButton.h"
#import "KSUserInfo.h"
#import "LMUserInfo.h"
#import "KSLoginManager.h"
#import "YPStyle.h"
#import "KSCommonUIFactory.h"
#import "KSGlobalSizeManager+Small.h"
#import "KSAdFreeGiftManager.h"
#import "KSAdFreeGiftScene.h"


@interface KSFeedCellFreeAdButton ()

@property (nonatomic, assign) CGPoint origin;
@property (nonatomic, assign) CGFloat expandWidth; /**< 展开后宽度 */
@property (nonatomic, assign) CGFloat minExpandWidth; /**< 最小展开宽度 */ // 为了保证能够完全遮住下面排版引擎的所有送礼按钮
@property (nonatomic, assign) CGFloat parentSizeH;

@property (nonatomic, strong) UIView *bgView;
@property (nonatomic, strong) UIImageView *iconImageView;
@property (nonatomic, strong) UILabel *contentLabel;

@property (nonatomic, copy) dispatch_block_t showBlk;
@property (nonatomic, copy) dispatch_block_t dismissBlk;

@property (nonatomic, strong) KSAdFreeGiftScene *giftScene;

@property (nonatomic, weak) KSimpleFeed *simpleFeed;

@end


@implementation KSFeedCellFreeAdButton

@synthesize exposeBlk;
@synthesize showedBlk;
@synthesize dismissedBlk;


- (instancetype)initWithFeed:(KSimpleFeed *)simpleFeed parentFrame:(CGRect)parentFrame {
    self = [super init];
    if (self) {
        _simpleFeed = simpleFeed;
        _origin = parentFrame.origin;
        _minExpandWidth = parentFrame.size.width;
        _parentSizeH = parentFrame.size.height;
        
        self.clipsToBounds = YES;
        
        [self configWithAniStatus];
    }
    return self;
}

- (void)configWithAniStatus {
    CGFloat sizeH = self.parentSizeH;
    switch (self.simpleFeed.quickGiftAniStatus) {
        case KSFeedQuickGiftAniStatusNone:
        {
            self.frame = CGRectMake(self.origin.x, self.origin.y, 0, sizeH);
            [self removeAllSubviews];
            
            self.showBlk = nil;
            self.dismissBlk = nil;
        }
            break;
            
        case KSFeedQuickGiftAniStatusShowed:
        {
            if (!self.bgView) {
                [self setupSubViews];
                [self updateWithDataModel];
            }
            
            self.frame = CGRectMake(self.origin.x, self.origin.y, self.expandWidth, sizeH);
            
            self.showBlk = nil;
            self.dismissBlk = nil;
        }
            break;
            
        case KSFeedQuickGiftAniStatusInit:
        {
            [self setupSubViews];
            [self updateWithDataModel];
            
            if (self.giftScene.giftBtnNum == 2) {
                // 双按钮样式不做动画，直接展示
                self.frame = CGRectMake(self.origin.x, self.origin.y, self.expandWidth, sizeH);
            } else {
                self.frame = CGRectMake(self.origin.x, self.origin.y, 0, sizeH);
            }
            
            [self setupShowBlock];
            [self setupDismissBlock];
        }
            break;
            
        case KSFeedQuickGiftAniStatusShowing:
        {
            [self setupSubViews];
            [self updateWithDataModel];
            self.frame = CGRectMake(self.origin.x, self.origin.y, self.expandWidth, sizeH);
            
            [self setupDismissBlock];
            
            // 补一个隐藏逻辑
            KS_WEAK_SELF(self);
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                CHECK_SELF_AND_RETURN()
                if (self.dismissBlk) {
                    self.dismissBlk();
                }
            });
        }
            break;
            
        default:
            break;
    }
}

- (void)setupSubViews {
    CGFloat bgViewHeight = self.parentSizeH;
    self.bgView = [UIView new];
    self.bgView.userInteractionEnabled = NO;
    self.bgView.backgroundColor = [UIColor ks_colorWithHexString:@"F2F4F6"];
    self.bgView.layer.cornerRadius = bgViewHeight/2.f;
    [self addSubview:self.bgView];
    [self.bgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self);
        make.height.equalTo(@(bgViewHeight));
        make.leadingMargin.equalTo(self);
        make.width.equalTo(self.mas_width);
    }];
    
    CGFloat iconSizeH = _size_mid_320(22);
    self.iconImageView = [UIImageView new];
    self.iconImageView.contentMode = UIViewContentModeScaleAspectFit;
    [self addSubview:self.iconImageView];
    [self.iconImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(@(iconSizeH));
        make.height.equalTo(@(iconSizeH));
        make.left.equalTo(self).offset(13.f);
        make.centerY.equalTo(self.bgView);
    }];
    
    self.contentLabel = [UILabel new];
    self.contentLabel.font = _font_mid_320(YPFont_PingFangSemiboldSize(13));
    self.contentLabel.textColor = UIColor.ks_primaryTextColor;
    self.contentLabel.textAlignment = NSTextAlignmentCenter;
    [self addSubview:self.contentLabel];
    [self.contentLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.iconImageView.mas_right).offset(2.f);
        make.right.equalTo(self).offset(-13.f);
        make.height.equalTo(@(self.parentSizeH/2));
        make.centerY.equalTo(self.bgView);
    }];
    
}

/// 视图移除及重置操作
- (void)prepareForReuse {
    self.showBlk = nil;
    self.exposeBlk = nil;
    self.dismissBlk = nil;
    self.simpleFeed = nil;
    
    [self removeFromSuperview];
}

#pragma mark - Config

- (void)updateWithFeed:(KSimpleFeed *)simpleFeed {
    self.simpleFeed = simpleFeed;
    
    // 礼物数据异步返回，这里构建下UI
    [self updateWithDataModel];
}

- (void)updateWithDataModel {
    
    if (!self.simpleFeed.giftGuide) {
#ifdef INTERNALBUILD
        KINFO(@"[QGB] 没有礼物数据");
#endif
        self.expandWidth = 0;
        
        return;
    }
    
    // 目前只有关注feed
    self.giftScene = [KSAdFreeGiftScene sceneWithType:KSAdFreeGiftSceneType_FollowFeed];
    
    NSString *titleStr = @"看视频免费送";
    if (self.giftScene.giftBtnNum == 2 ||
        kScreenWidthBM() == 320) {
        // 固定样式 OR 系统放大模式
        titleStr = @"免费送";
    }
    
    if ([WnsConfigManager sharedInstance].isPGSH) {
        titleStr = @"立即送";
    }
    
    // ICON
    NSString *logoImgUrl = [[WnsConfigManager sharedInstance].appConfig.urlConfig getIapGiftPicUrlByStrLogo:self.simpleFeed.giftGuide.logoStr];
    if (!IS_EMPTY_STR_BM(logoImgUrl)) {
        [self.iconImageView setImageWithUrl:logoImgUrl];
    } else {
        self.iconImageView.image = [UIImage imageNamed:@"ad_free_gift_icon"];
    }
    
    // 文案
    UIFont *contentFont = _font_mid_320(YPFont_PingFangSemiboldSize(13));
    CGSize quickGiftStrSize = [titleStr kSongSizeWithFont:contentFont];
    self.contentLabel.text = titleStr;
    
    // 快捷送礼点击按钮宽度
    CGFloat expandWidth = 26 + 2 + _size_mid_320(22) + ceil(quickGiftStrSize.width);
    self.expandWidth = MAX(expandWidth, self.minExpandWidth);
}

#pragma mark - Animation

- (void)startQuickGiftAnimation {
    if (self.simpleFeed.quickGiftAniStatus != KSFeedQuickGiftAniStatusInit) {
        return;
    }
    
    if (self.giftScene.giftBtnNum != 2) {
        [self setNeedsLayout];
        [self layoutIfNeeded];
        NSTimeInterval showAniTime = 0.4f;
        KS_WEAK_SELF(self);
        [UIView animateWithDuration:showAniTime animations:^{
            CHECK_SELF_AND_RETURN()
            if (self.showBlk) {
                self.showBlk();
            }
        } completion:^(BOOL finished) {
            CHECK_SELF_AND_RETURN()
            if (self.simpleFeed.quickGiftAniStatus == KSFeedQuickGiftAniStatusInit) {
                if (self.showBlk) {
                    self.simpleFeed.quickGiftAniStatus = KSFeedQuickGiftAniStatusShowing;
                    // 曝光
                    if (self.exposeBlk) {
                        self.exposeBlk();
                    }
                } else {
                    // 不回收直接展示完整按钮
                    self.simpleFeed.quickGiftAniStatus = KSFeedQuickGiftAniStatusShowed;
                    return;
                }
            }
            // icon摇动动画，时长3s
            [KSAdFreeGiftManager addShakeAnimationToGiftIcon:self.iconImageView];
            
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                CHECK_SELF_AND_RETURN()
                if (self.dismissBlk) {
                    self.dismissBlk();
                }
            });
        }];
    } else {
        // icon摇动动画，时长3s
        [KSAdFreeGiftManager addShakeAnimationToGiftIcon:self.iconImageView];
        KS_WEAK_SELF(self);
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            CHECK_SELF_AND_RETURN()
            if (self.dismissBlk) {
                self.dismissBlk();
            }
        });
    }
}

- (void)setupShowBlock {
    KS_WEAK_SELF(self);
    self.showBlk = ^{
        CHECK_SELF_AND_RETURN()
        
        if (self.expandWidth > 100 && [self.contentLabel.text isEqualToString:@"送礼"]) {
            self.contentLabel.text = @"一键送礼";
        }
        self.width = self.expandWidth;
    };
}

- (void)setupDismissBlock {
    KS_WEAK_SELF(self);
    self.dismissBlk = ^{
        CHECK_SELF_AND_RETURN()
        
        if (self.simpleFeed.quickGiftAniStatus == KSFeedQuickGiftAniStatusShowing) {
            self.simpleFeed.quickGiftAniStatus = KSFeedQuickGiftAniStatusShowed;
        }
    };
}

- (void)cancelQuickGiftAnimation {
    
    if (self.simpleFeed.quickGiftAniStatus == KSFeedQuickGiftAniStatusShowing) {
        self.simpleFeed.quickGiftAniStatus = KSFeedQuickGiftAniStatusShowed;
    }
    
    self.showBlk = nil;
    self.exposeBlk = nil;
    self.dismissBlk = nil;
}


@end

