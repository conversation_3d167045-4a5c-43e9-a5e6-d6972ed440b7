//
//  KSLayoutableTimelineFeedCellV2.h
//  QQKSong
//
//  Created by she<PERSON><PERSON><PERSON><PERSON> on 2019/6/15.
//  Copyright © 2019 Tencent. All rights reserved.
//

#import "KSLayoutableFeedBaseCell.h"
#import "KSVideoDisplayView.h"
#import <KSLottie/Lottie.h>
#import "KSDetailLyricPanel.h"
#import "KSAdapterImageView.h"
#import "KSFeedPlayBar.h"
#import "KSFeedPlayBarV4.h"
#import "KSRecCardFeedVideolizeView.h"
#import "KSAnimationPendantAvatarView.h"
#import "KSBasePlaybackView.h"
#import "KSGlobePlayerButton.h"
#import "KSPraiseButton.h"
#import "KSFeedPreviewManager.h"
#import "KSRecFeedLivingView.h"
#import "KSAudioPreviewManager.h"
#import "KSRichLabel.h"
#import "KSFeedFullScreenLayouMgr.h"
#import "KSFoldedFeedsView.h"
#import "KSGradientTag.h"

#import "KSFeedBizActionButton.h"
#import "KSNearbyBubbleView.h"
#import "KSRecFeedRightGiftIconView.h"
#import "KSRecCardActionButton.h"
#import "KSBubbleView.h"
#import "KSFeedCellTagViewProtocol.h"
#import "KSKTVRoomCardTableViewCell.h"
#import "KSKtvCardModuleDefine.h"
#import "KSKTVRoomCardDataManager.h"
#import "proto_room_RoomType.h"
#import "KSKTVRoomCardErrorView.h"
#import "KSKTVRoomCardLoadingView.h"
#import "KSKtvCardModuleDefine.h"
#import "KSHorizonalBannerView.h"
#import "proto_room_lottery_RoomLotteryGift.h"
#import "proto_new_gift_Gift.h"
#import "KSIapGift.h"
#import "KSMultipleFriendsSendGiftView.h"
#import "KSSingleFriendSendGiftView.h"
#import "KSLayoutVideoAnimationView.h"
#import "KSFollowKTVRoomFeedInteractGameContainer.h"
#import <KSLottie/LOTAnimationView.h>
#import "KSBurstCommonDefine.h"
#import "KSFollowFeedSendGiftBtnProtocol.h"
#import "KSRecFeedVipMarketingView.h"
#import "KSRecFeedRightSingButton.h"
#import "KSRecFeedBottomBar.h"
#import "KSRecBottomSendGiftView.h"
#import "KSRecAICoverSongAIGuideView.h"
#import "KSFeedAiImageGridView.h"
#import "KSRecAiImageContentView.h"
#import "KSRecBottomAITakePicBar.h"
#import "KSFollowFeedAITakePicView.h"

@class proto_ktv_recfeed_webapp_KtvItem;
@class TMEGModuleLoader;
@class KSUIItem;
@class KSTimeLineFeedLiveStreamView;
@class KSLivingTagView;
@class KSKtvRoomRecInfoView;
@class KSLiveShowRecInfoView;
@class KSFeedCellTagViewPlugin;
@class KSSongGodBigCardView;
@class proto_ad_channel_user_pay_task_QueryPayTaskStatusRsp;
@class KSFeedLiveCardView;
@class KSFeedCellQuickGiftButton;
@class KSRecCardShortPlayBar;
@class KSFollowKTVRoomFeedContainer;
@class KSFollowKTVRoomFeedOnlineNumContainer;
@class KSFollowKTVRoomFeedInteractGameContainer;
@class KSSingleDetailBurstIconView;
@class KSSingleDetailBurstAnimView;
@class KSSingleDetailBurstTimeManager;
@class KSFollowFeedMultiSendGiftView;
@class KSNewSinglesDetailHonorItemView;
@class KSFeedProductCardView;
@class KSAiSongRecPanel;
@class KSAISongPublishFeedPanel;
@class TLMLiveMediaPlayer;
@class KSRecCardLiveEnterPlayBar;
@class KSFollowAdFreeHotBtn;

#define kClickPlayFlag  @"isClickPlay"



typedef NS_ENUM(NSInteger, KSABTestFeedToSingGuideType)
{
    KSABTestFeedToSingGuideType_None = 0,                // 无
    KSABTestFeedToSingGuideType_ToRecord = 1,            // 直接录唱
    KSABTestFeedToSingGuideType_Chorus = 2,              // 万能合唱
    KSABTestFeedToSingGuideType_AddToOrderedList = 3,    // 加入已点
};

/// 大卡片滑动方向
typedef NS_ENUM(NSUInteger, KSRecFeedCardScrollDirection)
{
    KSRecFeedCardScrollDirectionNone,
    KSRecFeedCardScrollDirectionUp,
    KSRecFeedCardScrollDirectionDown,
};


@protocol KSLayoutableTimelineFeedCellV2AutoPlayDelegate <KSFeedCellVAutoPlayDelegate>

@optional

- (void)feedAutoPLayDidClickPlayOrPause:(KSLayoutableTimelineFeedCellV2 *)cell;

- (void)feedAutoScrollToNext:(KSLayoutableTimelineFeedCellV2 *)cell;

@end


@protocol KSLayoutableTimelineFeedCellV2CurrentPositionDelegate <NSObject>

@optional

- (void)feedCell:(KSLayoutableTimelineFeedCellV2 *)cell currentPosition:(NSTimeInterval)currentPosition duration:(NSTimeInterval)duration;

@end


@protocol KSLayoutableTimelineFeedCellV2AblumDelegate <NSObject>

@optional

- (void)feedCell:(KSLayoutableTimelineFeedCellV2 *)cell clickFeedAblumAnchorPoint:(KSimpleFeed *)feed;
- (void)feedCell:(KSLayoutableTimelineFeedCellV2 *)cell playStatusChange:(KSPlayStatus)status;

@end


@class KSTimeLineFeedLiveTipsView;
@class KSimpleFeedManager;

@interface KSLayoutableTimelineFeedCellV2 : KSLayoutableFeedBaseCell
<UICollectionViewDelegate,
UICollectionViewDataSource,
KSLayoutableTimelineFeedCellV2AutoPlayDelegate,
KSAudioPreviewPlayerDelegate,
KSFeedCellTagViewProtocol,
KSFollowKTVRoomFeedInteractGameContainerDelegate>

@property (nonatomic, weak) UIViewController *parentVC;  // cell容器VC
@property (nonatomic, assign) CGRect coverImageViewRect;
@property (nonatomic, strong) KSAdapterImageView *bgimageView; //背景图
@property (nonatomic, assign) NSUInteger lastBgHashValue;       //背景图的hashvalue
@property (nonatomic, strong) UIImageView *timelineVideoPlayingView;   // 整个视频区域的 container，包括视频播放 layer
@property (nonatomic, strong) KSAnimationPendantAvatarView *avatarView; //支持头像动画、挂件的头像
@property (nonatomic, strong) UIButton *clickedButton; //用于处理双击的交互
@property (nonatomic, strong) UILabel *debugLab;         // debuglabel
#ifdef INTERNALBUILD
@property (nonatomic, strong) UIButton *debugInfoButton;
#endif

//视频类
@property (nonatomic,strong) UILabel *songName;                 //歌名
@property (nonatomic,strong) UIImageView *splietLine;           //互动按钮分割线
@property (nonatomic,strong) KSBaseButton *actionBtn;           //各类互动按钮
@property (nonatomic,strong) KSTagView *ugcTypeView;            //ugc类型
@property (nonatomic,strong) UILabel  *chorusLabel;             // 合唱人数
@property (nonatomic,strong) UILabel *commentLable;             //评论
@property (nonatomic,strong) KSTagView *godCoverView;           //神仙翻唱标签
@property (nonatomic,strong) KSTagView *familyRecommendView;    //家族退家标签
@property (nonatomic,strong) KSTagView *recommendView;          //推荐 7.2推荐标签
@property (nonatomic,strong) KSTagView *rankView;               //评级
@property (nonatomic,strong) KSTagView *listenTagView;          //收听
@property (nonatomic,strong) UIView *recReasonAndListenNumView; // 2022新版视频feed推荐理由图片和收听人数信息view
@property (nonatomic,strong) UIView *leftTopTagView;           // 2022新版视频feed左上角标签图片
@property (nonatomic,strong) UILabel *bottomDescLabel;          //加入合唱
@property (nonatomic,strong) UILabel *beaterInfo;               //好友擂主信息
@property (nonatomic,strong) UIImageView *maskListenView;       //蒙版
// 这里只用到了这个类里面的视频layer和播放控制面板，实际上可以复用更多，后面可以考虑让搞个子类整个替换到cell里面
@property (nonatomic, strong) KSBasePlaybackView *videoPlayView;
@property (nonatomic, strong) UIButton *playButton;              //视频预览原来依赖playbutton,贴到playbutton
@property (nonatomic, strong) UIButton *audioPlayOrPauseButton;  /**< feed音频播放按钮 */
@property (nonatomic, strong) KSGlobePlayerButton *globalPlayBtn; //推荐卡片流用到全局播放按钮
@property (nonatomic, strong) KSFeedBizActionButton *recordActionButton; /**< 录唱相关按钮 */
@property (nonatomic, copy) void(^externalRecordActionButtonClick)(KSimpleFeed* simpleFeed); //外部实现录唱相关按钮点击事件（目前搜索合唱feed点击在搜索实现，涉及单独的上报等）

//推荐cell
@property (nonatomic, strong) KSFeedPlayBarV4 *playBarV4;            //伴奏条
@property (nonatomic, strong) KSRecFeedBottomBar *bottomBar;     // 底部条，热门活动配置用
@property (nonatomic, strong) KSRecCardLiveEnterPlayBar *liveEnterPlayBar;//直播推荐进房
@property (nonatomic, strong) KSRecCardShortPlayBar *shortPlayBar;
@property (nonatomic, strong) KSRecCardFeedVideolizeView *audioProductVideolizeView;  //音频视频化用到的gpuimage
@property (nonatomic, strong) KSRichLabel *descLabel;  //描述信息
@property (nonatomic, assign) BOOL hasShowVideolize;  //音频视频化用到的gpuimage

// 右边栏
@property (nonatomic, strong) UIView *rightAreaBar; // 右边栏（点赞，评论等按钮的父view）
@property (nonatomic, strong) KSRecFeedRightGiftIconView *giftIconView;  //送礼按钮(包含数字)
@property (nonatomic, strong) UIButton  *shareBtn;                   //分享按钮
@property (nonatomic, strong) UILabel   *shareTextLab;              //分享按钮底部文案“分享”
@property (nonatomic, strong) UIButton  *commentBtn;                //评论 按钮
@property (nonatomic, strong) UILabel   *commentLabel;               //评论 标签
@property (nonatomic, strong) UIButton  *praiseBtn;                    //点赞按钮
@property (nonatomic, strong) UILabel   *praiseCountLable;           //点赞数量
@property (nonatomic, strong) UIButton  *subscribeBtn;                //追剧 按钮
@property (nonatomic, strong) UILabel   *subscribeLabel;               //追剧 标签
@property (nonatomic, strong) KSRecFeedRightSingButton *singButton;    //我要唱

@property (nonatomic, strong) LOTAnimationView *subscribeAnimView;    //追剧 动画
@property (nonatomic, strong) KSLivingTagView *livingTagView;      //作品直播态标签
@property (nonatomic, weak)   KSimpleFeedManager   *feedManager;           //feed管理manager
@property (nonatomic, strong) KSFeedCellTagViewPlugin* tagviewPlugin;      //统一标签插件
@property (nonatomic, assign) BOOL isGiftDiffToHeatBtn; // 送礼按钮是否异化为上热门
@property (nonatomic, assign) BOOL isMediaPlaying;

// lottie动画view
@property(nonatomic,strong) LOTAnimationView *ktvCoverAnimateView; // 似乎没有初始化的地方了，暂时不敢删 2022.4.7
//关注cell
@property (nonatomic, strong) KSFollowKTVRoomFeedContainer *liteKtvContainerView; // 关注 ktv cell ,用于播放动态-关注feed音视频
@property (nonatomic, strong) KSFollowKTVRoomFeedOnlineNumContainer *liteKtvOnlineNumContainerView; // 关注 ktv cell ,用于播放动态-关注feed在线人数汇总
@property (nonatomic, strong) UIButton *liteKtvPlayOrSilenceButton;  // 关注 ktv cell ,播放，静音按钮按钮
@property (nonatomic,strong) UILabel  *liteKtvGameStatusLabel;       // 关注 ktv cell 玩法状态
@property (nonatomic) CGFloat  liteKtvNormalStatusWidth;       // 关注 ktv cell 玩法状态标签初始宽度

@property (nonatomic, strong) KSFollowKTVRoomFeedInteractGameContainer *interactGameContainer;

// 视频动画view
@property(nonatomic, strong) KSLayoutVideoAnimationView *videoAnimationView;

// 直播view
@property (nonatomic, strong) KSFeedLiveCardView *feedLiveCardView; // 新版推荐直播大卡片
@property (nonatomic, strong) KSFeedProductCardView *feedProductCardView; // 推荐商品卡片
@property (nonatomic, strong) KSLiveShowRecInfoView *liveShowRecView;   // 直播视图
@property (nonatomic, strong) KSTimeLineFeedLiveStreamView *livingStreamView;   // 直播流封面
@property (nonatomic, strong) KSVideoDisplayView *liveshowVideoView; // 直播动态封面or高光时刻回放
@property (nonatomic, strong) KSRecFeedLivingView *livingView;       //直播态
@property (nonatomic, strong) KSVideoDisplayView *liveVideoView; // 这个直播视图关注feed在用
@property (nonatomic, assign) BOOL isLiveVideoPlaying; // 当前直播动态封面是否在播放（数据上报用）
@property (nonatomic, assign) BOOL isLiveShowStreamFeed; // 直播
@property (nonatomic, assign) BOOL liveBtnClickPlaying; //手动触发播放
/// 视频广告 view
@property (nonatomic, strong) KSVideoDisplayView *advertiseVideoView;
@property (nonatomic, weak) KSVideoDisplayView *gameADVideoView; //
// 推荐歌房feeds收到刷新界面回调
@property (nonatomic, copy) void (^reloadCellBolck)(KSLayoutableTimelineFeedCellV2 *cell, BOOL isFeedNull);

// 歌房大卡片相关
@property (nonatomic, assign) BOOL isKtvRoomDetailToCard;
// 刷新状态
@property (nonatomic, assign) KSKTVRefreshState refreshState;
@property (nonatomic, strong) KSKTVRoomCardErrorView *ktvRoomCardErrorView;
@property (nonatomic, strong) KSKTVRoomCardLoadingView *ktvRoomCardLoadingView;
@property (nonatomic, strong) KSKTVRoomCardTableViewCell *ktvRoomCardBackCell;
@property (nonatomic, weak) KSKTVRoomCardDataManager *pageManager;
@property (nonatomic, strong) UIImage *socialKtvGameBackImage;  // KTV玩法背景图
@property (nonatomic, strong) UIImage *socialKtvBigStageBackImage; // 大舞台玩法背景图
@property (nonatomic, strong) NSMutableDictionary *ktvBackImageDic; // 游戏玩法背景图缓存
@property (nonatomic, strong) UIImageView *likeImageView; // 2022新版feed UI点赞按钮
@property (nonatomic, strong) UILabel *likeCountLabel; // 2022新版feed UI点赞数
// 进房引导
/// 引导展示倒计时
@property (nonatomic, strong) NSTimer *executeEnterRoomGuideTimer;
/// 自动进房倒计时
@property (nonatomic, strong) NSTimer *executeAutoEnterRoomTimer;


@property (nonatomic, strong) KSFoldedFeedsView *foldedFeedsView;

//7.8大卡片广告容器view
@property (nonatomic, strong) UIView *recCardAdView;
//7.9 大卡片负反馈
@property (nonatomic, strong) KSActionSheet *actionSheet;
// 8.18 VIP营销视图
@property (nonatomic, strong) KSRecFeedVipMarketingView *vipMarketingView;

@property (nonatomic, strong) KSFeedFullScreenLayouMgr *layoutMgr;//布局管理

@property (nonatomic, strong) UIView *infoPanelView; /**< 大卡片左下信息区容器视图 */ // 仅限推荐全屏大卡片
@property (nonatomic, assign) CGRect infoPanelViewFrame;

@property (nonatomic, strong) KSNearbyBubbleView *nearbyBubbleView; // 包含冒泡动画，也能展示性别年龄

// 直播大卡片停留时长上报
@property (nonatomic, assign) CFTimeInterval liveFeedRectEnterTime;

// 歌房大卡片停留时长上报
@property (nonatomic, assign) CFTimeInterval ktvRoomFeedRecEnterTime;

// AI唱推荐卡片停留时长上报
@property (nonatomic, assign) CFTimeInterval aiSongFeedRecEnterTime;

@property (nonatomic, strong) KSSlider  *progressSlider; //7.10大卡片进度条
@property (nonatomic, assign) BOOL isSliderUsing;
@property (nonatomic, strong) UIView *playTimeSeparate;//分割线
@property (nonatomic, strong) UILabel *leftCurrentPlayingTimeLabel;
@property (nonatomic, strong) UILabel *rightTotalTimeLabel;


@property (nonatomic, strong) UILabel *delimiter;

@property (nonatomic, weak) id<KSLayoutableTimelineFeedCellV2AutoPlayDelegate> previewPlayDelegate; /**< feed 自动播放 */
@property (nonatomic, weak) id<KSLayoutableTimelineFeedCellV2CurrentPositionDelegate> currentPositionDelegate;
@property (nonatomic, weak) id<KSLayoutableTimelineFeedCellV2AblumDelegate> collectionDelegate; //  推荐合唱合集

@property (nonatomic, assign) KSABTestFeedToSingGuideType feedToSingGuideType; /**< 录唱引导样式 */

@property (nonatomic, weak) UIViewController *currenVC;//弱引用当前所在vc
@property (nonatomic, strong) UIButton *liveAudioButton; //直播声音开关

@property (nonatomic, assign) KSRecFeedCardScrollDirection direction; // 滚动方向
@property (nonatomic, assign) BOOL iGiftBtnAnimating; //是否在呼吸态动画过程中

@property (nonatomic, strong) KSSongGodBigCardView *songGodCardView;

// 音乐心情轮播view
@property (nonatomic, strong) KSHorizonalBannerView *musicMoodBannerView;
// 音乐心情图文右上角图片数量label
@property (nonatomic, strong) UILabel *musicMoodPicNumLabel;
// 音乐心情图文右上角图片数量label的背景view
@property (nonatomic, strong) UIView *musicMoodPicNumLabelBgView;
// 音乐心情图文下方进度条
@property (nonatomic, strong) UIView *musicMoodPicProcessView;
// 音乐心情图文滑动引导view蒙版
@property (nonatomic, strong) UIView *musicMoodGuideMaskView;

/// 关注feed Ai图文 图片区域View
@property (nonatomic, strong) KSFeedAiImageGridView *feedAiImageGridView;
/// 推荐feed Ai图文 图片内容区域
@property (nonatomic, strong) KSRecAiImageContentView *recAiImageContentView;

// 大卡片底部异化引导（关注/送礼）
@property (nonatomic, strong) KSRecBottomSendGiftView *sendGiftGuideBar;
/// 大卡片底部异化引导（AI翻唱）
@property (nonatomic, strong) KSRecAICoverSongAIGuideView *aiCoverSongAIGuideBar;
/// 大卡片底部异化引导（AI拍同款）
@property (nonatomic, strong) KSRecBottomAITakePicBar *aiTakePicBar;
/// 关注feed拍同款
@property (nonatomic, strong) KSFollowFeedAITakePicView *followFeedAITakePicView;

// 好友多人生日送礼
@property (nonatomic, strong) KSMultipleFriendsSendGiftView *friendsSendGiftView;
// 单人生日送礼
@property (nonatomic, strong) KSSingleFriendSendGiftView *singleSendGiftView;
// 送礼按钮异化定时器
@property (nonatomic, strong) NSTimer *giftBtnBreathEffectTimer;

// 关注feed多人送礼
@property (nonatomic, strong) KSFollowFeedMultiSendGiftView *followMultiSendGiftView;
// 关注feed送礼按钮异化
@property (nonatomic, strong) KSBaseButton<KSFollowFeedSendGiftBtnProtocol> *followFeedSpecialSendGiftBtn;
// 关注feed异化荣誉榜
@property (nonatomic, strong) KSNewSinglesDetailHonorItemView *honorItemView;
// Ai歌声推广面板 TAPD: https://tapd.woa.com/10088931/prong/stories/view/1010088931116238157
@property (nonatomic, strong) KSAiSongRecPanel *aiSongRecPanel;
/// AI歌声引导发布面板
@property (nonatomic, strong) KSAISongPublishFeedPanel *aiSongPublishFeedPanel;

@property (nonatomic, strong) TMEGModuleLoader *moduleLoader;
@property (nonatomic, strong) proto_ktv_recfeed_webapp_KtvItem *nextItem;

@property (nonatomic, strong) TLMLiveMediaPlayer *liveMediaPlayer;
@property (nonatomic, nullable, strong) NSMutableDictionary *livePlayerModelDict;
/// 远端视频流回调map key:muid value:代理对象
@property (atomic, strong) NSMutableDictionary *videoDelegateDict;
@property (nonatomic, strong) NSLock *videoDelegateDictLock;

@property (nonatomic, strong) NSString *liveMediaPlayerRoomId;
@property (nonatomic, assign) BOOL liveMediaPlayerHaveReceivedFirstAudioFrame;
@property (nonatomic, assign) BOOL liveMediaPlayerHaveReceivedFirstVideoFrame;

@property (nonatomic, assign) BOOL forbidQuickGiftAndHonor;

@property (nonatomic, strong) KSFollowAdFreeHotBtn *freeHotBtn;
// AI翻唱引导定时器
@property (nonatomic, strong) NSTimer *aiCoverSongTimer;


#pragma mark - Func

// 视频feed 播放按钮
- (UIButton *)playButton;

- (KSVideoDisplayView *)videoView;

- (void)didVCWillAppear;

- (BOOL)isLottieViewPlaying;

- (BOOL)isVideoAnimationViewPlaying;

//播放是是否是当前feed
- (BOOL)isFeedCurrentPlayingUgc;

//布局播放按钮
- (void)layoutPlayBtnAndPauseBtn:(KSUIItem*)item;

//布局视频播放view
- (void)layoutVideoView;

//布局音频GPUView
- (void)layoutAudioVideolizeView:(KSimpleFeed*)simpleFeed;

//布局蒙版view
- (void)layoutMasListenView;

//监听播放状态变化的通知
- (void)timelineDetailPlayStatusChange:(NSNotification *)notification;

//推荐卡片支持关注态状态统一，监听通知保持状态一致
- (void)followStateChanged:(NSNotification *)notification;

//暂停预览音视频，进入详情页
- (void)playUgcBtnDidClicked:(UIButton*)sender;

//从当前feed开始播放
- (void)playUgcInBackgourndFromCurrentFeed:(KSimpleFeed *)feed;

//是否当前预览的feed
- (BOOL)isFeedPrviewCurrentPlayingUgc;

/// 开始单流Feed预览播放，包括视频、音频及KTV音频
- (void)startVideoPreviewPlay;

- (void)changeGradientMastState:(BOOL)playing;

//设置alpha值来显示或者隐藏subview
- (void)setUgcInfoSubviewAlpha:(CGFloat)alphaValue;

- (void)autoPlayForUserProfile;

// 设置子view 除播放视图的透明度
- (void)setSubviewExcludePlayViewAlpha:(CGFloat)alpha;

- (void)updateFeedAudioAndVideoPlayView;

- (void)processBackgroundComposite:(BOOL)enter;

- (void)removeAllSpecialSendGiftBtn;

// Feed 送礼样式异化
- (void)showFeedQuickGift;

- (void)didTapFollowSpecialGiftBtn:(UIButton *)sender;

@end


