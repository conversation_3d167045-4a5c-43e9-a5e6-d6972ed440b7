//
//  KSLayoutableTimelineFeedCellV2.m
//  QQKSong
//
//  Created by she<PERSON><PERSON><PERSON><PERSON> on 2019/6/15.
//  Copyright © 2019 Tencent. All rights reserved.
//

#import "KSNotificationDefines.h"
#import "KSLayoutableTimelineFeedCellV2.h"
#import "KSimpleFeed.h"
#import "KSNavigationManager.h"
#import "KSLayoutUIManagerTimeline.h"
#import "KSLayoutUIManagerTimeline+LayoutV3.h"
#import "KSTimelineRootVC+Statistic.h"
#import "KSTimelineRootVC+NewReport.h"
#import "KSLayoutableTableCell+View.h"
#import "KSCommonConfigDataKey.h"
#import "KSTimelineBaseVC+Statistic.h"
#import "JceTimeline_Detail_KGE_UGC_MASK_BIT.h"
#import "KSUserProfileVC_V2.h"
#import "KSUgcPlayManager.h"
#import "KSBasePlaybackView.h"
#import "KSNickIconView.h"
#import "KSDrawItemAction.h"
#import "KSCommonUIFactory.h"
#import "KSAdFreeGiftScene.h"
#import "KSTraceReportEnumV2.h"
#import "KSVideoChannelVC.h"
#import "KSTimelinePhotoCell.h"
#import "KSNetPhotoShowVC_V2.h"
#import "KSUserProfileVC_V2+Property.h"
#import "KSTraceReprotHelper_V2+Feed.h"
#import "KSTimelineRootVC+NewReport.h"
#import "KSFeedPreviewManager.h"
#import "KSUIItem.h"
#import "KSUserProfileVC_V2+TraceReport.h"
#import "KSAdapterImageView.h"
#import "kSSongGodBigCardView.h"
#import "KSABTestManager.h"
#import <BaseModule/UIButton+HitTestArea.h>
#import "KSStarChorusBorderView.h"
#import "KSLayoutableTimelineFeedCellV2+AutoPlay.h"
#import "KSRecCardActionButton.h"
#import "KSLottiePlayManager.h"
#import "KSASIHttpHelper.h"
#import "KSimpleFeedManager+Setting.h"
#import "KSUgcPlayManager+Player.h"
#import "KSStyleV2.h"
#import "KSLottieExposureControlManager.h"
#import "KSSingleSongRankListVC.h"
#import "KSSingleSongRankListVC+QualityRank.h"
#import "JceTimeline_enum_advertType.h"
#import "KSTimelineRootVC+VideoAutoPlay.h"
#import "KSTimelineRootVC+Action.h"
#import "KSNewSinglesDetailVC+Action.h"
#import "KSTmpAudioPlayManger.h"
#import "KSUgcPlayMgr.h"
#import "KSPreviewConfigMgr.h"
#import "KSFeedAdManager+KSFeed.h"
#import "KSimpleFeedManager+KSong.h"
#import "UIImageView+Web.h"
#import "KSLayoutUIManager_FeedCommonDefines.h"
#import "KSSingleSongRankListVC+QualityRank.h"

#import "KSShareActionReportMgr.h"
#import "FileUpload.h"
//7.0动态新增频谱

#import "KSUgcPlayManager_Private.h"
#import "KSUgcPlayManager+Hippy.h"
#import "KSLayoutableTimelineFeedCellV2+Lyric.h"
#import "KSAnimationSupport+KSong.h"
#import "KSUnitedSearchVC.h"
#import "KSLayoutableTimelineFeedCellV2+LayoutV2.h"
#import "KSLayoutableTimelineFeedCellV2+LayoutV3.h"
#import "UIImageView+VGImage.h"

#import "KSLayoutableTimelineFeedCellV2+RecFeed.h"
#import "KSLayoutableTimelineFeedCellV2+AutoPlay.h"
#import "KSUnitedSearchVC.h"
#import "KSCommonEnum.h"

#import "KSAudioPreviewManager.h"
#import "KSPreloadImageMgr.h"
#import "KSTimelineRootVC+Action.h"//点赞动画需要
#import "KSimpleFeedManager+audioPrdVideolize.h"
#import "KSimpleFeedManager+Gift.h"
#import "KSTimelineForward.h"
#import "KSLayoutInfo.h"
#import "KSFeedLayoutConfig.h"
#import "KSong+Common.h"
#import "KSTraceReportModel_V2.h"
#import "KSDetailPlaybackModel.h"
#import "KSCommonModel.h"
#import "KSLoginManager.h"
#import "KSimpleFeedMacros.h"
#import "KSUIABTestConstants.h"
#import "JceTimeline_cell_hc.h"
#import "KSUIABTestManager.h"
#import "JceTimeline_cell_ktv.h"
#import "KSSongExtraInfo.h"
#import "JceTimeline_cell_ktv_mike.h"
#import "JceTimeline_cell_like.h"
#import "KSPaySoloAlbumManager.h"
#import <KSIMLogDef.h>
#import "KSVideoPreviewPlayManager.h"
#import "KSTimelineManager+Gift.h"

#import "KSTimelineRootVC+VideoAutoPlay.h"
#import "KSTimeLineFeedLiveTipsView.h"
#import "KSUgcPlayManager+LockScreen.h"
#import "KSBigCardVideolizeSwitch.h"
#import "KSTimeLineFeedLiveStreamView.h"
#import "KSLivingTagView.h"
#import "KSLiveShowRecInfoView.h"
#import "KSKTVPreviewManager.h"
#import "KSRedPackedDialog.h"
#import "JceTimeline_EnumAWardCode.h"
#import "JceTimeline_UserInfo.h"
#import "JceTimeline_enum_filter_mask.h"
#import "KSLayoutableTimelineFeedCellV2+TraceReport.h"
#import "JceTimeline_s_feed_list_item.h"
#import "KSFeedBizActionButton.h"
#import "KSFeedCellTagViewPlugin.h"
#import "KSAdapterImageView+KSBigCard.h"
#import <Anunnaki/Anunnaki.h>
#import "KSImageListBrowserVC.h"
#import "KSLayoutableTimelineFeedCellV2+RecFeed.h"
#import "KSLayoutableTextView.h"
#import "KSBackgroundCompositeManager.h"
#import "JceTimeline_Detail_UgcTopic.h"
#import "KSAppDelegate+PlayConflict.h"
#import "KSUgcStarChorusManager.h"
#import <KSFoundation/KSDateFormatterHelper.h>
#import "KSImageDownloadManager.h"
#import "KSLayoutableTimelineFeedCellV2+KtvRoomCard.h"
#import "KSLayoutableTimelineFeedCellV2+BirthdaySendGift.h"
#import "KSLayoutableFeedBaseCell+ContinueClickFlower.h"
#import "KSHorizonalBannerConfig.h"
#import "KSHorizonalBannerItemData.h"
#import "KSLottiePlayAssetLocalModel.h"
#import "KSRootTabBarController.h"
#import "KSFeedCellQuickGiftButton.h"
#import "KSFeedCellFreeAdButton.h"
#import "KSimpleFeedManager+Action.h"
#import "KSimpleFeedManager+Gift.h"
#import "KSRecCardShortPlayBar.h"
#import "proto_ktv_recfeed_webapp_KtvItem.h"
#import "KSRecFeedBottomGuideFrequencyControlMgr.h"
#import "KSFollowKTVRoomFeedContainer.h"
#import "KSLayoutableTimelineFeedCellV2+FollowLiteKTV.h"
#import "proto_feed_webapp_ktv_game_status.h"
#import "KSFollowKTVRoomFeedOnlineNumContainer.h"
#import "JceTimeline_KTV_ROOM_STATUS_TYPE_ENUM.h"
#import "KSPublicKtvGameHelper.h"
#import "KSFollowKTVRoomFeedInteractGameContainer.h"
#import "proto_feed_webapp_game_status_comm.h"
#import "proto_unified_ktv_game_CommonWebGameInfo.h"
#import "proto_feed_webapp_game_status_webview.h"
#import "KSPlayletRequestHelper.h"
#import "proto_feed_webapp_cell_mini_show.h"

#import "WnsConfigManager+KSBusiness.h"
#import "KSLayoutableTimelineFeedCellV2+CustomView.h"
#import "KSFollowFeedMultiSendGiftView.h"
#import "KSFollowFeedSendGiftBtnProtocol.h"
#import "KSNewSinglesDetailHonorCell.h"
#import "KSNSDHonorCellViewModel.h"
#import "PROTO_UGC_WEBAPP_UgcRankDetail.h"
#import "KSFollowFeedRushGiftListButton.h"
#import "proto_feed_webapp_GetDataAfterExposureRsp.h"
#import "proto_feed_webapp_ENUM_DATA_TYPE_AFTER_EXP.h"
#import "KSTimelineBaseVC+IapGift.h"
#import "KSIapGiftManager.h"
#import "KSFeedProductCardView.h"
#import "KSLayoutableTimelineFeedCellV2+AiSongRec.h"
#import "KSLayoutableTimelineFeedCellV2+AiImageCard.h"
#import <libpag/PAGView.h>
#import "KSRecCardLiveEnterPlayBar.h"
#import "KSGlobalSizeManager+Small.h"
#import "KSAISongPublishFeedPanel.h"
#import "KSFollowAdFreeHotBtn.h"
#import "KSAdFreeHotCardManager.h"
#import "JceTimeline_cell_mini_heat_card.h"
#import "JceTimeline_s_user.h"
#import "JceTimeline_cell_userinfo.h"

static NSString * collectionCellId = @"UICollectionViewCell";

// 【7.34】 音乐心情改成滚动图片时的用户引导 是否已显示
static NSString *kMusicMoodUserGuideHasShowKey = @"kMusicMoodUserGuideHasShowKey";

#define kMusicMoodUserGuideViewTag 9527

@interface KSLayoutableTimelineFeedCellV2 ()
<KSIconViewActionDelegate,
KSTimelinePhotoCellDelegate,
KSVideoDisplayViewDelegate,
AnuDirectorDelegate,
KSUgcPlayManagerDelegate,
CAAnimationDelegate,
KSBasePlaybackViewDelegate,
KSVideoPreviewPlayManagerDelegate,
KSFoldedFeedsViewDelegate,
KSKTVPreviewManagerDelegate,
KSImageListBrowserVCDelegate,
KSHorizonalBannerViewDelegate,
KSFollowKTVRoomFeedContainerDelegate>


// 这里只用到了这个类里面的视频layer和播放控制面板，实际上可以复用更多，后面可以考虑让搞个子类整个替换到cell里面
//@property (nonatomic, strong) KSBasePlaybackView *videoPlayView;

//转发feed的ugcid和普通作品的ugcid不同，这里用strUgcId作为中间adapter
@property(nonatomic,strong) NSString* strUgcId;
@property(nonatomic,strong) NSString* strShareId;
@property(nonatomic,strong) NSString* strForwardFeedId;

@property(nonatomic,strong) CAAnimation *playButtonRotateAnimation;
@property(nonatomic,strong) CAShapeLayer *playButtonRotateLayer;

//7.0动态改版新增
@property (nonatomic, strong) UIView  *adFeedmoveOutButton;
@property (nonatomic, strong) GPUImageView *renderView;//频谱载体

//9宫格Item
@property(nonatomic,weak) KSUIItem* grid9Item;


@property (nonatomic, assign) NSTimeInterval lastPosition;

@end


@implementation KSLayoutableTimelineFeedCellV2

- (CAAnimation *)playButtonRotateAnimation
{
    if (!_playButtonRotateAnimation)
    {
        CABasicAnimation *animation = [CABasicAnimation animationWithKeyPath:@"transform.rotation.z"];
        animation.duration = 1;
        animation.repeatCount = 1000000;
        animation.timingFunction = [CAMediaTimingFunction functionWithName: kCAMediaTimingFunctionLinear];
        animation.fromValue = [NSNumber numberWithFloat:0];
        animation.toValue = [NSNumber numberWithFloat:M_PI * 2];
        _playButtonRotateAnimation = animation;
    }
    
    return _playButtonRotateAnimation;
}

- (CAShapeLayer *)playButtonRotateLayer
{
    if (!_playButtonRotateLayer)
    {
        _playButtonRotateLayer = [CAShapeLayer layer];
        _playButtonRotateLayer.lineWidth = 2;
        _playButtonRotateLayer.frame = CGRectMake(0, 0, 32, 32);
        _playButtonRotateLayer.anchorPoint = CGPointMake(0.5, 0.5);
        
        UIBezierPath *path = [UIBezierPath bezierPath];
        _playButtonRotateLayer.strokeColor = UIColor.blackColor.CGColor;
        [path addArcWithCenter:CGPointMake(16, 16) radius:16 startAngle:-M_PI * 0.5 endAngle:-M_PI * 0.25 clockwise:YES];
        _playButtonRotateLayer.path = path.CGPath;
    }
    
    return _playButtonRotateLayer;
}

- (void)setContent:(KSLayoutInfo *)info withDelegate:(id<KSLayoutableTableCellDelegate>)delegate
{
    if (info && info == self.simpleFeed.layoutInfo)
    {
        //如果布局没有更新，这里不刷新了，避免频繁setcontent,这里的风险就是刷新界面的时候要将layout设置nil，否则不刷新界面
        return;
    }
    
    KSimpleFeed *simpleFeed = (KSimpleFeed *)self.busiData;
    self.simpleFeed = simpleFeed;
    self.strUgcId = simpleFeed.simpleFeedCommon.strFeedId;
    self.strForwardFeedId = simpleFeed.forwardFeed.strForwardId;
    self.strShareId = simpleFeed.simpleFeedCommon.strShareid;
    
    //父类会异步加载子view,子类相关逻辑写在asynSetContentFinishBlock里面
    [super setContent:info withDelegate:delegate];
    
    // 短视频封面清理
    KSVideoDisplayView *videoView = SAFE_CAST([self.subviewItems objectForKey:@(TimelineFeedCellLiveVideoShowView)], KSVideoDisplayView);
    if (!videoView.superview && videoView) {
        videoView.delegate = nil;
        [[videoView player] pause];
        videoView = nil;
    }
    
    UIView *songContainerView = [self.contentView viewWithTag:TimelineFeedCellAudioSongContainerView];
    if ([self isFeedCurrentPlayingUgc] &&
        [KSUgcPlayManager sharedManager].isUgcPlaying) { // 处于全局播放 && 正在播放
        // 全局播放该feed时，如果开了自动播放，feed是暂停状态且无歌词
        // 如果没开自动播放，feed是播放态，并且歌词滚动
        if ([self.simpleFeed isRecCardFeed]) {
            [self show2022FeedUGCInfoOrLyric:NO delay:NO animate:NO source:1];
        } else {
            [self show2022FeedUGCInfoOrLyric:[self isFeedAutoPlay] delay:NO animate:NO source:1];
        }
        self.lastPosition = [[KSUgcPlayManager sharedManager] currentPlayingTime];
        
    } else if ([self isFeedAutoPlay] &&
               [self isFeedPrviewCurrentPlayingUgc] &&
               ([KSFeedPreviewManager sharedManager].currentPreviewPlayingType != PreviewPlayingTypeNone ||
               [[KSFeedPreviewManager sharedManager].audioPreviewManager fetchAudioPlayerStatus] == KSAudioPlayerStatus_Playing)) {
        // 如果开启了自动播放，且该feed正处于自动播放中
        if ([self.simpleFeed isKindOfAudioTypeFeed]) {
            [self show2022FeedUGCInfoOrLyric:NO delay:NO animate:NO source:2];
        } else {
            songContainerView.alpha = 0;
        }
        self.lastPosition = [[KSFeedPreviewManager sharedManager] currentTime];
        
    } else {
        /// 显示歌名
        BOOL showNameInfo = ![self.simpleFeed isRecCardFeed];
        [self show2022FeedUGCInfoOrLyric:showNameInfo delay:NO animate:NO source:3];
    }
    
    if (self.liveAudioButton) {
        for (KSUIItem* item in info.items) {
            if ([item isKindOfClass:[KSVideoDisplayItem class]] && ((KSVideoDisplayItem*)item).viewType == DRAWITEM_CUSTOM_ADD_LiveFeedVoice) {
                [self refreshLiveAudioPlayButton:item];
                break;
            }
        }
    }
}

// 广告Abtest动画
- (void)layoutADFeedAbTeatAnimation:(KSLayoutInfo*)info
{
    if (self.simpleFeed.isShowParkingAnim && self.simpleFeed.feedAd) {
        self.simpleFeed.isShowParkingAnim = NO;
        
        UIView *moveOutButton = nil;
        UIView *moveInButton = nil;
        for (KSUIItem *item in info.items)
        {
            if (item.tag == TimelineFeedCellAdLinkText) {
                moveInButton = [self viewDictionary:self.subviewItems objectForLayoutItem:item];
            } else if (item.tag == TimelineFeedCellAdFeedRightDescButton) {
                moveOutButton = [self viewDictionary:self.subviewItems objectForLayoutItem:item];
            }
            if (moveOutButton && moveInButton) {
                break;
            }
        }
        self.adFeedmoveOutButton = moveOutButton;
        if (moveInButton && moveOutButton) {
            moveInButton.backgroundColor = [UIColor ks_colorWithRGBHex:0xFE4F4F];
            [KSAnimationSupport scaleTransitionOutView:moveOutButton scaleInView:moveInButton delegate:nil];
        }
    }
}

- (BOOL)isLottieViewPlaying
{
    return self.ktvCoverAnimateView.isAnimationPlaying;
}

- (BOOL)isVideoAnimationViewPlaying
{
    return self.videoAnimationView.isPlaying;
}

#pragma mark 布局视频feed
- (void)layoutVideoFeed:(KSimpleFeed *)simpleFeed withUIItem:(KSUIItem *)item
{
    // 布局整个视频区域的 container，包括视频以及下方灰色区域的额外信息
    if (!self.timelineVideoPlayingView) {
        self.timelineVideoPlayingView = [[UIImageView alloc] init];
        self.timelineVideoPlayingView.userInteractionEnabled = YES;
    }
    self.timelineVideoPlayingView.frame = item.rect;
    
    [self.contentView addSubview:self.timelineVideoPlayingView];
    
    //封面
    [self layoutCoverImageV2:simpleFeed];
    
    //视频view改为预览了，预览的时候才布局视频view

    // 信息蒙版
    [self layoutMasListenView];
    
    // ugc相关信息
    // 布局视频歌名 + 推荐理由图片 + 收听人数
    [self layoutSongInfo_2022:simpleFeed];
    
    //播放按钮
    [self layoutPlayBtnAndPauseBtn:item];
    
    //
    [[NSNotificationCenter defaultCenter] removeObserver:self name:KSNotification_PlayStatusDidChange object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(timelineDetailPlayStatusChange:) name:KSNotification_PlayStatusDidChange object:nil];
    
    // 处理听原唱音频抢占处理，这里只是临时处理，不要使用该方法判断听原唱的相关信息
    [[NSNotificationCenter defaultCenter] removeObserver:self name:KSNotification_AuidoStartPlaying object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(timelineDetailPlayStatusChange:) name:KSNotification_AuidoStartPlaying object:nil];
}

// 音频作品的播放按钮，只布局播放按钮，rect全部为按钮的区域
- (void)layoutAudioPlayBtn:(CGRect)rect {
    if (self.simpleFeed.layoutConfig.isHiddenPlayAction) {
        // 详情页推荐feed不显示播放按钮
        return;
    }
    
    if (!self.audioPlayOrPauseButton) {
        self.audioPlayOrPauseButton = [KSBaseButton buttonWithType:UIButtonTypeCustom];
        // KTV模式用类似MV的Feed
        [self.audioPlayOrPauseButton addTarget:self action:@selector(audioPlayOrPauseButtonClick) forControlEvents:UIControlEventTouchUpInside];
        self.audioPlayOrPauseButton.accessibilityLabel = KString(@"播放或暂停");
        self.audioPlayOrPauseButton.contentHorizontalAlignment = UIControlContentHorizontalAlignmentFill;
        self.audioPlayOrPauseButton.contentVerticalAlignment = UIControlContentVerticalAlignmentFill;
    }
    [self.audioPlayOrPauseButton setImage:[self getPlayAndStopButtonImage:YES black:YES] forState:UIControlStateNormal];
    [self.audioPlayOrPauseButton setImage:[self getPlayAndStopButtonImage:NO black:YES] forState:UIControlStateSelected];
    self.audioPlayOrPauseButton.frame = CGRectMake(rect.origin.x, rect.origin.y, rect.size.height, rect.size.width);
    [self.contentView addSubview:self.audioPlayOrPauseButton];
    
    KSJumpToOtherSceneFrom sceneFrom = [[KSNavigationManager sharedManager] canJumpFromCurrentToOtherScene];
    if (sceneFrom != KSJumpOtherSceneFrom_NO) {
        self.audioPlayOrPauseButton.userInteractionEnabled = NO;
    } else {
        //这里和商业化逻辑冲突，统一这里处理
        if (self.simpleFeed.layoutConfig.disablePlayAction) {
            // self.audioPlayOrPauseButton.userInteractionEnabled = NO;
        } else {
            self.audioPlayOrPauseButton.userInteractionEnabled = YES;
        }
    }
}

- (UIView *)videoView
{
    if (self.simpleFeed.feedAd.jce_advertType == JceTimeline_enum_advertType_AdvertTypeVideo || self.simpleFeed.feedAd.jce_advertType == JceTimeline_enum_advertType_AdvertTypeVerticalVideo
        || [self.simpleFeed isRecCardAdFeed] || [self.simpleFeed isRecCardTMEAdFeed])
    {
        UIView * videoView = self.advertiseVideoView;
        if (self.simpleFeed.amsFeedAd) {
            GDTMediaView *amsVideoView = [[KSFeedAdManager sharedManager] getVideoViewAtCell:self];
            if (amsVideoView) {
                videoView = amsVideoView;
            }
        }
        else if (self.simpleFeed.tmeFeedAd){
            TMEAdMediaView * tmeAdVideoView = [[KSFeedAdManager sharedManager] getTMEAdVideoViewAtCell:self];
            if (tmeAdVideoView) {
                videoView = tmeAdVideoView;
            }
        }
        return videoView;
    }
    return self.liveVideoView;
}

// MARK:布局视频按钮
- (void)layoutPlayBtnAndPauseBtn:(KSUIItem *)item
{
    if (self.simpleFeed.layoutConfig.isHiddenPlayAction)
    {
        //详情页推荐feed不显示播放按钮
        return;
    }
    //播放按键,这里不复用，复用有bug,视频预览播放的view是加在playbtn上面,复用有问题，会加载
    self.playButton = [KSBaseButton buttonWithType:UIButtonTypeCustom];
    self.playButton.accessibilityLabel = KString(@"播放");
    
    //解决reloadtable闪过playbtn的时候上面有btnImage的问题
    if ([self.simpleFeed isKindOfGameAdvertTypeFeed]) {
        self.playButton.hidden = YES;
    }
    if ([self.simpleFeed isRecCardFeed]) { // 大卡片
        UIImage *playButtonImage = [self.simpleFeed isRecCardFeed] ? [UIImage imageNamed:@"V7feed_play_80_fullScreen"] : [UIImage imageNamed:@"V7feed_play_80"];
        [self.playButton setImage:playButtonImage forState:UIControlStateNormal];
    } else { // 普通feed
        [self.playButton setImage:[self getPlayAndStopButtonImage:YES black:NO] forState:UIControlStateNormal];
        [self.playButton setImage:[self getPlayAndStopButtonImage:NO black:NO] forState:UIControlStateSelected];
    }
    
    CGFloat sizeW = _size_mid(28);
    self.playButton.frame = CGRectMake(self.timelineVideoPlayingView.right - 14 - sizeW, self.timelineVideoPlayingView.bottom - 14 - sizeW, sizeW, sizeW);
    [KSCommonUIFactory applyRoundCornerForView:self.playButton radius:kFeedImageRadiusCorner];
    
    if ([self.simpleFeed.songinfo isKTVAudioUGC]) {
        [self.playButton addTarget:self action:@selector(audioPlayOrPauseButtonClick) forControlEvents:UIControlEventTouchUpInside];
    } else if (UGC_TYPE(self.simpleFeed.songinfo.ugcMask) == AUDIO_UGC) {
        [self.playButton addTarget:self action:@selector(playUgcBtnDidClicked:) forControlEvents:UIControlEventTouchUpInside];
    } else {
         [self.playButton addTarget:self action:@selector(playButtonClickForVideo) forControlEvents:UIControlEventTouchUpInside];
    }
    
    [self.contentView addSubview:self.playButton];
}

- (void)processBackgroundComposite:(BOOL)enter
{
}

//布局音频GPUView
- (void)layoutAudioVideolizeView:(KSimpleFeed *)simpleFeed
{
    if (![simpleFeed audioShouldVideolize]) {
        [self.audioProductVideolizeView removeFromSuperview];
        self.audioProductVideolizeView = nil;
        return;
    }
    
    if (self.audioProductVideolizeView == nil) {
        /// 第一次创建的时候就确定下来 KSRecCardFeedVideolizeView 的尺寸，
        /// 后面只控制显示和隐藏，不要去修改 frame 。否则后台修改 frame 会造成 crash
        self.audioProductVideolizeView = [[KSRecCardFeedVideolizeView alloc] initWithFrame:self.bgimageView.bounds];
    }

    [self.timelineVideoPlayingView addSubview:self.audioProductVideolizeView];
    [self.feedManager resetRenderViewWithFeed:simpleFeed videolizeView:self.audioProductVideolizeView];
}

/// 布局大卡片或者Feed视频播放view
- (void)layoutVideoView
{
    static NSMutableArray *videoRenderViews;
    if (!videoRenderViews) {
        videoRenderViews = [NSMutableArray new];
    }
    
    NSString *uniqueID = self.simpleFeed.simpleFeedCommon.strFeedId ?: @"";
    if ([self.simpleFeed isRecCardFeed]) {
        uniqueID = [NSString stringWithFormat:@"rec_card_%@", uniqueID];
    }
    /// 优化复用导致的闪烁
    for (KSBasePlaybackView *item in videoRenderViews) {
        if ([item.uniqueID isEqualToString:uniqueID]) {
            KDEBUG(@"find video play view with uniqueID:%@ self:%p", uniqueID, self);
            self.videoPlayView = item;
            break;
        }
    }

    if (self.videoPlayView == nil) {
        KSBasePlaybackViewStyle style = [self.simpleFeed isRecCardFeed] ? KSBasePlaybackViewStyle_RecCardFeedFullScreen : KSBasePlaybackViewStyle_FollowFeed;
        self.videoPlayView = [[KSBasePlaybackView alloc] initWithFrame:self.timelineVideoPlayingView.bounds
                                                         playViewStyle:style
                                                               playMgr:nil];
        // 隐藏loadingView
        self.videoPlayView.loadingView.hidden = YES;
        [self.videoPlayView addSubview:self.videoPlayView.loadingView];
        self.videoPlayView.backgroundColor = nil;
        self.videoPlayView.videoView.backgroundColor = nil;
        self.videoPlayView.clipsToBounds = YES;
        self.videoPlayView.layer.cornerRadius = [self.simpleFeed isRecCardFeed] ? 0 : 8;
    }
    
    self.videoPlayView.playbackViewDelegate = self;
    [self.videoPlayView updatUgcId:self.simpleFeed.simpleFeedCommon.strFeedId];
    [self.videoPlayView addSubview:self.videoPlayView.videoView];
    self.videoPlayView.frame = self.timelineVideoPlayingView.bounds;
    [self.timelineVideoPlayingView addSubview:self.videoPlayView];
    
    /// uniqueID(ugcId) <-> videoView 映射
    self.videoPlayView.uniqueID = uniqueID;
    NSInteger renderViewNum = 4;
    if ([videoRenderViews containsObject:self.videoPlayView]) {
        [videoRenderViews removeObject:self.videoPlayView];
        [videoRenderViews safeAddObject:self.videoPlayView];
    } else {
        if (videoRenderViews.count >= renderViewNum) {
            [videoRenderViews safeRemoveObjectAtIndex:0];
        }
        [videoRenderViews safeAddObject:self.videoPlayView];
    }
    
    /// 非大卡片场景
    if (![self.simpleFeed isRecCardFeed]){
        [self.videoPlayView updateVideoViewFrame:self.videoPlayView.bounds];
        
        if ([self isFeedCurrentPlayingUgc]) {
            /// 需要重新贴setPlaybackView
            if (self.currenVC && [[KSNavigationManager sharedManager] getTopViewController] == self.currenVC) {
                [[KSUgcPlayManager sharedManager] setPlaybackView:self.videoPlayView];
                [KSUgcPlayManager sharedManager].delegate = self;
            }
        }
    }
}

//布局蒙版view
- (void)layoutMasListenView
{
    if (!self.maskListenView) {
        self.maskListenView = [[UIImageView alloc] init];
        [self.contentView addSubview:self.maskListenView];
    }
    CGFloat radius = kFeedImageRadiusCorner;
    NSInteger listentMaskHeight = DYNAMIC_VALUE_ForAllScreen(_size_mid(55));
    if ([self.simpleFeed isRecCardFeed]) {
        radius = kBgImageRadiusCorner;
        listentMaskHeight = DYNAMIC_VALUE_ForAllScreen(250);
    }
    
    CGFloat originY = self.timelineVideoPlayingView.bottom - listentMaskHeight;
    CGFloat sizeW = self.timelineVideoPlayingView.width;
    CGRect frame = CGRectMake(self.timelineVideoPlayingView.x, originY, sizeW, listentMaskHeight);
    self.maskListenView.frame = frame;
    [self.maskListenView setMaskType:KSMaskType_GradientBottomToTop];
    [self.contentView addSubview:self.maskListenView];
    if (![self.simpleFeed isRecCardFeed]) {
        [KSCommonUIFactory applyRoundCornerForView:self.maskListenView radius:radius byRoundingCorners:UIRectCornerBottomLeft|UIRectCornerBottomRight];
    }
    
}

- (void)didClickOnActionBtn:(UIButton *)sender {
    KSDrawItemAction *action = [KSDrawItemAction new];
    action.type = sender.tag;
    action.busiData = (KSimpleFeed*)self.busiData;
    [self handleAction:action];
}

// 音频feed点击事件
- (void)audioPlayOrPauseButtonClick
{
    KSimpleFeed* simpleFeed = (KSimpleFeed*)self.busiData;
    
    if ([self isFeedAutoPlay])
    {
        // Feed 单流自动播放
        [self playOrPauseFeedAutoPlay];
    }
    else if ([[KSFeedPreviewManager sharedManager] currentPreviewPlayingType] != PreviewPlayingTypeNone)
    {
        // 关闭下异常场景下的feed自动播放
        [self forcePauseFeedAutoPlay];
    }
    else
    {
        // 提示开启流量下自动播放
        if (self.audioPlayOrPauseButton.isSelected == NO && [self isCurrentFeedAutoPlayView])
        {
            [[KSPreviewConfigMgr sharedManager] checkToShowFeedAutoPlayAlertWithPlayingStatus:NO];
        }
        if (self.audioPlayOrPauseButton.isSelected) {
            [self pauseButtonClickForAudio];
        } else {
            [self playButtonClickForAudio];
        }
        
        [[[KSNavigationManager sharedManager] getTimelineRootVC] playUGCInBackground:self];
    }
    
    //上报
    UIViewController* topVC = [[KSNavigationManager sharedManager] getTopViewController];
    if ([topVC isKindOfClass:[KSUserProfileVC_V2 class]])
    {
        KSUserProfileVC_V2* userProfileVC = SAFE_CAST(topVC, KSUserProfileVC_V2);
        KSTraceReportModel_V2* passModel = [KSTraceReportModel_V2 new];
        passModel.commonInt1 = self.audioPlayOrPauseButton.isSelected ? 2 : 1; //1：点击后播放 2:点击后暂停;
        passModel.commonInt3 = self.indexPath.row+1; //下标从1开始;
        passModel.commonInt7 = simpleFeed.simpleUser.userinfo.userId;
        [userProfileVC onUgcFeedClickPlayButtonWithSimpleFeed:simpleFeed passReportModel:passModel];
        
    }
    else if([topVC isKindOfClass:[KSTimelineRootVC class]])
    {
        KSTimelineRootVC *rootVC = [[KSNavigationManager sharedManager] getTimelineRootVC];
        [rootVC onUgcFeedClickPlayButtonWithSimpleFeed:simpleFeed];
    }
    else if ([topVC isKindOfClass:[KSSingleSongRankListVC class]])
    {
        KSSingleSongRankListVC* songRankVC = SAFE_CAST(topVC, KSSingleSongRankListVC);
        [songRankVC onUgcFeedClickPlayButtonWithSimpleFeed:simpleFeed cell:self];
    }
    else if ([topVC isKindOfClass:[KSUnitedSearchVC class]])
    {
        KSUnitedSearchVC* songRankVC = SAFE_CAST(topVC, KSUnitedSearchVC);
        [songRankVC onUgcFeedClickPlayButtonWithSimpleFeed:simpleFeed cell:self];
    }
    
    if ([simpleFeed isMiniHeatCardFeed]) {
        [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
            reportModel.key = @"feed_creation#xtg_recommend#but#click#0";
            reportModel.commonInt1 = simpleFeed.miniHeatCardItem.jce_lNum;
            reportModel.commonInt2 = 4;
            reportModel.ugcid = simpleFeed.miniHeatCardItem.jce_strUgcid;
            reportModel.touid = simpleFeed.miniHeatCardItem.jce_userinfo.jce_user.jce_lUid;
            reportModel.mid = simpleFeed.songinfo.songMid;
        }];
    }
}

- (void)liveAudioButtonClick
{
    self.liveAudioButton.selected = !self.liveAudioButton.selected;
    if (self.liveAudioButton.selected) {
        self.liveBtnClickPlaying = NO;
        [self.liveVideoView setMute:YES];
    } else {
        if (!self.liveVideoView.isPlaying) {
            [self.liveVideoView resume];
        }
        [self.liveVideoView setMute:NO];
        self.liveBtnClickPlaying = YES;
        [self playOrPauseFeedAutoPlay];
    }
}

//监听播放状态变化的通知 (setContent即cellforRow也会调用该方法)
- (void)timelineDetailPlayStatusChange:(NSNotification *)notification
{
    if ([self.simpleFeed isRecCardFeed] == NO && ([self isFeedAutoPlay] || self.liveVideoView.isPlaying))
    {
        BOOL needUpdate = !notification || [notification.name isEqualToString:KSNotification_AuidoStartPlaying];
        if (needUpdate) {
            [self updateFeedAudioAndVideoPlayView];
        }
        
        if ([KSAppDelegate isContentPlaying]) {
            if (![self.simpleFeed isKindOfVideoAdvertisementFeed]) {
                // 切换至全局播放时，停止feed自动播放并刷新UI
                // 关注feed视频广告可以静音播放
                [self processStopMediaPreviewPlayWhen:KSTimelineStopVideoWhenGlobalUGCPlay];
            }
        }
        return;
    }
    
    KSimpleFeed *simpleFeed = (KSimpleFeed *)self.busiData;
    
    if (simpleFeed.ugcRemark)
    {
        [self reorderSubviewsForAudio];
    }
    else if (UGC_TYPE(simpleFeed.songinfo.ugcMask) == MUSIC_VIDEO_UGC &&
             simpleFeed.songinfo.audioTransVideoPlayType != JceTimeline_EnumAudioTransVideoPlayType_eAudioTransVideo_type_native_tempalte_video)
    {
        [self reorderSubviewsForVideo];
    }
    else
    {
        [self reorderSubviewsForAudio];
    }
}

//当前feed是否跟全局播放feedID一样
- (BOOL)isFeedCurrentPlayingUgc
{
    KSGlobalPlayItem *playItem = [KSUgcPlayManager sharedManager].passPlayItem;
    //这里播放列表经常没有forwardid,导致播放状态不同步，这里放开，只有两个关注好友转发同一feed,这时候两个feed都会呈现播放状态,暂时先这样
    if (([self.strUgcId isEqualToString:playItem.ugcId] || [self.strShareId isEqualToString:playItem.ugcId])
        && ((!playItem.strForwardId) || (playItem.strForwardId.length>0 && [self.strForwardFeedId isEqualToString:playItem.strForwardId])))
    {
        return YES;
    }
    else
    {
        return NO;
    }
}

- (BOOL)isFeedPrviewCurrentPlayingUgc
{
    KSDetailPlaybackModel *playbackModel = [KSFeedPreviewManager sharedManager].playbackModel;
    
    //这里播放列表经常没有forwardid,导致播放状态不同步，这里放开，只有两个关注好友转发同一feed,这时候两个feed都会呈现播放状态,暂时先这样
    if (([self.strUgcId isEqualToString:playbackModel.playbackInfo.ugcId] || [self.strShareId isEqualToString:playbackModel.playbackInfo.ugcId]))
    {
        return YES;
    }
    else
    {
        return NO;
    }
}

- (void)changeGradientMastState:(BOOL)playing
{
    CAGradientLayer *gl;
    for (CALayer *layer in [self.contentView viewWithTag:TimelineFeedCellSongCover].layer.sublayers) {
        if ([layer isKindOfClass:[CAGradientLayer class]])
        {
            gl = (CAGradientLayer *)layer;
            break;
        }
    }
    
    if (gl)
    {
        [CATransaction begin];
        [CATransaction setDisableActions:YES];
        UIColor *color = [UIColor ks_colorWithHexString:self.simpleFeed.songinfo.strMagicColor];
        if (playing)
        {
            gl.colors = @[(id)[color colorWithAlphaComponent:1].CGColor, (id)color.CGColor];
        }
        else
        {
            gl.colors = @[(id)[color colorWithAlphaComponent:0].CGColor, (id)color.CGColor];
        }
        [CATransaction commit];
    }
}

//刷新成暂停态
- (void)refreshToPauseStateUI
{
    self.audioPlayOrPauseButton.hidden = NO;
    self.audioPlayOrPauseButton.selected = NO;
    [self changeGradientMastState:NO];
    self.playButton.selected = NO;
    self.playButton.accessibilityLabel = @"开始播放";
    [self.audioPlayOrPauseButton setAccessibilityLabel:KString(@"开始播放")];
    [self.audioPlayOrPauseButton setImage:[self getPlayAndStopButtonImage:YES black:YES] forState:UIControlStateNormal];
    
    [self show2022FeedUGCInfoOrLyric:YES delay:NO animate:NO source:4];
}

// 音频播放按钮(由 全局播放/预览播放 状态变化的通知)
- (void)reorderSubviewsForAudio
{
    self.globalPlayBtn.selected = NO;
    BOOL isFeedPlaying = [self isFeedCurrentPlayingUgc];
    //不管什么状态默认移除
    [self.playButtonRotateLayer removeAllAnimations];
    [self.playButtonRotateLayer removeFromSuperlayer];
    if (isFeedPlaying) { // 如果当前feed是全局播放的feed
        KSPlayStatus status = [KSUgcPlayManager sharedManager].currentPlayStatus;
        if (status == KSPlayStatus_Playing) { // 播放中
            self.audioPlayOrPauseButton.hidden = NO;
            self.audioPlayOrPauseButton.selected = YES;
            [self.audioPlayOrPauseButton setImage:[self getPlayAndStopButtonImage:YES black:YES] forState:UIControlStateNormal];
            [self changeGradientMastState:YES];
            [self.audioPlayOrPauseButton setAccessibilityLabel:KString(@"暂停播放")];
            //bugfix播放的时候不展示图标

            self.globalPlayBtn.selected = YES;
            self.playButton.selected = YES;
            self.audioPlayOrPauseButton.selected = YES;
            self.globalPlayBtn.accessibilityLabel = @"双击可暂停";
            self.playButton.accessibilityLabel = @"双击可暂停";
            self.lastPosition = [[KSUgcPlayManager sharedManager] currentPlayingTime];

            [self show2022FeedUGCInfoOrLyric:NO delay:NO animate:NO source:5];
            
        } else if (status == KSPlayStatus_Prepare || status == KSPlayStatus_Buffering) {
            // 加载中
            [self.audioPlayOrPauseButton setAccessibilityLabel:KString(@"双击可暂停")];
            [self addRotateLayerWithButton:self.audioPlayOrPauseButton isAudio:YES]; // 加载动画
        } else {
            [self refreshToPauseStateUI];
        }
    } else {
        if ([self isFeedAutoPlay] &&
            [self isFeedPrviewCurrentPlayingUgc] &&
            [KSFeedPreviewManager sharedManager].currentPreviewPlayingType == PreviewPlayingTypeAudio) {
            [self.audioPlayOrPauseButton setAccessibilityLabel:KString(@"双击可暂停")];
            self.audioPlayOrPauseButton.selected = YES;
            [self show2022FeedUGCInfoOrLyric:NO delay:NO animate:NO source:17];
        } else {
            [self refreshToPauseStateUI];
        }
    }
    
    [self reorderLyricView:self.simpleFeed];

    // 大卡片音频才走这个逻辑
    if ([self.simpleFeed isRecCardAudioFeed]) {

        KSPlayStatus status = [KSUgcPlayManager sharedManager].currentPlayStatus;
        self.globalPlayBtn.selected = NO;
        if(isFeedPlaying)
        {
            switch (status) {
                case KSPlayStatus_Prepare:
                case KSPlayStatus_Buffering:
                {
                    [self removeSubviewsPlayingView];
                    
                    [self.timelineVideoPlayingView addSubview:self.bgimageView];
                    [self.timelineVideoPlayingView addSubview:self.audioProductVideolizeView];
                    //缓冲的时候不添加playbutton
                    self.playButton.hidden = YES;
                }
                    break;
                case KSPlayStatus_Playing:
                {
                    [self removeSubviewsPlayingView];
                    
                    [self.timelineVideoPlayingView addSubview:self.bgimageView];
                    [self.timelineVideoPlayingView addSubview:self.audioProductVideolizeView];
                    //bugfix播放的时候不展示图标
                    self.playButton.hidden = YES;
                    self.globalPlayBtn.selected = YES;
                    self.lastPosition = [[KSUgcPlayManager sharedManager] currentPlayingTime];
                }
                    break;
                case KSPlayStatus_Pause:
                {
                    [self removeSubviewsPlayingView];
                    [self.timelineVideoPlayingView addSubview:self.bgimageView];
                    [self.timelineVideoPlayingView addSubview:self.audioProductVideolizeView];
                    
                    BOOL isSameFeed = [self.simpleFeed isSameWithSimpleFeed:self.feedManager.curSimpleFeed];
                    self.playButton.hidden = !isSameFeed;
                    UIImage *playButtonImage = [self.simpleFeed isRecCardFeed]?[UIImage imageNamed:@"V7feed_play_80_fullScreen"]:[UIImage imageNamed:@"V7feed_play_80"];
                    [self.playButton setImage:playButtonImage forState:UIControlStateNormal];
                }
                    break;
                case KSPlayStatus_Stop:
                {
                    [self removeSubviewsPlayingView];
                    [self.timelineVideoPlayingView addSubview:self.bgimageView];
                    [self.timelineVideoPlayingView addSubview:self.audioProductVideolizeView];
                    self.playButton.hidden = [KSUgcPlayManager sharedManager].isPlayerStatusEnding;
                }
                default:
                    break;
            }
        }
        else
        {
            self.playButton.hidden = YES;
        }
    }

    if ([self.simpleFeed isRecCardAdFeed] || [self.simpleFeed isAiSongRec]  || [self.simpleFeed isAISongPublish] || ([self.simpleFeed isRecCardFeed] && [self.simpleFeed isKindOfKTVRoomFeed]))
    {
        self.playButton.hidden = YES;
    }
}

//点击后延迟渲染，看代码是为了避免画面没有渲染出来闪过黑色背景
- (void)ResetVideoPlayViewDelay
{
    if ([[KSNavigationManager sharedManager] isCurrentVCTopVC:self.currenVC])
    {
        KLog(@"[视频view]setup[feed]%@",self.strUgcId);
        [[KSUgcPlayManager sharedManager] setPlaybackView:self.videoPlayView];
    }
}

- (void)hiddenUgcInfoWithAnimation:(BOOL)hidden {
    if (hidden) {
        [self performSelector:@selector(hiddenUgcInfoWithAnimationDelay) withObject:nil afterDelay:3];
    } else {
        [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(hiddenUgcInfoWithAnimationDelay) object:nil];
        [UIView animateWithDuration:0.3 animations:^{
            [self setUgcInfoSubviewAlpha:1];
        }];
    }
}

- (void)hiddenUgcInfoWithAnimationDelay {
    [UIView animateWithDuration:0.3 animations:^{
        [self setUgcInfoSubviewAlpha:0];
    }];
}

- (void)reorderSubviewsForVideo
{
    BOOL isRecCard = [self.simpleFeed isRecCardFeed];

    //不管什么状态默认移除
    [self.playButtonRotateLayer removeAllAnimations];
    [self.playButtonRotateLayer removeFromSuperlayer];
    if ([self isFeedCurrentPlayingUgc]) {
        KSPlayStatus status = [KSUgcPlayManager sharedManager].currentPlayStatus;
        
        //个人页改版后有bug这里改为当前cell所在的vc是topVC并当前cell可见才播放视频，否则return
        if (![self isCellVisibleAndCellOfVCPresentTopInVCStack:self block:nil])
        {
            if ([self.simpleFeed isRecCardFeed] && status == KSPlayStatus_Playing)
            {
                //修复大卡片bug，详情页播放，动态这里还是暂停，退出到动态页没有刷新
                self.playButton.hidden = YES;
            }
            
            if (status == KSPlayStatus_Playing)
            {
                self.playButton.selected = YES;
                self.playButton.accessibilityLabel = @"双击可暂停";
                
                [self hiddenUgcInfoWithAnimation:YES];
                self.lastPosition = [[KSUgcPlayManager sharedManager] currentPlayingTime];
            }
            else
            {
                self.playButton.selected = NO;
                self.playButton.accessibilityLabel = @"双击可暂停";
                
                [self hiddenUgcInfoWithAnimation:NO];
            }
            return;
        }
        
        self.videoPlayView.isInFeedCell = YES;
        self.videoPlayView.reportTabIndex = [KSTimelineRootVC getTimeLineRootVCTab];
        
        // 需要主动刷一遍播放面板内部状态
        [self.videoPlayView updatePlayingStatus:status];
        
        self.globalPlayBtn.selected = NO;
        
        switch (status) {
            case KSPlayStatus_Prepare:
            case KSPlayStatus_Buffering:
            {
                [self removeSubviewsPlayingView];

                [self.timelineVideoPlayingView addSubview:self.bgimageView];
                [self.timelineVideoPlayingView addSubview:self.videoPlayView];
                //缓冲的时候不添加playbutton
                if (isRecCard) {
                    self.playButton.hidden = YES;
                    if (![KSimpleFeedManager enableFeedAutoPlay] && ![KSUgcPlayManager sharedManager].shouldRecCardResume) {
                        self.playButton.hidden = NO;
                    }
                } else {
                    [self addRotateLayerWithButton:self.playButton isAudio:NO]; // 加载动画
                }
            }
                break;
            case KSPlayStatus_Playing:
            {
                [self removeSubviewsPlayingView];

                [self.timelineVideoPlayingView addSubview:self.bgimageView];
                [self.timelineVideoPlayingView addSubview:self.videoPlayView];
                if (isRecCard || UGC_TOSING(self.simpleFeed.songinfo.ugcMask))
                {
                    self.playButton.hidden = YES;
                }
                else
                {
                    [self.contentView addSubview:self.playButton];
                    
                    [self hiddenUgcInfoWithAnimation:YES];
                }
                if (!isRecCard) {
                    self.globalPlayBtn.selected = YES;
                    self.playButton.hidden = NO;
                    self.playButton.selected = YES;
                    self.playButton.accessibilityLabel = @"双击可暂停";
                    [self.playButton setImage:[self getPlayAndStopButtonImage:YES black:NO] forState:UIControlStateNormal];
                    [self setUgcInfoSubviewAlpha:0];
                }
                
                self.lastPosition = [[KSUgcPlayManager sharedManager] currentPlayingTime];
            }
                break;
            case KSPlayStatus_Pause:
            {
                [self removeSubviewsPlayingView];
     
                [self.timelineVideoPlayingView addSubview:self.bgimageView];
                [self.timelineVideoPlayingView addSubview:self.videoPlayView];
                if (isRecCard)
                {
                    BOOL isSameFeed = [self.simpleFeed isSameWithSimpleFeed:self.feedManager.curSimpleFeed];
                    self.playButton.hidden = !isSameFeed;
                    UIImage *playButtonImage = [self.simpleFeed isRecCardFeed]?[UIImage imageNamed:@"V7feed_play_80_fullScreen"]:[UIImage imageNamed:@"V7feed_play_80"];
                    [self.playButton setImage:playButtonImage forState:UIControlStateNormal];
                    UIViewController* topVC = [[KSNavigationManager sharedManager] getTopViewController];
                    if ([topVC isKindOfClass:[KSTimelineRootVC class]])
                    {
                        KSTimelineRootVC *rootVC = [[KSNavigationManager sharedManager] getTimelineRootVC];
                        if (![rootVC isViewInTheMiddle:self] && [KSimpleFeedManager enableFeedAutoPlay])
                        {
                            self.playButton.hidden = YES;
                        }
                    }
                }
                else
                {
                    [self.contentView addSubview:self.playButton];
                    [self hiddenUgcInfoWithAnimation:NO];
                }
                
                self.playButton.selected = NO;
                self.playButton.accessibilityLabel = @"开始播放";
            }
                break;
            case KSPlayStatus_Stop:
            {
                [self removeSubviewsPlayingView];
                [self.timelineVideoPlayingView addSubview:self.bgimageView];
                [self.timelineVideoPlayingView addSubview:self.videoPlayView];

                if (isRecCard)
                {
                    self.playButton.hidden = [KSUgcPlayManager sharedManager].isPlayerStatusEnding;
                    UIImage *playButtonImage = [self.simpleFeed isRecCardFeed]?[UIImage imageNamed:@"V7feed_play_80_fullScreen"]:[UIImage imageNamed:@"V7feed_play_80"];
                    [self.playButton setImage:playButtonImage forState:UIControlStateNormal];
                }
                else
                {
                    [self hiddenUgcInfoWithAnimation:NO];
                    [self.playButton setImage:[self getPlayAndStopButtonImage:YES black:NO] forState:UIControlStateNormal];
                }
                
                self.playButton.selected = NO;
                self.playButton.accessibilityLabel = @"开始播放";
            }
                break;
            default:
                break;
        }
    }
    else
    {
        [self removeSubviewsPlayingView];
        [self.timelineVideoPlayingView addSubview:self.bgimageView];
        [self.timelineVideoPlayingView addSubview:self.videoPlayView];

        if (isRecCard)
        {
            self.playButton.hidden = YES;
        }
        else
        {
            self.recReasonAndListenNumView.hidden = NO;
            self.songName.hidden = NO;
            self.rankView.hidden = NO;
            self.listenTagView.hidden = NO;
            self.ugcTypeView.hidden = NO;
            self.godCoverView.hidden = NO;
            self.familyRecommendView.hidden = NO;
            self.recommendView.hidden = NO;
            self.chorusLabel.hidden = NO;
            self.beaterInfo.hidden = NO;
            
            self.playButton.selected = NO;
            self.playButton.accessibilityLabel = @"开始播放";
            
            [self hiddenUgcInfoWithAnimation:NO];
            [self.playButton setImage:[self getPlayAndStopButtonImage:YES black:NO] forState:UIControlStateNormal];
        }
    }
    
    if ([self.simpleFeed isRecCardAdFeed] || [self.simpleFeed isAiSongRec] || [self.simpleFeed isAISongPublish] || ([self.simpleFeed isRecCardFeed] && ([self.simpleFeed isKindOfLiveShowFeed] || [self.simpleFeed isKindOfKTVRoomFeed] || [self.simpleFeed isKindOfProductCardFeed])))
    {
        self.playButton.hidden = YES;
    }
}

// timelineDetailPlayStatusChange调用
- (void)updateFeedAudioAndVideoPlayView
{
    if (([self.simpleFeed isRecCardFeed] == NO) && [self isFeedAutoPlay]) {
        // Feed设置对应控件状态
        BOOL isPlaying = [KSFeedPreviewManager sharedManager].currentPreviewPlayingType != PreviewPlayingTypeNone;
        BOOL isPlayingCurFeed = isPlaying && [[KSFeedPreviewManager sharedManager] isPlayingCurrentFeed:self.simpleFeed];

        if ([self.simpleFeed isKindOfAudioTypeFeed]) {
            [self show2022FeedUGCInfoOrLyric:!isPlayingCurFeed delay:NO animate:NO source:7];
        }
        
        self.playButton.hidden = NO;
        self.playButton.selected = isPlayingCurFeed;
        self.audioPlayOrPauseButton.selected = isPlayingCurFeed;
        self.recReasonAndListenNumView.alpha = isPlayingCurFeed ? 0 : 1;
        self.leftTopTagView.alpha = isPlayingCurFeed ? 0 : 1;

        if (self.videoPlayView) {
            [self.videoPlayView removeFromSuperview];
            self.videoPlayView = nil;
        }
    }
}

- (void)showOrHideSubviewAbovePlayingView:(BOOL)visibleStatus
{    
    self.ugcTypeView.alpha = visibleStatus ? 0 : 1;
    self.commentLable.alpha = visibleStatus ? 0 : 1;

}

- (BOOL)isStrForwardFeedIdEqual:(NSString*) strForwardId detailVCForwardId:(NSString *) detailVCForforwardId
{
    if (!strForwardId && ! detailVCForforwardId)
    {
        return YES;
    }
    else if(strForwardId && detailVCForforwardId && [strForwardId isEqualToString:detailVCForforwardId])
    {
        return YES;
    }
    else
    {
        return NO;
    }
}

- (void)autoPlayForUserProfile
{
    if (!([self isFeedCurrentPlayingUgc] && [KSUgcPlayManager sharedManager].isUgcPlaying))
    {
        KSGlobalPlayItem* playItem = [[KSGlobalPlayItem alloc] initWithFeed:self.simpleFeed];
        playItem.fromPage = @"homepage_guest#creation#friends_update_entry";
        [[KSUgcPlayManager sharedManager] playUgcWithItem:playItem showPlayVC:NO];
        
        if (UGC_TYPE(self.simpleFeed.songinfo.ugcMask) == VIDEO_UGC)
        {
            [self layoutVideoView];
            [self.timelineVideoPlayingView addSubview:self.bgimageView];
            [self.timelineVideoPlayingView addSubview:self.videoPlayView];
            
            if ([[KSNavigationManager sharedManager] isCurrentVCTopVC:self.currenVC])
            {
                KLog(@"[视频view]setup[feed]%@",self.strUgcId);
                [[KSUgcPlayManager sharedManager] setPlaybackView:self.videoPlayView];
            }
            [self setCurFeedWhenPlayUgc:self.simpleFeed];
        }
    }
}

//开始音频播放
- (void)playButtonClickForAudio {
    KSimpleFeed *simpleFeed = (KSimpleFeed *)self.busiData;
    
    [self show2022FeedUGCInfoOrLyric:NO delay:NO animate:YES source:18];
    
    if ([self isFeedCurrentPlayingUgc])
    {
        //同一个ugc从不同的路径进来，
        [KSUgcPlayManager sharedManager].passPlayItem.isPlayedByRecFeed = NO;
        [self playUgcInBackgourndFromCurrentFeed:simpleFeed];
        if (![[KSUgcPlayManager sharedManager] isUgcPlaying])
        {
            [[KSUgcPlayManager sharedManager] playControl];
        }
    }
    else
    {
        [self playUgcInBackgourndFromCurrentFeed:simpleFeed];
        if (!UGC_IS_SHORT_AUDIO(simpleFeed.songinfo.ugcMaskExt))
        {
//            self.audioPlayOrPauseButton.hidden = YES;
        }
    }
    
    //上报
    NSUInteger reserves = [KSTimelineRootVC getTimeLineRootVCReservesWithAction:KSTimelineRootVCMusicPlay];
    NSInteger commonInt1 = [KSTimelineRootVC getTimeLineRootVCTab];
    [KSTimelineBaseVC reportAudioVideoPlayBtnDidClickedForContributed:simpleFeed reserve:reserves int1:commonInt1 int2:0];
    
    [KSTimelineBaseVC reportAudioVideoPlayBtnDidClicked:simpleFeed reserve:reserves];
    
    [self audioUGCPayRightReport:simpleFeed strUgcId:self.strUgcId];
    
    [self setCurFeedWhenPlayUgc:simpleFeed];
    [KSUgcPlayManager sharedManager].delegate = self;
}

//大卡片暂停预览音视频，进入详情页
- (void)playUgcBtnDidClicked:(UIButton*)sender
{
    KSimpleFeed *simpleFeed = (KSimpleFeed*)self.busiData;
    
    [[KSFeedPreviewManager sharedManager] pausePreview];
           
    KSGlobalPlayItem *playItem = [[KSGlobalPlayItem alloc] initWithFeed:simpleFeed];
    playItem.passSeekTime = 0;//全局播放进详情页都是从头播放
    [[KSUgcPlayManager sharedManager] playUgcWithItem:playItem];
    
    [self setCurFeedWhenPlayUgc:simpleFeed];

}

- (void)playButtonClickForVideo
{
    //上报问题修复20180828
    KSimpleFeed *simpleFeed = (KSimpleFeed *)self.busiData;
    
    if ([self isFeedAutoPlay]) {
        // Feed 单流自动播放
        [self playOrPauseFeedAutoPlay];
    }
    else if ([[KSFeedPreviewManager sharedManager] currentPreviewPlayingType] != PreviewPlayingTypeNone)
    {
        // 关闭下异常场景下的feed自动播放
        [self forcePauseFeedAutoPlay];
    }
    else
    {
        // 提示开启流量下自动播放
        if (self.playButton.isSelected == NO && [self isCurrentFeedAutoPlayView])
        {
            [[KSPreviewConfigMgr sharedManager] checkToShowFeedAutoPlayAlertWithPlayingStatus:NO];
        }
        
        // 保护下播放列表被清空的情况
        if ([self isFeedCurrentPlayingUgc] && [[KSUgcPlayManager sharedManager] isCurGlobalItemInPlayList])
        {
            //同一个ugc从不同的路径进来，
            [KSUgcPlayManager sharedManager].passPlayItem.isPlayedByRecFeed = NO;
            [self layoutVideoView];
            if ([[KSUgcPlayManager sharedManager] isUgcPlaying])
            {
                [[KSUgcPlayManager sharedManager] pauseControl];
            }
            else
            {
                [[KSUgcPlayManager sharedManager] playControl];
            }
            [self setCurFeedWhenPlayUgc:simpleFeed];
        }
        else
        {
            [self layoutVideoView];
            [self playUgcInBackgourndFromCurrentFeed:simpleFeed];
            [self.timelineVideoPlayingView addSubview:self.bgimageView];
            [self.timelineVideoPlayingView addSubview:self.videoPlayView];
            KLog(@"[视频view]setup[feed]%@",self.strUgcId);
            [[KSUgcPlayManager sharedManager] setPlaybackView:self.videoPlayView];
            [KSUgcPlayManager sharedManager].delegate = self;
            [self setCurFeedWhenPlayUgc:simpleFeed];
        }
        
        [[[KSNavigationManager sharedManager] getTimelineRootVC] playUGCInBackground:self];
        
        [self performSelector:@selector(ResetVideoPlayViewDelay) withObject:nil afterDelay:0.2];
    }
    
    // 上报
    NSUInteger reserves = [KSTimelineRootVC getTimeLineRootVCReservesWithAction:KSTimelineRootVCMusicPlay];
    NSInteger commontInt1 = [KSTimelineRootVC getTimeLineRootVCTab];
    [KSTimelineBaseVC reportAudioVideoPlayBtnDidClickedForContributed:simpleFeed reserve:reserves int1:commontInt1 int2:0];
    [KSTimelineBaseVC reportAudioVideoPlayBtnDidClicked:simpleFeed reserve:reserves];
    
    [self videoUGCPayRightReport:simpleFeed strUgcId:self.strUgcId];
    
    if (UGC_TYPE(simpleFeed.songinfo.ugcMask) ==  MUSIC_VIDEO_UGC)
    {
        UIViewController* topVC = [[KSNavigationManager sharedManager] getTopViewController];
        if ([topVC isKindOfClass:[KSUserProfileVC_V2 class]])
        {
            KSUserProfileVC_V2* userProfileVC = SAFE_CAST(topVC, KSUserProfileVC_V2);
            KSTraceReportModel_V2* passModel = [KSTraceReportModel_V2 new];
            passModel.commonInt1 = 1; //1：点击后播放 2:点击后暂停;
            passModel.commonInt3 = self.indexPath.row+1; //下标从1开始;
            passModel.commonInt7 = simpleFeed.simpleUser.userinfo.userId;
            [userProfileVC onUgcFeedClickPlayButtonWithSimpleFeed:simpleFeed passReportModel:passModel];
            
        }
        else if ([topVC isKindOfClass:[KSTimelineRootVC class]])
        {
            KSTimelineRootVC *rootVC = [[KSNavigationManager sharedManager] getTimelineRootVC];
            [rootVC onUgcFeedClickPlayButtonWithSimpleFeed:simpleFeed];
        }
        else if ([topVC isKindOfClass:[KSSingleSongRankListVC class]])
        {
            KSSingleSongRankListVC* songRankVC = SAFE_CAST(topVC, KSSingleSongRankListVC);
            [songRankVC onUgcFeedClickPlayButtonWithSimpleFeed:simpleFeed cell:self];
        }
        else if ([topVC isKindOfClass:[KSUnitedSearchVC class]])
        {
            KSUnitedSearchVC* songRankVC = SAFE_CAST(topVC, KSUnitedSearchVC);
            [songRankVC onUgcFeedClickPlayButtonWithSimpleFeed:simpleFeed cell:self];
        }

    }
}

//暂停音频播放
- (void)pauseButtonClickForAudio
{
    if ([[KSUgcPlayManager sharedManager] isUgcPlaying])
    {
        [[KSUgcPlayManager sharedManager] pauseControl];
    }
    
    //投稿作品暂停播放上报
    KSimpleFeed *simpleFeed = (KSimpleFeed*)self.busiData;
    NSUInteger reserves = [KSTimelineRootVC getTimeLineRootVCReservesWithAction:KSTimelineRootVCMusicPlay ];
    NSInteger commonInt1 = [KSTimelineRootVC getTimeLineRootVCTab];
    [KSTimelineBaseVC reportAudioVideoPlayBtnDidClickedForContributed:simpleFeed reserve:reserves int1:commonInt1 int2:1];
}

- (void)removeSubviewsPlayingView
{
    for (UIView *view in self.timelineVideoPlayingView.subviews) {
        [view removeFromSuperview];
    }
}
- (void)cleanContent
{
    [super cleanContent];
    if (WnsLocalServerBoolConfig(@"DisableFollowCellRestoreNil")) {
        KLog(@"DisableFollowCellRestoreNil");
        return;
    }
    self.audioProductVideolizeView = nil;
    self.rightAreaBar = nil;
    self.recCardAdView= nil;
    self.clickedButton= nil;
    self.singButton= nil;
    self.praiseBtn= nil;
    self.praiseCountLable= nil;
    self.commentBtn= nil;
    self.commentLabel= nil;
    self.subscribeBtn= nil;
    self.subscribeLabel= nil;
    self.subscribeAnimView= nil;
    self.giftIconView = nil;
    self.shareBtn = nil;
    self.shareTextLab= nil;
    self.timelineVideoPlayingView=nil;
    self.playButton=nil;
    self.audioPlayOrPauseButton= nil;
    self.playTimeSeparate= nil;
    self.liveShowRecView= nil;

    self.shortPlayBar=nil;
    self.playBarV4=nil;
    self.bottomBar=nil;
    self.progressSlider=nil;
    self.leftCurrentPlayingTimeLabel=nil;
    self.rightTotalTimeLabel=nil;
    
    self.livingTagView=nil;
    
    self.liteKtvContainerView= nil;
    
    self.liteKtvOnlineNumContainerView= nil;
    
    self.liteKtvPlayOrSilenceButton= nil;
    
    self.liteKtvGameStatusLabel= nil;
    
    
    self.interactGameContainer=nil;

    
    self.doubleLineLyricveiw=nil;
    
}

- (void)prepareForReuse
{
    [super prepareForReuse];
    [[NSNotificationCenter defaultCenter] removeObserver:self];
    [NSObject cancelPreviousPerformRequestsWithTarget:self];
    [self removeSubviewsPlayingView];
    
    [self.audioProductVideolizeView removeFromSuperview];
    [self.rightAreaBar removeFromSuperview];
    [self.recCardAdView removeFromSuperview];
    [self.clickedButton removeFromSuperview];
    [self.singButton removeFromSuperview];
    [self.praiseBtn removeFromSuperview];
    [self.praiseCountLable removeFromSuperview];
    [self.commentBtn removeFromSuperview];
    [self.commentLabel removeFromSuperview];
    [self.subscribeBtn removeFromSuperview];
    [self.subscribeLabel removeFromSuperview];
    [self.subscribeAnimView stop];
    [self.subscribeAnimView removeFromSuperview];
    [self.giftIconView removeFromSuperview];
    [self.shareBtn removeFromSuperview];
    [self.shareTextLab removeFromSuperview];
    [self.timelineVideoPlayingView removeFromSuperview];
    [self.playButton removeFromSuperview];
    [self.recordActionButton removeFromSuperview];
    [self.audioPlayOrPauseButton removeFromSuperview];
    [[KSUgcPlayManager sharedManager] removeDelegate:self];
    
    [self.playTimeSeparate removeFromSuperview];
    [self.livingView removeFromSuperview];
    [self.liveShowRecView removeFromSuperview];

    [self.shortPlayBar removeFromSuperview];
    [self.playBarV4 removeFromSuperview];
    [self.bottomBar removeFromSuperview];
    [self.progressSlider removeFromSuperview];
    [self.leftCurrentPlayingTimeLabel removeFromSuperview];
    [self.rightTotalTimeLabel removeFromSuperview];
    
    [self.livingTagView removeFromSuperview];
    UIView *songContainerView = [self.contentView viewWithTag:TimelineFeedCellAudioSongContainerView];
    songContainerView.hidden = NO;
    songContainerView.alpha = 1;
    
    [self.liteKtvContainerView removeFromSuperview];
    self.liteKtvContainerView.delegate = nil;
    
    [self.liteKtvOnlineNumContainerView removeFromSuperview];
    
    [self.liteKtvPlayOrSilenceButton removeFromSuperview];
    
    [self.liteKtvGameStatusLabel removeFromSuperview];
    
    self.liteKtvNormalStatusWidth = 0;
    
    [self.interactGameContainer removeFromSuperview];
    self.interactGameContainer.delegate = nil;

    // 清除歌词
    [self.doubleLineLyricveiw removeFromSuperview];
    self.doubleLineLyricveiw.alpha = 0;
    
    // 送礼、荣誉榜异化
    [self.followFeedSpecialSendGiftBtn prepareForReuse];
    self.followFeedSpecialSendGiftBtn = nil;
    [self.honorItemView removeFromSuperview];
    self.honorItemView = nil;
    [self.followFeedAITakePicView removeFromSuperview];
    self.followFeedAITakePicView = nil;
    [self.aiTakePicBar removeFromSuperview];
    self.aiTakePicBar = nil;
    
    
    [self.ugcTypeView removeFromSuperview];
    self.ugcTypeView = nil;
    [self.songName removeFromSuperview];
    
    [self.maskListenView removeFromSuperview];
    self.maskListenView = nil;
    [self.chorusLabel removeFromSuperview];
    self.chorusLabel = nil;
    
    [self.beaterInfo removeFromSuperview];
    [self.commentLable removeFromSuperview];
    [self.actionBtn removeFromSuperview];
    self.actionBtn = nil;               //这里actionBtn不重用，状态太多
    [self.recommendView removeFromSuperview];
    self.recommendView = nil;
    [self.godCoverView removeFromSuperview];
    self.godCoverView = nil;
    [self.familyRecommendView removeFromSuperview];
    self.familyRecommendView = nil;
    [self.rankView removeFromSuperview];
    self.rankView = nil;
    [self.tagviewPlugin cleanWhilePrepareReused];
    self.tagviewPlugin = nil;
    [self.recReasonAndListenNumView removeAllSubviews];
    [self.recReasonAndListenNumView removeFromSuperview];
    self.recReasonAndListenNumView = nil;
    [self.leftTopTagView removeFromSuperview];
    self.leftTopTagView = nil;

    [self.descLabel removeFromSuperview];
    self.globalPlayBtn.progress = 0.0;//复用的时候进度设置为0
    [self.globalPlayBtn setNeedsDisplay];

    [self.collectionView removeFromSuperview];
        
    [self.foldedFeedsView removeFromSuperview];
    
    [self.nearbyBubbleView removeFromSuperview];
    //暂停预加载
    NSString* imageUrl = [self.simpleFeed getImageUrlFromEachKindOfFeed];
    KLog(@"[预加载]feedCover取消，ugcId=%@ songName=%@",self.simpleFeed.simpleFeedCommon.strFeedId,self.simpleFeed.songinfo.name);
    [[KSPreloadImageMgr defaultManager] removePreloadTask:imageUrl];
    
    if ([KSimpleFeedManager enablePreloadCommonFeedFirstFrame])
    {
        [[KSImageDownloadManager defaultManager] removeTask:self.simpleFeed.songinfo.strIFrameUrl delegate:nil];

        KDEBUG(@"[Feed] 取消首帧图预下载:%@", self.simpleFeed.songinfo.strIFrameUrl)
    }
    
    // MARK: 在这里重置了信息
    self.simpleFeed = nil;
    [self.layoutMgr prepareForReuse];
    self.layoutMgr = nil;
    //重用的时候取消对应图片的下载
    
    [self.bgimageView cleanPreRenderedFirstFrame];
    [self.bgimageView stopCurrentDownloadTask];
    [self.bgimageView removeLastDownloadTask:self.bgimageView.ksImageUrl];
    self.bgimageView.image = nil;
    
    [self.bgimageView.internalImageView stopCurrentDownloadTask];
    [self.bgimageView.internalImageView removeLastDownloadTask:self.bgimageView.ksImageUrl];
    self.bgimageView.internalImageView.image = nil;
    self.bgimageView.ksImageUrl = nil;
    self.bgimageView.ksFirstFrameImgUrl = nil;
    
    self.bgimageView.enableBGGaussianBlur = NO;
    self.bgimageView.videoSize = CGSizeZero;
    self.lastBgHashValue = self.bgimageView.hash;
    self.bgimageView = nil;//这里的bgimageview太复杂了，大卡片和普通feed cellId不一样居然会复用，这里不复用了
    [self.videoPlayView updatUgcId:nil];
    self.videoPlayView = nil;
    
    [self.avatarView.avatarImageView stopCurrentDownloadTask];
        
    //复用的时候cell布局的层次关系需要恢复默认
    self.drawItemTextDrawAtFront = NO;
    self.direction = KSRecFeedCardScrollDirectionNone;
    
    [self cleanAdFeedShowTag];
    
    [self.liveAudioButton removeFromSuperview];
    [self.liveVideoView removeFromSuperview];
    
    [self.playButtonRotateLayer removeAllAnimations];
    [self.playButtonRotateLayer removeFromSuperlayer];
    self.playButtonRotateLayer = nil;
    
    [self.musicMoodBannerView removeFromSuperview];
    self.musicMoodBannerView = nil;
    [self.musicMoodBannerView stopNotificationScroll];
    
    /// 关注feedAi图片
    [self.feedAiImageGridView removeFromSuperview];
    /// 推荐大卡片Ai图片
    [self.recAiImageContentView removeFromSuperview];
    
    [self.musicMoodPicNumLabel removeFromSuperview];
    self.musicMoodPicNumLabel = nil;
    [self.musicMoodPicNumLabelBgView removeFromSuperview];
    self.musicMoodPicNumLabelBgView = nil;
    [self.musicMoodPicProcessView removeFromSuperview];
    self.musicMoodPicProcessView = nil;
    [self.musicMoodGuideMaskView removeFromSuperview];
    self.musicMoodGuideMaskView = nil;
    
    [self.sendGiftGuideBar removeFromSuperview];
    self.sendGiftGuideBar = nil;
    
    [self.friendsSendGiftView removeFromSuperview];
    self.friendsSendGiftView = nil;
    
    [self.singleSendGiftView cancelScrollBless];
    [self.singleSendGiftView removeFromSuperview];
    self.singleSendGiftView = nil;
    
    [self.followMultiSendGiftView removeFromSuperview];
    self.followMultiSendGiftView = nil;
    
    [self.vipMarketingView removeFromSuperview];
    self.vipMarketingView = nil;
    
    [self.aiSongRecPanel removeFromSuperview];
    self.aiSongRecPanel = nil;
    
    [self.aiSongPublishFeedPanel removeFromSuperview];
    self.aiSongPublishFeedPanel = nil;
    
    [self.liveEnterPlayBar removeFromSuperview];
    self.liveEnterPlayBar.delegate = nil;
    self.liveEnterPlayBar = nil;
    
    [self.freeHotBtn removeFromSuperview];
    self.freeHotBtn = nil;
}

- (void)cleanAdFeedShowTag
{
    if (self.simpleFeed.feedAd) {
        self.simpleFeed.feedParkingGuideState = ksFeedParkingGuideStateNotHasShow;
    }
}

//从当前feed开始播放
- (void)playUgcInBackgourndFromCurrentFeed:(KSimpleFeed *)feed
{
    if (self.cellDelegate && [self.cellDelegate respondsToSelector:@selector(KSLayoutableTableCell:didClickWithAction:)])
    {
        KSDrawItemAction* action = [KSDrawItemAction new];
        action.busiData = feed;
        action.type = DRAWITEM_ACTION_TYPE_BUTTON_PLAY;
        [self.cellDelegate KSLayoutableTableCell:self didClickWithAction:action];
    }
    
}

//点击动态中的多图feed进入详情页
- (void)jumpToDetailVC:(NSInteger)index
{
    KSimpleFeed *feed = (KSimpleFeed*)self.busiData;
    
    KSGlobalPlayItem* playItem = [[KSGlobalPlayItem alloc] initWithFeed:feed];
    playItem.passSeekTime = 0;//全局播放进详情页都是从头播放
    playItem.picIndex = index;
    
    [[KSUgcPlayManager sharedManager] playUgcWithItem:playItem];
}


//布局9宫格
- (void)layoutGrid9ViewWithUIItem:(KSUIItem*)Grid9Item
{
    //这里需要处理好KSLayoutableTimelineFeedCellV2 中重用问题
    KSLayoutableTableCell* layoutCell = SAFE_CAST(self, KSLayoutableTableCell);
    if (!layoutCell.collectionView)
    {
        UICollectionViewFlowLayout *flowLayout = [[UICollectionViewFlowLayout alloc] init];
        flowLayout.minimumLineSpacing = kGrid9ViewCellLineSpacing;
        flowLayout.minimumInteritemSpacing = kGrid9ViewCellLineSpacing;
        flowLayout.sectionInset = UIEdgeInsetsZero;
        flowLayout.scrollDirection = UICollectionViewScrollDirectionVertical;
        layoutCell.collectionView = [[KSUICollectionView alloc] initWithFrame:Grid9Item.rect collectionViewLayout:flowLayout];
        layoutCell.collectionView.showsHorizontalScrollIndicator = NO;
        layoutCell.collectionView.showsVerticalScrollIndicator = NO;
        layoutCell.collectionView.scrollEnabled = YES;
        layoutCell.collectionView.dataSource = layoutCell;
        layoutCell.collectionView.bounces = NO;
        layoutCell.collectionView.backgroundColor =  UIColor.ks_normalBGColor;
        
        layoutCell.collectionView.delegate = layoutCell;
        [layoutCell.collectionView registerClass:[KSTimelinePhotoCell class] forCellWithReuseIdentifier:@"UICollectionViewCell"];
        
        [layoutCell.contentView addSubview:layoutCell.collectionView];
    }
    
    if (!layoutCell.collectionView.superview)
    {
        [layoutCell.contentView addSubview:layoutCell.collectionView];
    }
    self.grid9Item = Grid9Item;
    layoutCell.collectionView.frame = Grid9Item.rect;
    [layoutCell.collectionView reloadData];
}

// 布局「音乐心情」「图文」图片轮播view
- (void)layoutMusicMoodCyclePicViewWithUIItem:(KSUIItem *)item {
    if (self.musicMoodBannerView) {
        [self.musicMoodBannerView removeFromSuperview];
    }
    
    // 轮播图片
    KSHorizonalBannerConfig *config = [KSHorizonalBannerConfig defaultConfig];
    config.bannerGap = 0;
    config.bannerMarginEdge = 0;
    config.bannerSize = item.rect.size;
    config.autoScrollTime = 5;
    
    KSHorizonalBannerView *bannerView = [[KSHorizonalBannerView alloc] initWithConfig:config frame:item.rect];
    bannerView.userInteractionEnabled = YES;
    bannerView.layer.cornerRadius = 8;
    bannerView.delegate = self;
    bannerView.clipsToBounds = YES;
    NSInteger index = self.simpleFeed.musicMoodePicIndex - 1; // 因为reloadWithDataArray内部会加1，所以这里减1(这代码真吐了)
    if (index < 0) {
        index = 0;
    }
    bannerView.curPageIndex = index;
    NSMutableArray *dataArr = [[NSMutableArray alloc] init];

    NSInteger realIndex = 0;
    for (JceTimeline_picinfo *picInfo in self.simpleFeed.songinfo.feedImageList) {
        JceTimeline_pic_detail *picDetail = [picInfo.jce_mPic objectForKey:[NSNumber numberWithInt:kSimpleFeedImageSize640]];
        KSHorizonalBannerItemData *data = [KSHorizonalBannerItemData itemDataWithImageStr:picDetail.jce_strUrl isLocalImage:NO];
        data.realIndex = realIndex;
        realIndex++;
        [dataArr safeAddObject:data];
    }
    [bannerView reloadWithDataArray:dataArr];
    [self.contentView addSubview:bannerView];

    // 音乐心情feed or 音频转音乐心情样式的feed 开启轮播滚动
    if (dataArr.count > 1 || [self.simpleFeed isAudioTransferToMusicMood]) {
        [bannerView startNotificationScroll];
    }
    self.musicMoodBannerView = bannerView;
        
    // 底部黑色蒙层
    CAGradientLayer *gradientLayer = [CAGradientLayer layer];
    gradientLayer.colors = @[(__bridge id)[UIColor ks_colorWithRGBHex:0x000000 alpha:0.4].CGColor, (__bridge id)[UIColor clearColor].CGColor];
    gradientLayer.locations = @[@0, @1.0];
    gradientLayer.startPoint = CGPointMake(0, 1);
    gradientLayer.endPoint = CGPointMake(0, 0);
    CGFloat layerHeight = self.musicMoodBannerView.height * 0.4;
    gradientLayer.frame = CGRectMake(0, self.musicMoodBannerView.height - layerHeight, self.musicMoodBannerView.width, layerHeight);
    [self.musicMoodBannerView.layer addSublayer:gradientLayer];
    
    // 右上角数字
    if (self.simpleFeed.songinfo.feedImageList.count > 1) {
        NSString *numString = [NSString stringWithFormat:@"%ld/%ld", self.simpleFeed.musicMoodePicIndex, self.simpleFeed.songinfo.feedImageList.count];
        UIFont *textFont = [KSLayoutUIManagerTimeline getAvantFontWithSize:12 bold:YES];
        CGSize numStringSize = [numString kSongSizeWithFont:textFont];
        CGFloat bgViewWidth = numStringSize.width + 8 + 8;
        CGFloat bgViewHeight = numStringSize.height + 3 + 7;
        CGRect bgViewRect = CGRectMake(self.musicMoodBannerView.width - 8 - bgViewWidth, 8, bgViewWidth, bgViewHeight);
        
        self.musicMoodPicNumLabelBgView = [[UIView alloc] init];
        self.musicMoodPicNumLabelBgView.backgroundColor = [UIColor ks_colorWithRGBHex:0x000000 alpha:0.5];
        self.musicMoodPicNumLabelBgView.layer.cornerRadius = bgViewHeight / 2;
        self.musicMoodPicNumLabelBgView.frame = bgViewRect;
        [self.musicMoodBannerView addSubview:self.musicMoodPicNumLabelBgView];
        
        self.musicMoodPicNumLabel = [UILabel ks_labelWithFontType:KSFontType_SmallBold textColor:[UIColor ks_colorWithRGBHex:0xFFFFFF alpha:0.85]];
        self.musicMoodPicNumLabel.font = textFont;
        self.musicMoodPicNumLabel.frame = CGRectMake(8, 6.5, numStringSize.width, numStringSize.height);
        self.musicMoodPicNumLabel.text = numString;
        [self.musicMoodPicNumLabel sizeToFit];
        [self.musicMoodPicNumLabelBgView addSubview:self.musicMoodPicNumLabel];
    }
    
    // 进度条
    if (self.simpleFeed.songinfo.feedImageList.count > 1) {
        CGFloat picCount = self.simpleFeed.songinfo.feedImageList.count;
        CGFloat maxWidth = bannerView.width - 14 - 14;
        CGFloat gapWidth = 2;
        CGFloat perWidth = (maxWidth - gapWidth * (picCount - 1)) / picCount;
        self.musicMoodPicProcessView = [[UIView alloc] initWithFrame:CGRectMake(14, bannerView.height - 12 - 2, maxWidth, 2)];
        [self.musicMoodBannerView addSubview:self.musicMoodPicProcessView];
        CGFloat usedX = 0;
        for (int i = 0; i < picCount; i++) {
            UIView *lineView = [[UIView alloc] initWithFrame:CGRectMake(usedX, 0, perWidth, 2)];
            if (i <= self.simpleFeed.musicMoodePicIndex - 1) {
                lineView.backgroundColor = [UIColor ks_colorWithRGBHex:0xFFFFFF alpha:0.75];
            } else {
                lineView.backgroundColor = [UIColor ks_colorWithRGBHex:0xFFFFFF alpha:0.25];
            }
            
            lineView.layer.cornerRadius = 1;
            lineView.tag = i + 1;
            [self.musicMoodPicProcessView addSubview:lineView];
            usedX = usedX + 2 + perWidth;
        }
    }
    
    // 歌名
    if ([self.simpleFeed getSongName].length > 0) {
        CGFloat songNameY = 0;
        if (self.musicMoodPicProcessView) {
            songNameY = self.musicMoodPicProcessView.top - 14 - 14;
        } else {
            songNameY = bannerView.height - 14 - 14;
        }
        UIImageView *nameImageView = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"feed_music_mood_music_name_icon"]];
        nameImageView.frame = CGRectMake(14, songNameY, 14, 14);
        [self.musicMoodBannerView addSubview:nameImageView];
        CGFloat songNameMaxWidth = self.musicMoodBannerView.width - (nameImageView.right + 2);
        UILabel *nameLabel = [UILabel ks_labelWithFontType:KSFontType_Middle textColor:[UIColor ks_colorWithRGBHex:0xFFFFFF alpha:0.75]];
        nameLabel.frame = CGRectMake(nameImageView.right + 2, songNameY, songNameMaxWidth, 14);
        nameLabel.text = [self.simpleFeed getSongName];
        [self.musicMoodBannerView addSubview:nameLabel];
    }
    
    // 引导
    BOOL isHasShow = LocalBoolConfig(kMusicMoodUserGuideHasShowKey);

    // 图片数量多于一张时才显示引导图
    if (!isHasShow && self.simpleFeed.songinfo.feedImageList.count > 1) {
        // 只在第一个显示引导图
        SaveLocalBoolConfig(kMusicMoodUserGuideHasShowKey, YES);
        // 半透明蒙版
        UIView *maskView = [[UIView alloc] initWithFrame:self.musicMoodBannerView.bounds];
        maskView.backgroundColor = [UIColor ks_colorWithHexString:@"#000000" alpha:0.6];
        self.musicMoodGuideMaskView = maskView;
        [self.musicMoodBannerView  addSubview:maskView];
        
        // 引导图动画
        // 想在lottieView上加滑动手势有冲突 所以将lottie View 加在scrollView上
        UIScrollView *scrollView = [[UIScrollView alloc] initWithFrame:self.musicMoodBannerView.bounds];
        CGFloat width = self.musicMoodBannerView.bounds.size.width;
        CGFloat height = self.musicMoodBannerView.bounds.size.height;
        scrollView.contentSize = CGSizeMake(width * 1.2, height);
        scrollView.tag = kMusicMoodUserGuideViewTag;
        scrollView.delegate = self;
        
        KSLottiePlayAssetLocalModel *model = [[KSLottiePlayAssetLocalModel alloc] init];
        model.resourceID = KSAssetDownloadLocalIDMusicMoodUserGuideAnimLottie;
        model.type = KSLottiePlayModelTypeLocalAsset;
        model.animationName = @"musicMood_userGuide_lottie";
        NSString *bundlePath = [NSBundle.mainBundle pathForResource:@"LottieLocalAssets" ofType:@"bundle"];
        model.localJsonPath = [bundlePath stringByAppendingPathComponent:@"musicMood_userGuide_lottie/data.json"];
        
        LOTAnimationView* lottieView = [[KSLottiePlayManager sharedManager] createLOTAnimationViewWithModel:model];
        lottieView.userInteractionEnabled = YES;
        lottieView.contentMode = UIViewContentModeScaleAspectFit;
        lottieView.loopAnimation = YES;
        lottieView.frame = self.musicMoodBannerView.bounds;
        [lottieView play];
        [scrollView addSubview:lottieView];
        
        [maskView addSubview:scrollView];
    }
}

/// Ai图文feed 图片区域
- (void)layoutFeedAiImageGridView:(KSUIItem *)item
{
    if (self.feedAiImageGridView.superview) {
        [self.feedAiImageGridView removeFromSuperview];
    }
    
    NSInteger count = self.simpleFeed.AiImageText.imageArr.count;
    if (count == 0) {
        return;
    }
    self.feedAiImageGridView = [[KSFeedAiImageGridView alloc] initWithFrame:item.rect];
    self.feedAiImageGridView.simpleFeed = self.simpleFeed;
    [self.contentView addSubview:self.feedAiImageGridView];
}

- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    CGFloat contentOffSetX = scrollView.contentOffset.x;
    
    if (scrollView.tag == kMusicMoodUserGuideViewTag) {
        // 音乐心情用户引导手势scrollView
        if (contentOffSetX != 0) {
            [self.musicMoodGuideMaskView removeFromSuperview];
            self.musicMoodGuideMaskView = nil;
            SaveLocalBoolConfig(kMusicMoodUserGuideHasShowKey, YES);
        }
    }
}

#pragma mark cell中包含9宫格的delegate
- (UICollectionViewCell*)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath
{
    
    KSTimelinePhotoCell *photoCell = [collectionView dequeueReusableCellWithReuseIdentifier:collectionCellId forIndexPath:indexPath];
    if (!photoCell)
    {
        photoCell = [[KSTimelinePhotoCell alloc] init];
    }
    
    photoCell.indexPath = indexPath;
    photoCell.kstimelinePhotoCellDelegate = self;
    JceTimeline_picinfo* picInfo = [self.simpleFeed.songinfo.feedImageList safeObjectAtIndex:indexPath.row];
    
    JceTimeline_pic_detail *picDetail = [picInfo.jce_mPic objectForKey:[NSNumber numberWithInt:kSimpleFeedImageSize320]];
    photoCell.coverUrl = picDetail.jce_strUrl;
    
    
    if (self.simpleFeed.songinfo.feedImageList.count>kIMAGELISTMAXSHOWCOUNT && indexPath.row ==(kIMAGELISTMAXROWCOUNT-1))
    {
        photoCell.title = NSIToString(self.simpleFeed.songinfo.feedImageList.count);
        photoCell.desc = @"全部图片";
        photoCell.cellContentView.backgroundColor = UIColor.ks_normalBGColor;
        
    }
    else
    {
        photoCell.title = nil;
        photoCell.desc = nil;
        photoCell.cellContentView.backgroundColor =  UIColor.ks_normalBGColor;
    }
    CGFloat cornerR = (self.simpleFeed.songinfo.feedImageList.count== 1)?4:2.8;
    photoCell.cellContentView.hidden = NO;

    photoCell.coverUrlView.layer.cornerRadius = cornerR;
    photoCell.coverUrlView.clipsToBounds = YES;
    photoCell.cellContentView.layer.cornerRadius = cornerR;
    photoCell.cellContentView.clipsToBounds = YES;
    
    return photoCell;
    
}

- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout*)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath
{
    CGRect rect = self.grid9Item.rect;

    if (self.simpleFeed.songinfo.feedImageList.count == 1)
    {
        return rect.size;
    }
    else if (self.simpleFeed.songinfo.feedImageList.count == 2 || self.simpleFeed.songinfo.feedImageList.count == 4)
    {
        NSInteger itemWidth = (rect.size.width - kGrid9ViewCellLineSpacing)/2;
        return CGSizeMake(itemWidth, itemWidth);
    }
    else
    {
        NSInteger count = min(self.simpleFeed.songinfo.feedImageList.count, kIMAGELISTCOUNT) ;
        NSInteger itemWidth = (rect.size.width -kGrid9ViewCellLineSpacing*(count-1))/count;
        return CGSizeMake(itemWidth, itemWidth);
    }
}

- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section
{
    return min(9,self.simpleFeed.songinfo.feedImageList.count);
}

- (NSInteger)numberOfSectionsInCollectionView:(UICollectionView *)collectionView
{
    return 1;
}

- (void)didClickOnKSTimelinePhotoCell:(KSTimelinePhotoCell *)cell
{
    //下面是转场动画用到的变量
    BOOL isSonRankList = NO;//是否是伴奏详情页abtest 影响上报的str6
    NSString* key = nil;
    UIViewController* currentVC = [cell getSelfViewControllerForCurrent];
    if ([currentVC isKindOfClass:[KSTimelineRootVC class]])
    {
        KSTimelineRootVC* timelineRootVC = SAFE_CAST(currentVC, KSTimelineRootVC);
        timelineRootVC.timelinePhtocell = cell;
        key = [KSTimelineRootVC getTimeLineRootReportKeyWithAction:KSTimelineRootVCFeedImageListClick simpleFeed:self.simpleFeed];
    }
    else if ([currentVC isKindOfClass:[KSUserProfileVC_V2 class]])
    {
        KSUserProfileVC_V2 *userProfile = SAFE_CAST(currentVC, KSUserProfileVC_V2);
        userProfile.timelinePhtocell = cell;
        key = userProfile.isGuest?@"homepage_guest#creation#background_image#click#0":@"homepage_me#creation#background_image#click#0";
    }
    else if ([currentVC isKindOfClass:[KSSingleSongRankListVC class]])
    {
        key = @"details_of_comp_page#creation#background_image#click#0";
        isSonRankList = YES;
    }
    
    //查看图片默认播放
    KSimpleFeed *simpleFeed = (KSimpleFeed *)self.busiData;
    if (simpleFeed.isMusicMoodFeed && [simpleFeed.musicMoodFeed musicMoodUgcType] != KSMusicMoodUgcType_UGC) {
        
        // 进大图预览页面
        KSImageListBrowserConfig config = KSImageListBrowserConfigIndicatorDot | KSImageListBrowserConfigTotalFixed | KSImageListBrowserConfigDataSourceURL | KSImageListBrowserConfigSupportDesc;
        KSImageListBrowserVC *browserVC = [[KSImageListBrowserVC alloc] initWithIndex:cell.indexPath.row config:config];
        browserVC.delegate = self;
        [[[KSNavigationManager sharedManager] getTopViewController] presentViewController:browserVC animated:YES completion:nil];
        
        
        [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
            reportModel.key = @"geographic_details_page#creation#image#click#0";
            reportModel.commonInt2 = [self.simpleFeed.songinfo.feedImageList count];
            reportModel.commonInt3 = self.indexPath.row + 1;
        } simpleFeed:self.simpleFeed];
    }
    else if ([self isFeedCurrentPlayingUgc])
    {   // 如果当前feed跟全局播放feedID一样
        [self playUgcInBackgourndFromCurrentFeed:simpleFeed];
        if (![[KSUgcPlayManager sharedManager] isUgcPlaying]) {
            [[KSUgcPlayManager sharedManager] playControl];
        }
        [self jumpToDetailVC:cell.indexPath.row];
    } else {
        [self playUgcInBackgourndFromCurrentFeed:simpleFeed];
        [self jumpToDetailVC:cell.indexPath.row];
    }
    
    NSString *abtestId = [NSString string];
    if ([KSTimelineManager sharedManager].currentFiltrMask == [KSTimelineManager sharedManager].filtrMaskFollow) {
        abtestId = ksUIABTest_Module_FeedFollow_Key;
    } else if ([KSTimelineManager sharedManager].currentFiltrMask == [KSTimelineManager sharedManager].filtrMaskRecommend) {
        abtestId = ksUIABTest_Module_RecommendFeedLiveShow;
    }
    
    if (isSonRankList)
    {
        abtestId = ksUIABTest_Module_SongRankList;
    }
    
    [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
        
        reportModel.key = key;
        reportModel.commonInt2 = self.simpleFeed.forwardFeed.strForwardId.length>0?1:2;
        reportModel.commonInt3 = self.indexPath.row+1;
        reportModel.commonInt4 = [KSTimelineRootVC getUgcFromTypeWithFeed:simpleFeed];
        reportModel.commonStr6 = [[KSUIABTestManager sharedManager] getAllTestIdsWithModuleId:abtestId contentTestId:simpleFeed.abTestReport];
        if (self.simpleFeed.songinfo.mapRight)
        {
            NSString *contentId = [[KSPaySoloAlbumManager sharedManager] getStrId:self.simpleFeed.songinfo.mapRight];
            if (contentId)
            {
                reportModel.commonStr1 = contentId;
            }
        }
        reportModel.commonInt11 = 12; //根据配置平台上报
        
        // 浅关系链推荐数据
        reportModel.commonInt13 = simpleFeed.isFromRelationChainRec ? 1 : 0;
        
    } simpleFeed:self.simpleFeed];
}

//播放按钮
- (UIButton *)playButton
{
    return _playButton;
}

#pragma mark - 修改播放UI状态

- (void)setUgcInfoSubviewAlpha:(CGFloat)alphaValue {
    
    self.songName.alpha = alphaValue;
    self.rankView.alpha = alphaValue;
    self.listenTagView.alpha = alphaValue;
    self.ugcTypeView.alpha = alphaValue;
    self.familyRecommendView.alpha = alphaValue;
    self.recommendView.alpha = alphaValue;
    self.chorusLabel.alpha = alphaValue;
    self.beaterInfo.alpha = alphaValue;
    self.godCoverView.alpha = alphaValue;
    self.recReasonAndListenNumView.alpha = alphaValue;
    self.leftTopTagView.alpha = alphaValue;
    
    UIView *songContainerView = [self.contentView viewWithTag:TimelineFeedCellAudioSongContainerView];
    songContainerView.alpha = alphaValue;

    if (self.recordActionButton && self.recordActionButton.superview != nil && self.recordActionButton.bizType != KSFeedUniversalBizButtonType_None)
    {
        BOOL hidden = alphaValue > 0;
        
        if (self.simpleFeed.layoutConfig.isActionBtnInRightCorner)
        {
            //搜索合唱feed合唱按钮是常驻
            self.recordActionButton.hidden = NO;
        }
        else
        {
            // 播放时展示引导录唱按钮
            self.recordActionButton.hidden = hidden;
            
            if (hidden == NO)
            {
                // 曝光(内部去重)
                [self reportFeedUniversalBizButtonExpose];
            }
        }
    }

}

- (void)setSubviewExcludePlayViewAlpha:(CGFloat)alpha
{
    for (UIView *view in self.contentView.subviews) {
        if (view == self.bgimageView ||
            view == self.timelineVideoPlayingView ||
            view == self.videoView ||
            view == self.videoPlayView ||
            view == self.audioProductVideolizeView ||
            view == self.maskListenView ||
            view == self.progressSlider ||
            view == self.postView ||
            view == self.playButton ||
            view == self.clickedButton) {
            continue;
        }
        view.alpha = alpha;
    }
}

// 用于付费作品异步鉴权后，修正cell的播放相关UI
- (void)updateCellWithPlayingStatus:(BOOL)isPlaying
{
    CGFloat alpha = isPlaying ? 0 : 1; // 默认可见
    
    if ([self.simpleFeed isKindOfVideoTypeFeed])
    {
        if (self.songName.alpha == alpha)
        {
            // 避免重复刷新
            return;
        }
        
        // 视频
        [self setUgcInfoSubviewAlpha:alpha];
        
        self.playButton.selected = isPlaying;
    }
    else if ([self.simpleFeed isKindOfAudioTypeFeed]) { // 音频
        self.audioPlayOrPauseButton.selected = isPlaying;
        [self show2022FeedUGCInfoOrLyric:!isPlaying delay:NO animate:YES source:5];
        if (isPlaying) {
            if ([self canShowFeedLyric:self.simpleFeed isNeedCheckPlayStatus:YES completeBlock:nil]) {
                // 重新布局下歌词
                [self reorderLyricView:self.simpleFeed];
            }
        }
    }
}

/// 开始单流Feed预览播放，包括视频、音频及KTV音频
- (void)startVideoPreviewPlay {
    KSimpleFeed *feed = (KSimpleFeed *)self.busiData;
    
    if (feed.playModel.playItem == nil || feed.playModel.detail == nil) {
        feed.playModel = [[KSDetailPlaybackModel alloc] init];
        
        KSTimelineDetail *detail = [[KSTimelineDetail alloc] init];
        detail.ugcId = self.strUgcId;
        detail.songName = feed.songinfo.name;
        if (feed.songinfo.strVId) {
            detail.vid = feed.songinfo.strVId;
        } else if (feed.publishContent.ktvDraftItemContent.ktvDraftItemVid) {
            detail.vid = feed.publishContent.ktvDraftItemContent.ktvDraftItemVid;
        }
        detail.shareId = feed.songinfo.shareId;
        detail.getUrlKey = feed.songinfo.getUrlKey;
        detail.ksongMid = feed.songinfo.songMid;
        detail.ugcMask = feed.songinfo.ugcMask;
        detail.ugc_mask_ext = feed.songinfo.ugcMaskExt;
        detail.UGC_mask_ext_1 = feed.songinfo.ugcMaskExt1;
        detail.isMusicMoodFeed = feed.isMusicMoodFeed;
        detail.score = feed.songinfo.score;
        detail.scoreRank = feed.songinfo.scoreRank;
        detail.mapRight = feed.songinfo.mapRight;
        detail.originTopic = [[JceTimeline_Detail_UgcTopic alloc] init];
        detail.originTopic.jce_enAudioTransVideoPlayType = (JceInt32)feed.songinfo.audioTransVideoPlayType;
        
        feed.playModel.detail = detail;
        feed.playModel.songInfo = feed.songinfo;
        
        if (feed.playModel.songInfo.duration == 0)
        {
            NSInteger duration = (feed.songinfo.iEndTime - feed.songinfo.iStartTime) / 1000;
            if (duration > 0)
            {
                feed.playModel.songInfo.duration = duration;
            }
        }
        
        KSGlobalPlayItem *playItem = [[KSGlobalPlayItem alloc] initWithFeed:feed];
        playItem.ugcId = self.strUgcId;
        playItem.strForwardId = self.simpleFeed.forwardFeed.strForwardId;
        playItem.passedBusinessData = self.simpleFeed;
        // 播放来源 frompage
        playItem.fromPage = [KSLayoutableTimelineFeedCellV2 getFromePageAccordingCurrentVC:feed];
        feed.playModel.playItem = playItem;
    }
    KINFO(@"[Feed自动播放] %ld 准备自动播放:%@", (long)self.indexPath.row, feed.songinfo.name);
    
    if ([self.simpleFeed isKindOfVideoTypeFeed] && self.simpleFeed.songinfo.audioTransVideoPlayType != JceTimeline_EnumAudioTransVideoPlayType_eAudioTransVideo_type_native_tempalte_video) {
        // 视频自动播放
        if (![self isFeedAutoPlay]) {
            return;
        }
        
        CGFloat videoPreviewCurTime = [[[KSVideoPreviewPlayManager sharedManager] curVideoPlayer] currentTime];
        if ([[KSUgcPlayManager sharedManager] isUgcPlaying] &&
            [[[KSUgcPlayManager sharedManager] currentUgcDetail].ugcId isEqualToString:feed.playModel.detail.ugcId]) {
            feed.playModel.playbackInfo.passSeekTime = [[KSUgcPlayManager sharedManager] currentPlayingTime];
        } else if (videoPreviewCurTime > 0) {
            //这里reloadtable之后不知道谁改了passSeekTime，这里用最新值刷回去,不然会出现视频帧抖动
            feed.playModel.playbackInfo.passSeekTime = videoPreviewCurTime;
        }
        
        [KSFeedPreviewManager sharedManager].videoPreviewManager.delegate = self;

        BOOL canPlay = [[KSFeedPreviewManager sharedManager] playPreviewWithPlaybackModel:feed.playModel inView:self.bgimageView];
        if (canPlay) {
            [self setUgcInfoSubviewAlpha:0]; // 视频
            self.playButton.selected = YES;
            self.playButton.accessibilityLabel = @"双击可暂停";
            
            // 送礼样式异化
            [self showFeedQuickGift];
        }
    } else if ([self.simpleFeed isKindOfAudioTypeFeed]) { // 音频自动播放
        if (![self isFeedAutoPlay]) {
            return;
        }
        
        // 音频自动播放
        
        // 2022新版feed (ktv也走音频播放)
        [[KSFeedPreviewManager sharedManager] setAudioPreviewMgrDelegate:self];
        
        // 启动自动播放
        BOOL canPlay = [[KSFeedPreviewManager sharedManager] playPreviewWithPlaybackModel:feed.playModel inView:self.bgimageView];
        if (canPlay) {
            // 普通音频
            if (self.simpleFeed.playModel == [KSFeedPreviewManager sharedManager].playbackModel) {
                // 因为上面的playPreviewWithPlaybackModel没有传self.bgimageView进去，所以内部自动走了音频的preview类
                self.audioPlayOrPauseButton.selected = YES;
                self.audioPlayOrPauseButton.accessibilityLabel = @"暂停播放";
                [self show2022FeedUGCInfoOrLyric:NO delay:NO animate:YES source:6];
                if ([self canShowLyricByFeed:self.simpleFeed]) {
                    if (self.simpleFeed.playModel.playItem.passSeekTime > 0) {
                        // 对于有seek的音频，同步歌词进度 bug = 99413717
                        [self reorderLyricView:self.simpleFeed progress:self.simpleFeed.playModel.playItem.passSeekTime];
                    } else {
                        [self reorderLyricView:self.simpleFeed];
                    }
                }
            }
            
            // 送礼样式异化
            [self showFeedQuickGift];
        }
    } else if (![self.liveVideoView isPlaying] && self.liveVideoView.playerType == KSVideoDisplayPlayerType_TXplayer && [KSimpleFeedManager canPlayLiveVideo]) {
        for (KSUIItem *item in self.simpleFeed.layoutInfo.items) {
            if ([item isKindOfClass:[KSVideoDisplayItem class]]) {
                [self.liveVideoView updateWithDisplayItem:(KSVideoDisplayItem *)item];
                self.liveAudioButton.selected = NO;
                if (!((KSVideoDisplayItem *)item).mutePlay) {
                    [self.liveVideoView setMute:NO];
                }
                break;
            }
        }
    }
    else if ([self.simpleFeed isPublicSingleKtvFeed])
    {
        [self processStartMediaPreviewPlayWhen:KSTimelineStartVideoWhenDefault];
    }
}

/// 停止单流Feed预览播放
- (void)processStopMediaPreviewPlayWhen:(KSTimelineStopVideoWhen)when {
    KSimpleFeed *simpleFeed = self.busiData;
    self.isMediaPlaying = NO;
    if (self.simpleFeed.songGod && [self.simpleFeed isRecCardFeed])
    {
        self.simpleFeed.songGodAudioReport = ([KSTmpAudioPlayManger sharedManager].audioPlayerStatus == KSAudioPlayerStatus_Playing || [KSTmpAudioPlayManger sharedManager].audioPlayerStatus == KSAudioPlayerStatus_PlayingEnding) ? 1 : 2;
        [self.songGodCardView stopAudioPlay];
    }
    else if ([self.simpleFeed isKindOfKTVRoomFeed])
    {
        [self.ktvCoverAnimateView stop];
        KS_WEAK_SELF(self);
        [UIView animateWithDuration:0.25 animations:^{
            CHECK_SELF_AND_RETURN()
            self.ktvCoverAnimateView.alpha = 0;
        }];
        
        if ([self.simpleFeed isFollowTabFeed] && [self.simpleFeed isPublicSingleKtvFeed]) {
            [self followFeedKtvRoomExitRoom];
        }
       
    }
    else if ([self.simpleFeed isKtvRoomCard] && [self.simpleFeed isRecCardFeed]) {
        if (!self.feedManager.isKtvRoomPushToDetail) {  // 还要判断当前的tab是不是推荐大卡片
            if (!self.feedManager.isForceFresh && when == KSTimelineStopVideoWhenScroll && [self.feedManager.curSimpleFeed isKtvRoomCard] && self.feedManager.curSimpleFeed.bigCardKtvItem.jce_uType == self.simpleFeed.bigCardKtvItem.jce_uType) {
                // 上下滑是同一种类型的歌房， 先不退房， 走switchRoom
                [self clearKtvCoverView];
            } else if(!self.feedManager.isForceFresh && when == -1 && [self.feedManager.curSimpleFeed isKtvRoomCard] && [self.feedManager.curSimpleFeed.bigCardKtvItem.jce_strRoomId isEqualToString:self.simpleFeed.bigCardKtvItem.jce_strRoomId] && [self.pageManager.bizController ktvCardList_isAvEnterSuccess]){
                // 当前cell已成功进房，且roomid相同就不退房
                KINFO(@"[Feed歌房大卡片] 当前cell已成功进房，且roomid相同,不退房");
                [self clearKtvCoverView];
            } else {
                KINFO(@"processStopMediaPreviewPlayWhen");
                [self exitKtvRoomOperation];
            }
        }
        self.feedManager.isKtvRoomPushToDetail = NO;
    }
    else if ([self.simpleFeed isKindOfLiveShowFeed])
    {
        [self stopLiveFeedVideoPlayWhen:when];
    }
    else if ([self.simpleFeed isKindOfProductCardFeed])
    {
        // 推荐商品卡片停止播放
        if (self.feedProductCardView) {
            [self.feedProductCardView pauseAudioPlayer];
        }
    }
    else if ([self.simpleFeed isKindOfVideoAdvertisementFeed])
    {
        [self stopVideoAdvertisementPlayWhen:when];
    }
    else if (self.simpleFeed.oneShotAd) {
        [self stopOneShotPlayWhen:when];
    }
    else if ([self.simpleFeed isKindOfGameAdvertTypeFeed]) {
        self.playButton.hidden = NO;
        self.playButton.selected = YES;
        [self.gameADVideoView stopDisplayWithWhen:when];
        
    }
    else if ([self.simpleFeed isAiSongRec] || [self.simpleFeed isAISongPublish])
    {
        [self stopPlayAiSong:when];
    }

    else if (self.simpleFeed.vipMarketingModel.type == KSRecVipMarketingModelType_Card)
    {
        KSRecCardVipMarketingModel *model = SAFE_CAST(simpleFeed.vipMarketingModel, [KSRecCardVipMarketingModel class]);
        if ([model isPAGValidate] && [model isModelValidate]) {
            [self.vipMarketingView.pagView stop];
        }
    }
    else if ([self.simpleFeed isKindOfAIImageFeed])
    {
        /// 停止AI图文卡片播放
        [self stopPlayAiImageCard];
    }
    else if ([self.simpleFeed isRecCardFeed])
    {
        //卡片流预览播放，离开动态页进入详情页产品要求续播，所以这里不能停
        if ([simpleFeed isSameWithPlayItem:[KSUgcPlayManager sharedManager].passPlayItem])
        {
            [KSUgcPlayManager sharedManager].passPlayItem.recFeedStayDuration = DURATION_FROM_MS(self.simpleFeed.appearST);
        }

        if ([self canStopPreviewPlay])
        {
            KLog(@"[Feed Rec Card] stop play rec card feed :[%@] when=%ld", simpleFeed.simpleFeedCommon.strFeedId, (long)when);
            [self stopFeedVideoPlayUsingGlobalPlaying];
            if ([self.simpleFeed videolizeProduct])
            {
                [self processBackgroundComposite:NO];
            }
        }
    }
    else if (self.liveVideoView)
    {
        [self.liveVideoView stopDisplay];
    }
    else
    {
        // 暂停音视频预览
        if ([[KSFeedPreviewManager sharedManager] isPlayingCurrentFeed:simpleFeed]) {
            [[KSFeedPreviewManager sharedManager] pausePreview];
        }
        
        [self.playButtonRotateLayer removeFromSuperlayer];
        [self.playButtonRotateLayer removeAllAnimations];
        
        // UI样式
        if ([self.simpleFeed isKindOfAudioTypeFeed]) { // 音频
            self.audioPlayOrPauseButton.selected = NO;
            self.audioPlayOrPauseButton.accessibilityLabel = @"开始播放";
            [self show2022FeedUGCInfoOrLyric:YES delay:NO animate:YES source:8];
        } else if ([self.simpleFeed isKindOfVideoTypeFeed]) { // 视频
            [self setUgcInfoSubviewAlpha:1];
            self.playButton.selected = NO;
            self.playButton.accessibilityLabel = @"开始播放";
        }
        
        // 停止自动播时异化消失
        if (when != -1) {
            // -1:意为reloadTable
            [self.followFeedSpecialSendGiftBtn cancelQuickGiftAnimation];
        }
        
        KINFO(@"[Feed自动播放] when=%ld idx=%ld 停止自动播放:%@", (long)when, (long)self.indexPath.row, simpleFeed.songinfo.name);
    }
}

#pragma mark KSUgcPlayManagerDelegate <NSObject>
// 全局播放音频进度回调（用于滚动歌词）
- (void)playerCurrentPosition:(NSTimeInterval)position duration:(NSTimeInterval)duration playItem:(KSGlobalPlayItem *)playItem
{
    if ([self.simpleFeed isSameWithPlayItem:playItem])
    {
        if (playItem.isPlayedByRecFeed)
        {
            if (position > duration)
            {
                NSTimeInterval getNewPostion = position - [KSUgcPlayManager sharedManager].currentUgcDetail.segmentStart/1000;
                position = (getNewPostion > 0)?getNewPostion:position;
            }
        }

        [self updateLyricViewProgross:position withFeed:self.simpleFeed];
        //进度回调
        if ([self.currentPositionDelegate respondsToSelector:@selector(feedCell:currentPosition:duration:)])
        {
            [self.currentPositionDelegate feedCell:self currentPosition:position duration:duration];
        }
        
        if ([self.simpleFeed isRecCardFeed])
        {
            if (!self.progressSlider.isSliderUsing)
            {
                //进度条设置
                CGFloat progress = duration > 0 ? (position/duration) : 0;
                BOOL showAnim = progress > 0 && self.progressSlider.value > 0;
                if (showAnim) {
                    [UIView animateWithDuration:0.4 animations:^{
                        [self.progressSlider setValue:progress animated:YES];
                    }];
                } else {
                    self.progressSlider.value = progress;
                }
                self.leftCurrentPlayingTimeLabel.text = [KSDateFormatterHelper formatTime:position];
            }
            self.rightTotalTimeLabel.text = [KSDateFormatterHelper formatTime:duration];
            if (!self.playButton.hidden) {
                self.playButton.hidden = YES;
            }
            
            // 更新短剧入口异化
            if (self.shortPlayBar.superview) {
                CGFloat diffPos = WnsConfigManager.shortplayDiffDuration;
                if (diffPos <= 0) {
                    diffPos = duration * WnsConfigManager.shortplayDiffProgress;
                }
                BOOL showDiff = position > diffPos;
                [self.shortPlayBar changeToRed:showDiff];
            }
        }
        
        self.simpleFeed.playingTime += position - self.lastPosition;
        self.simpleFeed.lastPlayPosition = position;
        self.lastPosition = position;
    }
}

- (void)playerChangeStatus:(KSPlayStatus)toStatus playItem:(KSGlobalPlayItem *)playItem;
{
    if ([self.simpleFeed isRecCardFeed] == NO && [self isFeedAutoPlay])
    {
        // 自动播放场景不修改状态
        return;
    }
    
    if ([self.simpleFeed isSameWithPlayItem:playItem] || [self.simpleFeed isRecCardFeed])
    {
        if (toStatus == KSPlayStatus_Playing)
        {
            self.playButton.selected = YES;
            self.playButton.accessibilityLabel = @"双击可暂停";
            self.lastPosition = [[KSUgcPlayManager sharedManager] currentPlayingTime];
            
            if ([self.simpleFeed isRecCardFeed]) {
                self.playButton.hidden = YES;
            }
            if ([self.simpleFeed isSameWithPlayItem:playItem])
            {
                NSTimeInterval position = [KSUgcPlayManager sharedManager].currentPlayingTime;
                [self updateLyricViewProgross:position withFeed:self.simpleFeed];
            }
        }
        else if (toStatus == KSPlayStatus_Pause)
        {
            if ([self.simpleFeed isRecCardFeed]) {
                self.playButton.hidden = ![self.simpleFeed isSameWithPlayItem:playItem];
            }
        }
        
        if ([self.simpleFeed isRecCardAdFeed] || [self.simpleFeed isKindOfGameAdvertTypeFeed])
        {
           self.playButton.hidden = YES;
        }
        
        if ([self.collectionDelegate respondsToSelector:@selector(feedCell:playStatusChange:)]) {
            [self.collectionDelegate feedCell:self playStatusChange:toStatus];
        }
    }
}

- (void)dealloc
{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
    self.videoPlayView.playbackViewDelegate = nil;
    [self cleanLyricView];
    [self.musicMoodBannerView stopNotificationScroll];
    [self stopEnterRoomCountDownTimer];
    [self stopVideoAnimationPlay];
    
    [self.singleSendGiftView cancelScrollBless];
    [self.singleSendGiftView removeFromSuperview];
    self.singleSendGiftView = nil;
}

#pragma mark CAAnimationDelegate
- (void)animationDidStop:(CAAnimation *)anim finished:(BOOL)flag
{
    CAAnimation *animation = [self.adFeedmoveOutButton.layer animationForKey:@"scaleOutSecondStepAnimation"];
    if ([anim isEqual:animation] && flag) {
        [self.adFeedmoveOutButton removeFromSuperview];
    }
}

#pragma mark @override 父类异步布局完所有子view之后的block回调
- (void)asynSetContentFinishBlock:(KSLayoutInfo*)info withDelegate:(id<KSLayoutableTableCellDelegate>) delegate
{
    // 切歌的时候拉取歌词
    [[NSNotificationCenter defaultCenter] removeObserver:self name:KSNotification_PlayingUgcDidChange object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(didReceivePlayingUgcChangedNotification:) name:KSNotification_PlayingUgcDidChange object:nil];
    
    // 付费作品异步鉴权后起播通知
    [[NSNotificationCenter defaultCenter] removeObserver:self name:KSNotification_FeedAutoPayUGCPlayingStatusChange object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(didReceivePayUGCPlayingNotification:) name:KSNotification_FeedAutoPayUGCPlayingStatusChange object:nil];
    
    // 付费作品自动播放失败后，把UI刷回未播放状态
    [[NSNotificationCenter defaultCenter] removeObserver:self name:@"KSNotification_PayUGCAutoPlayFailed" object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(didReceivePayUGCAutoPlayFailedNotification:) name:@"KSNotification_PayUGCAutoPlayFailed" object:nil];
    
    [[NSNotificationCenter defaultCenter] removeObserver:self name:KS_IAP_GIFT_SEND_GIFT_SUCC_NOTIFICATION object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(didReceiveSendGiftSuccessNotification) name:KS_IAP_GIFT_SEND_GIFT_SUCC_NOTIFICATION object:nil];
    
    // Ai统一支付面板的消息通知
    [[NSNotificationCenter defaultCenter] removeObserver:self name:KSNotification_AISellSongPayPanelEvent object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(handelAISellSongPayPanelEvent:) name:KSNotification_AISellSongPayPanelEvent object:nil];
    
    // Mini热度卡
    [[NSNotificationCenter defaultCenter] removeObserver:self
                                                    name:KSAdFreeHotGetRewardSuccessNotification
                                                  object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(adFreeHotGetRewardSuccess:) name:KSAdFreeHotGetRewardSuccessNotification
                                               object:nil];

    [self addLyricReadyNotification];
    
    [self addFirstFrameImageDidLoadNotification];

    if (UGC_TYPE(self.simpleFeed.songinfo.ugcMask) == VIDEO_UGC && self.simpleFeed.songinfo.audioTransVideoPlayType != JceTimeline_EnumAudioTransVideoPlayType_eAudioTransVideo_type_native_tempalte_video) {
        if (self.listenTagView) {
            [self.listenTagView removeFromSuperview];
        }
        if (self.musicMoodBannerView) {
            [self.musicMoodBannerView removeFromSuperview];
        }
        
        for (KSUIItem *item in info.items)
        {
            if ([item isKindOfClass:[KSUIItemCustomAddView class]] && ((KSUIItemCustomAddView*)item).viewType == DRAWITEM_CUSTOM_ADD_VIDEO_PLAY)
            {
                //视频
                [self layoutVideoFeed:self.simpleFeed withUIItem:item];
            }
            
            if ([item isKindOfClass:[KSUIItemCustomAddView class]] && ((KSUIItemCustomAddView*)item).viewType == DRAWITEM_CUSTOM_ADD_RecFeed)
            {
                //推荐大图视频feed
                [self layoutCommonViewsForRecCardFeed:self.simpleFeed withUIItem:item];
                
                if ([self.simpleFeed isRecCardFeed] || [self isFeedAutoPlay] == NO)
                {
                    //设置全局播放delegate，回调播放进度
                    [[KSUgcPlayManager sharedManager] addDelegate:self];
                } else {
                    [[KSUgcPlayManager sharedManager] removeDelegate:self];
                }
            }
            else if ([item isKindOfClass:[KSUIItemCustomAddView class]] && ((KSUIItemCustomAddView*)item).viewType == DRAWITEM_CUSTOM_ADD_FoldedFeeds)
            {
                [self layoutFoldedFeedsView:item];
            } else if ([item isKindOfClass:[KSUIItemCustomAddView class]] && ((KSUIItemCustomAddView*)item).viewType == DRAWITEM_CUSTOM_ADD_BubbleView) {
                [self layoutAndCreateNearbyBubbleView:item];
            }
        }
    } else {
        if (self.listenTagView)
        {
            [self.listenTagView removeFromSuperview];
        }
        if (self.musicMoodBannerView)
        {
            [self.musicMoodBannerView removeFromSuperview];
        }
        
        //音频
        for (KSUIItem* item in info.items)
        {
            if (item.tag == TimelineFeedCellKTVRoomTitleLabel)
            {
                //RoomTitle
                [self layoutRoomTitle:self.simpleFeed withUIItem:item];
            }
            else if (item.tag == TimelineFeedCellGiftAreaActionButton || item.tag == TimelineFeedCellInteractGameJumpGameActionButton)
            {
                //RoomTitle
                [self layoutBottomGiftButtonForPublicFeed:self.simpleFeed withUIItem:item];
            }
            else if ([item isKindOfClass:[KSUIItemCustomAddView class]] &&
                ((KSUIItemCustomAddView *)item).viewType == DRAWITEM_CUSTOM_ADD_AUDIO_PLAY)
            {
                if (!self.simpleFeed.isMusicMoodFeed || !self.simpleFeed.isRefUGCUnavailable) {
                    //普通音频+快唱，音乐心情作品不可用时候不展示
                    [self layoutAudioPlayBtn:item.rect];
                    
                    [self prepareLyricData:self.simpleFeed];
                    self.coverImageViewRect = item.rect;
                    
                    if ([self.simpleFeed isRecCardFeed] || [self isFeedAutoPlay] == NO) {
                        //设置全局播放delegate，回调播放进度
                        [[KSUgcPlayManager sharedManager] addDelegate:self];
                    }
                }
                
                if([self.simpleFeed isPublicSingleKtvFeed])
                {
                    [self layoutBottomGiftButtonForPublicFeed:self.simpleFeed withUIItem:item];
                }
                
                // byxml
                [[NSNotificationCenter defaultCenter] removeObserver:self name:KSNotification_PlayStatusDidChange object:nil];
                [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(timelineDetailPlayStatusChange:) name:KSNotification_PlayStatusDidChange object:nil];
                
                // 处理听原唱音频抢占处理，这里只是临时处理，不要使用该方法判断听原唱的相关信息
                [[NSNotificationCenter defaultCenter] removeObserver:self name:KSNotification_AuidoStartPlaying object:nil];
                [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(timelineDetailPlayStatusChange:) name:KSNotification_AuidoStartPlaying object:nil];
            }
            else if ([item isKindOfClass:[KSUIItemCustomAddView class]] && ((KSUIItemCustomAddView*)item).viewType == DRAWITEM_CUSTOM_ADD_RecFeed)
            {
                //推荐大图视频feed
                [self layoutCommonViewsForRecCardFeed:self.simpleFeed withUIItem:item];
                [self prepareLyricData:self.simpleFeed];
                
                if ([self.simpleFeed isRecCardFeed] || [self isFeedAutoPlay] == NO)
                {
                    //设置全局播放delegate，回调播放进度
                    [[KSUgcPlayManager sharedManager] addDelegate:self];
                }
            }
            else if ([item isKindOfClass:[KSUIItemCustomAddView class]] && ((KSUIItemCustomAddView*)item).viewType == DRAWITEM_CUSTOM_ADD_PhotoList)
            {
                //9宫格图片
                [self layoutGrid9ViewWithUIItem:item];
            }
            else if ([item isKindOfClass:[KSUIItemCustomAddView class]] && ((KSUIItemCustomAddView*)item).viewType == DRAWITEM_CUSTOM_ADD_PhotoList_2022)
            {
                // 音乐心情轮播图片
                [self layoutMusicMoodCyclePicViewWithUIItem:item];
            }
            else if ([item isKindOfClass:[KSUIItemCustomAddView class]] && ((KSUIItemCustomAddView*)item).viewType == DRAWITEM_CUSTOM_ADD_Feed_Ai_Image_Grid)
            {
                // 关注feed Ai图文图片
                [self layoutFeedAiImageGridView:item];
            }
            else if ([item isKindOfClass:[KSUIItemCustomAddView class]] && ((KSUIItemCustomAddView*)item).viewType == DRAWITEM_CUSTOM_ADD_FoldedFeeds)
            {
                [self layoutFoldedFeedsView:item];
            }
            else if ([item isKindOfClass:[KSUIItemCustomAddView class]] && ((KSUIItemCustomAddView*)item).viewType == DRAWITEM_CUSTOM_ADD_SingleSendGift)
            {
                // 单人生日送礼
                [self layoutSingleSendGiftView:item];
            }
            else if ([item isKindOfClass:[KSUIItemCustomAddView class]] && ((KSUIItemCustomAddView*)item).viewType == DRAWITEM_CUSTOM_ADD_MultipleSendGift)
            {
                // 多人生日送礼
                [self layoutMultipleSendGiftView:item];
            }
            else if (self.simpleFeed.feedAd.jce_advertType == JceTimeline_enum_advertType_AdvertTypeVideo)
            {
                //广告视频
                [self layoutVideoAdvertisementViewWithDelegate:delegate];
            } else if ([item isKindOfClass:[KSUIItemCustomAddView class]] && ((KSUIItemCustomAddView*)item).viewType == DRAWITEM_CUSTOM_ADD_BubbleView) {
                [self layoutAndCreateNearbyBubbleView:item];
            } else if ([item isKindOfClass:[KSVideoDisplayItem class]] && ((KSVideoDisplayItem*)item).viewType == DRAWITEM_CUSTOM_ADD_LiveFeedVoice) {
                if (!((KSVideoDisplayItem*)item).mutePlay && [[[KSNavigationManager sharedManager] getTopViewController] isKindOfClass:[KSTimelineRootVC class]] && [KSimpleFeedManager canPlayLiveVideo]) {
                    [self layoutLiveAudioSwitch:(KSLayoutInfo*)info item:item];
                }
            }
            else if ([item isKindOfClass:[KSUIItemCustomAddView class]] && ((KSUIItemCustomAddView*)item).viewType == DRAWITEM_CUSTOM_ADD_LiteKtv)
            {
                // 关注，歌房feed
                [self layoutLiteKtvFeed:self.simpleFeed withUIItem:item];
                
                // 关注，歌房feed 播放按钮
                if (self.simpleFeed.feedVCType != KSimpleFeedSourceType_UserProfile) {
                    [self layoutLiteKtvFeedPlayBtn:item.rect];
                }
            }
            else if ([item isKindOfClass:[KSUIItemCustomAddView class]] && ((KSUIItemCustomAddView*)item).viewType == DRAWITEM_CUSTOM_ADD_LiteKtv_OnlineNum)
            {
                // 关注，歌房feed
                [self layoutLiteKtvFeedOnlineNum:self.simpleFeed withUIItem:item];
            }
            else if ([item isKindOfClass:[KSUIItemCustomAddView class]] && ((KSUIItemCustomAddView*)item).viewType == DRAWITEM_CUSTOM_ADD_LiteKtv_GameStatus)
            {
                // 关注，歌房feed
                [self layoutLiteKtvGameStatusText:self.simpleFeed withUIItem:item];
            }
            else if ([item isKindOfClass:[KSUIItemCustomAddView class]] && ((KSUIItemCustomAddView*)item).viewType == DRAWITEM_CUSTOM_ADD_Interact_GameContainer)
            {
                // 关注，歌房feed
                [self layoutLiteKtvFeedInteractGameContainer:self.simpleFeed withUIItem:item];
            }
            else if ([item isKindOfClass:[KSUIItemCustomAddView class]] && ((KSUIItemCustomAddView*)item).viewType == DRAWITEM_CUSTOM_ADD_Follow_Feed_Multi_Send_Gift)
            {
                // 关注Feed多人送礼
                [self layoutFollowFeedMultiSendGift:item];
            }
        }
    }
    
    // 详情页点赞更新通知
    [[NSNotificationCenter defaultCenter] removeObserver:self name:@"KSNotification_UGC_LikeStatus_Change" object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(didReceiveUGCLikeStatusChangedNotification:) name:@"KSNotification_UGC_LikeStatus_Change" object:nil];
    
    // 评论成功通知
    [[NSNotificationCenter defaultCenter] removeObserver:self name:KSNotification_AddCommentSuccess object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(didReceiveUGCCommentStatusChangedNotification:) name:KSNotification_AddCommentSuccess object:nil];
    
    // 订阅短剧通知
    [[NSNotificationCenter defaultCenter] removeObserver:self name:kPlayletSubscribeStateChange object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(didReceiveUGCSubscribeSuccNotification:) name:kPlayletSubscribeStateChange object:nil];

    // feed自动播放通知切换
    [[NSNotificationCenter defaultCenter] removeObserver:self name:@"KSNotification_FeedAutoPlay_UGC_Change" object:nil];
    [[NSNotificationCenter defaultCenter] removeObserver:self name:@"KSNotification_FeedAutoPlay_Live_Pause" object:nil];
    
    if (![self.simpleFeed isRecCardFeed]) {
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(didReceiveFeedAutoPlayUGCChangedNotification:) name:@"KSNotification_FeedAutoPlay_UGC_Change" object:nil];
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(didReceiveLiveFeedPauseNotification:) name:@"KSNotification_FeedAutoPlay_Live_Pause" object:nil];
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(timelineDetailPlayStatusChange:) name:KSNotification_PlayStatusDidChange object:nil];
    }
    
    // lottie动画资源
    [self layoutLottieView];
    [self layoutLiveVideoViewWithDelegate:delegate];

    [self layoutADFeedAbTeatAnimation:info];
    
    [self layoutVideoAnimationView];
    
    [self timelineDetailPlayStatusChange:nil];
    
    [self layoutFollowFeedSpecialSendGiftBtn];
    
    [self layoutMiniHeatCardBtn];
    
    for (UIView *view in self.contentView.subviews) {
        if ([view isKindOfClass:[KSBaseButton class]]) {
            [self.contentView bringSubviewToFront:view];
        }
        
        // 找到点赞(数) 控件对象
        if (view.tag == TimelineFeedCellLikeButton) {
            self.likeImageView = SAFE_CAST(view, UIImageView);
        } else if (view.tag == TimelineFeedCellLikeCount) {
            self.likeCountLabel = SAFE_CAST(view, UILabel);
        }
    }
}

- (void)layoutFoldedFeedsView:(KSUIItem*)item
{
    if (!self.foldedFeedsView)
    {
        self.foldedFeedsView = [[KSFoldedFeedsView alloc] initWithFrame:item.rect];
        self.foldedFeedsView.delegate = self;
    }
    
//    self.foldedFeedsView.frame = item.rect;
    self.foldedFeedsView.frame = CGRectMake(0, item.rect.origin.y, SCREEN_WIDTH, item.rect.size.height);
    [self.contentView addSubview:self.foldedFeedsView];
    
//    self.foldedFeedsView.foldedFeeds = self.simpleFeed.foldedFeeds;
    self.foldedFeedsView.simpleFeed = self.simpleFeed;
}

// 同城feed中冒泡View
- (void)layoutAndCreateNearbyBubbleView:(KSUIItem *)item {
    if (!self.nearbyBubbleView) {
        self.nearbyBubbleView = [[KSNearbyBubbleView alloc] initWithFrame:item.rect];
    }
    [self.nearbyBubbleView updateWithSimpleFeed:self.simpleFeed];
    self.nearbyBubbleView.frame = item.rect;
    [self.contentView addSubview:self.nearbyBubbleView];
}

//直播feed音量控制
- (void)layoutLiveAudioSwitch:(KSLayoutInfo*)info item:(KSUIItem *)item
{
    if (!self.liveAudioButton)
    {
        self.liveAudioButton = [KSBaseButton buttonWithType:UIButtonTypeCustom];
        [self.liveAudioButton setImage:[UIImage imageNamed:@"feed_live_voice_on_new"] forState:UIControlStateNormal];
        [self.liveAudioButton setImage:[UIImage imageNamed:@"feed_live_voice_off_new"] forState:UIControlStateSelected];
        [self.liveAudioButton addTarget:self action:@selector(liveAudioButtonClick) forControlEvents:UIControlEventTouchUpInside];
        self.liveAudioButton.accessibilityLabel = KString(@"播放或暂停");
        self.liveAudioButton.selected = YES;
        [self.liveVideoView setMute:YES];
    }
    [self refreshLiveAudioPlayButton:item];
    [self.contentView addSubview:self.liveAudioButton];
}

- (void)adFreeHotGetRewardSuccess:(NSNotification *)notification {
    NSDictionary *info = notification.userInfo;
    NSString *ugcId = [info safeObjectForKey:KSAdFreeHotGetRewardSuccessUgcIdKey];
    NSNumber *remain = [info safeObjectForKey:KSAdFreeHotGetRewardSuccessRemainKey];
    if ([self.simpleFeed.miniHeatCardItem.jce_strUgcid isEqualToString:ugcId]) {
        KINFO(@"[AdFreeHotCard][Feed] ugcId匹配 ugcId:%@  remain:%@", ugcId, remain);
        self.simpleFeed.miniHeatCardItem.jce_lNum = remain.unsignedIntValue;
        [self.freeHotBtn updateUIWithRemainCount:self.simpleFeed.miniHeatCardItem.jce_lNum];
    } else {
        KINFO(@"[AdFreeHotCard][Feed] ugcId不匹配 now:%@ noti:%@", self.simpleFeed.miniHeatCardItem.jce_strUgcid, ugcId);
    }
}

#pragma mark -- 布局关注ktv feed
- (void)layoutLiteKtvFeed:(KSimpleFeed *)simpleFeed withUIItem:(KSUIItem *)item
{
    // 布局整个视频区域的 container，包括视频以及下方灰色区域的额外信息
    if (!self.liteKtvContainerView) {
        self.liteKtvContainerView = [[KSFollowKTVRoomFeedContainer alloc] initWithFrame:item.rect];
        self.liteKtvContainerView.userInteractionEnabled = NO;
    }
    self.liteKtvContainerView.delegate = self;
    self.liteKtvContainerView.frame = item.rect;
    self.liteKtvContainerView.feedUserInfo = simpleFeed.simpleUser.userinfo;
    
    UIImageView *coverImageView = [self.contentView viewWithTag:TimelineFeedCellLiveShowCoverImage];
    [self.contentView insertSubview:self.liteKtvContainerView aboveSubview:coverImageView];
}

- (void)layoutLiteKtvFeedOnlineNum:(KSimpleFeed *)simpleFeed withUIItem:(KSUIItem *)item
{
    // 布局整个底部区域的 container，包括视频以及下方灰色区域的额外信息
    BOOL isInteractGameType = [simpleFeed isInteractGameInPlayingStatus]; // 是否是互动游戏状态
    
    if (!self.liteKtvOnlineNumContainerView) {
        self.liteKtvOnlineNumContainerView = [[KSFollowKTVRoomFeedOnlineNumContainer alloc] initWithFrame:item.rect];
        self.liteKtvOnlineNumContainerView.userInteractionEnabled = NO;
    }
    self.liteKtvOnlineNumContainerView.frame = item.rect;
    
    // 观众数
    NSString *strAudience = nil;
    // mic数
    NSString *strMic = nil;
    // 歌房内歌名
    NSString *currentSongName = nil;
    if (simpleFeed.ktvRoomShow)
    {
        strAudience = [KSFormatHelper formatNewNumber:simpleFeed.ktvRoomShow.jce_uOnlineNum];
        strMic = [KSFormatHelper formatNewNumber:simpleFeed.ktvRoomShow.jce_uWaitMikeNum];
        currentSongName = simpleFeed.ktvRoomShow.jce_strCurrSongName;
    }
    else if (simpleFeed.ktvRoomMike)
    {
        strAudience = [KSFormatHelper formatNewNumber:simpleFeed.ktvRoomMike.jce_uOnlineNum];
        strMic = [KSFormatHelper formatNewNumber:simpleFeed.ktvRoomMike.jce_uWaitMikeNum];
        currentSongName = simpleFeed.ktvRoomMike.jce_strSongName;
    }
    
    // 先用下发的Feed数据初始化一下数据
    if(isInteractGameType)
    {
        if (self.liteKtvOnlineNumContainerView)
        {
            [self.liteKtvOnlineNumContainerView clearAllOnlineNumViews];
        }
    }
    else
    {
        [self.liteKtvOnlineNumContainerView refreshUIDataWithAudienceNum:strAudience
                                                                  micNum:strMic
                                                         currentSongName:currentSongName];
    }
    
    [self.contentView addSubview:self.liteKtvOnlineNumContainerView];
}

- (void)layoutLiteKtvGameStatusText:(KSimpleFeed *)simpleFeed withUIItem:(KSUIItem *)item
{
    // 布局整个底部区域的 container，包括视频以及下方灰色区域的额外信息
    BOOL isInteractGameType = [simpleFeed isInteractGameInPlayingStatus]; // 是否是互动游戏状态
    
    if (!self.liteKtvGameStatusLabel) {
        UIFont *font = _font_mid([KSLayoutUIManagerTimeline getAvantFontWithSize:12 bold:YES]);
        self.liteKtvGameStatusLabel = [UILabel labelWithFont:font textColor:[UIColor whiteColor] alignment:NSTextAlignmentCenter lines:1];
        self.liteKtvGameStatusLabel.userInteractionEnabled = NO;
        self.liteKtvGameStatusLabel.backgroundColor = [[UIColor ks_colorWithRGBHex:0x000000] colorWithAlphaComponent:0.1];
    }
    self.liteKtvGameStatusLabel.frame = item.rect;
    self.liteKtvNormalStatusWidth = item.rect.size.width;
    
    // 游戏状态
    NSString *gameStatusText = @"";
    NSString *game_name = @"";
    NSInteger roomStatus = 0; // 歌房游戏名:房间状态
    NSInteger gifStatus = 0;  // 歌房游戏名:动画状态
    NSString *currentSongName = nil;
    
    if (simpleFeed.ktvRoomShow)
    {
        game_name = [simpleFeed.ktvRoomShow.jce_mapExt safeObjectForKey:@"game_tag"];
        roomStatus = [[simpleFeed.ktvRoomShow.jce_mapExt safeObjectForKey:@"room_status_type"] intValue];
        currentSongName = self.simpleFeed.ktvRoomShow.jce_strCurrSongName;
        gifStatus = [[simpleFeed.ktvRoomShow.jce_mapExt safeObjectForKey:@"gif_status"] intValue];
    }
    else if (simpleFeed.ktvRoomMike)
    {
        game_name = [simpleFeed.ktvRoomMike.jce_mapExt safeObjectForKey:@"game_tag"];
        roomStatus = [[simpleFeed.ktvRoomMike.jce_mapExt safeObjectForKey:@"room_status_type"] intValue];
        currentSongName = self.simpleFeed.ktvRoomMike.jce_strSongName;
        gifStatus = [[simpleFeed.ktvRoomMike.jce_mapExt safeObjectForKey:@"gif_status"] intValue];
    }
    
    if(!IS_EMPTY_STR_BM(self.simpleFeed.liteKtvGameName))
    {
        game_name = self.simpleFeed.liteKtvGameName;
    }
    
    // 先用下发的Feed数据初始化一下数据
    if(isInteractGameType)
    {
        gameStatusText = @"游戏中";
    }
    else if (!IS_EMPTY_STR_BM(game_name)) {
        gameStatusText = game_name;
    }
    else if ((roomStatus == JceTimeline_KTV_ROOM_STATUS_TYPE_ENUM_WAITING_MIKE ||
              roomStatus == JceTimeline_KTV_ROOM_STATUS_TYPE_ENUM_PLAYING_SONG ||
              roomStatus == JceTimeline_KTV_ROOM_STATUS_TYPE_ENUM_PLAYING_KTV_GAME ||
              gifStatus == KTVLottieSourceTypeInKtvSing ||
              simpleFeed.ktvRoomMike) &&
             !IS_EMPTY_STR_BM(currentSongName))
    { // 这里跟安卓的逻辑保持一致
        gameStatusText = @"欢唱中";
    }
    
    [self updateLiteKtvGameStatusTextWith:gameStatusText];;
    
    [self.contentView addSubview:self.liteKtvGameStatusLabel];
}

// [Ktv][欢唱歌房]作品的播放、静音操作按钮
- (void)layoutLiteKtvFeedPlayBtn:(CGRect)rect {
    if (!self.liteKtvPlayOrSilenceButton) {
        self.liteKtvPlayOrSilenceButton = [KSBaseButton buttonWithType:UIButtonTypeCustom];
        // KTV模式用类似MV的Feed
        [self.liteKtvPlayOrSilenceButton addTarget:self action:@selector(liteKtvPlayOrSilenceButtonClick:) forControlEvents:UIControlEventTouchUpInside];
        self.liteKtvPlayOrSilenceButton.accessibilityLabel = KString(@"播放");
    }
    [self.liteKtvPlayOrSilenceButton setImage:[UIImage imageNamed:@"feed_follow_ktv_play_btn"] forState:UIControlStateNormal];
    [self.liteKtvPlayOrSilenceButton setImage:[UIImage imageNamed:@"feed_follow_ktv_play_btn"] forState:UIControlStateSelected];
    self.liteKtvPlayOrSilenceButton.frame = CGRectMake(rect.origin.x + rect.size.width - KSMargin_Dynamic_10 - KSMargin_Dynamic(28), rect.origin.y + rect.size.height - KSMargin_Dynamic(12) - KSMargin_Dynamic(28), KSMargin_Dynamic(28), KSMargin_Dynamic(28));
    
    [self updateliteKtvPlayOrSilenceButton];
    [self.contentView addSubview:self.liteKtvPlayOrSilenceButton];
}

- (void)layoutLiteKtvFeedInteractGameContainer:(KSimpleFeed *)simpleFeed withUIItem:(KSUIItem *)item
{
    // 布局整个底部区域的 container，包括视频以及下方灰色区域的额外信息
    if (!self.interactGameContainer) {
        self.interactGameContainer = [[KSFollowKTVRoomFeedInteractGameContainer alloc] initWithFrame:item.rect];
    }
    self.interactGameContainer.frame = item.rect;
    self.interactGameContainer.delegate = self;
    [self.interactGameContainer updateUIWithFeed:self.simpleFeed];
    
    [self.contentView addSubview:self.interactGameContainer];
}

// 重新布局底部送礼按钮的样式
- (void)layoutBottomGiftButtonForPublicFeed:(KSimpleFeed *)simpleFeed withUIItem:(KSUIItem *)item
{
    [self updateWebGameEnterButtonText];
    
    UIView *bottom_gift_view = [self.contentView viewWithTag:TimelineFeedCellFadeGiftView];
    UIButton *bottom_gift_button = [self.contentView viewWithTag:TimelineFeedCellGiftAreaActionButton];
    UIButton *bottom_func_button = [self.contentView viewWithTag:TimelineFeedCellFucTypeActionButton];
    
    UIView *bottom_WebGameEnter_view = [self.contentView viewWithTag:TimelineFeedCellInteractGameJumpGameBackground];
    UIButton *bottom_WebGameEnter_button = [self.contentView viewWithTag:TimelineFeedCellInteractGameJumpGameActionButton];
    
    [self.contentView bringSubviewToFront:bottom_gift_view];
    [self.contentView bringSubviewToFront:bottom_WebGameEnter_view];
    if ([self.simpleFeed isInteractGameInPlayingStatus])
    {
        bottom_gift_view.hidden = YES;
        bottom_gift_button.hidden = YES;
        bottom_func_button.hidden = YES;
        
        bottom_WebGameEnter_view.hidden = NO;
        bottom_WebGameEnter_button.hidden = NO;
    }
    else
    {
        bottom_gift_view.hidden = NO;
        bottom_gift_button.hidden = NO;
        bottom_func_button.hidden = NO;
        
        bottom_WebGameEnter_view.hidden = YES;
        bottom_WebGameEnter_button.hidden = YES;
    }
}


- (void)layoutRoomTitle:(KSimpleFeed *)simpleFeed withUIItem:(KSUIItem *)item
{
    UILabel *roomTitleLabel = [self.contentView viewWithTag:TimelineFeedCellKTVRoomTitleLabel];
    CGFloat titleMaxWidth = SCREEN_WIDTH - KSMargin_Dynamic(70) - KSMargin_Dynamic(14) - KSMargin_Dynamic(20) - KSMargin_Dynamic(14) - KSMargin_Dynamic(28);
   
    if (self.simpleFeed.ktvRoomShow && self.simpleFeed.ktvRoomWebgameStatusInfo)
    {
        BOOL isInteractGameType = [self.simpleFeed isInteractGameInPlayingStatus]; // 是否是互动游戏状态
        if(isInteractGameType)
        {
            titleMaxWidth = titleMaxWidth - KSMargin_Dynamic(80);
        }
        roomTitleLabel.width = titleMaxWidth;
    }
}

- (void)updateliteKtvPlayOrSilenceButton
{
    BOOL isMuted = self.liteKtvContainerView.isMuted;
    [self.liteKtvPlayOrSilenceButton setSelected:isMuted];
    
    UIImage *btnImage = [UIImage imageNamed:@"feed_follow_ktv_play_btn"];
    UIImage *selectedBtnImage = [UIImage imageNamed:@"feed_follow_ktv_play_btn"];
    if([self.contentView.subviews containsObject:self.liteKtvContainerView] && self.liteKtvContainerView.enterRoomResult)
    {
        btnImage = [UIImage imageNamed:@"feed_follow_ktv_silence_btn"];
        selectedBtnImage = [UIImage imageNamed:@"feed_follow_ktv_silence_btn_selected"];
    }
    else
    {
        btnImage = [UIImage imageNamed:@"feed_follow_ktv_play_btn"];
        selectedBtnImage = [UIImage imageNamed:@"feed_follow_ktv_play_btn"];
    }
    
    [self.liteKtvPlayOrSilenceButton setImage:btnImage forState:UIControlStateNormal];
    [self.liteKtvPlayOrSilenceButton setImage:selectedBtnImage forState:UIControlStateSelected];
    
    [self.contentView bringSubviewToFront:self.liteKtvPlayOrSilenceButton];
}

- (void)liteKtvPlayOrSilenceButtonClick:(id)sender
{
    if(self.liteKtvContainerView.enterRoomResult)
    {
        // 操作静音开关
        [sender setSelected:![sender isSelected]];
        [self.liteKtvContainerView liteKtvFeedMuted:[sender isSelected]];
    }
    else
    {
        // 启动播放
        [self startFeedPreviewPlayByClickWithPlayingStatus:NO];
    }
}

- (void)refreshLiveAudioPlayButton:(KSUIItem *)item {
    if (self.liveAudioButton) {
        CGRect rect = item.rect;
        CGFloat iconSize = 28;
        CGFloat audioX = rect.origin.x + rect.size.width - iconSize - 16;
        self.liveAudioButton.frame = CGRectMake(audioX, rect.origin.y + rect.size.height - iconSize - 16, iconSize, iconSize);
    }
}

- (void)feedsView:(KSFoldedFeedsView*)feedsView didClickAtIndex:(NSUInteger)index
{
    JceTimeline_s_feed_list_item *item = [feedsView.simpleFeed.foldedFeeds.feedArray safeObjectAtIndex:index];
    KSGlobalPlayItem* playItem = [[KSGlobalPlayItem alloc] init];
    playItem.ugcId = item.jce_strFeedSourceId;
    if (feedsView.simpleFeed.foldedFeeds.sourceType == 1)
    {
        playItem.fromPage = @"feed_following#creation#same_content_merging";
        playItem.detailVCFromPage = @"feed_following#creation#same_content_merging";
    }
    else if(feedsView.simpleFeed.foldedFeeds.sourceType == 2)
    {
        if (feedsView.simpleFeed.feedVCType == KSimpleFeedSourceType_TimelineRoot ||
            feedsView.simpleFeed.feedVCType ==KSimpleFeedSourceType_UserProfile)
        {
            playItem.fromPage = @"feed_following#creation#post_merge_content";
            playItem.detailVCFromPage = @"feed_following#creation#post_merge_content";
        }
        else if (feedsView.simpleFeed.feedVCType == KSimpleFeedSourceType_SingleDetail)
        {
            playItem.fromPage = @"details_of_creations#recommend#post_merge_content";
            playItem.detailVCFromPage = @"details_of_creations#recommend#post_merge_content";
        }
    }
    
    [[KSUgcPlayManager sharedManager] playUgcWithItem:playItem showPlayVC:YES];
}

- (void)feedsViewDraggindBeyondRight:(KSFoldedFeedsView*)feedsView
{
    if (self.simpleFeed.foldedFeeds.jumpMoreUrl.length > 0)
    {
        [[KSNavigationManager sharedManager] dealWithScheme:self.simpleFeed.foldedFeeds.jumpMoreUrl];
    }
    else
    {
        KSUidType uid = 0;
        if (self.simpleFeed.forwardFeed)
        {
            uid = self.simpleFeed.forwardFeed.feedUserInfo.userinfo.userId;
        }
        else
        {
            uid = self.simpleFeed.simpleUser.userinfo.userId;
        }
        
        [[KSNavigationManager sharedManager] showProfileView:uid
                                                    fromPage:@"feed_following#creation#same_content_merging"
                                                withRectItme:nil
                                                   extraInfo:nil
                                             defaultTabIndex:KSUserProfileTabIndex_New_Feeds
                                                 driftBottle:nil];
    }
    
}

//切歌了，拉取新的歌词
- (void)didReceivePlayingUgcChangedNotification:(NSNotification*)notificatoin
{
    if (notificatoin.object && [notificatoin.object isKindOfClass:[NSString class]])
    {
        
        NSString* feedId = SAFE_CAST(notificatoin.object, NSString);
        if (feedId.length>0 && [feedId isEqualToString:self.simpleFeed.simpleFeedCommon.strFeedId])
        {
            UIViewController* topVC = [[KSNavigationManager sharedManager] getTopViewController];
            if ([topVC isKindOfClass:[KSTimelineRootVC class]])
            {
                self.feedManager.videolizeView = self.audioProductVideolizeView;
                self.feedManager.currentRecPrdFeed = self.simpleFeed;
               
                if (self.simpleFeed.songInfoResult.qrc)
                {
                    [[KSUgcPlayManager sharedManager] updateLyricInfoWithSongIngoResult:self.simpleFeed.songInfoResult];
                }
                
                KS_WEAK_SELF(self);
                [self canShowFeedLyric:self.simpleFeed isNeedCheckPlayStatus:NO completeBlock:^(BOOL canShow) {
                    CHECK_SELF_AND_RETURN();
                
                    [self loadLyricAndNote:self.simpleFeed];
                }];
            }
           
        }
    }
}

- (void)didVCWillAppear
{
    if ([self.simpleFeed isGlobalPlaying])
    {
        if ([self.simpleFeed isKindOfVideoTypeFeed])
        {
            // 视频
            if ([self.simpleFeed isRecCardFeed] == NO)
            {
                // Feed 单流场景
                if ([self isFeedAutoPlay])
                {
                    [self.videoPlayView removeFromSuperview];
                    self.videoPlayView = nil;
                }
                else
                {
                    [self layoutVideoView];
                    [self.timelineVideoPlayingView addSubview:self.videoPlayView];
                    KLog(@"[视频view]setup[feedId]%@",self.simpleFeed.simpleFeedCommon.strFeedId);
                    [[KSUgcPlayManager sharedManager] setPlaybackView:self.videoPlayView];
                    [KSUgcPlayManager sharedManager].delegate = self;
                }
            }
            else
            {
                // 大卡片场景
                [self layoutVideoView];
                [self.timelineVideoPlayingView addSubview:self.videoPlayView];
                KLog(@"[视频view]setup[feedId]%@",self.simpleFeed.simpleFeedCommon.strFeedId);
                [[KSUgcPlayManager sharedManager] setPlaybackView:self.videoPlayView];
            }
        }
        else if ([self.simpleFeed isKindOfAudioTypeFeed])
        {
            // 音频
            if ([self.simpleFeed isRecCardFeed] == NO)
            {
                // Feed 单流场景
                if ([self isFeedAutoPlay])
                {   
                    if ([self.simpleFeed isKTVAudioFeed])
                    {
                        // KTV
                        [self.videoPlayView removeFromSuperview];
                        self.videoPlayView = nil;
                    }
                }
                else
                {
                    [KSUgcPlayManager sharedManager].delegate = self;
                    [KSUgcPlayMgr sharedManager].delegate = self;
                }
            }
        }
    }
}

#pragma mark KSBasePlaybackViewDelegate //是否和父容器宽度自适配，宽=父容器，高度按比例缩放
- (BOOL)enableSelfAdaptableWithContainerWidth
{
    /// 大卡片根据视频实际宽高适配，关注feed播放区域跟容器一样大小
    return [self.simpleFeed isRecCardFeed];
}

#pragma mark KSBasePlaybackViewDelegate //问业务层要容器的size
- (UIView*)getContainserView
{
    return self.timelineVideoPlayingView;
}

#pragma mark KSBasePlaybackViewDelegate //是否和父容器宽度自适配，需要判断ugcId一致性
- (KSTimelineDetail*)getSelfAdaptableUgcDetail
{
    KSTimelineDetail* detail = [KSTimelineDetail new];
    detail.ugcId = self.simpleFeed.simpleFeedCommon.strFeedId;
    detail.songName = self.simpleFeed.songinfo.name;
    return detail;
}

#pragma mark KSBasePlaybackViewDelegate //播放图层已经宽度适配了容器，通知业务层
- (void)playLayerViewDidAdapativeToContainerWidth:(KSBasePlaybackView*)playBackView
{
    if (!playBackView)
    {
        return;
    }
    CGRect internalImageViewFrame = [self getRecfeedFirstFrameImageRectByOriginRect:playBackView.videoView.frame];
    [self.bgimageView updateInternalImageViewFrame:internalImageViewFrame];
}


/// 详情页修改点赞状态通知
/// @param notification 通知
- (void)didReceiveUGCLikeStatusChangedNotification:(NSNotification *)notification {
    /**
     @{
     @"ugcID": self.timelineDetail.ugcId,
     @"likeStatus": @(resultStatus),
     @"likeCount": @(self.timelineDetail.ugcLikeInfo.jce_num),
     @"simpleFeed": self.simpleFeed
     };
     */
    
    NSString *ugcID = [notification.userInfo safeObjectForKey:@"ugcID" ClassType:NSString.class];
    NSNumber *likeStatus = [notification.userInfo safeObjectForKey:@"likeStatus" ClassType:NSNumber.class];
    NSNumber *likeCount = [notification.userInfo safeObjectForKey:@"likeCount" ClassType:NSNumber.class];
    BOOL successed = [[notification.userInfo safeObjectForKey:@"successed" ClassType:NSNumber.class] boolValue];
    BOOL isDoubleClickedRecFeed = [[notification.userInfo safeObjectForKey:@"isReDoubleClickedRecFeed" ClassType:NSNumber.class] boolValue];
    
    if ([self.simpleFeed.simpleFeedCommon.strFeedId isEqualToString:ugcID]) {
        if (!successed) {
            return;
        }
        
        self.simpleFeed.likeInfo.jce_status = likeStatus.intValue;
        self.simpleFeed.likeInfo.jce_num = likeCount.intValue;
        
        // 这里再去遍历取出点赞按钮就太麻烦了
        KS_WEAK_SELF(self);
        dispatch_async(dispatch_get_main_queue(), ^{
            KS_STRONG_SELF(self);
            
            [self updateLikeStatus:likeStatus.integerValue count:likeCount.integerValue];
            
            if ([self.simpleFeed isRecCardFeed]) {
                BOOL bFollowed = YES;
                if ((self.simpleFeed.likeInfo && self.simpleFeed.likeInfo.jce_status ==1)) {
                    bFollowed = NO;
                }
                
                if (bFollowed) {
                    if (self.cellDelegate && [self.cellDelegate respondsToSelector:@selector(playPraiseButtonLottieAnimationWithCompletion:)]) {
                        [self.cellDelegate playPraiseButtonLottieAnimationWithCompletion:nil];
                    }
                    
                    if (!isDoubleClickedRecFeed) {
                        [self updatePraiseStatus];
                    }
                                        
                    // 陌生人点赞后触发 礼物异化效果展示
                    [self showRecSendGiftBarByPraise];
                } else {
                    // 未点赞修改点赞状态
                    [self updatePraiseStatus];
                }
            } else {
                // 这里不再刷新，直接修改按钮属性，避免刷新导致闪烁
                [self updateLikeButtonState];
            }
        });
    }
}

- (void)updateLikeButtonState
{
    KSBaseButton *button = [self.contentView viewWithTag:TimelineFeedCellLikeButton];
    if ([button isKindOfClass:[KSBaseButton class]])
    {
        NSString *likeImage = @"feed_like";
        NSString *likeTitle = @"赞";
        UIColor *likeColor = UIColor.ks_primaryTextColor;
        UIFont *likeFont = [UIFont ks_fontWithFontType:KSFontType_MiddleBold];
        
        //主人态点赞状态（0=已点赞；1=未点赞）
        if (self.simpleFeed.likeInfo.jce_status == 0)
        {
            likeImage = @"feed_like_red";
            likeColor = UIColor.ks_brandColor;
        }
        
        if (self.simpleFeed.likeInfo.jce_num > 0)
        {
            if (self.simpleFeed.likeInfo.jce_num == (JceUInt32)NSNotFound)
            {
                // 异常数据处理
                self.simpleFeed.likeInfo.jce_num = 0;
            }
            else
            {
                likeTitle = [KSFormatHelper stringFormatNumber:self.simpleFeed.likeInfo.jce_num];
                likeFont = [UIFont ks_fontWithFontType:KSFontType_Small];
            }
        }
        
        CGFloat likeTitleWidth = ceil([likeTitle boundingRectWithSize:CGSizeMake(CGFLOAT_MAX, 21) options:NSStringDrawingUsesLineFragmentOrigin | NSStringDrawingUsesFontLeading attributes:@{NSFontAttributeName:likeFont} context:nil].size.width);
        CGFloat likeButtonWidth = 30 + 18 + 5 + likeTitleWidth;
        
        button.width = likeButtonWidth;
        [button setTitle:likeTitle forState:UIControlStateNormal];
        [button setImage:[UIImage imageNamed:likeImage] forState:UIControlStateNormal];
        [button setTitleColor:likeColor forState:UIControlStateNormal];
        [button.titleLabel setFont:likeFont];
    }
}

//推荐视频推荐状态保持一致
- (void)followStateChanged:(NSNotification *)notification
{
    NSDictionary *userInfo = SAFE_CAST(notification.userInfo, [NSDictionary class]);
    if (userInfo && [self.simpleFeed isRecCardFeed])
    {
        KSUidType uid = [[userInfo objectForKey:@"uid"] longLongValue];
        BOOL isFollowed = [[userInfo objectForKey:@"follow"] boolValue];
        
        if (uid == self.simpleFeed.simpleUser.userinfo.userId)
        {
            if (isFollowed) {
                self.simpleFeed.simpleUser.userinfo.relationFlag |= JceRelation_VerifyResult_VERIFYRESULT_FOLLOW;
                if ([self.simpleFeed isKindOfLiveShowFeed]) {
                    UIButton *followBtn = [self viewWithTag:TimelineFeedCellFollowUserBtn];
                    followBtn.hidden = YES;
                } else {
                    [self.layoutMgr.followBtn followed];
                }
            }
            else
            {
                self.simpleFeed.simpleUser.userinfo.relationFlag &= (~JceRelation_VerifyResult_VERIFYRESULT_FOLLOW);
                [self reloadCurrentTableView];
            }
        }
        
    }
}

- (void)reloadCurrentTableView
{
    UITableView *tv = SAFE_CAST(self.superview, UITableView.class);
    if (tv)
    {
        self.simpleFeed.layoutInfo = nil;
        [tv reloadData];
    }
}

- (void)reloadCurrentRowInTableView
{
    UITableView *tv = SAFE_CAST(self.superview, UITableView.class);
    if (tv)
    {
        self.simpleFeed.layoutInfo = nil;
        [tv reloadRowsAtIndexPaths:@[self.indexPath] withRowAnimation:UITableViewRowAnimationNone];
    }
}

- (void)updatePraiseStatus
{
    UIImage* img = (self.simpleFeed.likeInfo && self.simpleFeed.likeInfo.jce_status ==0)?[UIImage imageNamed:@"V7FeedPraise"]:[UIImage imageNamed:@"V7RecFeedLike"];
    [self.praiseBtn setImage:img forState:UIControlStateNormal];
    NSString* likeCountStr = self.simpleFeed.likeInfo.jce_num>0? [KSFormatHelper formatNewNumber:self.simpleFeed.likeInfo.jce_num]:@"赞";
    [self.praiseCountLable setText:likeCountStr];
}

// 评论成功（写操作）
- (void)didReceiveUGCCommentStatusChangedNotification:(NSNotification *)notification {
    NSString *ugcId = SAFE_STR_BM(notification.object);
    if (ugcId && [ugcId isEqualToString:self.simpleFeed.simpleFeedCommon.strFeedId]) {
        [self showRecBottomSendGiftBarGuide:@(3)];
    }
}

// 追剧通知
- (void)didReceiveUGCSubscribeSuccNotification:(NSNotification *)notification {
    NSInteger albumId = [notification.userInfo integerValueForKey:@"albumId"];
    NSInteger isSubscribed = [notification.userInfo integerValueForKey:@"subscribe"] == 1;
    if (albumId && albumId == self.simpleFeed.shortPlay.jce_lAlbumId) {
        /// 修改数据
        self.simpleFeed.shortPlay.jce_bHasCollected = isSubscribed;
        UIImage *img = isSubscribed ?
                       [UIImage imageNamed:@"V7RecFeedSubscribe_Done"] :
                       [UIImage imageNamed:@"V7RecFeedSubscribe_Normal"];
        [self.subscribeBtn setImage:img forState:UIControlStateNormal];
        self.subscribeLabel.text = isSubscribed ? @"已追剧" : @"追剧";
    }
}

#pragma mark - Utils
// 付费作品异步鉴权后起播通知
- (void)didReceivePayUGCPlayingNotification:(NSNotification *)notification
{
    if ([notification.object isKindOfClass:NSString.class])
    {
        NSString *UGCID = (NSString *)notification.object;
        if ([UGCID isEqualToString:self.simpleFeed.simpleFeedCommon.strFeedId])
        {
            [self updateCellWithPlayingStatus:YES];
        }
    }
}

// 付费作品自动播放失败后，把UI刷回未播放状态
- (void)didReceivePayUGCAutoPlayFailedNotification:(NSNotification *)notification {
    if ([notification.object isKindOfClass:NSString.class]) {
        NSString *UGCID = (NSString *)notification.object;
        if ([UGCID isEqualToString:self.simpleFeed.simpleFeedCommon.strFeedId]) {
            [self updateCellWithPlayingStatus:NO];
        }
    }
}

#pragma mark KSAudioPreviewPlayerDelegate <NSObject>
// 自动播放音频进度回调（用于滚动歌词）
- (void)audioPlayerWithCurrentPosition:(NSTimeInterval)position duration:(NSTimeInterval)duration timeliendetail:(KSTimelineDetail*)timelineDetail
{
    if ([timelineDetail.ugcId isEqualToString:self.simpleFeed.simpleFeedCommon.strFeedId])
    {
        [self updateLyricViewProgross:position withFeed:self.simpleFeed];
        
        self.simpleFeed.playingTime += position - self.lastPosition;
        self.lastPosition = position;
    }
}

- (void)audioPlayerChangeStatus:(KSAudioPlayerStatus)status params:(NSDictionary *)params {
    if (status == KSAudioPlayerStatus_Playing) {
        CGFloat currentTime = [[KSFeedPreviewManager sharedManager] currentTime];
        self.lastPosition = currentTime;
        [self updateLyricViewProgross:currentTime withFeed:self.simpleFeed];
    }
}

- (void)playerStatusChange:(KSVideoPlayerItem *)playerItem from:(KSVideoPlayerStatus)from to:(KSVideoPlayerStatus)newStatus {
    if (newStatus == KSVideoPlayerStatusPlaying) {
        CGFloat currentTime = [[KSFeedPreviewManager sharedManager] currentTime];
        self.lastPosition = currentTime;
        
        if (self.simpleFeed.songinfo.audioTransVideoPlayType == JceTimeline_EnumAudioTransVideoPlayType_eAudioTransVideo_type_native_tempalte_video) {
            [self updateLyricViewProgross:currentTime withFeed:self.simpleFeed];
        }
    }
}

- (void)syncPlayingTime:(NSTimeInterval)currentTime duration:(NSTimeInterval)duration
{
    if ([[[KSFeedPreviewManager sharedManager] playbackModel].playItem.ugcId isEqualToString:self.simpleFeed.simpleFeedCommon.strFeedId])
    {
        self.simpleFeed.playingTime += currentTime - self.lastPosition;
        self.lastPosition = currentTime;
    }
}

#pragma mark - KSImageListBrowserVCDelegate
- (NSInteger)imageListBrowserVCItemNumber:(KSImageListBrowserVC *)vc
{
    if ([self.simpleFeed isKindOfAIImageFeed]) {
        /// Ai 图片预览
        return self.simpleFeed.AiImageText.imageArr.count;
    }
    else {
        /// 音乐心情图片预览
        return [self.simpleFeed.songinfo.feedImageList count];
    }
}

- (NSString *)imageListBrowserVC:(KSImageListBrowserVC *)vc imageUrlAtIndex:(NSInteger)index
{
    if ([self.simpleFeed isKindOfAIImageFeed]) {
        /// Ai 图片 url
        KSimpleFeedAiImageInfo *aiImageInfo = [self.simpleFeed.AiImageText.imageArr safeObjectAtIndex:index];
        return aiImageInfo.imageUrl;
    }
    else {
        /// 音乐心情图片 url
        JceTimeline_picinfo *picInfo = [self.simpleFeed.songinfo.feedImageList safeObjectAtIndex:index];
        JceTimeline_pic_detail *picDetail = [picInfo.jce_mPic objectForKey:[NSNumber numberWithInt:kSimpleFeedImageSize640]];
        return picDetail.jce_strUrl;
    }
}

// 由外部处理关闭事件
- (void)imageListBrowserVCRequestClose:(KSImageListBrowserVC *)vc
{
    [vc dismissViewControllerAnimated:YES completion:nil];
}

/// 进入相册浏览
- (void)presentImageListBrowser:(NSInteger)index number:(NSInteger)number
{
    // 进大图预览页面
    KSImageListBrowserConfig config = KSImageListBrowserConfigIndicatorNum  |
                                      KSImageListBrowserConfigTotalFixed    |
                                      KSImageListBrowserConfigDataSourceURL |
                                      KSImageListBrowserConfigSupportDesc;
    KSImageListBrowserVC *browserVC = [[KSImageListBrowserVC alloc] initWithIndex:index config:config];
    browserVC.delegate = self;
    browserVC.indicatorNumber = number;
    [[[KSNavigationManager sharedManager] getTopViewController] presentViewController:browserVC animated:YES completion:nil];
}

#pragma mark - KSHorizonalBannerViewDelegate
- (void)ksHorizonalBannerView:(KSHorizonalBannerView *)bannerVie didScrollToItemNewIndex:(NSInteger)index {
    NSInteger curIndex = index;
    if (curIndex == 0 || curIndex > self.simpleFeed.songinfo.feedImageList.count) {
        curIndex = self.simpleFeed.songinfo.feedImageList.count;
    }

    NSString *numString = [NSString stringWithFormat:@"%ld/%ld", curIndex, self.simpleFeed.songinfo.feedImageList.count];
    CGSize numStringSize = [numString kSongSizeWithFont:[KSLayoutUIManagerTimeline getAvantFontWithSize:12 bold:YES]];
    CGFloat bgViewWidth = numStringSize.width + 8 + 8;
    CGFloat bgViewHeight = numStringSize.height + 3 + 7;
    CGRect bgViewRect = CGRectMake(self.musicMoodBannerView.width - 8 - bgViewWidth, 8, bgViewWidth, bgViewHeight);
    self.musicMoodPicNumLabelBgView.frame = bgViewRect;
    self.musicMoodPicNumLabel.text = numString;
    self.musicMoodPicNumLabel.frame = CGRectMake(8, 6.5, numStringSize.width, numStringSize.height);
    
    for (UIView *view in self.musicMoodPicProcessView.subviews) {
        if (view.tag <= curIndex) {
            view.backgroundColor = [UIColor ks_colorWithRGBHex:0xFFFFFF alpha:0.75];
        } else {
            view.backgroundColor = [UIColor ks_colorWithRGBHex:0xFFFFFF alpha:0.25];
        }
    }
    self.simpleFeed.musicMoodePicIndex = curIndex;
}


- (void)ksHorizonalBannerView:(KSHorizonalBannerView *)bannerView
         didClickWithItemData:(KSHorizonalBannerItemData *)itemData
                    indexPath:(NSIndexPath *)indexPath
{
    [self presentImageListBrowser:itemData.realIndex number:self.simpleFeed.songinfo.feedImageList.count];
    
    [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
        reportModel.key = @"feed_nearby#creation#image#click#0";
        reportModel.ugcmask1 = self.simpleFeed.songinfo.ugcMask;
        reportModel.ugcmask2 = self.simpleFeed.songinfo.ugcMaskExt;
        reportModel.ugcid = self.simpleFeed.simpleFeedCommon.strFeedId;
        reportModel.touid = self.simpleFeed.simpleUser.userinfo.userId;
        reportModel.commonInt1 = indexPath.row + 1;

    } simpleFeed:self.simpleFeed];
}

#pragma mark - KSFeedPlayBarDelegate
// 点击开始播放音乐
- (void)KSFeedPlayBar:(KSFeedPlayBar *)feedPlayBar playBtnDidClickedForMarqueeSong:(UIButton *)playBtn {
    return;
}

#pragma mark - 评论点赞数更新
// 针对2022新版feed UI
- (void)updateLikeStatus:(NSInteger)likeStatus count:(NSInteger)count {

    if (likeStatus == 0) { // 已点赞(点赞动画夭折了>.<)
        self.likeImageView.image = [UIImage imageNamed:@"feed_timeline_like_number_red"];
        self.likeCountLabel.text = [KSFormatHelper stringFormatNumber:self.simpleFeed.likeInfo.jce_num];
        self.likeCountLabel.textColor = [UIColor ks_colorWithRGBHex:0xff4d4b];
        [self.likeCountLabel sizeToFit];
    } else {
        if (self.simpleFeed.likeInfo.jce_num > 0) {
            self.likeImageView.image = [UIImage imageNamed:@"feed_timeline_like_number"];
            self.likeCountLabel.text = [KSFormatHelper stringFormatNumber:self.simpleFeed.likeInfo.jce_num];
            self.likeCountLabel.textColor = [UIColor ks_colorWithRGBHex:0x323333];
            [self.likeCountLabel sizeToFit];
        } else {
            self.likeImageView.image = [UIImage imageNamed:@"feed_timeline_like_no_number"];
            self.likeCountLabel.text = @"";
        }
    }
}

- (UIImage *)getPlayAndStopButtonImage:(BOOL)normal black:(BOOL)black {
    if ([self.simpleFeed isAudioTransferToMusicMood]) {
        // 音频转音乐心情样式，需要使用白色按钮
        black = NO;
    }
    NSString *imageName = @"";
    if (normal) {
        imageName = black ? @"feed_timeline_play_icon_black" : @"feed_timeline_play_icon";
    } else {
        imageName = black ? @"feed_timeline_stop_icon_black" : @"feed_timeline_stop_icon";
    }
    return [UIImage imageNamed:imageName];
}

- (void)addRotateLayerWithButton:(UIButton *)button isAudio:(BOOL)isAudio {
    [self.playButtonRotateLayer removeFromSuperlayer];
    [self.playButtonRotateLayer removeAllAnimations];
    CGRect layerFrame = self.playButtonRotateLayer.frame;
    CGFloat layerX = button.frame.origin.x - ((layerFrame.size.width - button.frame.size.width) / 2);
    CGFloat layerY = button.frame.origin.y - ((layerFrame.size.height - button.frame.size.height) / 2);
    CGRect newLayerFrame = CGRectMake(layerX, layerY, layerFrame.size.width, layerFrame.size.height);
    self.playButtonRotateLayer.frame = newLayerFrame;
    if (isAudio) {
        self.playButtonRotateLayer.strokeColor = [UIColor blackColor].CGColor;
    } else {
        self.playButtonRotateLayer.strokeColor = [UIColor whiteColor].CGColor;
    }
    [self.contentView.layer addSublayer:self.playButtonRotateLayer];
    [self.playButtonRotateLayer addAnimation:self.playButtonRotateAnimation forKey:@"playButtonRotateAnimation"];
}

// Feed 送礼样式异化
- (void)showFeedQuickGift {
    if (self.simpleFeed.quickGiftAniStatus == KSFeedQuickGiftAniStatusInit) {
        if (self.simpleFeed.followGiftBtnInfo) {
            [self showFollowFeedSpecialSendGiftBtn];
        } else if ([[KSABTestManager sharedManager] isShowHeatEntrance]) {
            KS_WEAK_SELF(self);
            // 请求礼物数据后展示
            KSimpleFeedManager *feedManager = (KSimpleFeedManager *)self.cellDelegate;
            if (feedManager) {
                [feedManager getFeedGiftInfoWithFeed:self.simpleFeed completionBlk:^(BOOL success) {
                    KS_STRONG_SELF(self);
                    if (success) {
                        [self showFollowFeedSpecialSendGiftBtn];
                    }
                }];
            }
        }
    }
}

- (void)removeAllSpecialSendGiftBtn {
    if (!self.forbidQuickGiftAndHonor) {
        KINFO(@"[AdFreeHotCard] 热度卡未展示 禁止清理异化Btn");
        return;
    }
    
    if (self.followFeedSpecialSendGiftBtn) {
        [self.followFeedSpecialSendGiftBtn removeFromSuperview];
        self.followFeedSpecialSendGiftBtn = nil;
    }
    if (self.honorItemView) {
        [self.honorItemView removeFromSuperview];
        self.honorItemView = nil;
    }
    if (self.followFeedAITakePicView) {
        [self.followFeedAITakePicView removeFromSuperview];
        self.followFeedAITakePicView = nil;
    }
}

- (void)layoutMiniHeatCardBtn {
    UIView *miniHeatView = [self.contentView viewWithTag:TimelineFeedCellMiniHeatCardFadeView];
    if (miniHeatView) {
        if (self.freeHotBtn) {
            [self.freeHotBtn removeFromSuperview];
            self.freeHotBtn = nil;
        }
        self.freeHotBtn = [KSFollowAdFreeHotBtn.alloc initWithFrame:CGRectMake(miniHeatView.x, miniHeatView.y, miniHeatView.width, miniHeatView.height)];
        [self.contentView addSubview:self.freeHotBtn];
        UITapGestureRecognizer *tap = [UITapGestureRecognizer.alloc initWithTarget:self action:@selector(didTapAdFreeHotBtn)];
        [self.freeHotBtn addGestureRecognizer:tap];
        [self.freeHotBtn updateUIWithRemainCount:self.simpleFeed.miniHeatCardItem.jce_lNum];
    }
}

- (void)didTapAdFreeHotBtn {
    if (self.simpleFeed.miniHeatCardItem.jce_lNum <= 0) {
        return;
    }
    [KSAdFreeHotCardManager.sharedManager jumpToShowAdWithUgcId:self.simpleFeed.miniHeatCardItem.jce_strUgcid
                                                       fromPage:KSAdFreeHotCardFromFollowFeedStarPusher];
    
    [KSTraceReprotHelper_V2 reportDataWithBlock:^(KSTraceReportModel_V2 *reportModel) {
        reportModel.key = @"feed_creation#xtg_recommend#but#click#0";
        reportModel.commonInt1 = self.simpleFeed.miniHeatCardItem.jce_lNum;
        reportModel.commonInt2 = 1;
        reportModel.ugcid = self.simpleFeed.miniHeatCardItem.jce_strUgcid;
        reportModel.touid = self.simpleFeed.miniHeatCardItem.jce_userinfo.jce_user.jce_lUid;
        reportModel.mid = self.simpleFeed.songinfo.songMid;
    }];
}

/// 关注feed送礼按钮位置异化
- (void)layoutFollowFeedSpecialSendGiftBtn {
    if (![self.simpleFeed isKindOfUgcTypeFeed] &&
        ![self.simpleFeed isKindOfAIImageFeed]) {
        return;
    }
    
    if (self.simpleFeed.needShowFeedAITakePic) {
        /// 展示AI拍同款入口
        UIView *parentView = [self.contentView viewWithTag:TimelineFeedCellFollowFeedAITakePic];
        if (parentView && !self.followFeedAITakePicView) {
            self.followFeedAITakePicView = [[KSFollowFeedAITakePicView alloc] initWithFrame:parentView.frame];
            [self.followFeedAITakePicView updateFeed:self.simpleFeed];
            [self.contentView addSubview:self.followFeedAITakePicView];
            
            KS_WEAK_SELF(self);
            self.followFeedAITakePicView.clickBlock = ^{
                CHECK_SELF_AND_RETURN()
                
                KSDrawItemAction *action = [KSDrawItemAction new];
                action.type = DRAWITEM_ACTION_TYPE_FEED_CLICK_AI_TAKE_PIC;
                action.busiData = self.simpleFeed;
                [self handleAction:action];
            };
        }
        return;
    }
    
    if (self.simpleFeed.followGiftBtnInfo) {
        KS_WEAK_SELF(self);
        // 作品Feed送礼按钮异化
        UIView *normalGiftView = [self.contentView viewWithTag:TimelineFeedCellFadeGiftView];
        if (normalGiftView) {
            if (self.followFeedSpecialSendGiftBtn) {
                [self.followFeedSpecialSendGiftBtn removeFromSuperview];
                self.followFeedSpecialSendGiftBtn = nil;
            }
            
            if (self.simpleFeed.followGiftBtnInfo.jce_unDataType == proto_feed_webapp_ENUM_DATA_TYPE_AFTER_EXP_EM_DATA_TYPE_AFTER_EXP_QUICK_GIFT &&
                self.simpleFeed.giftGuide) {
                if (self.simpleFeed.giftGuide.isFreeAd) {
                    // 异化看广告送礼
                    self.followFeedSpecialSendGiftBtn = [[KSFeedCellFreeAdButton alloc] initWithFeed:self.simpleFeed parentFrame:normalGiftView.frame];
                } else {
                    // 异化快捷送礼
                    self.followFeedSpecialSendGiftBtn = [[KSFeedCellQuickGiftButton alloc] initWithFeed:self.simpleFeed parentFrame:normalGiftView.frame];
                }
                // 曝光
                self.followFeedSpecialSendGiftBtn.exposeBlk = ^{
                    CHECK_SELF_AND_RETURN()
                    [self onFeedQuickGiftBtnExposeWithSimpleFeed:self.simpleFeed quickGiftGuide:self.simpleFeed.giftGuide];
                };
            } else if (self.simpleFeed.followGiftBtnInfo.jce_unDataType == proto_feed_webapp_ENUM_DATA_TYPE_AFTER_EXP_EM_DATA_TYPE_AFTER_EXP_RANK) {
                // 异化上榜按钮（调低优先级，后续单独提单下掉）
                self.followFeedSpecialSendGiftBtn = [[KSFollowFeedRushGiftListButton alloc] initWithFeed:self.simpleFeed parentFrame:normalGiftView.frame];
                self.followFeedSpecialSendGiftBtn.showedBlk = ^{
                    CHECK_SELF_AND_RETURN();
                    [self.contentView addSubview:self.honorItemView];
                };
                self.followFeedSpecialSendGiftBtn.dismissedBlk = ^{
                    CHECK_SELF_AND_RETURN();
                    self.honorItemView.hidden = YES;
                };
            } else {
                KDEBUG(@"[FeedQG] 缺少送礼按钮%p", normalGiftView);
                return;
            }

            [self.contentView addSubview:self.followFeedSpecialSendGiftBtn];
            [self.followFeedSpecialSendGiftBtn addTarget:self action:@selector(didTapFollowSpecialGiftBtn:) forControlEvents:UIControlEventTouchUpInside];
        } else {
            KDEBUG(@"[FeedQG] 缺少送礼按钮%p", normalGiftView);
        }
        
        UIView *maskView = [self.contentView viewWithTag:TimelineFeedCellGiftListFadeView];
        if (maskView
            && self.simpleFeed.followGiftBtnInfo.jce_unDataType == proto_feed_webapp_ENUM_DATA_TYPE_AFTER_EXP_EM_DATA_TYPE_AFTER_EXP_RANK) {
            KSNSDHonorCellItemViewModel *item = [[KSNSDHonorCellItemViewModel alloc] initWithFollowFeedData:self.simpleFeed.followGiftBtnInfo.jce_stFeedRankDetail];
            if (self.honorItemView) {
                [self.honorItemView removeFromSuperview];
                self.honorItemView = nil;
            }
            CGRect frame = CGRectMake(0, 0, maskView.width, maskView.height + 10);
            self.honorItemView = [KSNewSinglesDetailHonorItemView.alloc initWithFrame:frame viewModel:item];
            self.honorItemView.bottom = maskView.bottom + 10;
            self.honorItemView.left = maskView.left;
            self.honorItemView.didClickItemBlk = ^(KSNSDHonorCellItemViewModel *itemViewModel) {
                if (!IS_EMPTY_STR(itemViewModel.jumpURL)) {
                    // 跳转
                    [[KSNavigationManager sharedManager] dealWithScheme:itemViewModel.jumpURL];
                }
            };
            self.followFeedSpecialSendGiftBtn.showedBlk = ^{
                CHECK_SELF_AND_RETURN();
                self.honorItemView.hidden = NO;
                [self.contentView addSubview:self.honorItemView];
            };
            
            if (self.simpleFeed.quickGiftAniStatus != KSFeedQuickGiftAniStatusInit
                && self.simpleFeed.quickGiftAniStatus != KSFeedQuickGiftAniStatusNone)  {
                [self.contentView addSubview:self.honorItemView];
            }
            
            if (self.simpleFeed.quickGiftAniStatus == KSFeedQuickGiftAniStatusShowing) {
                self.honorItemView.hidden = NO;
            } else {
                self.honorItemView.hidden = YES;
            }
        }
    }
}

- (void)showFollowFeedSpecialSendGiftBtn {
    if (self.forbidQuickGiftAndHonor) {
        // 假feed展示热度卡逻辑
        KINFO(@"[AdFreeHotCard] 热度卡展示中 禁止异化Btn");
        [self removeAllSpecialSendGiftBtn];
        return;
    }
    [self layoutFollowFeedSpecialSendGiftBtn];
    [self.followFeedSpecialSendGiftBtn updateWithFeed:self.simpleFeed];
    [self.followFeedSpecialSendGiftBtn startQuickGiftAnimation];
    
    if (self.simpleFeed.AIPicSingInfo) {
        [self.followFeedAITakePicView startAnimation];
    }
}

// MARK: 关注-快捷送礼/免费送-按钮点击
- (void)didTapFollowSpecialGiftBtn:(UIButton *)sender {
    KSTimelineRootVC *rootVC = SAFE_CAST(self.currenVC, KSTimelineRootVC);
    if (self.simpleFeed.followGiftBtnInfo.jce_unDataType == proto_feed_webapp_ENUM_DATA_TYPE_AFTER_EXP_EM_DATA_TYPE_AFTER_EXP_RANK) {
        // 送礼上榜（后面提单下掉）
        KSDrawItemAction *action = [KSDrawItemAction actionWithType:DRAWITEM_ACTION_TYPE_SEND_PRESENT];
        action.busiData = self.simpleFeed;
        [rootVC.feedManager didTapSendGiftWithCell:self didClickWithAction:action];
    } else if (self.simpleFeed.followGiftBtnInfo.jce_unDataType == proto_feed_webapp_ENUM_DATA_TYPE_AFTER_EXP_EM_DATA_TYPE_AFTER_EXP_QUICK_GIFT) {
        // 广告免费送
        BOOL isFreeAd = self.simpleFeed.giftGuide.isFreeAd;
        [self reportFeedQuickGiftBtnClickWithSimpleFeed:self.simpleFeed isFreeAd:isFreeAd];
        if (isFreeAd) {
            [self.followFeedSpecialSendGiftBtn cancelQuickGiftAnimation];
            if (rootVC.feedManager && [rootVC.feedManager respondsToSelector:@selector(freeAdGiftButtonDidClickWithFeed:)]) {
                [rootVC.feedManager freeAdGiftButtonDidClickWithFeed:self.simpleFeed];
            }
        } else {
            // 普通快捷送礼
            if (rootVC.feedManager && [rootVC.feedManager respondsToSelector:@selector(quickGiftButtonDidClickWithFeed:)]) {
                [rootVC.feedManager quickGiftButtonDidClickWithFeed:self.simpleFeed];
            }
            [self.followFeedSpecialSendGiftBtn cancelQuickGiftAnimation];
        }
    }
}

// 送礼成功通知
- (void)didReceiveSendGiftSuccessNotification {
    if ([self.simpleFeed isFollowFeed]) {
        if (self.simpleFeed.followGiftBtnInfo.jce_unDataType == proto_feed_webapp_ENUM_DATA_TYPE_AFTER_EXP_EM_DATA_TYPE_AFTER_EXP_RANK &&
            self.simpleFeed.quickGiftAniStatus == KSFeedQuickGiftAniStatusShowing) {
            // 榜单
            [self.followFeedSpecialSendGiftBtn cancelQuickGiftAnimation];
        } else if (self.simpleFeed.followGiftBtnInfo.jce_unDataType == proto_feed_webapp_ENUM_DATA_TYPE_AFTER_EXP_EM_DATA_TYPE_AFTER_EXP_QUICK_GIFT) {
            if (self.simpleFeed.giftGuide.isFreeAd) {
                // 看广告免费送
                KSAdFreeGiftScene *scene = [KSAdFreeGiftScene sceneWithType:KSAdFreeGiftSceneType_FollowFeed];
                if (scene.scenceShow) {
                    KSTimelineRootVC *rootVC = SAFE_CAST(self.currenVC, KSTimelineRootVC);
                    [rootVC.feedManager getFeedFreeADGiftInfoWithScene:KSAdFreeGiftSceneType_FollowFeed completionBlk:^(proto_feed_webapp_GetDataAfterExposureRsp *rsp, KSimpleFeedGiftGuide *gift) {
                        [KSTimelineManager sharedManager].adFreeGiftBtnInfo = rsp;
                        [KSTimelineManager sharedManager].adFreeGift = gift;
                        if (!rsp || !gift) {
                            UIView *baseGiftBtn = [self viewWithTag:TimelineFeedCellDoubleGiftBaseGiftTag];
                            [UIView animateWithDuration:0.2 animations:^{
                                self.followFeedSpecialSendGiftBtn.alpha = 0;
                                [self.followFeedSpecialSendGiftBtn removeFromSuperview];
                                self.followFeedSpecialSendGiftBtn = nil;
                                if (baseGiftBtn) {
                                    baseGiftBtn.alpha = 0;
                                    [baseGiftBtn removeFromSuperview];
                                }
                            }];
                        }
                    }];
                } else {
                    [KSTimelineManager sharedManager].adFreeGiftBtnInfo = nil;
                    [KSTimelineManager sharedManager].adFreeGift = nil;
                    UIView *baseGiftBtn = [self viewWithTag:TimelineFeedCellDoubleGiftBaseGiftTag];
                    [UIView animateWithDuration:0.2 animations:^{
                        self.followFeedSpecialSendGiftBtn.alpha = 0;
                        [self.followFeedSpecialSendGiftBtn removeFromSuperview];
                        self.followFeedSpecialSendGiftBtn = nil;
                        if (baseGiftBtn) {
                            baseGiftBtn.alpha = 0;
                            [baseGiftBtn removeFromSuperview];
                        }
                    }];
                }
            }
        }
    }
}

#pragma mark -- KSFollowKTVRoomFeedContainerDelegate
- (void)followKTVRoomFeedContainer:(KSFollowKTVRoomFeedContainer *)feedContainer reloadInteractGameInfoWith:(id)gameInfo
{
    if(![self.simpleFeed isPublicSingleKtvFeed]){
        return;
    }
    
    UILabel *roomTitleLabel = [self.contentView viewWithTag:TimelineFeedCellKTVRoomTitleLabel];
    CGFloat titleMaxWidth = SCREEN_WIDTH - KSMargin_Dynamic(70) - KSMargin_Dynamic(14) - KSMargin_Dynamic(20) - KSMargin_Dynamic(14) - KSMargin_Dynamic(28);
    if([gameInfo isKindOfClass:proto_feed_webapp_ktv_game_status.class])
    {
        // 这里数据变化了，需要清空layoutinfo触发他的重新生成渲染
        self.simpleFeed.layoutInfo = nil;
        if (self.simpleFeed.ktvRoomShow)
        {
            self.simpleFeed.ktvRoomShow.jce_stGameStatus = gameInfo;
            self.simpleFeed.liteKtvGameIcon = self.simpleFeed.ktvRoomWebgameStatusInfo.jce_stComm.jce_strIcon;
            [self.interactGameContainer updateUIWithFeed:self.simpleFeed];
        } else {
            self.simpleFeed.extraKtvRoomWebgameStatus = gameInfo;
            self.simpleFeed.liteKtvGameIcon = [(proto_feed_webapp_ktv_game_status *)gameInfo jce_stComm].jce_strIcon;
            [self.interactGameContainer updateUIWithFeed:self.simpleFeed];

        }
        
        BOOL isInteractGameType = [self.simpleFeed isInteractGameInPlayingStatus]; // 是否是互动游戏状态
        if(isInteractGameType)
        {
            // Update strTicketDoc
            NSString *strTicketDoc = [self.simpleFeed.ktvRoomWebgameStatusInfo.jce_stComm.jce_mapExt safeObjectForKey:@"strTicketDoc"];
            [self.liteKtvOnlineNumContainerView refreshUIDataWithstrTicketDoc:strTicketDoc];
            
            titleMaxWidth = titleMaxWidth - KSMargin_Dynamic(80);
            
            [self updateLiteKtvGameStatusTextWith:@"游戏中"];
            
            // 设置游戏封面
            NSString *coverUrl =  !IS_EMPTY_STR_BM(self.simpleFeed.ktvRoomWebgameStatusInfo.jce_stGameWebview.jce_strBgURL) ? self.simpleFeed.ktvRoomWebgameStatusInfo.jce_stGameWebview.jce_strBgURL : self.simpleFeed.ktvRoomShow.jce_strCoverUrl;
            // 一些游戏没有封面，降级先用歌房封面
            if(IS_EMPTY_STR_BM(coverUrl)) {
                if (self.simpleFeed.ktvRoomMike) {
                    coverUrl = self.simpleFeed.ktvRoomMike.jce_strCoverUrl;
                }
            }
            UIImageView *coverImageView = [self.contentView viewWithTag:TimelineFeedCellLiveShowCoverImage];
            [coverImageView setImageWithUrl:coverUrl placeholderImage:[KSLayoutUIManager cahceImageWithName:@"DefaultAlbum"]];
        }
        
        roomTitleLabel.width = titleMaxWidth;
    }
    else if ([gameInfo isKindOfClass:proto_unified_ktv_game_CommonWebGameInfo.class])
    {
        proto_unified_ktv_game_CommonWebGameInfo *commonWebGameInfo = SAFE_CAST(gameInfo, proto_unified_ktv_game_CommonWebGameInfo);
        // Update strTicketDoc
        NSString *strTicketDoc = [commonWebGameInfo.jce_mapExt safeObjectForKey:@"strTicketDoc"];
        [self.liteKtvOnlineNumContainerView refreshUIDataWithstrTicketDoc:strTicketDoc];
    }
    if(![gameInfo isKindOfClass:proto_feed_webapp_ktv_game_status.class])
    {
        self.simpleFeed.extraKtvRoomWebgameStatus = nil;
    }
    
    [self layoutBottomGiftButtonForPublicFeed:self.simpleFeed withUIItem:nil];
}

- (void)followKTVRoomFeedContainer:(KSFollowKTVRoomFeedContainer *)feedContainer updatePlayingSongNameWith:(NSString *)songName
{
    NSInteger iMemberNum = 0;
    if (self.simpleFeed.ktvRoomShow)
    {
        iMemberNum = self.simpleFeed.ktvRoomShow.jce_uOnlineNum;
    }
    else if (self.simpleFeed.ktvRoomMike)
    {
        iMemberNum = self.simpleFeed.ktvRoomMike.jce_uOnlineNum;
    }
    
    [self updateOnlineMemberNumWith:iMemberNum playingSongName:songName];
}

- (void)followKTVRoomFeedContainer:(KSFollowKTVRoomFeedContainer *)feedContainer updateOnlineMemberNumWith:(NSInteger)onlineMemberNum
{
    NSString *curPlayingSongName = @"";
    if (self.simpleFeed.ktvRoomShow)
    {
        curPlayingSongName = self.simpleFeed.ktvRoomShow.jce_strCurrSongName;
    }
    else if (self.simpleFeed.ktvRoomMike)
    {
        curPlayingSongName = self.simpleFeed.ktvRoomMike.jce_strSongName;
    }
    
    [self updateOnlineMemberNumWith:onlineMemberNum playingSongName:curPlayingSongName];
}

- (void)followKTVRoomFeedContainer:(KSFollowKTVRoomFeedContainer *)feedContainer notifyEenterRoomResultWith:(BOOL)result
{
    if(result)
    {
        self.liteKtvPlayOrSilenceButton.hidden = NO;
    }
    else
    {
        self.liteKtvPlayOrSilenceButton.hidden = YES;
    }
    
    [self updateliteKtvPlayOrSilenceButton];
}

- (void)followKTVRoomFeedContainer:(KSFollowKTVRoomFeedContainer *)feedContainer updateGameReportIdChangeWith:(NSUInteger)gameReportId
                    gameStatusWith:(NSString *)gameStatusText
                       newGameType:(NSInteger)gameType;
{
    self.simpleFeed.ktvFeed_report_gameType = gameReportId;
    self.simpleFeed.liteKtvGameType = gameType;
    self.simpleFeed.liteKtvGameName = gameStatusText;
    [self updateLiteKtvGameStatusTextWith:gameStatusText];
    
    // 重设feed封面
    NSString *coverUrl = self.simpleFeed.ktvRoomShow.jce_strCoverUrl;
    if (IS_EMPTY_STR(coverUrl) && self.simpleFeed.ktvRoomMike.jce_strCoverUrl) {
        coverUrl = self.simpleFeed.ktvRoomMike.jce_strCoverUrl;
    }
    if(gameType == proto_unified_ktv_game_emUnifiedKtvGameType_EM_UNIFIED_KTV_COMMON_WEB_GAME) {
        coverUrl = !IS_EMPTY_STR_BM(self.simpleFeed.ktvRoomWebgameStatusInfo.jce_stGameWebview.jce_strBgURL) ? self.simpleFeed.ktvRoomWebgameStatusInfo.jce_stGameWebview.jce_strBgURL : coverUrl;
    }
    UIImageView *coverImageView = [self.contentView viewWithTag:TimelineFeedCellLiveShowCoverImage];
    [coverImageView setImageWithUrl:coverUrl placeholderImage:[KSLayoutUIManager cahceImageWithName:@"DefaultAlbum"]];
    
    if(gameType != proto_unified_ktv_game_emUnifiedKtvGameType_EM_UNIFIED_KTV_COMMON_WEB_GAME)
    {
        // 清理布局的layoutInfo
        [self clearLiteKtvInteractGameLayout];
        
        NSString *curPlayingSongName = @"";
        NSInteger iMemberNum = 0;
        if (self.simpleFeed.ktvRoomShow)
        {
            iMemberNum = self.simpleFeed.ktvRoomShow.jce_uOnlineNum;
            curPlayingSongName = self.simpleFeed.ktvRoomShow.jce_strCurrSongName;
        }
        else if (self.simpleFeed.ktvRoomMike)
        {
            iMemberNum = self.simpleFeed.ktvRoomMike.jce_uOnlineNum;
            curPlayingSongName = self.simpleFeed.ktvRoomMike.jce_strSongName;
        }
        [self updateOnlineMemberNumWith:iMemberNum playingSongName:curPlayingSongName];
    }
    
    [self layoutBottomGiftButtonForPublicFeed:self.simpleFeed withUIItem:nil];
}

- (void)updateOnlineMemberNumWith:(NSInteger)onlineMemberNum playingSongName:(NSString *)songName
{
    // 观众数
    NSString *strAudience = nil;
    // mic数
    NSString *strMic = nil;
    // 歌房内歌名
    NSString *currentSongName = nil;
    if (self.simpleFeed.ktvRoomShow)
    {
        self.simpleFeed.ktvRoomShow.jce_strCurrSongName = songName;
        self.simpleFeed.ktvRoomShow.jce_uOnlineNum = (JceUInt32)onlineMemberNum;
        
        strAudience = [KSFormatHelper formatNewNumber:self.simpleFeed.ktvRoomShow.jce_uOnlineNum];
        strMic = [KSFormatHelper formatNewNumber:self.simpleFeed.ktvRoomShow.jce_uWaitMikeNum];
        currentSongName = self.simpleFeed.ktvRoomShow.jce_strCurrSongName;
        
        // 除游戏玩法才可以更新
        if(self.simpleFeed.liteKtvGameType != proto_unified_ktv_game_emUnifiedKtvGameType_EM_UNIFIED_KTV_COMMON_WEB_GAME)
        {
            [self.liteKtvOnlineNumContainerView refreshUIDataWithAudienceNum:strAudience
                                                                      micNum:strMic
                                                             currentSongName:currentSongName];
        }
    }
    else if (self.simpleFeed.ktvRoomMike)
    {
        self.simpleFeed.ktvRoomMike.jce_strSongName = songName;
        self.simpleFeed.ktvRoomMike.jce_uOnlineNum = (JceUInt32)onlineMemberNum;
        
        strAudience = [KSFormatHelper formatNewNumber:self.simpleFeed.ktvRoomMike.jce_uOnlineNum];
        strMic = [KSFormatHelper formatNewNumber:self.simpleFeed.ktvRoomMike.jce_uWaitMikeNum];
        currentSongName = self.simpleFeed.ktvRoomMike.jce_strSongName;
        
        [self.liteKtvOnlineNumContainerView refreshUIDataWithAudienceNum:strAudience
                                                                  micNum:strMic
                                                         currentSongName:currentSongName];
    }

}

- (void)updateLiteKtvGameStatusTextWith:(NSString *)gameStatusText
{
    // 状态文字的size
    UIFont *font = _font_mid([KSLayoutUIManagerTimeline getAvantFontWithSize:12 bold:YES]);
    CGSize roomStatusTextSize = [gameStatusText kSongSizeWithFont:font];
   
    CGFloat totalWidth = self.liteKtvNormalStatusWidth; // 状态文字左右padding给7
    if(roomStatusTextSize.width > 0)
    {
        totalWidth = self.liteKtvNormalStatusWidth + roomStatusTextSize.width + KSMargin_Dynamic(7) + KSMargin_Dynamic(7);
    }
    
    self.liteKtvGameStatusLabel.text = gameStatusText;
    
    UIView *statusBgContainer = [self.contentView viewWithTag:TimelineFeedCellKTVStatusMask];
    statusBgContainer.width = totalWidth;
    
    self.liteKtvGameStatusLabel.width = totalWidth - self.liteKtvNormalStatusWidth;
}

- (void)updateWebGameEnterButtonText
{
    NSString *strIcon = self.simpleFeed.liteKtvGameIcon;
    if(self.simpleFeed.ktvRoomWebgameStatusInfo)
    {
        strIcon = self.simpleFeed.ktvRoomWebgameStatusInfo.jce_stGameWebview.jce_strBgURL;
        if (strIcon == nil || strIcon.length == 0) {
            strIcon = self.simpleFeed.ktvRoomWebgameStatusInfo.jce_stComm.jce_strIcon;
        }
    }
    
    NSString *gameName = self.simpleFeed.liteKtvGameName;
    if(self.simpleFeed.ktvRoomWebgameStatusInfo.jce_stComm.jce_strGameName)
    {
        gameName = self.simpleFeed.ktvRoomWebgameStatusInfo.jce_stComm.jce_strGameName;
    }
    
    CGSize gameIconSize = CGSizeMake(18, 18);
    UIFont *gameNameFont = _font_mid_320([UIFont ks_fontWithFontType:KSFontType_SmallBold]);
    CGSize gameNameSize = [gameName kSongSizeWithFont:gameNameFont];

    CGFloat enterGameButtonWidth = 13 + gameIconSize.width + 5 + gameNameSize.width + 12;
    CGFloat enterGameButtonHeight = _size_mid_320(32);
    
    UIView *webGameEnterView = [self.contentView viewWithTag:TimelineFeedCellInteractGameJumpGameBackground];
    webGameEnterView.width = enterGameButtonWidth;
    
    UIImageView *webGameIconView = [webGameEnterView viewWithTag:TimelineFeedCellInteractGameJumpGameIcon];
    [webGameIconView setImageWithUrl:strIcon placeholderImage:[KSLayoutUIManager cahceImageWithName:@"DefaultAlbum"]];
    
    UILabel *webGameLabel = [webGameEnterView viewWithTag:TimelineFeedCellInteractGameBottomGiftTextLabel];
    [webGameLabel setText:gameName];
    webGameLabel.width = gameNameSize.width;
    webGameLabel.y = (enterGameButtonHeight-gameNameSize.height) * 0.5;
}



// 清理游戏互动layout
- (void)clearLiteKtvInteractGameLayout
{
    [self.interactGameContainer clearGameContainer];
}

#pragma mark -- KSFollowKTVRoomFeedInteractGameContainerDelegate
- (void)kInteractGameContainer:(KSFollowKTVRoomFeedInteractGameContainer *)gameContainer didOperateWithFeed:(KSimpleFeed *)simpleFeed
{
    BOOL isGamePlaying = [[simpleFeed.ktvRoomWebgameStatusInfo.jce_stComm.jce_mapExt safeObjectForKey:@"uStatus"] integerValue] == 2;
    KSDrawItemAction *action = [KSDrawItemAction new];
    action.type = isGamePlaying? DRAWITEM_ACTION_TYPE_INTERACTGAME_STARTMATCH : DRAWITEM_ACTION_TYPE_INTERACTGAME_JOINGAME;
    action.busiData = simpleFeed;
    [self handleAction:action];
}


- (void)handleAction:(KSDrawItemAction *)action
{
    // 子类处理一下待传入的数据
    KSimpleFeed *simpleFeed = (KSimpleFeed *)self.busiData;
    if([simpleFeed isPublicSingleKtvFeed] )
    {
        simpleFeed.liteKTVPreloadEnterInfo = [self.liteKtvContainerView getPreloadEnterInfo];
        action.busiData = simpleFeed;
    }

    // 调用父类的
    [super handleAction:action];
}

@end
