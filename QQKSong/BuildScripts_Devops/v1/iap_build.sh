# (可更改)构建对应的 info.plist
export plistFileName=IAP-QQKSong-Info.plist

# (可更改)构建对应的 configuration
export configurationName=IAP

# (可更改)构建对应的 configuration
export buildPrivateMachine=true

# (可更改)获取编译架构
echo [BuildArch] selected: $BuildArch
tmpBuildArch=$BuildArch
tmpBuildArch=${BuildArch/,/ } # 字符串替换，把 , 替换为一个空格
if [ ${#tmpBuildArch} -eq 0 ]; then
    tmpBuildArch = "arm64"
fi
export finalBuildArch=$tmpBuildArch
echo [BuildArch] exported: $finalBuildArch

source $(dirname "$0")/build.sh