# WORKSPACE='/Users/<USER>/projects/Xcode/QQKSong2'

echo "需要校验一些配置，确保不带到外网包及 TF 包里。（如 setup_fakeRelease.sh 里修改的）"
# 查找某个文件中命中关键字的数量，第一个参数为关键字(支持正则)，第二个参数为文件路径
function has_keyword()
{
    keyword_count=`grep -c "$1" $2`
    echo "search keyword: $1, count: ${keyword_count}, at path: $2"
    if [ ${keyword_count} -ne '0' ]; then
        return 1
    fi
    return 0
}

# Podfile 校验
podfilePath=${WORKSPACE}/QQKSong/Podfile
has_keyword "all_debug_build_conf.*\'Release\'" ${podfilePath}
is_wrong_podfile=$?
if [ ${is_wrong_podfile} == 1 ]; then
    echo "[ERROR]: Release 模式下 Podfile 的 all_debug_build_conf 不应该有 Release 配置\n"
    exit 1
fi


# xcconfig 校验
xcconfigPath=${WORKSPACE}/QQKSong/xconfigs/Release/QQKSong.release.xcconfig
has_keyword "KS_OTHER_CFLAGS.*-DFAKE_RELEASE=1" ${xcconfigPath}
is_wrong_xcconfig_CFlag_FAKERELEASE=$?
has_keyword "KS_OTHER_CFLAGS.*-DINTERNALBUILD" ${xcconfigPath}
is_wrong_xcconfig_CFlag_INTERNALBUILD=$?
if [ ${is_wrong_xcconfig_CFlag_FAKERELEASE} == 1 ] || [ ${is_wrong_xcconfig_CFlag_INTERNALBUILD} == 1 ]; then
    echo "[ERROR]: Release 模式下 QQKSong.release.xcconfig 的 KS_OTHER_CFLAGS 不应该有 -DFAKE_RELEASE=1 或 -DINTERNALBUILD 配置\n"
    exit 1
fi
has_keyword "KS_OTHER_LDFLAGS.*libWNSCapDylib\.dylib" ${xcconfigPath}
is_wrong_xcconfig_LDFlag=$?
if [ ${is_wrong_xcconfig_LDFlag} == 1 ]; then
    echo "[ERROR]: Release 模式下 QQKSong.release.xcconfig 的 KS_OTHER_LDFLAGS 不应该有 libWNSCapDylib.dylib 配置\n"
    exit 1
fi

# release_build.sh 校验
releaseBuildShellPath=${WORKSPACE}/QQKSong/BuildScripts_Devops/v1/release_build.sh
has_keyword "^export podBuildENV=Release$" ${releaseBuildShellPath}
is_valid_releaseBuildShell=$?
echo "check release, podBuildENV: ${podBuildENV}"
if [ ${is_valid_releaseBuildShell} == 0 ] || [ ${podBuildENV} != 'Release' ]; then
    echo "[ERROR]: Release 模式下 release_build.sh 的 podBuildENV 只能为 Release\n"
    exit 1
fi