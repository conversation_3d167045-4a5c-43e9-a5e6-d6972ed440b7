echo "=================CocoaPods Setup================="

KSongSpecsName="KSongSpecs"
KSongSpecsURL="http://git.code.oa.com/karaoke-cocoapods/Specs.git"

KSongDebugSpecsName="KSongDebugSpecs"
KSongDebugSpecsURL="http://git.code.oa.com/karaoke_ios/DebugSpec.git"

HippySpecsName="oa-hippy-hippy_pod_source"
HippySpecsURL="http://git.code.oa.com/hippy/hippy_pod_source.git"

# $1 为 spec 名字，$2 为 spec 地址
function addPodSpecRepoIfNeeded() 
{
    if [ -z "$(pod repo list | grep $2)" ]
    then
        echo "现在添加 $1"
        pod repo add $1 $2
        handleError "pod repo add failed $1: $2"
    else
        echo "已经配置 $1"
    fi
}

# $1 为错误信息
function handleError()
{
    if [ "$?" != "0" ]
    then
        echo $1

        unsetProxy

        exit 1
    fi
}

# 配置代理，由于现在项目内不依赖 Github 的第三方库，
# 有依赖到的话需要拉取到 git.oa.com 中，
# 所以不需要再使用代理。
function setProxy()
{
    export https_proxy=http://web-proxy.tencent.com:8080
    export http_proxy=http://web-proxy.tencent.com:8080
    export ftp_proxy=http://web-proxy.tencent.com:8080
    export no_proxy="*.oa.com,*.local,10.*.*.*,192.168.*.*,*.local,localhost,127.0.0.1"
}

function unsetProxy()
{
    unset https_proxy
    unset http_proxy
    unset ftp_proxy
    unset no_proxy
}

function updatePodRepo() 
{
    pod repo update $KSongSpecsName
    handleError "pod repo update $KSongSpecsName failed"

    pod repo update $KSongDebugSpecsName
    handleError "pod repo update $KSongDebugSpecsName failed"
    
    pod repo update $HippySpecsName
    handleError "pod repo update $HippySpecsName failed"
}

function installPod()
{
    echo "=================Pod Version================="
    pod --version
    echo "=================Pod Version================="
    pod install --repo-update
    handleError "pod install failed"
}


function setupCocoaPods() 
{
    # 彻底清一波缓存
    # if [[ $cleanAction == "clean" ]]
    # then
    #     pod cache clean --all
    # fi

    # 配置 spec repo
    addPodSpecRepoIfNeeded $KSongSpecsName $KSongSpecsURL
    addPodSpecRepoIfNeeded $KSongDebugSpecsName $KSongDebugSpecsURL
    addPodSpecRepoIfNeeded $HippySpecsName $HippySpecsURL
    
    # 更新 spec repo
    # updatePodRepo # 不再需要手动更新
    installPod
}

setupCocoaPods
