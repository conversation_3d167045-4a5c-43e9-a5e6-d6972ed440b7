# WORKSPACE='/Users/<USER>/projects/Xcode/QQKSong2'
# FakeRelease=true

echo "修改的内容要在 check_release.sh 里进行校验，确保不带到外网包及 TF 包里"

# 原始值
allDebugBuildConf="['Debug', 'Debug_Build', 'DailyBuild_DevOps', 'IAP', 'DailyBuild_DevOps_TestCoverage']"
ksOtherCFlags="-DNS_BLOCK_ASSERTIONS=1 -DNDEBUG"
ksOtherLDFlags='-ld64 -ObjC -lstdc++ -lz -force_load "$(PROJECT_DIR)/QQKSong/Librarys/libWeiboSDK/libWeiboSDK.a" -force_load "$(PROJECT_DIR)/QQKSong/Librarys/QBar/libQBar.a"'
podEnvValue=Release
# FakeRelease 配置下的值
if [[ ${FakeRelease} == true ]]; then
    echo "使用 ReleaseBuild 配置编包，但需要使用某些调试选项。一般系统测试时使用。"
    allDebugBuildConf="['Debug', 'Debug_Build', 'DailyBuild_DevOps', 'IAP', 'DailyBuild_DevOps_TestCoverage', 'Release']"
    # ksOtherCFlags="-DNS_BLOCK_ASSERTIONS=1 -DNDEBUG -DFAKE_RELEASE=1" # 这个有部分调试选项不能用 (要用这个的话，revert 这个提交 b27676479487)
    ksOtherCFlags="-DNS_BLOCK_ASSERTIONS=1 -DNDEBUG -DINTERNALBUILD" # 直接用 INTERNALBUILD 就基本具备所有调试能力
    ksOtherLDFlags='-ld64 -ObjC -lstdc++ -lz -force_load "$(PROJECT_DIR)/QQKSong/Librarys/libWeiboSDK/libWeiboSDK.a" -force_load "$(PROJECT_DIR)/QQKSong/Librarys/QBar/libQBar.a" -force_load "$(PROJECT_DIR)/QQKSong/Librarys/CapMock/libWNSCapDylib.dylib"'
    podEnvValue=FakeRelease

    # 直接增加info.plist中文件夹的共享值
    plistFilePath=${projectDir}/${plistFileName}
    /usr/libexec/PlistBuddy -c "Add :UIFileSharingEnabled bool true" ${WORKSPACE}/QQKSong/Publish-QQKSong-Info.plist
fi

# 修改 Podfile
podfilePath=${WORKSPACE}/QQKSong/Podfile
echo "修改 Podfile: ${podfilePath}"
sed -i "" "/\$all_debug_build_conf = \[/c\ 
  \$all_debug_build_conf = ${allDebugBuildConf}
" ${podfilePath}

# 修改 xcconfig
xcconfigPath=${WORKSPACE}/QQKSong/xconfigs/Release/QQKSong.release.xcconfig
echo "修改 xcconfig: ${xcconfigPath}"
sed -i "" "/\KS_OTHER_CFLAGS = -/c\ 
KS_OTHER_CFLAGS = ${ksOtherCFlags}
" ${xcconfigPath}
sed -i "" "/\KS_OTHER_LDFLAGS = -/c\ 
KS_OTHER_LDFLAGS = ${ksOtherLDFlags}
" ${xcconfigPath}

# 修改 release_build.sh
releaseBuildShellPath=${WORKSPACE}/QQKSong/BuildScripts_Devops/v1/release_build.sh
echo "修改 pod 环境变量: ${releaseBuildShellPath}"
sed -i "" "/\export podBuildENV=/c\ 
export podBuildENV=${podEnvValue}
" ${releaseBuildShellPath}

# 这里子进程 export 变量的话，父进程是收不到的，所以用 echo _POE_ENV_DO_NOT_CHANGE_ 这个特殊标记是用来给父脚本进程提取字符串，得到当前 podEnvValue
# podBuildENV=$(echo "$(bash ${fake_release_sehll})" | grep -Eo '_POE_ENV_DO_NOT_CHANGE_.*_POE_ENV_DO_NOT_CHANGE_' | sed "s/_POE_ENV_DO_NOT_CHANGE_//g")
echo "_POE_ENV_DO_NOT_CHANGE_${podEnvValue}_POE_ENV_DO_NOT_CHANGE_"