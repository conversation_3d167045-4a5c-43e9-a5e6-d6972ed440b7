# (可更改)构建对应的 info.plist
export plistFileName=DevOps-QQKSong-Info.plist

# (可更改)构建对应的 configuration
export configurationName=Debug_Build

# (可更改)获取编译架构
echo [BuildArch] selected: $BuildArch
tmpBuildArch=$BuildArch
tmpBuildArch=${BuildArch/,/ } # 字符串替换，把 , 替换为一个空格
if [ ${#tmpBuildArch} -eq 0 ]; then
    tmpBuildArch = "arm64"
fi
export finalBuildArch=$tmpBuildArch
echo [BuildArch] exported: $finalBuildArch

# 更改 ipa 包名字，加上版本号
versionStr=${MajorVersion}.${MinorVersion}.${FixVersion}
/usr/libexec/PlistBuddy -c "Set :CFBundleDisplayName 全民K歌${versionStr}" ${WORKSPACE}/QQKSong/${plistFileName}

source $(dirname "$0")/build.sh
