echo WorkSpace: $WORKSPACE
setEnv "configurationName" $configurationName

if [[ $BuildSimulator == true ]]; then
    IS_SIMULATOR=true
fi

begintime=`date |sed s/[[:space:]]//g `
shellBeginTime=`date +%s`

# 环境信息
shortVersionStr=${MajorVersion}.${MinorVersion}.${FixVersion}
RealBuildNo=${BuildNo}
if [ -z "$C_MajorVersion" ]
then
      echo "用蓝盾推荐版本号"
else
    echo "自定义参数版本号"
    shortVersionStr=${C_MajorVersion}.${C_MinorVersion}.${C_FixVersion}
    RealBuildNo=${C_BuildNo}
fi
echo $shortVersionStr $RealBuildNo

fullVersionStr=${shortVersionStr}.${RealBuildNo}
commitHash=$(git rev-parse HEAD)
commitHash=${commitHash:0:8}
branchName=$(git rev-parse --abbrev-ref HEAD)

# 路径
projectDir=${WORKSPACE}/QQKSong
buildScriptDir=${projectDir}/BuildScripts_Devops/v1
derivedDataDir=${WORKSPACE}/../DerivedData # WorkSpace 默认为 pipelineID/src/，防止 git reset 时把 DerivedData 给清掉了，增加jobid，并行构建目录分离
mkdir -p $derivedDataDir
pushd ${WORKSPACE}/../DerivedData # WorkSpace 默认为 pipelineID/src/，防止 git reset 时把 DerivedData 给清掉了，增加jobid，并行构建目录分离
setEnv derivedDataDir `pwd`

#删除.app里面的资源文件并排除需要编译的资源文件
derivedDataAppDir=Build/Products/${configurationName}-iphoneos/QQKSong.app 
if [ -d $derivedDataAppDir ];then
    pushd $derivedDataAppDir
    ls | grep -v '\(.car\|.storyboardc\|_CodeSignature\|AppIcon\)' | xargs rm -rfv
    popd
fi

popd
plistFilePath=${projectDir}/${plistFileName}

git lfs install
git lfs pull

# 更新主工程的 info.plist
/usr/libexec/PlistBuddy -c "Set :CFBundleVersion ${RealBuildNo}" $plistFilePath
/usr/libexec/PlistBuddy -c "Set :CFBundleShortVersionString ${shortVersionStr}" $plistFilePath
/usr/libexec/PlistBuddy -c "Set :GitCommitHash ${commitHash}" $plistFilePath
# 添加蓝盾流水线信息
/usr/libexec/PlistBuddy -c "Set :PipelineId ${PIPELINE_ID}" $plistFilePath
/usr/libexec/PlistBuddy -c "Set :PipelineBuildId ${PIPELINE_BUILD_ID}" $plistFilePath
/usr/libexec/PlistBuddy -c "Set :PipelineJobId ${PIPELINE_TASK_ID}" $plistFilePath
/usr/libexec/PlistBuddy -c "Set :PipelineTaskId ${PIPELINE_JOB_ID}" $plistFilePath

# 更新 Notification Extension 的 info.plist
notifiExtDir=${projectDir}/QQKSongNotificationServiceExtension
/usr/libexec/PlistBuddy -c "Set :CFBundleVersion ${RealBuildNo}" ${notifiExtDir}/info.plist
/usr/libexec/PlistBuddy -c "Set :CFBundleShortVersionString ${shortVersionStr}" ${notifiExtDir}/info.plist

# 更新 Sticker Extension 的 info.plist
stickExtDir=${projectDir}/QQKSongStickerPackExtension
/usr/libexec/PlistBuddy -c "Set :CFBundleVersion ${RealBuildNo}" ${stickExtDir}/info.plist
/usr/libexec/PlistBuddy -c "Set :CFBundleShortVersionString ${shortVersionStr}" ${stickExtDir}/info.plist

# 更新 Widget Extension 的 info.plist
widgetExtDir=${projectDir}/QQKSongWidget
/usr/libexec/PlistBuddy -c "Set :CFBundleVersion ${RealBuildNo}" ${widgetExtDir}/info.plist
/usr/libexec/PlistBuddy -c "Set :CFBundleShortVersionString ${shortVersionStr}" ${widgetExtDir}/info.plist

# 更新 Intent Extension 的 info.plist
intentExtDir=${projectDir}/QQKsongIntentExtension
/usr/libexec/PlistBuddy -c "Set :CFBundleVersion ${RealBuildNo}" ${intentExtDir}/info.plist
/usr/libexec/PlistBuddy -c "Set :CFBundleShortVersionString ${shortVersionStr}" ${intentExtDir}/info.plist

# 更新 KGRecordLiveHandle 的 info.plist
liveHandleDir=${projectDir}/KGRecordLiveHandle
/usr/libexec/PlistBuddy -c "Set :CFBundleVersion ${RealBuildNo}" ${liveHandleDir}/info.plist
/usr/libexec/PlistBuddy -c "Set :CFBundleShortVersionString ${shortVersionStr}" ${liveHandleDir}/info.plist



set -e # 命令的返回值一旦非 0，整个脚本马上退出
set -o pipefail # 其后面的命令（包括管道命令）的返回值，为最后一个非零的命令的返回值（或者当管道内的所有命令都执行成功后返回零）

# 清空所有的本地化数据
> ${projectDir}/QQKSong/Resources/strings/en.lproj/InfoPlist.strings
> ${projectDir}/QQKSong/Resources/strings/zh-Hans.lproj/InfoPlist.strings

source ${projectDir}/update_versionurl.sh $shortVersionStr $plistFilePath

#打印一下DerivedData路径内容
cd $derivedDataDir
pwd;ls

# 使用 ReleaseBuild 配置编包，但需要使用某些调试选项。一般系统测试时使用（注意此开关不要在外网包和 TF 包打开）。
if [[ ${FakeRelease} == true ]]; then
    echo "使用 FakeRelease 配置..."
    fake_release_sehll=${buildScriptDir}/setup_fakeRelease.sh
    # bash $fake_release_sehll
    podBuildENV=$(echo "$(bash ${fake_release_sehll})" | grep -Eo '_POE_ENV_DO_NOT_CHANGE_.*_POE_ENV_DO_NOT_CHANGE_' | sed "s/_POE_ENV_DO_NOT_CHANGE_//g")
fi

# 校验 release 包和 TF 包配置
if [[ ${FakeRelease} == false ]] && ([[ ${configurationName} == 'Release' ]] || [[ ${configurationName} == 'Testflight' ]]); then
    echo "校验 release 包和 TF 包配置..."
    check_release_shell=${buildScriptDir}/check_release.sh
    bash $check_release_shell
fi

cd $projectDir

#当前构建环境是dev公有机器时，更改pod缓存地址
if [ ${#buildPrivateMachine} -eq 0 ]; then
    #pod cache到数据磁盘
    mkdir -p /Volumes/data/aboutpod
    export CP_HOME_DIR=/Volumes/data/aboutpod
fi

# Pod 环境变量
if [ ${#podBuildENV} -eq 0 ]; then
    podBuildENV='InternalBuild'
fi
# 如 env podEnv=Release pod install --repo-update
echo "podBuildENV: ${podBuildENV}"

podBeginTime=`date +%s`
env podEnv=${podBuildENV} pod update --repo-update
podEndTime=`date +%s`

cd -

# Clean & Build
xcodebuildPath=${XCODE_PATH}/Contents/Developer/usr/bin/xcodebuild
BuildArch="arm64"#这里强制写arm64，armv7被放弃了
if [[ $IS_SIMULATOR == true ]]; then
 BuildArch="x86_64"
 finalBuildArch=x86_64
 buildsdk=iphonesimulator
else
 buildsdk=iphoneos
fi

echo [BuildArch] final: $finalBuildArch
#忽略inode信息，提高缓存命中率
export IgnoreFileSystemDeviceInodeChanges=1

compileBeginTime=`date +%s`
if [ $XcodeBuildTee = true ]; then
    set -o pipefail && $xcodebuildPath -workspace ${projectDir}/QQKSong.xcworkspace -scheme dailybuildipa -configuration $configurationName -sdk $compileEnv $cleanAction build -derivedDataPath $derivedDataDir ARCHS="$finalBuildArch" VALID_ARCHS="$finalBuildArch" -verbose $teeParams | tee xcodebuild.log
elif [ $XcodeBuildAnalyze = true ]; then
    set -o pipefail && $xcodebuildPath -workspace ${projectDir}/QQKSong.xcworkspace -scheme dailybuildipa -configuration $configurationName -sdk $compileEnv $cleanAction analyze -derivedDataPath $derivedDataDir ARCHS="$finalBuildArch" VALID_ARCHS="$finalBuildArch" -verbose $teeParams | tee xcodebuild.log
else 
    set -o pipefail && $xcodebuildPath -workspace ${projectDir}/QQKSong.xcworkspace -scheme dailybuildipa -configuration $configurationName -sdk $compileEnv $cleanAction build -derivedDataPath $derivedDataDir ARCHS="$finalBuildArch" VALID_ARCHS="$finalBuildArch" -verbose $teeParams | xcpretty
fi
compileEndTime=`date +%s`

productDir=${derivedDataDir}/Build/Products/${configurationName}-${buildsdk}

ipaBeginTime=`date +%s`

pushd ${WORKSPACE}/../DerivedData/Build/Products/${configurationName}-${buildsdk}
rm -rf QQKSong.ipa
rm -rf Payload
rm -rf SwiftSupport
mkdir -p Payload

cp -R QQKSong.app Payload

zip -r QQKSong.ipa Payload
popd

# Copy ipa file to dest dir
resultDir=${WORKSPACE}/result
ipaFileName=QQKSong_${fullVersionStr}_${commitHash}.ipa
if [[ $CODE_COVERAGE == true ]]; then
    ipaFileName=QQKSong_${fullVersionStr}_${commitHash}_coverage.ipa
fi
if [[ $IS_SIMULATOR == true ]]; then
    ipaFileName=QQKSong_x86_64_${fullVersionStr}_${commitHash}.ipa
fi
ipaFilePath=${resultDir}/${ipaFileName}
rm -rf $resultDir
mkdir -p $resultDir
ipaEndTime=`date +%s`

cp ${productDir}/QQKSong.ipa $ipaFilePath
echo Result ipa file path: $ipaFilePath

# Zip and copy .dsym files
dsymZipFileName=dsym_${fullVersionStr}_${commitHash}.zip
if [[ $CODE_COVERAGE == true ]]; then
    dsymZipFileName=dsym_${fullVersionStr}_${commitHash}_coverage.zip
fi
cd ${productDir}
zip -r -q ${resultDir}/${dsymZipFileName} *.dSYM
cd -

# zip and copy .gcno files
if [[ $CODE_COVERAGE == true ]]; then
    gcnoZipFileName=gcno_arm64.zip
    cd ${derivedDataDir}/Build/Intermediates.noindex/QQKSong.build/${configurationName}-iphoneos/QQKSong.build/Objects-normal/arm64
    find . -name '*.gcno' | zip -j ${resultDir}/${gcnoZipFileName} -@
    cd -
fi

shellEndTime=`date +%s`

reportfilepath=${WORKSPACE}/scripts/report/report_compile_cost.sh
if [ -e $reportfilepath ]
then
    echo "shell_begin_time=""$begintime""&""shell_cost_time="`(echo "$shellEndTime-$shellBeginTime" | bc)`"&""pod_install_time="`(echo "$podEndTime-$podBeginTime" | bc)`"&""xcode_compile_cost="`(echo "$compileEndTime-$compileBeginTime" | bc)`"&""ipa_cost_time="`(echo "$ipaEndTime-$ipaBeginTime" | bc)`
    $reportfilepath -d 0 -r "shell_begin_time=""$begintime""&""shell_cost_time="`(echo "$shellEndTime-$shellBeginTime" | bc)`"&""pod_install_time="`(echo "$podEndTime-$podBeginTime" | bc)`"&""xcode_compile_cost="`(echo "$compileEndTime-$compileBeginTime" | bc)`"&""ipa_cost_time="`(echo "$ipaEndTime-$ipaBeginTime" | bc)`
    echo "report cost time"
fi
